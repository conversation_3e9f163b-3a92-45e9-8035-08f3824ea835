import React from 'react';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import ContractList from '@/components/agreements/details/ContractList';
import { PERMISSIONS } from '@/utils/permissions';
import {HeaderWithTitle} from "@/components/common/shared/HeaderWithTitle/HeaderWithTitle";
import {Container} from "@mantine/core";

export default function AgreementDetailsContractsPage() {
  return (
    <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_AGREEMENT] } }}>
      <Container fluid>
      <HeaderWithTitle title="Lista kontraktów umowy" />
      <ContractList />
      </Container>
    </RoleBasedComponent>
  );
}
