import { Container } from '@mantine/core';
import { HeaderWithTitle } from '@/components/common/shared/HeaderWithTitle/HeaderWithTitle';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { PERMISSIONS } from '@/utils/permissions';
import PriceConfirmationList from '@/components/wallets/price-confirmation/PriceConfirmationList';

const PriceConfirmationPage = () => (
  <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_WALLET] } }}>
    <Container fluid>
      <HeaderWithTitle title="Przegląd zakupów" />
      <Container fluid mt="md">
        <PriceConfirmationList />
      </Container>
    </Container>
  </RoleBasedComponent>
);

export default PriceConfirmationPage;
