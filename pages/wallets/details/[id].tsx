import { Container, Divider, Title } from '@mantine/core';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import WalletDetails from '@/components/wallets/details/WalletDetails';
import { PERMISSIONS } from '@/utils/permissions';
import {HeaderWithTitle} from "@/components/common/shared/HeaderWithTitle/HeaderWithTitle";
import useWallet from '@/components/wallets/useWallet';

export default function WalletDetailsPage() {
  const walletQuery = useWallet();
  const wallet = walletQuery?.data;

  const headerTitle = wallet ? `Szczegóły portfela: ${wallet.name}` : 'Szczegóły portfela';

  return (
    <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_WALLET] } }}>
      <Container fluid>
        <HeaderWithTitle title={headerTitle} />
        <Container fluid mt="md">
          <WalletDetails />
        </Container>
      </Container>
    </RoleBasedComponent>
  );
}
