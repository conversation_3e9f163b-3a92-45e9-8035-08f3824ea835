--liquibase formatted sql






--changeset dodz:049_create_results_table.sql
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'wallet_results'

CREATE TABLE IF NOT EXISTS wallet_results (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    media VARCHAR(255) NOT NULL,
    value NUMERIC(38, 2) NOT NULL,
    time_unit VARCHAR(255) NOT NULL CHECK (time_unit IN
                                           ('M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M8', 'M9',
                                            'M10', 'M11', 'M12', 'Q1', 'Q2', 'Q3', 'Q4', 'Y')
        ),
    wallet_id UUID NOT NULL REFERENCES wallets,
    tenant_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

