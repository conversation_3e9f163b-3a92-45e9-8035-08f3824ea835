import { Supplier } from '@/types/Agreement';
import { Contract } from '@/types/Contract';
import { MediaType } from '@/types/MediaType';

interface Validation {
  valid: boolean;
  messages: string[];
}

interface Wallet {
  createdAt: string | null;
  updatedAt: string | null;
  id: string;
  name: string;
  tranches: any[];
  elements: any[];
  prices: any[];
  mediaType: string;
  walletType: string;
  startDate: string;
  year: string;
  fixedRate: string;
  variableRatePercentage: string;
  maxVariableRate: string;
  maxRate: string;
  flatRate: string;
  associatedWallet: string;
  simulationMode: string;
  greenPropertyRightCalculationType: GreenPropertyRightsCalculationType;
  description: string;
  monthlyTranches: [];
  agreement?: Agreement;
}

interface Agreement {
  id: string;
  customer: Customer;
  startDate: string;
  mediaType: MediaType;
  supplier: Supplier;
  contracts: Contract[];
}

interface Customer {
  name: string;
}

interface WalletResponse {
  id: string;
  validation: Validation;
  wallet: Wallet;
}

export enum TimeUnit {
  Y = 'Y',
  Q1 = 'Q1',
  Q2 = 'Q2',
  Q3 = 'Q3',
  Q4 = 'Q4',
  M1 = 'M1',
  M2 = 'M2',
  M3 = 'M3',
  M4 = 'M4',
  M5 = 'M5',
  M6 = 'M6',
  M7 = 'M7',
  M8 = 'M8',
  M9 = 'M9',
  M10 = 'M10',
  M11 = 'M11',
  M12 = 'M12',
}

export enum RealisationType {
  ENERGY = 'ENERGY',
  GAS = 'GAS',
  PROPERTY_RIGHT = 'GREEN_PROPERTY_RIGHTS',
}

export enum GreenPropertyRightsCalculationType {
  ENEA = 'ENEA',
  ENERGA_COMPLEX = 'ENERGA_COMLEX',
  ENERGA_SIMPLE = 'ENERGA_SIMPLE',
  EON = 'EON',
  PGE = 'PGE',
  TIEW = 'TIEW',
}

export enum ElementType {
  NET = 'Cena TGE',
  COSTS = 'Koszty',
  EXCISE = 'Akcyza',
  PROFILE = 'Profil',
  PROFILE_PERCENT = 'Profil (%)',
  GREEN_PROPERTY_RIGHTS = 'Zielone certyfikaty',
  BLUE_PROPERTY_RIGHTS = 'Błękitne certyfikaty',
  WHITE_PROPERTY_RIGHTS = 'Białe certyfikaty',
  TOTAL_PROPERTY_RIGHTS = 'Suma praw majątkowych',
  TOTAL = 'Cena końcowa',
  CORRECTION_FACTOR = 'Składnik korygujący (widoczny)',
  // CORRECTION_FACTOR = 'Składnik korygujący',
  // TGE_CORRECTION_FACTOR = 'Składnik korygujący cenę TGE',
  // MULTIPLIER = 'Mnożnik',
  // CJ = 'Zielone współczynnik - CJ',
  // WF = 'Zielone współczynnik - WF',
  // MARGIN = 'Zielone - marża',
}

export interface ElementForm {
  type: string;
  media: string;
  value: number;
  timeUnit: string;
}

export interface PriceForm {
  type: string;
  media: string;
  value: number;
  timeUnit: string;
}

export interface ContractForm {
  label: string;
  value: string;
}

export interface TrancheForm {
  executionDate: string | Date;
  size: number;
  contract: string;
  price: number;
  timeUnit: TimeUnit;
  priceReference: string;
}


export enum ProductType {
  VOLUME,
  VOLUME_Y,
  VOLUME_Q,
  VOLUME_M,
  VOLUME_SPOT,
  MARKET_MEAN,
  CLIENT_MEAN,
  BENCHMARK_RESULT,
}

export type WalletCategory = 'all' | 'energy' | 'gas';

export type { Wallet, WalletResponse };
