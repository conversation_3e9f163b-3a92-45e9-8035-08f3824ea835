/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.update;

import com.codemonkeys.wallet.agreements.application.common.AgreementConstants;
import com.codemonkeys.wallet.common.framework.crud.update.UpdateByIdControllerImpl;
import com.codemonkeys.wallet.common.framework.security.Permission;
import com.codemonkeys.wallet.common.framework.security.Permissions;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for updating agreements. This class extends {@link UpdateByIdControllerImpl} to
 * handle HTTP PUT requests for updating an existing agreement by its ID.
 */
@RestController
@RequestMapping(AgreementConstants.PATH)
@Slf4j
public class UpdateAgreementControllerImpl
    extends UpdateByIdControllerImpl<
        AgreementId, UpdateAgreementRequest, UpdateAgreementResponse, UpdateAgreementServiceImpl> {
  ObjectMapper objectMapper;

  /**
   * Constructor for {@link UpdateAgreementControllerImpl}.
   *
   * @param service the service responsible for handling agreement updates.
   */
  @Autowired
  public UpdateAgreementControllerImpl(
      UpdateAgreementServiceImpl service, ObjectMapper objectMapper) {
    super(service);
    this.objectMapper = objectMapper;
  }

  /**
   * Handles HTTP PUT requests to update an agreement by its ID.
   *
   * @param id the ID of the agreement to update.
   * @param toBeUpdated the {@link UpdateAgreementRequest} containing the updated data.
   * @return a {@link ResponseEntity} containing the updated agreement details.
   */
  @Override
  @PutMapping("/{id}")
  @Permission(Permissions.UPDATE_AGREEMENT)
  public ResponseEntity<UpdateAgreementResponse> update(
      @PathVariable @NonNull AgreementId id,
      @RequestBody @NonNull UpdateAgreementRequest toBeUpdated) {
    if (toBeUpdated.getId() == null) {
      toBeUpdated.setId(id);
    }
    ResponseEntity<UpdateAgreementResponse> uar = super.update(id, toBeUpdated);
    try {
      String test = objectMapper.writeValueAsString(uar);
      log.info(test);
    } catch (Exception e) {
      log.info(e.getMessage());
    }
    return super.update(id, toBeUpdated);
  }
}
