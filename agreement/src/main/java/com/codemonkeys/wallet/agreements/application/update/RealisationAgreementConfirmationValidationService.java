/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.update;

import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.wallets.ElementRepository;
import com.codemonkeys.wallet.domain.wallets.TranchesRepository;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.google.common.collect.Lists;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service responsible for validating operations on agreements that could affect realisation confirmation status.
 * Focuses on contract updates and removals in the context of agreements, ensuring that operations
 * are not performed on contracts with confirmed realisations in associated wallets.
 */
@Service
public class RealisationAgreementConfirmationValidationService {
  /** Error message key for validation failures related to confirmed realisation changes */
  private static final String REALISATION_CHANGE_NOT_ALLOWED =
      "validations.wallet.confirmedRealisation";

  /**
   * Constructs a new RealisationAgreementConfirmationValidationService with required repositories.
   *
   * @param walletRepository Repository for accessing Wallet entities
   * @param elementRepository Repository for accessing Element entities
   * @param trancheRepository Repository for accessing Tranche entities
   * @param contractRepository Repository for accessing Contract entities
   */
  @Autowired
  public RealisationAgreementConfirmationValidationService(
      WalletRepository walletRepository,
      ElementRepository elementRepository,
      TranchesRepository trancheRepository,
      ContractRepository contractRepository) {}

  /**
   * Validates contract updates within an agreement to ensure that changes
   * do not affect contracts with confirmed realisations in associated wallets.
   * 
   * First checks for contracts being removed, then validates average calculation changes
   * for remaining contracts to prevent modifications to confirmed realisations.
   *
   * @param agreementId Identifier of the agreement being updated
   * @param contractsBeforeUpdate Set of contracts before the update
   * @param contractsAfterUpdate Set of contracts after the update
   * @throws ValidationFailedException if changes would affect contracts with confirmed realisations
   */
  public void verifyContractUpdate(
      AgreementId agreementId,
      Set<Contract> contractsBeforeUpdate,
      Set<Contract> contractsAfterUpdate) {
    // Verify contracts being removed
    Set<Contract> contractsToBeRemoved =
        Agreement.getContractDifference(
            new HashSet<>(contractsBeforeUpdate), new HashSet<>(contractsAfterUpdate));
    contractsToBeRemoved.forEach(this::verifyContractRemoval);
    
    // Create map of contracts before update for easy lookup
    Map<ContractId, Contract> beforeUpdateMap =
        contractsBeforeUpdate.stream()
            .collect(Collectors.toMap(Contract::getId, contract -> contract));
            
    // Check each updated contract for sensitive changes
    for (Contract updatedContract : contractsAfterUpdate) {
      Contract originalContract = beforeUpdateMap.get(updatedContract.getId());
      if (originalContract == null) {
        continue; // New contract, skip verification
      }
      
      // Check if average calculation dates have changed
      if (!originalContract
          .getAverageCalculation()
          .equals(updatedContract.getAverageCalculation())) {
          
        // Check all wallets associated with this contract
        Set<Wallet> wallets = originalContract.getAgreement().getWallets();
        TimeUnit timeUnit = originalContract.getName().getTimeUnit();
        
        if (timeUnit != null) {
          for (Wallet wallet : wallets) {
            if (wallet.hasConfirmedRealisation(timeUnit, originalContract.getMedia())) {
              throwValidationFailedException(REALISATION_CHANGE_NOT_ALLOWED);
            }
          }
        }
      }
    }
  }

  /**
   * Validates if a contract can be removed from an agreement based on associated wallets' realisation status.
   * Prevents removal of contracts if any associated wallet has confirmed realisation for the contract's time unit and media.
   *
   * @param contract The contract to be removed
   * @throws ValidationFailedException if any wallet has confirmed realisation for the contract's time unit and media
   */
  public void verifyContractRemoval(Contract contract) {
    Set<Wallet> wallets = contract.getAgreement().getWallets();
    TimeUnit timeUnit = contract.getName().getTimeUnit();
    
    if (timeUnit != null) {
      for (Wallet wallet : wallets) {
        if (wallet.hasConfirmedRealisation(timeUnit, contract.getMedia())) {
          throwValidationFailedException(REALISATION_CHANGE_NOT_ALLOWED);
        }
      }
    }
  }

  /**
   * Helper method to throw a ValidationFailedException with a translated message.
   *
   * @param message The message key to translate and include in the exception
   * @throws ValidationFailedException with the translated message
   */
  private void throwValidationFailedException(String message) {
    throw new ValidationFailedException(
        I18n.translate(message),
        Lists.newArrayList(WalletValidationMessage.of("", I18n.translate(message))));
  }
}