/* (C)2025 */
package com.codemonkeys.wallet.agreements.contract.application.priceReference;

import com.codemonkeys.wallet.agreements.application.common.ContractConstants;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.contract.vo.ContractName;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** REST controller for retrieving price reference based on agreement ID and contract name. */
@RestController
@RequiredArgsConstructor
@RequestMapping(ContractConstants.PATH)
public class PriceReferenceController {

  private final PriceReferenceService contractPriceReferenceService;

  /**
   * Handles HTTP GET requests to retrieve price reference for a specific contract within an
   * agreement.
   *
   * @param agreementId the {@link AgreementId} of the agreement.
   * @param contractName the {@link ContractName} of the contract.
   * @return a {@link ResponseEntity} containing the list of {@link PriceReference} values.
   */
  @GetMapping("/price-reference")
  public ResponseEntity<List<PriceReference>> getPriceReference(
      @RequestParam AgreementId agreementId, @RequestParam ContractName contractName) {
    return ResponseEntity.ok(
        contractPriceReferenceService.getPriceReference(agreementId, contractName));
  }
}
