/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.find;

import com.codemonkeys.wallet.common.framework.shared.dto.Response;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationResult;
import com.codemonkeys.wallet.domain.agreement.AgreementGroup;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.agreement.vo.Volumes;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import java.time.LocalDate;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Response object for finding an agreement by ID operations, including validation results and
 * agreement details.
 */
@Data
@AllArgsConstructor
public class FindAgreementByIdResponse implements Response {

  private ValidationResult validation;
  private AgreementId id;
  private String mediaType;
  private LocalDate startDate;
  private LocalDate endDate;
  private LocalDate averageCalculationStartDate;
  private Set<Contract> contracts;
  private Set<String> authorizedBuyers;
  private String purchaseModel;
  private Customer customer;
  private Supplier supplier;
  private Volumes volumes;
  private CreateContractRequest.ContractParameters media;
  private CreateContractRequest.ContractParameters propertyRights;
  private String description;
  private AgreementGroup agreementGroup;
  private boolean requiresCustomerAcceptance;
  private boolean sendRecommendation;

  /**
   * Constructs a response indicating a failed find operation with error messages.
   *
   * @param invalidResult the invalid result containing details about the failure.
   */
  public FindAgreementByIdResponse(InvalidResult invalidResult) {
    this.validation = new ValidationResult(false, invalidResult.getMessages());
  }

  /**
   * Determines if the find operation was valid based on the included validation results.
   *
   * @return true if the validation results are positive, otherwise false.
   */
  @Override
  public boolean isValid() {
    return validation.isValid();
  }
}
