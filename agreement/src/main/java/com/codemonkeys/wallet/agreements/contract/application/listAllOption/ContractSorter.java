/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.contract.application.listAllOption;

import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.vo.StringContract;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Component
public class ContractSorter {

  private static final String CONTRACT_YEAR = "contractYear";

  /**
   * Sorts and filters contract options based on the provided year suffix.
   *
   * @param contractOptions a set of unique contract options.
   * @param params a map of query parameters, including an optional "contractYear" parameter.
   * @return a sorted list of contract options.
   */
  public List<String> sortContractOptions(Set<String> contractOptions, Map<String, String> params) {
    // if (params.containsKey(CONTRACT_YEAR)) {
    //  String yearSuffix = STR."-\{params.get(CONTRACT_YEAR).substring(2)}";
    //  return contractOptions.stream()
    //      .filter(name -> name.endsWith(yearSuffix))
    //      .sorted(Comparator.naturalOrder())
    //      .collect(Collectors.toList());
    // }
    List<StringContract> wrapped = contractOptions.stream().map(StringContract::new).toList();
    return sortOptions(wrapped).stream().map(StringContract::toString).toList();
  }

  /**
   * Sorts contracts by type in the following order: Year, Quarter, Month, Spot, and Property
   * Rights.
   *
   * @param contracts Page of Contract objects to be sorted
   * @return List of sorted Contract objects maintaining type ordering
   */
  public List<Contract> sort(Page<Contract> contracts) {
    return sort(contracts.getContent());
  }

  /**
   * Sorts contracts by type in the following order: Year, Quarter, Month, Spot, and Property
   * Rights.
   *
   * @param contracts List of Contract objects to be sorted
   * @return List of sorted Contract objects maintaining type ordering
   */
  public List<Contract> sort(List<Contract> contracts) {
    return contractSortingCriteria().stream()
        .flatMap(predicate -> contracts.stream().filter(predicate).sorted(byName()))
        .toList();
  }

  /**
   * Sorts a list of {@link StringContract} in business order.
   *
   * @param contracts list to sort
   * @return sorted list
   */
  public List<StringContract> sortOptions(List<StringContract> contracts) {
    return stringSortingCriteria().stream()
        .flatMap(predicate -> contracts.stream().filter(predicate).sorted(byString()))
        .toList();
  }

  /** Comparator by raw contract name (lexicographic ascending). */
  private Comparator<Contract> byName() {
    return Comparator.comparing(c -> c.getName().getValue());
  }

  /** Comparator for {@link StringContract}. */
  private Comparator<StringContract> byString() {
    return Comparator.comparing(StringContract::toString);
  }

  /** Business grouping for entity contracts. */
  private List<Predicate<Contract>> contractSortingCriteria() {
    return List.of(
        Contract::isYearContract,
        Contract::isQuarterContract,
        Contract::isMonthContract,
        Contract::isSpotContract,
        Contract::isPropertyRightsContract);
  }

  /** Business grouping for raw string contracts. */
  private List<Predicate<StringContract>> stringSortingCriteria() {
    return List.of(
        StringContract::isYearContract,
        StringContract::isQuarterContract,
        StringContract::isMonthContract,
        StringContract::isSpotContract,
        StringContract::isPropertyRightsContract);
  }
}
