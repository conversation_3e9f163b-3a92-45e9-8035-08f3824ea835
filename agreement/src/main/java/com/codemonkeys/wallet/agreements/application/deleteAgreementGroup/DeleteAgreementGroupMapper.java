/* (C)2025 */
package com.codemonkeys.wallet.agreements.application.deleteAgreementGroup;

import com.codemonkeys.wallet.common.framework.crud.delete.DeleteMapper;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import org.springframework.stereotype.Service;

@Service
public class DeleteAgreementGroupMapper implements DeleteMapper<DeleteAgreementGroupResponse> {

  /**
   * Maps successful deletion of an agreement group to a response DTO.
   *
   * @return response indicating success
   */
  @Override
  public DeleteAgreementGroupResponse toEmptyDto() {
    return new DeleteAgreementGroupResponse(true, null);
  }

  /**
   * Maps validation errors during deletion into a response DTO.
   *
   * @param errors validation result containing failure reasons
   * @return response indicating failure
   */
  @Override
  public DeleteAgreementGroupResponse toDto(InvalidResult errors) {
    return new DeleteAgreementGroupResponse(false, errors);
  }
}
