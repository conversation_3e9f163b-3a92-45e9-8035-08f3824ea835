/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.update;

import com.codemonkeys.wallet.common.framework.crud.update.UpdateByIdServiceImpl;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service implementation for updating agreements. This class extends {@link UpdateByIdServiceImpl}
 * to provide the update logic for agreements based on the provided request.
 */
@Service
public class UpdateAgreementServiceImpl
    extends UpdateByIdServiceImpl<
        UpdateAgreementRequest,
        UpdateAgreementResponse,
        Agreement,
        AgreementId,
        AgreementRepository,
        UpdateAgreementMapper,
        UpdateAgreementValidator> {

  /**
   * Constructor for {@link UpdateAgreementServiceImpl}.
   *
   * @param repository the repository for managing {@link Agreement} entities.
   * @param mapper the mapper for converting update requests and entities.
   * @param validator the validator for validating update requests.
   */
  @Autowired
  public UpdateAgreementServiceImpl(
      AgreementRepository repository,
      UpdateAgreementMapper mapper,
      UpdateAgreementValidator validator) {
    super(repository, mapper, validator);
  }

  /**
   * Updates an agreement by its ID based on the provided {@link UpdateAgreementRequest}.
   *
   * @param agreementId the ID of the agreement to update.
   * @param request the request containing the updated agreement data.
   * @return the response containing the updated agreement details.
   */
  @Override
  public UpdateAgreementResponse updateById(
      AgreementId agreementId, UpdateAgreementRequest request) {
    return super.updateById(agreementId, request);
  }
}
