/* (C)2025 */
package com.codemonkeys.wallet.agreements.application.deleteAgreementGroup;

import com.codemonkeys.wallet.agreements.application.common.AgreementConstants;
import com.codemonkeys.wallet.common.framework.crud.delete.DeleteByIdControllerImpl;
import com.codemonkeys.wallet.common.framework.security.Permission;
import com.codemonkeys.wallet.common.framework.security.Permissions;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementGroupId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(AgreementConstants.PATH + "/groups")
public class DeleteAgreementGroupController
    extends DeleteByIdControllerImpl<
        AgreementGroupId, DeleteAgreementGroupResponse, DeleteAgreementGroupService> {

  public DeleteAgreementGroupController(DeleteAgreementGroupService service) {
    super(service);
  }

  /**
   * REST controller for deleting an agreement group by its ID. Delegates the delete logic to {@link
   * DeleteAgreementGroupService}.
   */
  @DeleteMapping("/{id}")
  @Permission(Permissions.DELETE_AGREEMENT)
  public ResponseEntity<DeleteAgreementGroupResponse> deleteById(
      @PathVariable("id") AgreementGroupId id) {
    return super.deleteById(id);
  }
}
