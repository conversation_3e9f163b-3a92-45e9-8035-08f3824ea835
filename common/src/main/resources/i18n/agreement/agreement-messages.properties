validations.name.agreement={0} is too long name!
validations.failed.agreement=Validation failed for input name.
validations.agreement.name.unique=Agreement with this name already exists.

error.agreement.does.not.exist=Agreement does not exist.
error.agreement.empty.list=Agreement empty list
error.financial.instrument.not.found=Contract with ID {0} not found.
error.authorized.buyer.not.found=Authorized buyer with ID {0} not found.
error.agreement.supplier.not.found=Agreement supplier with ID {0} not found.
validation.agreement.contract.removal=The agreement cannot be saved because contracts for which portfolio tranches have been added have been deleted.
validation.agreementGroup.notFound=Agreement group with contains linked agreements and cannot be deleted.