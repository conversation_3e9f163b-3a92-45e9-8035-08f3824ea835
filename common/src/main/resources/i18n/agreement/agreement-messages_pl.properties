validations.name.agreement={0} jest zbyt długą nazwą!
validations.failed.agreement=Błąd walidacji dla inputu name.
validations.agreement.name.unique=Umowa o tej nazwie już istnieje.
validations.agreement.exists=Umowa dla danego okresu, noś<PERSON><PERSON>, klienta i sprzedawcy istnieje!
error.agreement.does.not.exist=Umowa nie istnieje.
error.agreement.empty.list=Pusta lista umów.
error.financial.instrument.not.found=Kontrakt o ID {0} nie został znaleziony.
error.authorized.buyer.not.found=Autoryzowany kupujący o ID {0} nie został znaleziony.
error.agreement.supplier.not.found=Dostawca umowy o ID {0} nie został znaleziony.
validation.agreement.contract.removal=Nie można zapisać umowy, poniew<PERSON>ż usunięto kontrakty, dla których istnieją dodane transze portfela
validation.agreementGroup.notFound=Grupa zakupowa zawiera powiązane umowy i nie może zostać usunięta.