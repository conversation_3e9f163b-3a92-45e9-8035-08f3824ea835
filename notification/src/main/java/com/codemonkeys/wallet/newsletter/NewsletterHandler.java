/* (C)2024-2025 */
package com.codemonkeys.wallet.newsletter;

import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.AttachmentStorage;
import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.AttachmentRepository;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerContactRepository;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.newsletter.Newsletter;
import com.codemonkeys.wallet.email.event.EmailEvent;
import com.codemonkeys.wallet.email.event.EmailEventAttachment;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class NewsletterHandler {

  private final CustomerRepository customerRepository;
  private final NewsletterTemplate emailTemplateDataPreparer;
  private final ApplicationEventPublisher eventPublisher;
  private final AttachmentRepository attachmentRepository;
  private final AttachmentStorage attachmentStorage;
  private final CustomerContactRepository customerContactRepository;

  /**
   * Handles the process of sending a newsletter to valid contacts. It checks if there are valid
   * contacts and if an attachment is present. If the attachment is not found, no email is sent.
   *
   * @param newsletter the newsletter object containing details of the message to be sent.
   */
  public void sendNewsletter(Newsletter newsletter) {
    try {
      List<CustomerContact> validContacts = findValidContacts();
      if (validContacts.isEmpty()) {
        log.info("Brak kontaktów spełniających kryteria wysyłki newslettera.");
        return;
      }

      Optional<InputStream> attachmentStream = fetchAttachmentStream(newsletter.getAttachmentId());
      sendEmailsWithAttachment(newsletter, validContacts, attachmentStream.orElse(null));

    } catch (Exception e) {
      log.error("Błąd podczas wysyłki newslettera", e);
    }
  }

  /**
   * Finds and returns the list of customer contacts that are eligible to receive the newsletter.
   *
   * @return a list of valid customer contacts that have opted in for newsletters and have email
   *     enabled.
   */
  private List<CustomerContact> findValidContacts() {
    return customerRepository.findAll().stream()
        .flatMap(customer -> customer.getContacts().stream())
        .filter(
            contact ->
                contact.getConfiguration().getNewsletter() && contact.getConfiguration().getEmail())
        .collect(Collectors.toList());
  }

  /**
   * Fetches the InputStream of the newsletter's attachment by its ID. If the attachment does not
   * exist or the ID is null, it returns an empty Optional.
   *
   * @param attachmentId the ID of the attachment to be fetched.
   * @return an Optional containing the InputStream of the attachment if found, otherwise an empty
   *     Optional.
   */
  private Optional<InputStream> fetchAttachmentStream(UUID attachmentId) {
    if (attachmentId == null) {
      log.info("Brak załącznika do tego newslettera.");
      return Optional.empty();
    }

    Optional<Attachment> attachmentOptional =
        attachmentRepository.findById(new AttachmentId(attachmentId));
    if (attachmentOptional.isEmpty()) {
      log.warn("Załącznik o ID {} nie został znaleziony.", attachmentId);
      return Optional.empty();
    }

    return attachmentStorage.prepareFileForDownload(attachmentOptional.get().getPath().getValue());
  }

  /**
   * Sends emails to the provided list of contacts, with or without an attachment.
   *
   * @param newsletter the newsletter object containing the content to be sent.
   * @param contacts the list of customer contacts who will receive the email.
   * @param attachment the InputStream of the attachment to be sent with the email, or null if no
   *     attachment exists.
   */
  private void sendEmailsWithAttachment(
      Newsletter newsletter, List<CustomerContact> contacts, InputStream attachment)
      throws IOException {

    String attachmentName =
        attachment != null ? findAttachmentName(newsletter.getAttachmentId()) : null;
    byte[] attachmentBytes = attachment != null ? attachment.readAllBytes() : null;

    for (CustomerContact contact : contacts) {
      Map<String, Object> variables =
          emailTemplateDataPreparer.prepareTemplateData(newsletter, contact);
      String templateName =
          emailTemplateDataPreparer.determineNewsletterTemplateName(contact, newsletter);

      if (attachmentBytes != null) {
        InputStream attachmentStream = new ByteArrayInputStream(attachmentBytes);
        eventPublisher.publishEvent(
            new EmailEventAttachment(
                this,
                contact.getEmail().getValue(),
                (String) variables.get("subject"),
                templateName,
                variables,
                attachmentStream,
                attachmentName));
      } else {
        eventPublisher.publishEvent(
            new EmailEvent(
                this,
                contact.getEmail().getValue(),
                (String) variables.get("subject"),
                templateName,
                variables));
      }
    }
  }

  /**
   * Finds the name of the attachment by its ID. If the attachment is not found, it returns a
   * default name "Załącznik".
   *
   * @param attachmentId the ID of the attachment.
   * @return the name of the attachment if found, otherwise a default name.
   */
  private String findAttachmentName(UUID attachmentId) {
    return attachmentRepository
        .findById(new AttachmentId(attachmentId))
        .map(attachment -> attachment.getName().getValue())
        .orElse("Załącznik");
  }
}
