/* (C)2024-2025 */
package com.codemonkeys.wallet.email.service;

import com.codemonkeys.wallet.email.config.EmailTechnicalProperties;
import com.codemonkeys.wallet.email.handler.EmailServiceHandler;
import com.codemonkeys.wallet.email.template.EmailTemplate;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.util.ByteArrayDataSource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

@Service
@Profile("example")
@RequiredArgsConstructor
@Slf4j
public class EmailServiceDev implements EmailServiceHandler {

  private final JavaMailSender mailSender;
  private final EmailTemplate templateService;
  private final EmailTechnicalProperties emailOverrideProperties;
  private static final String LOGO_PATH = "templates/logo.png";
  private static final String EMAIL = "<EMAIL>";

  @Override
  public void sendEmail(
      String to, String subject, String templateName, Map<String, Object> variables)
      throws MessagingException, IOException {
    String content = templateService.loadTemplateAndReplaceVariables(templateName, variables);

    String[] recipients = emailOverrideProperties.getRecipients().toArray(new String[0]);

    MimeMessage mimeMessage = mailSender.createMimeMessage();
    MimeMessageHelper helper =
        new MimeMessageHelper(mimeMessage, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, "UTF-8");
    helper.setBcc(recipients);
    helper.setSubject(subject);
    helper.setText(content, true);
    helper.setFrom(EMAIL);
    ClassPathResource logo = new ClassPathResource(LOGO_PATH);
    helper.addInline("logo", logo);
    mailSender.send(mimeMessage);
  }

  @Override
  public void sendEmailWithAttachment(
      String to,
      String subject,
      String templateName,
      Map<String, Object> variables,
      InputStream attachment,
      String attachmentName)
      throws MessagingException, IOException {

    String content = templateService.loadTemplateAndReplaceVariables(templateName, variables);
    String[] recipients = emailOverrideProperties.getRecipients().toArray(new String[0]);

    MimeMessage mimeMessage = mailSender.createMimeMessage();
    MimeMessageHelper helper =
        new MimeMessageHelper(mimeMessage, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED, "UTF-8");
    helper.setBcc(recipients);
    helper.setSubject(subject);
    helper.setText(content, true);
    helper.setFrom(EMAIL);
    byte[] bytes = attachment.readAllBytes();
    helper.addAttachment(
        attachmentName, new ByteArrayDataSource(bytes, MediaType.APPLICATION_PDF_VALUE));
    ClassPathResource logo = new ClassPathResource(LOGO_PATH);
    helper.addInline("logo", logo);

    mailSender.send(mimeMessage);
  }
}
