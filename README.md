# Monorepo Template (Java Spring Boot + Next.js + Shared Types)

Pełny przykład monorepo, w którym:

- **Spring Boot** udostępnia REST‑API oraz klasy DTO (Java 17)
- **typescript‑generator** zamienia te klasy na deklaracje TypeScript
- **Next.js (React 18)** importuje wygenerowane typy bezpośrednio z paczki‐workspace `shared‑types`

---

## Wymagania

| Narzędzie                   | Minimalna wersja    | Sprawdź poleceniem |
| --------------------------- | ------------------- | ------------------ |
| **Node.js**                 | 20 LTS              | `node -v`          |
| **pnpm**                    | 8 lub 10            | `pnpm -v`          |
| **JDK**                     | 17                  | `java -version`    |
| **Git**                     | dowolna współczesna | `git --version`    |
| (opc.) **Gradle globalnie** | 8.x                 | `gradle -v`        |

> Je<PERSON><PERSON> nie masz globalnego Gradle, wystarczy wrapper (`gradlew` / `gradlew.bat`). Wrapper tworzysz raz – patrz krok 3.

---

## Getting Started

```bash
# 1 – sklonuj repozytorium i przejdź do katalogu projektu
 git clone https://github.com/your‑org/monorepo‑template.git
 cd monorepo‑template

# 2 – zainstaluj zależności Node (z tolerancją peer‑deps)
 pnpm install --no-strict-peer-dependencies
```

### 3 – Wygeneruj **Gradle Wrapper** (jednorazowo) 

> ↓ Wybierz **jedną** z opcji instalacji Gradle w Windows, jeśli komenda `gradle` nie jest dostępna.

| Sposób                                  | Komendy          | Uwagi |
| --------------------------------------- | ---------------- | ----- |
| **Winget (Win 10/11)**                  | \`\`\`powershell |       |
| winget install --id Gradle.Gradle -e -h |                  |       |

````|
| **Scoop (bez admina)** | ```powershell
irm get.scoop.sh | iex
scoop install gradle
``` | instaluje się w katalogu użytkownika |
| **Chocolatey** | ```powershell
Set-ExecutionPolicy Bypass -Scope Process -Force; \
  iwr https://community.chocolatey.org/install.ps1 -UseBasicParsing | iex
choco install gradle -y
``` | wymaga PowerShell (Administrator) |
| **Ręczny ZIP** | 1. Pobierz `gradle‑8.8‑bin.zip` z <https://gradle.org/releases>  \
2. Rozpakuj do `C:\Gradle\gradle‑8.8`  \
3. Dodaj `...\bin` do zmiennej **PATH** (lub używaj pełnej ścieżki) | najprostsze offline |

Następnie:

```powershell
# Windows / PowerShell
 cd packages\backend
 gradle wrapper          # teraz powinno działać
 cd ..\..
````

```bash
# macOS / Linux
 cd packages/backend
 gradle wrapper
 cd ../..
```

Po sukcesie w `packages/backend` pojawi się `gradlew`, `gradlew.bat` oraz `gradle/wrapper/...`.

### 4 – Wygeneruj deklaracje TypeScript

```bash
pnpm run build:types       # ↳ uruchamia packages/backend/gradlew(.bat) :api:generateTypeScript
```

Efekt: `packages/shared-types/index.d.ts` zawiera świeże interfejsy.

### 5 – Uruchom środowisko dev (frontend + backend)

```bash
pnpm run dev               # równoległe Next.js + Spring Boot
```

| Usługa       | Komenda                                  | Port domyślny |
| ------------ | ---------------------------------------- | ------------- |
| **Backend**  | `packages/backend/gradlew(.bat) bootRun` | `8080`        |
| **Frontend** | `next dev`                               | `3000`        |

Otwórz [http://localhost:3000](http://localhost:3000) – powinieneś zobaczyć „Hello, Alice”.

---

## Struktura repo (skrót)

```
/monorepo-template
├── .gitignore
├── package.json             # skrypty root + pnpm workspaces
├── pnpm-workspace.yaml
├── settings.gradle.kts      # (opc.) definicja multi‑modułów JVM
└── packages
    ├── backend/
    │   ├── gradlew(.bat)
    │   ├── build.gradle.kts
    │   ├── api/            # DTO + typescript‑generator
    │   ├── core/
    │   └── persistence/
    ├── frontend/            # Next.js 18 + TS
    └── shared-types/        # index.d.ts generowany z Javy
```

---

## Najczęstsze problemy

| Objaw                                                   | Rozwiązanie                                                                                                                |
| ------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- |
| `ERR_PNPM_RECURSIVE_RUN_FIRST_FAIL` przy `pnpm run dev` | Uruchomiłeś `pnpm dev` przed `pnpm install`. Wykonaj instalację zależności.                                                |
| `gradle : The term 'gradle' is not recognized`          | Zainstaluj Gradle globalnie (patrz krok 3) lub użyj pełnej ścieżki do `gradle.bat`.                                        |
| Port 3000/8080 zajęty                                   | Ustaw `set PORT=4001` (Windows) lub `export PORT=4001` (Linux/macOS) przed `pnpm dev`; w Springu zmień `server.port`.      |
| Typy w VS Code nie odświeżają się                       | Po zmianie DTO uruchom `pnpm run build:types` i zrestartuj TS Server (`Ctrl + Shift + P → TypeScript: Restart TS Server`). |

---

Happy coding 🚀

