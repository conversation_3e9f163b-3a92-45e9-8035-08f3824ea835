import bundleAnalyzer from '@next/bundle-analyzer';
import nextTranslate from "next-translate-plugin";

const withBundleAnalyzer = bundleAnalyzer({
    enabled: process.env.ANALYZE === 'true',
});

export default nextTranslate(withBundleAnalyzer({
    reactStrictMode: true,
    output: 'standalone',
    logging: {
        fetches: {
            fullUrl: true,
        },
    },
    eslint: {
        ignoreDuringBuilds: true,
    },
    headers: async function () {
        return [
            {
                // matching all API routes
                source: '/api/:path*',
                headers: [
                    {key: 'Access-Control-Allow-Credentials', value: 'true'},
                    {key: 'Access-Control-Allow-Origin', value: '*'},
                    {key: 'Access-Control-Allow-Methods', value: 'GET,DELETE,PATCH,POST,PUT'},
                    {
                        key: 'Access-Control-Allow-Headers',
                        value:
                            'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version',
                    },
                ],
            },
        ];
    },
    async rewrites() {
        return [
            {
                source: '/api',
                destination: process.env.NEXT_PUBLIC_API_URL,
            },
        ];
    },
}));
