/* (C)2025 */
package com.codemonkeys.wallet.application.tenant;

import com.codemonkeys.wallet.application.auth.common.TenantRequest;
import com.codemonkeys.wallet.application.auth.config.KeycloakProperties;
import com.codemonkeys.wallet.application.auth.keycloak.TokenService;
import com.codemonkeys.wallet.common.framework.logger.SecureLogger;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantService {

  private final TokenService tokenService;
  private final KeycloakProperties keycloakProperties;
  private final ObjectMapper objectMapper = new ObjectMapper();

  /**
   * Switches the tenant for the current session, verifying newTenantId is in user's
   * "whiteList_tenant".
   */
  public boolean switchTenant(String token, String newTenantId) {
    try {
      String userId = tokenService.getUserIdFromToken(token);
      SecureLogger.info(log, "Switching tenant for user ID: {} to tenant-id: {}", userId, newTenantId);

      Keycloak keycloak = tokenService.getKeycloakWithToken(token);
      UserResource userResource = keycloak.realm(keycloakProperties.getRealm()).users().get(userId);
      UserRepresentation user = userResource.toRepresentation();

      Map<String, List<String>> attributes = user.getAttributes();
      if (attributes == null) {
        SecureLogger.warn(log, "User ID: {} has no attributes at all.", userId);
        return false;
      }

      List<String> whiteListValues = attributes.get("whiteList_tenant");
      if (whiteListValues == null || whiteListValues.isEmpty()) {
        SecureLogger.warn(log, "User ID: {} does not have a valid 'whiteList_tenant' attribute.", userId);
        return false;
      }

      boolean isValidTenant =
          whiteListValues.stream()
              .anyMatch(
                  jsonValue -> {
                    try {
                      TenantRequest tenant = objectMapper.readValue(jsonValue, TenantRequest.class);
                      return newTenantId.equals(tenant.getUuid());
                    } catch (Exception e) {
                      SecureLogger.warn(log, "Error parsing mini-JSON in 'whiteList_tenant': {}", jsonValue, e);
                      return false;
                    }
                  });

      if (!isValidTenant) {
        SecureLogger.warn(log, "Invalid tenant-id: {} for user ID: {}", newTenantId, userId);
        return false;
      }

      attributes.put("tenant-id", List.of(newTenantId));
      user.setAttributes(attributes);
      TenantContext.setTenantId(newTenantId);

      SecureLogger.info(log, "Tenant-id successfully switched to: {} for user ID: {}", newTenantId, userId);

      userResource.update(user);

      return true;
    } catch (Exception e) {
      SecureLogger.error(log, "Error while switching tenant", e);
      throw new RuntimeException("Error while switching tenant", e);
    }
  }

  /**
   * Switches the tenant and refreshes the token.
   *
   * <p>This method validates the input parameters, switches the tenant by calling {@link
   * #switchTenant(String, String)}, and if successful, refreshes the token using the provided
   * refresh token.
   *
   * @param accessToken the current access token
   * @param body a map containing "newTenantId" and "refreshToken"
   * @return a ResponseEntity containing the refreshed token data or an error message
   */
  public ResponseEntity<Map<String, Object>> switchTenantAndRefresh(
      String accessToken, Map<String, String> body) {

    Optional<ResponseEntity<Map<String, Object>>> validationError = validateSwitchTenantBody(body);
    if (validationError.isPresent()) {
      return validationError.get();
    }
    String newTenantId = body.get("newTenantId");
    String refreshToken = body.get("refreshToken");

    SecureLogger.info(log, "Attempting to switch tenant to ID: {}", newTenantId);
    boolean success = switchTenant(accessToken, newTenantId);
    if (!success) {
      SecureLogger.warn(log,
          "Failed to switch tenant to ID: {}. Invalid tenant ID or access not allowed.",
          newTenantId);
      return ResponseEntity.badRequest()
          .body(Map.of("error", "Invalid tenant ID or access not allowed."));
    }
    SecureLogger.info(log, "Tenant successfully switched to ID: {}", newTenantId);
    return refreshTokenAndReturn(refreshToken, newTenantId);
  }

  /** Validate if "newTenantId" and "refreshToken" exist in the request. */
  private Optional<ResponseEntity<Map<String, Object>>> validateSwitchTenantBody(
      Map<String, String> body) {
    String newTenantId = body.get("newTenantId");
    String refreshToken = body.get("refreshToken");

    if (!StringUtils.hasText(newTenantId)) {
      SecureLogger.warn(log, "Missing 'newTenantId' parameter in request body");
      return Optional.of(
          ResponseEntity.badRequest().body(Map.of("error", "Missing 'newTenantId' parameter")));
    }
    if (!StringUtils.hasText(refreshToken)) {
      SecureLogger.warn(log, "Missing 'refreshToken' parameter in request body");
      return Optional.of(
          ResponseEntity.badRequest().body(Map.of("error", "Missing 'refreshToken' parameter")));
    }
    return Optional.empty();
  }

  /**
   * Refreshes the token using the provided refresh token and returns the corresponding
   * ResponseEntity.
   *
   * @param refreshToken the refresh token
   * @param newTenantId the new tenant ID (used for logging)
   * @return a ResponseEntity containing the refreshed token data or an error message
   */
  private ResponseEntity<Map<String, Object>> refreshTokenAndReturn(
      String refreshToken, String newTenantId) {

    ResponseEntity<Map<String, Object>> newTokenResponse = tokenService.refreshToken(refreshToken);
    if (newTokenResponse.getStatusCode().is2xxSuccessful() && newTokenResponse.getBody() != null) {
      String newAccessToken = (String) newTokenResponse.getBody().get("access_token");
      String tenantIdFromJwt = getTenantIdFromToken(newAccessToken);
      SecureLogger.info(log,
          "Token refreshed successfully after switching tenant to ID: {}. JWT contains tenant-id: {}",
          newTenantId,
          tenantIdFromJwt);
      return newTokenResponse;
    } else {
      SecureLogger.error(log, "Failed to refresh token after switching tenant to ID: {}", newTenantId);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(Map.of("error", "Failed to refresh token after switching tenant."));
    }
  }

  /**
   * Extracts the tenant ID from a JWT token.
   *
   * @param token the JWT token
   * @return the tenant ID if present; otherwise "Unknown Tenant ID"
   */
  private String getTenantIdFromToken(String token) {
    String[] tokenParts = token.split("\\.");
    if (tokenParts.length < 2) {
      throw new IllegalArgumentException("Invalid token");
    }
    String payload = new String(Base64.getUrlDecoder().decode(tokenParts[1]));
    SecureLogger.debug(log, "Decoded JWT payload: {}", payload);

    JSONObject jsonObject = new JSONObject(payload);
    String tenantId = jsonObject.optString("tenant-id", null);
    if (tenantId == null) {
      SecureLogger.warn(log, "JWT does not contain 'tenant-id'. Payload: {}", payload);
      return "Unknown Tenant ID";
    }
    return tenantId;
  }
}
