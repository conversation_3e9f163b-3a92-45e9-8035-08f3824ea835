/* (C)2024-2025 */
package com.codemonkeys.wallet.application.auth.config;

import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;

/**
 * Custom JWT authentication converter that converts a {@link Jwt} token into an {@link
 * AbstractAuthenticationToken}. This class extracts authorities from the JWT token, including both
 * standard JWT claims and resource roles specific to the application.
 */
@Slf4j
@Component
public class JwtAuthConverter implements Converter<Jwt, AbstractAuthenticationToken> {

  private final JwtGrantedAuthoritiesConverter jwtGrantedAuthoritiesConverter =
      new JwtGrantedAuthoritiesConverter();

  @Value("${jwt.auth.converter.principle-attribute}")
  private String principleAttribute;

  @Value("${jwt.auth.converter.resource-id}")
  private String resourceId;

  /**
   * Converts the given {@link Jwt} token into an {@link AbstractAuthenticationToken} by extracting
   * granted authorities and the principal claim.
   *
   * @param jwt the JWT token to be converted.
   * @return an {@link AbstractAuthenticationToken} representing the JWT authentication.
   */
  @Override
  public AbstractAuthenticationToken convert(@NonNull Jwt jwt) {
    Collection<GrantedAuthority> authorities =
        Stream.concat(
                jwtGrantedAuthoritiesConverter.convert(jwt).stream(),
                extractResourceRoles(jwt).stream())
            .collect(Collectors.toSet());

    extractAndSetTenantId(jwt);

    return new JwtAuthenticationToken(jwt, authorities, getPrincipleClaimName(jwt));
  }

  /**
   * Extracts the tenant ID from the given {@link Jwt} token and sets it in the {@link
   * TenantContext}.
   *
   * <p>The tenant ID is retrieved from the "tenant-id" claim in the JWT token. If the claim is
   * present and non-empty, the ID is stored in {@link TenantContext} for subsequent usage in the
   * application. If the claim is absent or empty, the tenant ID is cleared from the {@link
   * TenantContext}.
   *
   * @param jwt the JWT token containing claims, including "tenant-id".
   */
  private void extractAndSetTenantId(Jwt jwt) {
    String tenantId = jwt.getClaim("tenant-id");
    if (tenantId != null && !tenantId.isEmpty()) {
      //      log.info("Setting tenant-id: {}", tenantId);
      TenantContext.setTenantId(tenantId);
    } else {
      log.debug("No tenant-id found in token. Clearing tenant context.");
      TenantContext.clear();
    }
  }

  /**
   * Extracts the principal claim from the JWT, using the configured principle attribute or the
   * default subject claim.
   *
   * @param jwt the JWT token.
   * @return the principal name from the JWT token.
   */
  private String getPrincipleClaimName(Jwt jwt) {
    String claimName = JwtClaimNames.SUB;
    if (principleAttribute != null) {
      claimName = principleAttribute;
    }
    return jwt.getClaim(claimName);
  }

  /**
   * Extracts resource roles specific to the application from the JWT token's resource_access claim.
   *
   * @param jwt the JWT token.
   * @return a collection of granted authorities based on the resource roles.
   */
  private Collection<? extends GrantedAuthority> extractResourceRoles(Jwt jwt) {
    Map<String, Object> resourceAccess;
    Map<String, Object> resource;
    Collection<String> resourceRoles;
    if (jwt.getClaim("resource_access") == null) {
      return Set.of();
    }
    resourceAccess = jwt.getClaim("resource_access");

    if (resourceAccess.get(resourceId) == null) {
      return Set.of();
    }
    resource = (Map<String, Object>) resourceAccess.get(resourceId);

    resourceRoles = (Collection<String>) resource.get("roles");
    return resourceRoles.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toSet());
  }
}
