/* (C)2024-2025 */
package com.codemonkeys.wallet.application;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@SpringBootApplication
@ComponentScan(basePackages = {"com.codemonkeys.wallet"})
@EntityScan(basePackages = {"com.codemonkeys.wallet"})
@EnableJpaRepositories(basePackages = {"com.codemonkeys.wallet"})
@ConfigurationPropertiesScan(basePackages = {"com.codemonkeys.wallet"})
@EnableJpaAuditing
@EnableScheduling
@EnableRetry
public class Application {

  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }

  @Configuration
  public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
      registry
          .addMapping("/**")
          .allowedOrigins(
              "https://portfel-efbeeqa2brgfhye6.z03.azurefd.net",
              "https://portfel.energysolution.pl",
              "https://wallet-frontend.lemoncliff-6b8b9072.polandcentral.azurecontainerapps.io",
              "http://localhost:3000",
              "http://localhost:3000/",
              "https://walletv2.dev.es-t.pl",
              "https://api.walletv2.dev.es-t.pl",
              "https://customer.wallet.dev.es-t.pl"
          )
          .allowedMethods("GET", "POST", "PUT", "DELETE", "PATCH")
          .allowedHeaders("*");
    }
  }
}
