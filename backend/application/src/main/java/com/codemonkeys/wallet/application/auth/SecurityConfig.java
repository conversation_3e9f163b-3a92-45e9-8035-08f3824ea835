/* (C)2024-2025 */
package com.codemonkeys.wallet.application.auth;

import com.codemonkeys.wallet.application.auth.config.JwtAuthConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Security configuration class for setting up OAuth2 resource server and method security. This
 * class configures the HTTP security, including CSRF protection, session management, and JWT-based
 * authentication using the custom {@link JwtAuthConverter}.
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

  private final JwtAuthConverter jwtAuthConverter;

  /**
   * Configures the security filter chain to use OAuth2 resource server and stateless session
   * management. Defines access control rules for specific endpoints and uses the custom {@link
   * JwtAuthConverter} to process JWT tokens.
   *
   * @param http the {@link HttpSecurity} object used to configure the HTTP security.
   * @return a {@link SecurityFilterChain} representing the configured security filter chain.
   * @throws Exception if an error occurs while configuring the security chain.
   */
  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    http.csrf(
            csrf ->
                csrf.ignoringRequestMatchers(
                    "/auth/token",
                    "/auth/refresh-token",
                    "/auth/reset-password",
                    "/v3/api-docs/**",
                    "/swagger-ui.html",
                    "/swagger-ui/**"))
        .formLogin(AbstractHttpConfigurer::disable) // Disable form login -> this is new
        .httpBasic(AbstractHttpConfigurer::disable) // Disable basic auth if not used -> this is new
        .sessionManagement(
            session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .authorizeHttpRequests(
            auth ->
                auth.requestMatchers(
                        "/auth/token",
                        "/auth/refresh-token",
                        "/auth/reset-password",
                        "/auth/code-exchange",
                        "/v3/api-docs/**",
                        "/swagger-ui.html",
                        "/swagger-ui/**")
                    .permitAll()
                    .anyRequest()
                    .authenticated())
        .oauth2ResourceServer(
            oauth2 -> oauth2.jwt(jwt -> jwt.jwtAuthenticationConverter(jwtAuthConverter)));
    return http.build();
  }
}
