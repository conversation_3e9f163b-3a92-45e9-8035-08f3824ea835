server:
  servlet:
    context-path: /api
spring:
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  application:
    name: wallet
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
  jpa:
    generate-ddl: false
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        tenant_identifier_resolver: com.codemonkeys.wallet.application.tenant.TenantIdentifierResolver
  liquibase:
    enabled: true
    change-log: classpath:liquibase/db-changelog-master.xml
  mail:
    host: ${SMTP_HOST}
    port: ${SMTP_PORT}
    username: ${SMTP_USER}
    password: ${SMTP_PASSWORD}
    smtp-auth: true
    starttls-enable: true
    mime-charset: UTF-8
    debug: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI}
          jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs

email:
  technical-addresses:
    enabled: true
    recipients:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>


keycloak:
  auth-server-url: ${KEYCLOAK_BASE_URL}
  realm: ${KEYCLOAK_REALM}
  resource: ${KEYCLOAK_RESOURCE}
  credentials:
    secret: ${KEYCLOAK_SECRET}

jwt:
  auth:
    converter:
      resource-id: wallet
      principle-attribute: preferred_username
    mvc:
      versioned-context-path: /api/v1


storage:
  access-key: ${STORAGE_ACCESS_KEY}
  secret-key: ${STORAGE_SECRET_KEY}
  endpoint: ${STORAGE_ENDPOINT}
  prefix: ${STORAGE_PREFIX}
  bucket: ${STORAGE_BUCKET}

archive-agreements:
  cron: "@daily"
attachment:
  cron: "@daily"
price-email:
  cron: 0 0 18 * * ?
price:
  cron: 0 0 17 * * *
  retry:
    max-attempts: 3
    delay: 2000
    multiplier: 1.5
  prices:
    energy:
      url: https://tge.pl/energia-elektryczna-otf
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazOtfPriceService
      expectedRecords:
        days:
          MONDAY: 10
          TUESDAY: 11
          WEDNESDAY: 11
          THURSDAY: 11
          FRIDAY: 11
          SATURDAY: 0
          SUNDAY: 0
        holiday: 0
    energy-rdn:
      url: https://tge.pl/energia-elektryczna-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazRdnPriceService
      expectedRecords:
        days:
          MONDAY: 6
          TUESDAY: 6
          WEDNESDAY: 6
          THURSDAY: 6
          FRIDAY: 6
          SATURDAY: 6
          SUNDAY: 6
        holiday: 6
    gas-otf:
      url: https://tge.pl/gaz-otf
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazOtfPriceService
      expectedRecords:
        days:
          MONDAY: 11
          TUESDAY: 12
          WEDNESDAY: 12
          THURSDAY: 12
          FRIDAY: 12
          SATURDAY: 0
          SUNDAY: 0
        holiday: 0
    property-rights:
      url: https://tge.pl/prawa-majatkowe-rpm
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.PropertyRightsPriceService
      expectedRecords:
        days:
          MONDAY: 0
          TUESDAY: 0
          WEDNESDAY: 0
          THURSDAY: 0
          FRIDAY: 0
          SATURDAY: 0
          SUNDAY: 0
        holiday: 0
    gas-rdn:
      url: https://tge.pl/gaz-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazRdnPriceService
      expectedRecords:
        days:
          MONDAY: 1
          TUESDAY: 1
          WEDNESDAY: 1
          THURSDAY: 1
          FRIDAY: 1
          SATURDAY: 1
          SUNDAY: 1
        holiday: 1
    gas-rdb:
      url: https://tge.pl/gaz-rdb
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.GazRdbPriceService
      expectedRecords:
        days:
          MONDAY: 1
          TUESDAY: 1
          WEDNESDAY: 1
          THURSDAY: 1
          FRIDAY: 1
          SATURDAY: 1
          SUNDAY: 1
        holiday: 1


logging:
  level:
    root: INFO
    org.springframework: INFO
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: INFO
    #    org.hibernate.SQL: DEBUG
    #    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    #    org.hibernate.orm.jdbc.bind: TRACE
    org.hibernate.engine.jdbc.spi.SqlExceptionHelper: INFO
    org.springframework.security: DEBUG
    #org.springframework.security.web: INFO
    # Add to application.properties