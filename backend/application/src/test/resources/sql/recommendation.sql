INSERT INTO public.recommendations (id, created_at, updated_at, agreement_group, carrier,
                                    contract_id, customer_id, customer_name, deadline,
                                    email_template_comment, executor, contract, price,
                                    price_reference, requires_customer_acceptance,
                                    send_recommendation, status, supplier_name, time_unit, volume, tenant_id)
VALUES ('a693afe7-5a7f-4839-a39c-f15c03da047f', '2024-11-26 16:58:00.987137',
        '2024-11-26 16:58:00.987137', '', 'EE', 'e62d764e-5ae3-48e0-b86d-d106f36910d0',
        '388dd89e-e50b-44a1-a4f1-8eafa80745e7', 'Klient_testy_integracyjne',
        '2024-11-26 10:51:00.000000', '', '', 'BASE_M-01-24', '111', 'DKR', false, false,
        'ACCEPTED_ORDER_PENDING', 'Sprzedawca_testy_integracyjne', 'M1', '55', 'ce34bac1-6d98-49d9-af53-0ecad235d172');
