/* (C)2024-2025 */
package com.codemonkeys.wallet.application.validation.calculation.element;

import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

import com.codemonkeys.wallet.application.validation.AbstractElementIntegrationTest;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.apache.commons.lang3.ArrayUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultMatcher;

/**
 * Integration test class for validating element creation in the wallet application.
 *
 * <p>Performs comprehensive validation of element creation across different time units and media
 * types, ensuring proper handling of elements.
 *
 * <p>Test methods utilize SQL scripts for database setup and JSON files for input data, covering
 * scenarios for year, quarter, and month elements.
 *
 * <h2>Test Coverage</h2>
 *
 * <ul>
 *   <li>Year element creation
 *   <li>Quarter element creation
 *   <li>Month element creation
 * </ul>
 *
 * <p>Extends {@link AbstractElementIntegrationTest} to inherit common testing infrastructure and
 * provides specific test cases for element validation.
 *
 * @see AbstractElementIntegrationTest
 * @see ElementType
 * @see TimeUnit
 * @see Media
 */
class CreateTotalPropertyRightsValidationIntegrationTest extends AbstractElementIntegrationTest {
  /**
   * Constructor for the integration test, autowiring necessary dependencies.
   *
   * @param objectMapper JSON object mapper for serialization/deserialization
   * @param mvc MockMvc for performing web requests in integration tests
   * @param repository Wallet repository for database interactions
   */
  @Autowired
  public CreateTotalPropertyRightsValidationIntegrationTest(
      ObjectMapper objectMapper, MockMvc mvc, WalletRepository repository) {
    super(objectMapper, mvc);
  }

  /**
   * Validates the successful creation of a year Total property rights element.
   *
   * <p>Uses predefined SQL scripts to set up the test environment and a JSON file with specific
   * element details for validation.
   *
   * @throws Exception if element creation validation fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
  })
  @Test
  void shouldSuccessfullyCreateYearTotalPropertyRightsElement() throws Exception {
    testElementCreation(
        "/json/valid/add-element-wallet-details/total_property_rights/addYearTotalPropertyRightsElement.json",
        Media.ENERGY,
        100.0,
        TimeUnit.Y,
        ElementType.TOTAL_PROPERTY_RIGHTS,
        200);
  }

  /**
   * Validates the successful creation of a quarter Total property rights element.
   *
   * <p>Uses predefined SQL scripts to set up the test environment and a JSON file with specific
   * element details for validation.
   *
   * @throws Exception if element creation validation fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
  })
  @Test
  void shouldSuccessfullyCreateQuarterTotalPropertyRightsElement() throws Exception {
    testElementCreation(
        "/json/valid/add-element-wallet-details/total_property_rights/addQuarterTotalPropertyRightsElement.json",
        Media.ENERGY,
        100.0,
        TimeUnit.Q1,
        ElementType.TOTAL_PROPERTY_RIGHTS,
        200);
  }

  /**
   * Validates the successful creation of a month Total property rights element.
   *
   * <p>Uses predefined SQL scripts to set up the test environment and a JSON file with specific
   * element details for validation.
   *
   * @throws Exception if element creation validation fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
  })
  @Test
  void shouldSuccessfullyCreateMonthTotalPropertyRightsElement() throws Exception {
    testElementCreation(
        "/json/valid/add-element-wallet-details/total_property_rights/addMonthTotalPropertyRightsElement.json",
        Media.ENERGY,
        100.0,
        TimeUnit.M1,
        ElementType.TOTAL_PROPERTY_RIGHTS,
        200);
  }

  /**
   * Validates the successful creation of a month Total property rights element.
   *
   * <p>Uses predefined SQL scripts to set up the test environment and a JSON file with specific
   * element details for validation.
   *
   * @throws Exception if element creation validation fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
  })
  @Test
  void shouldSuccessfullyCreateSummaryMonthTotalPropertyRightsElement() throws Exception {
    // add green elements, check property rights total is 100, add white elements check property
    // rights total is 200, add blue elements check property rights total is 300
    String greenPath =
        formatElementPath(Media.ENERGY, ElementType.GREEN_PROPERTY_RIGHTS, TimeUnit.Y, 100.0);
    ResultMatcher[] greenCreateMatchers =
        List.of(jsonPath("$.elements", hasSize(1)), jsonPath(greenPath).exists())
            .toArray(new ResultMatcher[0]);
    ResultMatcher[] greenPriceMatchers =
        timeUnitMatchers(TimeUnit.Y.getMonths(), ElementType.TOTAL_PROPERTY_RIGHTS, 100.0);
    ResultMatcher[] greenMatchers = ArrayUtils.addAll(greenCreateMatchers, greenPriceMatchers);
    testElementCreation(
        "/json/valid/add-element-wallet-details/total_property_rights/shouldSuccessfullyCreateSummaryMonthTotalPropertyRightsElement_1.json",
        200,
        greenMatchers);
    String whitePath =
        formatElementPath(Media.ENERGY, ElementType.WHITE_PROPERTY_RIGHTS, TimeUnit.Y, 100.0);
    ResultMatcher[] whiteCreateMatchers =
        List.of(jsonPath("$.elements", hasSize(2)), jsonPath(whitePath).exists())
            .toArray(new ResultMatcher[0]);
    ResultMatcher[] whitePriceMatchers =
        timeUnitMatchers(TimeUnit.Y.getMonths(), ElementType.TOTAL_PROPERTY_RIGHTS, 200.0);
    ResultMatcher[] whiteMatchers = ArrayUtils.addAll(whiteCreateMatchers, whitePriceMatchers);
    testElementCreation(
        "/json/valid/add-element-wallet-details/total_property_rights/shouldSuccessfullyCreateSummaryMonthTotalPropertyRightsElement_2.json",
        200,
        whiteMatchers);

    String bluePath =
        formatElementPath(Media.ENERGY, ElementType.BLUE_PROPERTY_RIGHTS, TimeUnit.Y, 100.0);
    ResultMatcher[] blueCreateMatchers =
        List.of(jsonPath("$.elements", hasSize(3)), jsonPath(bluePath).exists())
            .toArray(new ResultMatcher[0]);
    ResultMatcher[] bluePriceMatchers =
        timeUnitMatchers(TimeUnit.Y.getMonths(), ElementType.TOTAL_PROPERTY_RIGHTS, 300.0);
    ResultMatcher[] blueMatchers = ArrayUtils.addAll(blueCreateMatchers, bluePriceMatchers);
    testElementCreation(
        "/json/valid/add-element-wallet-details/total_property_rights/shouldSuccessfullyCreateSummaryMonthTotalPropertyRightsElement_3.json",
        200,
        blueMatchers);
  }
}
