/* (C)2025 */
package com.codemonkeys.wallet.application.tranche.update;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

import com.codemonkeys.wallet.application.validation.AbstractTrancheIntegrationTest;
import com.codemonkeys.wallet.common.tests.testcontainers.PostgresTest;
import com.codemonkeys.wallet.wallet.application.tranches.common.TrancheConstants;
import com.codemonkeys.wallet.wallet.application.tranches.update.UpdateTrancheRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;

/** Class for tranche update integration tests */
@SpringBootTest
@AutoConfigureMockMvc
@PostgresTest
@DirtiesContext
public class UpdateTrancheIntegrationTest extends AbstractTrancheIntegrationTest {

  /**
   * Constructs a new {@code AbstractIntegrationTest} with the specified dependencies.
   *
   * @param objectMapper the {@link ObjectMapper} for JSON operations.
   * @param mvc the {@link MockMvc} for testing MVC controllers.
   */
  @Autowired
  public UpdateTrancheIntegrationTest(ObjectMapper objectMapper, MockMvc mvc) {
    super(objectMapper, mvc);
  }

  /**
   * Test case to validate tranche update
   *
   * @throws Exception if the test execution fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/tranches.sql"
  })
  @Test
  void shouldUpdateValidTranche() throws Exception {
    String jsonFilePath = "json/valid/manipulate-tranche/update/shouldUpdateValidTranche.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    UpdateTrancheRequest utr = objectMapper.readValue(jsonPayload, UpdateTrancheRequest.class);
    String url = buildUrlPath(TrancheConstants.PATH, utr.getId().getId());
    String body = objectMapper.writeValueAsString(utr);
    String jsonPath = "$.tranche";
    String jsonPathTimeUnit = buildJsonPath(jsonPath, "timeUnit");
    String jsonPathSize = buildJsonPath(jsonPath, "size");
    String jsonPathPrice = buildJsonPath(jsonPath, "price");
    putRequest(url, body, 200)
        .andExpect(jsonPath(jsonPath).isNotEmpty())
        .andExpect(jsonPath(jsonPathTimeUnit).value("M1"))
        .andExpect(jsonPath(jsonPathSize).value(25))
        .andExpect(jsonPath(jsonPathPrice).value(111));
  }
}
