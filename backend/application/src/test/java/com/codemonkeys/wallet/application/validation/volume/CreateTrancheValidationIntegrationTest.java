/* (C)2024-2025 */
package com.codemonkeys.wallet.application.validation.volume;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

import com.codemonkeys.wallet.application.AbstractIntegrationTest;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.wallet.application.tranches.common.TrancheConstants;
import com.codemonkeys.wallet.wallet.application.tranches.create.CreateTrancheRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.JsonPathResultMatchers;

/**
 * Integration tests for validating the "Add Tranche" functionality.
 *
 * <p>This class verifies the server-side validation logic for adding tranches, including scenarios
 * where constraints such as wallet volume limits and recommendation volume limits are exceeded. The
 * tests ensure that invalid inputs are rejected with appropriate error messages and valid inputs
 * are accepted successfully.
 */
@Slf4j
class CreateTrancheValidationIntegrationTest extends AbstractIntegrationTest {
  /** Error message key for wallet volume exceeding its time unit limit. */
  private static final String WALLET_VOLUME_EXCEEDS_LIMIT =
      "validations.wallet.volumeTimeUnitLimit";

  /** Error message key indicating that the media volume is less than the minimum allowed value. */
  private static final String MEDIA_VOLUME_LESS_THAN_MIN = "validations.media.volumeLessThanMin";

  /** Error message key indicating that the media volume is not multiple of the allowed value. */
  private static final String MEDIA_VOLUME_MULTIPLE_MISMATCH =
      "validations.media.volumeMultipleMismatch";

  /** Error message key indicating that the media volume exceeds the maximum allowed value. */
  private static final String MEDIA_VOLUME_EXCEEDS_LIMIT = "validation.media.volumeAboveMax";

  /** Error message key for recommendation volume exceeding its time unit limit. */
  private static final String RECOMMENDATION_VOLUME_EXCEEDS_LIMIT =
      "validations.contract.recommendationVolumeExceedsLimit";

  /** Minimum allowed volume for a contract. */
  private static final Integer CONTRACT_MIN_VOLUME = 25;

  /** Multiple allowed volume for a contract. */
  private static final Integer CONTRACT_MULTIPLE_VOLUME = 10;

  /** Maximum allowed volume for a contract. */
  private static final Integer CONTRACT_MAX_VOLUME = 50;

  /**
   * Constructor for the test class.
   *
   * @param objectMapper the ObjectMapper for JSON processing
   * @param mvc the MockMvc instance for simulating HTTP requests
   * @param repository the WalletRepository for accessing wallet data
   */
  @Autowired
  public CreateTrancheValidationIntegrationTest(
      ObjectMapper objectMapper, MockMvc mvc, WalletRepository repository) {
    super(objectMapper, mvc);
  }

  /**
   * Tests that year tranche creation is rejected when the wallet's volume limit is exceeded.
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectYearTrancheCreationWhenWalletVolumeLimitExceeded() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-tranche/shouldRejectYearTrancheCreationWhenWalletVolumeLimitExceeded.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, TimeUnit.Y);
    postRequest(TrancheConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Tests that quarter tranche creation is rejected when the wallet's volume limit is exceeded.
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectQuarterTrancheCreationWhenWalletVolumeLimitExceeded() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-tranche/shouldRejectQuarterTrancheCreationWhenWalletVolumeLimitExceeded.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, TimeUnit.Q1);
    postRequest(TrancheConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Tests that month tranche creation is rejected when the wallet's volume limit is exceeded.
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenWalletVolumeLimitExceeded() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-tranche/shouldRejectMonthTrancheCreationWhenWalletVolumeLimitExceeded.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, TimeUnit.M1);
    postRequest(TrancheConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Tests that month tranche creation is rejected when the tranche volume is below minimum
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenVolumeBelowMinimum() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-tranche/shouldRejectMonthTrancheCreationWhenVolumeBelowMinimum.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(MEDIA_VOLUME_LESS_THAN_MIN, CONTRACT_MIN_VOLUME);
    postRequest(TrancheConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Tests that month tranche creation is allowed when the tranche volume is above minimum
   *
   * @throws Exception if an errors occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWhenVolumeAboveMinimum() throws Exception {
    String jsonFilePath =
        "/json/valid/add-tranche/shouldAllowMonthTrancheCreationWhenVolumeAboveMinimum.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(TrancheConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }

  /**
   * Tests that month tranche creation is rejected when the tranche volume is not multiple
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenVolumeIsNotMultiple() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-tranche/shouldRejectMonthTrancheCreationWhenVolumeIsNotMultiple.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage =
        I18n.translate(MEDIA_VOLUME_MULTIPLE_MISMATCH, CONTRACT_MULTIPLE_VOLUME);
    postRequest(TrancheConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test that month tranche creation is allowed when the tranche volume is multiple
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWhenVolumeIsMultiple() throws Exception {
    String jsonFilePath =
        "/json/valid/add-tranche/shouldAllowMonthTrancheCreationWhenVolumeIsMultiple.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(TrancheConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }

  /**
   * Tests that month tranche creation is rejected when the tranche volume is above maximum
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenVolumeAboveMaximum() throws Exception {
    String jsonFilePath =
        "json/invalid/add-tranche/shouldRejectMonthTrancheCreationWhenVolumeAboveMaximum.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(MEDIA_VOLUME_EXCEEDS_LIMIT, CONTRACT_MAX_VOLUME);
    postRequest(TrancheConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Tests that month tranche creation is allowed when the tranche volume is below maximum
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWhenVolumeBelowMaximum() throws Exception {
    String jsonFilePAth =
        "/json/valid/add-tranche/shouldAllowMonthTrancheCreationWhenVolumeBelowMaximum.json";
    String jsonPayload = loadJsonFromFile(jsonFilePAth);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(TrancheConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }

  /**
   * Tests that month tranche creation is rejected when the recommendation volume limit is exceeded.
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/recommendation.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenRecommendationVolumeLimitExceeded() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-tranche/shouldRejectMonthTrancheCreationWhenRecommendationVolumeLimitExceeded.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(RECOMMENDATION_VOLUME_EXCEEDS_LIMIT, TimeUnit.M1);
    postRequest(TrancheConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Tests that month tranche creation is allowed when the recommendation volume is valid.
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/recommendation.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWhenRecommendationVolumeValid() throws Exception {
    String jsonFilePath =
        "/json/valid/add-tranche/shouldAllowMonthTrancheCreationWhenRecommendationVolumeValid.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    String jsonPath = "$.id";
    postRequest(TrancheConstants.PATH, body, 200).andExpect(jsonPath(jsonPath).isNotEmpty());
  }

  /**
   * Tests that month tranche creation is allowed with valid data.
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWithValidData() throws Exception {
    String jsonFilePath =
        "/json/valid/add-tranche/shouldAllowMonthTrancheCreationWithValidData.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateTrancheRequest ctr = objectMapper.readValue(jsonPayload, CreateTrancheRequest.class);
    String body = objectMapper.writeValueAsString(ctr);
    String jsonPath = "$.id";
    postRequest(TrancheConstants.PATH, body, 200).andExpect(jsonPath(jsonPath).isNotEmpty());
  }
}
