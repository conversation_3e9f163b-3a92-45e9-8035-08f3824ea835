/* (C)2024-2025 */
package com.codemonkeys.wallet.application.validation.volume;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

import com.codemonkeys.wallet.application.AbstractIntegrationTest;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationConstants;
import com.codemonkeys.wallet.recommendation.application.create.CreateRecommendationRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.JsonPathResultMatchers;

/**
 * Integration tests for validating the creation of recommendations within a wallet system. The
 * tests ensure business rules and validation constraints are respected when creating
 * recommendations.
 *
 * <p>These tests utilize the Spring Framework's MockMvc to simulate HTTP requests and verify
 * server-side behavior. They also leverage SQL scripts for database setup and cleanup to maintain
 * test isolation.
 *
 * <p><strong>Tests include:</strong>
 *
 * <ul>
 *   <li>Rejecting tranche creation when wallet volume exceeds limits.
 *   <li>Rejecting new recommendations when conflicts with pending ones exist.
 *   <li>Successful creation of recommendations when validation passes.
 * </ul>
 *
 * <p>This class extends {@link AbstractIntegrationTest}, inheriting common testing setup for
 * integration tests.
 *
 * <p><strong>Dependencies:</strong>
 *
 * <ul>
 *   <li>{@link ObjectMapper} for JSON serialization and deserialization.
 *   <li>{@link MockMvc} for simulating HTTP requests.
 *   <li>{@link WalletRepository} for accessing wallet data.
 * </ul>
 *
 * <p><strong>SQL Scripts:</strong>
 *
 * <ul>
 *   <li>Various scripts for database setup and cleanup before each test.
 * </ul>
 *
 * <p><strong>Annotations:</strong>
 *
 * <ul>
 *   <li>{@code @Sql} for preloading SQL scripts.
 *   <li>{@code @Test} to mark test methods.
 * </ul>
 */
class CreateRecommendationValidationIntegrationTest extends AbstractIntegrationTest {
  /**
   * Error message key indicating that the wallet volume limit for a specific time unit has been
   * exceeded.
   */
  private static final String WALLET_VOLUME_EXCEEDS_LIMIT =
      "validations.wallet.volumeTimeUnitLimit";

  /** Error message key indicating that the media volume is less than the minimum allowed value. */
  private static final String MEDIA_VOLUME_LESS_THAN_MIN = "validations.media.volumeLessThanMin";

  /** Error message key indicating that the media volume is not multiple of the allowed value. */
  private static final String MEDIA_VOLUME_MULTIPLE_MISMATCH =
      "validations.media.volumeMultipleMismatch";

  /** Error message key indicating that the media volume exceeds the maximum allowed value. */
  private static final String MEDIA_VOLUME_EXCEEDS_LIMIT = "validation.media.volumeAboveMax";

  /** Error message key indicating that the recommendation volume limit has been exceeded. */
  private static final String RECOMMENDATION_VOLUME_EXCEEDS_LIMIT =
      "validations.contract.recommendationVolumeExceedsLimit";

  /** Minimum allowed volume for a contract. */
  private static final Integer CONTRACT_MIN_VOLUME = 25;

  /** Multiple allowed volume for a contract. */
  private static final Integer CONTRACT_MULTIPLE_VOLUME = 10;

  /** Maximum allowed volume for a contract. */
  private static final Integer CONTRACT_MAX_VOLUME = 50;

  /**
   * Constructs the test class with required dependencies.
   *
   * @param objectMapper the object mapper for JSON processing
   * @param mvc the MockMvc instance for HTTP request simulation
   * @param repository the wallet repository for data access
   */
  @Autowired
  public CreateRecommendationValidationIntegrationTest(
      ObjectMapper objectMapper, MockMvc mvc, WalletRepository repository) {
    super(objectMapper, mvc);
  }

  /**
   * Test case to validate rejection of year tranche creation when the wallet's volume exceeds the
   * allowed limit.
   *
   * @throws Exception if the test execution fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectYearTrancheCreationWhenWalletVolumeLimitExceeded() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-recommendation/shouldRejectYearTrancheCreationWhenWalletVolumeLimitExceeded.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, TimeUnit.Y);
    postRequest(RecommendationConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test case to validate rejection of quarter tranche creation when the wallet's volume exceeds
   * the allowed limit.
   *
   * @throws Exception if the test execution fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectQuarterTrancheCreationWhenWalletVolumeLimitExceeded() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-recommendation/shouldRejectQuarterTrancheCreationWhenWalletVolumeLimitExceeded.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, TimeUnit.Q1);
    postRequest(RecommendationConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test case to validate rejection of month tranche creation when the wallet's volume exceeds the
   * allowed limit.
   *
   * @throws Exception if the test execution fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenWalletVolumeLimitExceeded() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-recommendation/shouldRejectMonthTrancheCreationWhenWalletVolumeLimitExceeded.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);

    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, TimeUnit.M1);
    postRequest(RecommendationConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test case to validate rejection of month tranche creation when volume is below minimum
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenVolumeIsBelowMinimum() throws Exception {
    String jsonFilePath =
        "json/invalid/add-recommendation/shouldRejectMonthTrancheCreationWhenVolumeIsBelowMinimum.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(MEDIA_VOLUME_LESS_THAN_MIN, CONTRACT_MIN_VOLUME);
    postRequest(RecommendationConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test case to validate allowance of month tranche creation when volume is above minimum
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWhenVolumeIsAboveMinimum() throws Exception {
    String jsonFilePath =
        "/json/valid/add-recommendation/shouldAllowMonthTrancheCreationWhenVolumeIsAboveMinimum.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(RecommendationConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }

  /**
   * Test case to validate rejection of month tranche creation when volume is not multiple
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenVolumeIsNotMultiple() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-recommendation/shouldRejectMonthTrancheCreationWhenVolumeIsNotMultiple.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage =
        I18n.translate(MEDIA_VOLUME_MULTIPLE_MISMATCH, CONTRACT_MULTIPLE_VOLUME);
    postRequest(RecommendationConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test case to validate allowance of month tranche creation when volume is multiple
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWhenVolumeIsMultiple() throws Exception {
    String jsonFilePath =
        "/json/valid/add-recommendation/shouldAllowMonthTrancheCreationWhenVolumeIsMultiple.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(RecommendationConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }

  /**
   * Test case to validate rejection of month tranche creation when volume is above maximum
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldRejectMonthTrancheCreationWhenVolumeIsAboveMaximum() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-recommendation/shouldRejectMonthTrancheCreationWhenVolumeIsAboveMaximum.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest ccr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(ccr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(MEDIA_VOLUME_EXCEEDS_LIMIT, CONTRACT_MAX_VOLUME);
    postRequest(RecommendationConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test case to validate allowance of month tranche creation when volume is below maximum
   *
   * @throws Exception if an error occurs during the test execution
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldAllowMonthTrancheCreationWhenVolumeIsBelowMaximum() throws Exception {
    String jsonFilePath =
        "/json/valid/add-recommendation/shouldAllowMonthTrancheCreationWhenVolumeIsBelowMaximum.json";
    String jsonPayload = loadPayload(jsonFilePath);
    CreateRecommendationRequest ccr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(ccr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(RecommendationConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }

  /**
   * Test case to validate rejection of new recommendation creation when there are conflicting
   * pending recommendations.
   *
   * @throws Exception if the test execution fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/recommendation.sql",
  })
  @Test
  void shouldRejectNewRecommendationWhenPendingRecommendationExists() throws Exception {
    String jsonFilePath =
        "/json/invalid/add-recommendation/shouldRejectNewRecommendationWhenPendingRecommendationExists.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.detail");
    String expectedMessage = I18n.translate(RECOMMENDATION_VOLUME_EXCEEDS_LIMIT, TimeUnit.M1);
    postRequest(RecommendationConstants.PATH, body, 409).andExpect(jsonPath.value(expectedMessage));
  }

  /**
   * Test case to validate successful creation of a month tranche when the recommendation meets all
   * validation criteria.
   *
   * @throws Exception if the test execution fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/y_tranches.sql",
  })
  @Test
  void shouldSuccessfullyCreateMonthTrancheWhenValidRecommendation() throws Exception {
    String jsonFilePath =
        "/json/valid/add-recommendation/shouldSuccessfullyCreateMonthTrancheWhenValidRecommendation.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(RecommendationConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }

  /**
   * Test case to validate successful creation of a recommendation when there are no conflicts with
   * pending recommendations.
   *
   * @throws Exception if the test execution fails
   */
  @Sql({
    "/sql/cleanup.sql",
    "/sql/customer.sql",
    "/sql/supplier.sql",
    "/sql/agreement.sql",
    "/sql/contracts.sql",
    "/sql/wallets.sql",
    "/sql/recommendation.sql",
  })
  @Test
  void shouldSuccessfullyCreateRecommendationWhenNoPendingRecommendationConflicts()
      throws Exception {
    String jsonFilePath =
        "/json/valid/add-recommendation/shouldSuccessfullyCreateRecommendationWhenNoPendingRecommendationConflicts.json";
    String jsonPayload = loadJsonFromFile(jsonFilePath);
    CreateRecommendationRequest crr =
        objectMapper.readValue(jsonPayload, CreateRecommendationRequest.class);
    String body = objectMapper.writeValueAsString(crr);
    JsonPathResultMatchers jsonPath = jsonPath("$.id");
    postRequest(RecommendationConstants.PATH, body, 200).andExpect(jsonPath.isNotEmpty());
  }
}
