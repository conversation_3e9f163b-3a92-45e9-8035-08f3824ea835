/* (C)2024-2025 */
package com.codemonkeys.wallet.customer.factories;

import com.codemonkeys.wallet.common.framework.domain.Note;
import com.codemonkeys.wallet.common.framework.domain.enums.CountryCode;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.customers.customer.application.create.CreateCustomerRequest;
import com.codemonkeys.wallet.customers.customer.application.update.UpdateCustomerRequest;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerSegment;
import com.codemonkeys.wallet.domain.customer.vo.CustomerConfiguration;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.group.vo.GroupId;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import net.datafaker.Faker;

public class CustomerRequestTestFactory {
  protected static final Faker faker = new Faker(new Locale("pl"));

  public static CreateCustomerRequest createCustomerRequest(
      String name,
      String taxNumber,
      CountryCode country,
      CustomerSegment segment,
      CustomerConfiguration configuration,
      List<Note> notes,
      List<CustomerContact> contacts,
      List<AttachmentId> attachmentIds,
      GroupId group) {
    return new CreateCustomerRequest(
        name,
        taxNumber,
        country,
        segment,
        configuration,
        notes,
        contacts,
        attachmentIds,
        group != null ? UUID.fromString(group.getId()) : null);
  }

  public static UpdateCustomerRequest updateCustomerRequest(
      CustomerId id,
      String name,
      String taxNumber,
      CountryCode country,
      CustomerSegment segment,
      CustomerConfiguration configuration,
      List<Note> notes,
      List<CustomerContact> contacts,
      List<AttachmentId> attachmentIds,
      GroupId group) {
    return new UpdateCustomerRequest(
        id,
        name,
        taxNumber,
        country,
        segment,
        configuration,
        notes,
        contacts,
        attachmentIds,
        group != null ? UUID.fromString(group.getId()) : null);
  }

  public static class Valid {
    public static CreateCustomerRequest createDefaultPolishCustomerRequest() {
      return createCustomerRequest(
          faker.company().name(),
          "1222343244",
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static CreateCustomerRequest createDefaultPolishCustomerRequestWithGroup(
        GroupId groupId) {
      return createCustomerRequest(
          faker.company().name(),
          "1222343244",
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          groupId);
    }

    public static CreateCustomerRequest createDefaultPolishCustomerRequestWithTaxNumber(
        String taxnumber) {
      return createCustomerRequest(
          faker.company().name(),
          taxnumber,
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static CreateCustomerRequest createDefaultPolishCustomerRequestWithOptionalFields(
        CustomerConfiguration configuration, GroupId groupId, AttachmentId attachmentId) {
      return createCustomerRequest(
          faker.company().name(),
          "1222343244",
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          configuration,
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(attachmentId),
          groupId);
    }

    public static UpdateCustomerRequest updateDefaultPolishCustomerRequest(GroupId id) {
      return updateCustomerRequest(
          CustomerId.randomId(),
          faker.company().name(),
          "5228814439",
          CountryCode.EN,
          CustomerSegment.SHOPPING_CHAINS,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          id);
    }

    public static UpdateCustomerRequest randomUpdateCustomerRequestWithId(CustomerId customerId) {
      return updateCustomerRequest(
          customerId,
          faker.company().name(),
          "5228814439",
          CountryCode.EN,
          CustomerSegment.SHOPPING_CHAINS,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static UpdateCustomerRequest randomUpdateCustomerRequestWithIdAndTaxNumber(
        CustomerId customerId, String taxNumber) {
      return updateCustomerRequest(
          customerId,
          faker.company().name(),
          taxNumber,
          CountryCode.PL,
          CustomerSegment.SHOPPING_CHAINS,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static UpdateCustomerRequest randomUpdateCustomerRequestWithIdAndName(
        CustomerId customerId, String name) {
      return updateCustomerRequest(
          customerId,
          name,
          "5228814439",
          CountryCode.EN,
          CustomerSegment.SHOPPING_CHAINS,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static CreateCustomerRequest createDefaultPolishCustomerRequestWithFile() {
      return createCustomerRequest(
          faker.company().name(),
          "1222343244",
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static UpdateCustomerRequest updateDefaultPolishCustomerRequestWithNote(
        CustomerId customerId, Note note) {
      return updateCustomerRequest(
          customerId,
          faker.company().name(),
          "5228814439",
          CountryCode.EN,
          CustomerSegment.SHOPPING_CHAINS,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(note),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static UpdateCustomerRequest updateDefaultPolishCustomerRequestWithContact(
        CustomerId customerId, CustomerContact customerContact) {
      return updateCustomerRequest(
          customerId,
          faker.company().name(),
          "5228814439",
          CountryCode.EN,
          CustomerSegment.SHOPPING_CHAINS,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(customerContact),
          Lists.newArrayList(),
          null);
    }
  }

  public static class Invalid {
    public static CreateCustomerRequest createInvalidTaxNumberCustomerRequest() {
      return createCustomerRequest(
          faker.company().name(),
          faker.lorem().fixedString(3),
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static UpdateCustomerRequest updateInvalidTaxNumberCustomerRequest(
        CustomerId customerId) {
      return updateCustomerRequest(
          customerId,
          faker.company().name(),
          faker.lorem().fixedString(3),
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static CreateCustomerRequest createTooLongNameCustomer() {
      return createCustomerRequest(
          faker.lorem().fixedString(105),
          "1222343244",
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }

    public static UpdateCustomerRequest updateTooLongNameCustomer(CustomerId customerId) {
      return updateCustomerRequest(
          customerId,
          faker.lorem().fixedString(105),
          "1222343244",
          CountryCode.PL,
          CustomerSegment.INDUSTRY,
          CustomerConfiguration.of(false, false),
          Lists.newArrayList(),
          Lists.newArrayList(),
          Lists.newArrayList(),
          null);
    }
  }
}
