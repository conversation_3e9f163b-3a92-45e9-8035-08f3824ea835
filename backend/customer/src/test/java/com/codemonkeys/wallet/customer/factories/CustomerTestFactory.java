/* (C)2024-2025 */
package com.codemonkeys.wallet.customer.factories;

import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.Note;
import com.codemonkeys.wallet.common.framework.domain.enums.CountryCode;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.vo.*;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentPath;
import com.codemonkeys.wallet.common.framework.domain.vo.ModuleType;
import com.codemonkeys.wallet.common.framework.domain.vo.Name;
import com.codemonkeys.wallet.common.framework.domain.vo.NoteId;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerSegment;
import com.codemonkeys.wallet.domain.customer.vo.CustomerConfiguration;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.customer.vo.CustomerName;
import com.codemonkeys.wallet.domain.group.Group;
import com.codemonkeys.wallet.domain.group.vo.GroupName;
import com.google.common.collect.Sets;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import net.datafaker.Faker;
import org.springframework.mock.web.MockMultipartFile;

public class CustomerTestFactory {
  protected static final Faker faker = new Faker(new Locale("pl"));

  private static Country createPoland() {
    return Country.of(CountryCode.PL);
  }

  public static class Valid {
    public static Customer createDefaultPolishCustomer() {
      return createCustomer("Polish customer", "1222343244", createPoland());
    }

    public static Customer createDefaultPolishCustomerWithOptionalFields() {

      Attachment attachment = createAttachment();
      Note note = createNote();

      return createCustomerWithOptionalFields(
          "Polish customer", "1222343244", createPoland(), attachment, note);
    }

    public static Group createGroup() {
      return new Group(
          GroupName.of(faker.lorem().fixedString(50)),
          MediaType.ENERGY,
          Year.of("2024"),
          Sets.newConcurrentHashSet());
    }

    public static Attachment createAttachment() {
      return Attachment.of(
          AttachmentId.randomId(),
          Name.of("Attachment name"),
          AttachmentPath.of("prefix", "name"),
          Name.of("Storage name"),
          Name.of("Bucket name"),
          new MockMultipartFile("file name", new byte[] {}));
    }

    public static Note createNote() {
      return new Note(
          NoteId.randomId(), ModuleType.CUSTOMER, PersonName.of("test", "test"), "content");
    }

    public static Customer createRandomCustomer(String taxNumber) {
      return createCustomer(faker.company().name(), taxNumber, createPoland());
    }

    public static Customer createCustomer(String name, String taxNo, Country country) {
      GroupName groupName = GroupName.of(faker.lorem().fixedString(10));
      Year year = Year.of("2024");
      Group group = new Group(groupName, MediaType.ENERGY, year, new HashSet<>());

      return new Customer(
          CustomerName.of(name),
          TaxNumber.of(taxNo, country),
          CustomerSegment.INDUSTRY,
          country,
          CustomerConfiguration.of(true, true),
          group);
    }

    public static Customer createCustomerWithOptionalFields(
        String name, String taxNo, Country country, Attachment attachment, Note note) {
      GroupName groupName = GroupName.of(faker.lorem().fixedString(10));
      Year year = Year.of("2024");
      Group group = new Group(groupName, MediaType.ENERGY, year, new HashSet<>());

      return new Customer(
          CustomerId.randomId(),
          CustomerName.of(name),
          TaxNumber.of(taxNo, country),
          CustomerSegment.INDUSTRY,
          country,
          CustomerConfiguration.of(true, true),
          List.of(attachment),
          List.of(),
          List.of(note),
          group);
    }
  }
}
