/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.contact.application.update;

import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.Validator;
import com.codemonkeys.wallet.customers.contact.application.common.CreateUpdateCustomerContactRequest;
import com.codemonkeys.wallet.customers.contact.application.common.CreateUpdateCustomerContactValidator;
import com.codemonkeys.wallet.domain.customer.CustomerContactRepository;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import net.xyzsd.dichotomy.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Validator for validating customer contact update requests. Extends {@link
 * CreateUpdateCustomerContactValidator} to provide additional validation logic specific to updates.
 */
@Service
public class UpdateCustomerContactValidator extends CreateUpdateCustomerContactValidator
    implements Validator<CreateUpdateCustomerContactRequest> {

  /**
   * Constructs a new {@code UpdateCustomerContactValidator} with the specified repositories.
   *
   * @param repository the repository used to access customer contact data.
   * @param customerRepository the repository used to access customer data.
   */
  @Autowired
  protected UpdateCustomerContactValidator(
      CustomerContactRepository repository, CustomerRepository customerRepository) {
    super(repository, customerRepository);
  }

  /**
   * Validates the customer contact update request. Checks if the customer and contact exist, and
   * validates the uniqueness of the email and phone number.
   *
   * @param entity the customer contact request to validate.
   * @return a {@link Result} containing the validation result.
   */
  @Override
  public Result<ValidResult, InvalidResult> validate(CreateUpdateCustomerContactRequest entity) {
    Result<ValidResult, InvalidResult> customerExistResult =
        this.customerExist(entity.getCustomerId());
    Result<ValidResult, InvalidResult> contactExistResult = this.contactExist(entity.getId());
    Result<ValidResult, InvalidResult> emailResult =
        this.validateEmailUniqueness(entity.getEmail(), entity.getId());
    Result<ValidResult, InvalidResult> phoneResult =
        this.validatePhoneUniqueness(entity.getNumber(), entity.getId());
    return emailResult.and(phoneResult).and(contactExistResult).and(customerExistResult);
  }
}
