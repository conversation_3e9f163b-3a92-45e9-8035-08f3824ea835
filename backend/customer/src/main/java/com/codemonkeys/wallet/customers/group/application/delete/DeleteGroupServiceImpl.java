/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.group.application.delete;

import com.codemonkeys.wallet.common.framework.crud.delete.DeleteByIdServiceImpl;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationMessage;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.group.Group;
import com.codemonkeys.wallet.domain.group.GroupRepository;
import com.codemonkeys.wallet.domain.group.vo.GroupId;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * A service class that extends {@link DeleteByIdServiceImpl} to handle the deletion of groups. It
 * integrates with Spring's dependency injection to manage data access and validation logic
 * specifically tailored for group entities.
 */
@Slf4j
@Service
public class DeleteGroupServiceImpl
    extends DeleteByIdServiceImpl<
        GroupId,
        DeleteGroupResponse,
        Group,
        GroupRepository,
        DeleteGroupMapper,
        DeleteGroupIdValidator> {

  private GroupRepository groupRepository;
  private CustomerRepository customerRepository;

  /**
   * Constructs a new {@code DeleteGroupServiceImpl} with necessary dependencies.
   *
   * @param repository The {@link GroupRepository} used for group-related database operations. It
   *     provides access to the underlying data layer necessary for deletion operations.
   * @param validator The {@link DeleteGroupIdValidator} used for validating group deletion
   *     requests. This ensures that deletion operations adhere to business rules and constraints.
   * @param mapper The {@link DeleteGroupMapper} used for converting between Group domain objects
   *     and their corresponding DTOs. This mapper facilitates the translation of deletion results
   *     into responses that can be sent back to the client.
   */
  public DeleteGroupServiceImpl(
      GroupRepository repository,
      DeleteGroupIdValidator validator,
      DeleteGroupMapper mapper,
      CustomerRepository customerRepository,
      GroupRepository groupRepository) {
    super(repository, validator, mapper);
    this.customerRepository = customerRepository;
    this.groupRepository = groupRepository;
  }

  /**
   * Deletes a group identified by its unique identifier. This method handles the deletion process
   * in a transactional manner, ensuring that all related changes are committed as a single atomic
   * operation. It also ensures that all customers associated with the group are disassociated
   * before the group is deleted, preventing foreign key constraint violations.
   *
   * @param groupId The unique identifier of the group to be deleted.
   * @return A {@link DeleteGroupResponse} object that encapsulates the outcome of the deletion
   *     operation. This response includes a success status and potentially a list of errors if the
   *     operation fails.
   * @throws EntityNotFoundException if the group with the specified ID does not exist.
   */
  @Transactional
  @Override
  public DeleteGroupResponse deleteById(GroupId groupId) {
    Group group = groupRepository.findById(groupId).orElse(null);
    if (group == null) {
      log.error("Group not found with id: {}", groupId);
      String errorMessage = I18n.translate("error.group.not.found", groupId);
      List<ValidationMessage> errorMessages =
          List.of(WalletValidationMessage.of("groupId", errorMessage));
      return new DeleteGroupResponse(false, InvalidResult.of(errorMessages));
    }

    group
        .getCustomers()
        .forEach(
            customer -> {
              customer.setGroup(null);
              customerRepository.save(customer);
            });

    groupRepository.delete(group);
    log.info("Group successfully deleted with id: {}", groupId.getId());
    return new DeleteGroupResponse(true, null);
  }
}
