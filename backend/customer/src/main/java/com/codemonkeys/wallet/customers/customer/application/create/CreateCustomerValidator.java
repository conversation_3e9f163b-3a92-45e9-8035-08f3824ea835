/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.customer.application.create;

import com.codemonkeys.wallet.common.framework.domain.vo.Country;
import com.codemonkeys.wallet.common.framework.domain.vo.TaxNumber;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.Validator;
import com.codemonkeys.wallet.customers.customer.application.common.CreateUpdateCustomerValidator;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerName;
import net.xyzsd.dichotomy.Result;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * The CreateCustomerValidator class is responsible for validating the CreateCustomerRequest
 * objects. It ensures the uniqueness of the tax number and the name by checking the
 * CustomerRepository.
 */
@Service
public class CreateCustomerValidator extends CreateUpdateCustomerValidator
    implements Validator<CreateCustomerRequest> {

  /**
   * Constructor for CreateCustomerValidator.
   *
   * @param customerRepository {@link CustomerRepository} instance used for customer operations.
   *     Must not be {@code null}.
   * @see CustomerRepository
   */
  @Autowired
  public CreateCustomerValidator(CustomerRepository customerRepository) {
    super(customerRepository);
  }

  private static @NotNull CustomerName customerNameFromRequest(CreateCustomerRequest request) {
    return CustomerName.of(request.getName());
  }

  private static @NotNull TaxNumber taxNumberFromRequest(CreateCustomerRequest request) {
    Country country = Country.of(request.getCountry());
    return TaxNumber.of(request.getTaxNumber(), country);
  }

  @Override
  public Result<ValidResult, InvalidResult> validate(CreateCustomerRequest request) {
    TaxNumber tax = taxNumberFromRequest(request);
    CustomerName name = customerNameFromRequest(request);
    Result<ValidResult, InvalidResult> taxnumberResult = validateTaxNumberUniqueness(tax);
    Result<ValidResult, InvalidResult> nameResult = validateNameUniqueness(name);
    return taxnumberResult.and(nameResult);
  }
}
