/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.customer.application.delete;

import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.*;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import net.xyzsd.dichotomy.Result;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DeleteCustomerIdValidator implements Validator<CustomerId> {

  private final CustomerRepository customerRepository;

  /**
   * Validates if the specified CustomerId corresponds to an existing customer in the repository.
   *
   * <p>This method checks whether a customer with the given CustomerId exists in the customer
   * repository. It leverages the {@link CustomerRepository#existsById} method to perform this
   * check. If the customer exists, it returns a {@link Result} instance encapsulating a {@link
   * ValidResult}, indicating that the validation passed successfully. If the customer does not
   * exist, it returns a {@link Result} instance containing an {@link InvalidResult}, indicating
   * that the customer could not be found, and thus, the validation failed.
   *
   * <p>This method is typically used as a pre-validation step in operations that require the
   * existence of a customer in the database, such as deletion or update operations, to ensure data
   * integrity and provide meaningful feedback.
   *
   * @param customerId the unique identifier of the customer to validate, encapsulated in a {@link
   *     CustomerId} object.
   * @return a {@link Result} instance representing the outcome of the validation. It contains
   *     either a {@link ValidResult} if the customer exists, or an {@link InvalidResult} if the
   *     customer does not exist.
   */
  @Override
  public Result<ValidResult, InvalidResult> validate(CustomerId customerId) {
    if (customerId == null) {
      String errorMessage = I18n.translate("error.customer.id.null");
      List<ValidationMessage> errors =
          List.of(WalletValidationMessage.of("customerId", errorMessage));
      return Result.ofErr(InvalidResult.of(errors));
    }
    boolean exists = customerRepository.existsById(customerId);
    if (!exists) {
      String errorMessage = I18n.translate("error.customer.not.found", customerId);
      List<ValidationMessage> errors =
          List.of(WalletValidationMessage.of("customerId", errorMessage));
      return Result.ofErr(InvalidResult.of(errors));
    }
    return Result.ofOK(ValidResult.of());
  }
}
