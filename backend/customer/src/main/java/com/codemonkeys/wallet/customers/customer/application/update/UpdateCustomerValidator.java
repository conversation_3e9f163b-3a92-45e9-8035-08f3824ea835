/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.customer.application.update;

import com.codemonkeys.wallet.common.framework.domain.vo.Country;
import com.codemonkeys.wallet.common.framework.domain.vo.TaxNumber;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationMessage;
import com.codemonkeys.wallet.common.framework.shared.validation.Validator;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.customers.customer.application.common.CreateUpdateCustomerValidator;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.customer.vo.CustomerName;
import com.google.common.collect.Lists;
import java.util.List;
import net.xyzsd.dichotomy.Result;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

/**
 * The UpdateCustomerValidator class is responsible for validating the update of a customer entity.
 * It extends the CreateUpdateCustomerValidator class and implements the Validator interface. It
 * provides methods for validating the existence, uniqueness, and other properties of a customer.
 */
@Service
public class UpdateCustomerValidator extends CreateUpdateCustomerValidator
    implements Validator<UpdateCustomerRequest> {

  public static final String ID = "id";
  protected static final String NOT_EXISTS = "%s does not exist";

  public UpdateCustomerValidator(CustomerRepository customerRepository) {
    super(customerRepository);
  }

  private static @NotNull CustomerName customerNameFromRequest(UpdateCustomerRequest request) {
    return CustomerName.of(request.getName());
  }

  private static @NotNull TaxNumber taxNumberFromRequest(UpdateCustomerRequest request) {
    Country country = Country.of(request.getCountry());
    return TaxNumber.of(request.getTaxNumber(), country);
  }

  Result<ValidResult, InvalidResult> validateExists(CustomerId id) {
    List<ValidationMessage> messages =
        Lists.newArrayList(WalletValidationMessage.of(ID, String.format(NOT_EXISTS, id)));
    return repository.existsById(id)
        ? Result.ofOK(ValidResult.of())
        : Result.ofErr(InvalidResult.of(messages));
  }

  @Override
  public Result<ValidResult, InvalidResult> validate(UpdateCustomerRequest request) {
    TaxNumber tax = taxNumberFromRequest(request);
    CustomerName name = customerNameFromRequest(request);
    Result<ValidResult, InvalidResult> existsResult = validateExists(request.getId());
    Result<ValidResult, InvalidResult> taxnumberResult =
        validateTaxNumberUniqueness(tax, request.getId());
    Result<ValidResult, InvalidResult> nameResult = validateNameUniqueness(name, request.getId());
    return taxnumberResult.and(nameResult).and(existsResult);
  }
}
