/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.customer.application.update;

import com.codemonkeys.wallet.common.framework.domain.vo.Country;
import com.codemonkeys.wallet.common.framework.domain.vo.TaxNumber;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationMessage;
import com.codemonkeys.wallet.common.framework.shared.validation.Validator;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.customers.contact.application.create.CreateCustomerContactValidator;
import com.codemonkeys.wallet.customers.contact.application.update.UpdateCustomerContactValidator;
import com.codemonkeys.wallet.customers.customer.application.common.CreateUpdateCustomerValidator;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.customer.vo.CustomerName;
import com.google.common.collect.Lists;
import java.util.List;
import net.xyzsd.dichotomy.Result;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

/**
 * The UpdateCustomerValidator class is responsible for validating the update of a customer entity.
 * It extends the CreateUpdateCustomerValidator class and implements the Validator interface. It
 * provides methods for validating the existence, uniqueness, and other properties of a customer.
 */
@Service
public class UpdateCustomerValidator extends CreateUpdateCustomerValidator
    implements Validator<UpdateCustomerRequest> {

  public static final String ID = "id";
  protected static final String NOT_EXISTS = "%s does not exist";
  private final UpdateCustomerContactValidator updateCustomerContactValidator;
  private final CreateCustomerContactValidator createCustomerContactValidator;

  public UpdateCustomerValidator(
      CustomerRepository customerRepository,
      UpdateCustomerContactValidator updateCustomerContactValidator,
      CreateCustomerContactValidator createCustomerContactValidator) {
    super(customerRepository);
    this.updateCustomerContactValidator = updateCustomerContactValidator;
    this.createCustomerContactValidator = createCustomerContactValidator;
  }

  private static @NotNull CustomerName customerNameFromRequest(UpdateCustomerRequest request) {
    return CustomerName.of(request.getName());
  }

  private static @NotNull TaxNumber taxNumberFromRequest(UpdateCustomerRequest request) {
    Country country = Country.of(request.getCountry());
    return TaxNumber.of(request.getTaxNumber(), country);
  }

  Result<ValidResult, InvalidResult> validateExists(CustomerId id) {
    List<ValidationMessage> messages =
        Lists.newArrayList(WalletValidationMessage.of(ID, String.format(NOT_EXISTS, id)));
    return repository.existsById(id)
        ? Result.ofOK(ValidResult.of())
        : Result.ofErr(InvalidResult.of(messages));
  }

  @Override
  public Result<ValidResult, InvalidResult> validate(UpdateCustomerRequest request) {
    TaxNumber tax = taxNumberFromRequest(request);
    CustomerName name = customerNameFromRequest(request);
    Result<ValidResult, InvalidResult> existsResult = validateExists(request.getId());
    Result<ValidResult, InvalidResult> taxnumberResult =
        validateTaxNumberUniqueness(tax, request.getId());
    Result<ValidResult, InvalidResult> nameResult = validateNameUniqueness(name, request.getId());
    Result<ValidResult, InvalidResult> finalResult =
        taxnumberResult.and(nameResult).and(existsResult);
    Result<ValidResult, InvalidResult> contactsResult = validateContacts(request.getContacts());
    finalResult = validateContracts(request, finalResult);
    return finalResult.and(contactsResult);
  }

  private Result<ValidResult, InvalidResult> validateContracts(
      UpdateCustomerRequest request, Result<ValidResult, InvalidResult> finalResult) {
    for (CustomerContact contact : request.getContacts()) {
      if (contact.getId().getId() != null) {
        finalResult =
            finalResult.and(
                updateCustomerContactValidator.validate(
                    request.getId(), contact.getEmail(), contact.getId()));
      } else {
        finalResult =
            finalResult.and(
                createCustomerContactValidator.validate(request.getId(), contact.getEmail()));
      }
    }
    return finalResult;
  }
}
