/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.customer.application.create;

import com.codemonkeys.wallet.common.framework.domain.Note;
import com.codemonkeys.wallet.common.framework.domain.enums.CountryCode;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.framework.shared.dto.Request;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerSegment;
import com.codemonkeys.wallet.domain.customer.vo.CustomerConfiguration;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** The CreateCustomerRequest class represents a request to create a customer. */
@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class CreateCustomerRequest implements Request {

  private String name;
  private String taxNumber;
  private CountryCode country;
  private CustomerSegment segment;
  private CustomerConfiguration configuration;
  private List<Note> notes;
  private List<CustomerContact> contacts;
  private List<AttachmentId> attachments;
  private UUID group;
}
