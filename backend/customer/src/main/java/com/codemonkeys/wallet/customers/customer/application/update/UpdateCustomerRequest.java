/* (C)2024-2025 */
package com.codemonkeys.wallet.customers.customer.application.update;

import com.codemonkeys.wallet.common.framework.domain.Note;
import com.codemonkeys.wallet.common.framework.domain.enums.CountryCode;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.framework.shared.dto.Request;
import com.codemonkeys.wallet.customers.customer.application.create.CreateCustomerRequest;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerSegment;
import com.codemonkeys.wallet.domain.customer.Remuneration;
import com.codemonkeys.wallet.domain.customer.vo.CustomerConfiguration;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;

@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = false)
@Data
public class UpdateCustomerRequest extends CreateCustomerRequest implements Request {
  private CustomerId id;

  public UpdateCustomerRequest(
      CustomerId id,
      String name,
      String taxNumber,
      CountryCode country,
      CustomerSegment segment,
      CustomerConfiguration configuration,
      List<Note> notes,
      List<CustomerContact> contacts,
      List<AttachmentId> attachments,
      List<Remuneration> remunerations,
      UUID group) {
    super(
        name,
        taxNumber,
        country,
        segment,
        configuration,
        notes,
        contacts,
        attachments,
        remunerations,
        group);
    this.id = id;
  }
}
