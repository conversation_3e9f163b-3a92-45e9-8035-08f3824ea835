/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;

import com.codemonkeys.wallet.common.tests.testcontainers.PostgresTest;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.config.PriceProperties.PriceConfig;
import com.codemonkeys.wallet.price.factories.PriceConfigFactory;
import jakarta.transaction.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@PostgresTest
@RunWith(SpringRunner.class)
public class PriceManagerServiceIntegrationTest {

  @Autowired private PriceManagerService priceManagerService;

  @Autowired private PriceRepository priceRepository;

  @MockBean private PriceProperties pricePropertiesMock;

  @MockBean private ApplicationEventPublisher eventPublisherMock;

  @MockBean private HolidayChecker holidayCheckerMock;

  @Before
  public void setup() {
    // Set up mock properties using factory
    Map<String, PriceConfig> scrapers = new HashMap<>();
    scrapers.put("energy", PriceConfigFactory.createEnergyGazOtfPriceConfig());
    scrapers.put("energy-rdn", PriceConfigFactory.createEnergyGazRdnPriceConfig());
    scrapers.put("gas-otf", PriceConfigFactory.createGasOtfPriceConfig());
    scrapers.put("property-rights", PriceConfigFactory.createPropertyRightsPriceConfig());
    scrapers.put("gas-rdn", PriceConfigFactory.createGasRdnPriceConfig());
    scrapers.put("gas-rdb", PriceConfigFactory.createGasRdbPriceConfig());

    Mockito.when(pricePropertiesMock.getPrices()).thenReturn(scrapers);
    Mockito.when(pricePropertiesMock.getCron()).thenReturn("0 0 23 * * *");

    // Inject mock properties into the service
    priceManagerService =
        new PriceManagerService(
            pricePropertiesMock, priceRepository, eventPublisherMock, holidayCheckerMock);
  }

  @Test
  @Transactional
  public void testScheduledScrape() {
    // When
    priceManagerService.scheduledScrape();

    // Then
    List<Price> contracts = priceRepository.findAll();
    assertNotNull(contracts);
    assertFalse(contracts.isEmpty());

    Price contract = contracts.getFirst();
    assertNotNull(contract.getCreatedAt());
  }
}
