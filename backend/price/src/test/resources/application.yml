server:
  servlet:
    context-path: /api
spring:
  application:
    name: price
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
  jpa:
    generate-ddl: true
    hibernate:
      ddl-auto: create
  liquibase:
    enabled: false
    change-log: classpath:liquibase/db-changelog-master.xml
  mvc:
    versioned-context-path: /api/v1
  mail:
    host: smtp.eu.mailgun.org
    port: 587
    username: <EMAIL>
    password: **************************************************
    smtp-auth: true
    starttls-enable: true
    mime-charset: UTF-8
    debug: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://keycloak.dev.es-t.pl/realms/wallet
          jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs

email:
  override-addresses:
    enabled: true
    recipients:
      - <EMAIL>


keycloak:
  auth-server-url: https://keycloak.dev.es-t.pl
  realm: wallet
  resource: wallet
  credentials:
    secret: WEtq16WeqEKHEGEozObV5Wl9Kq0tr36Q

jwt:
  auth:
    converter:
      resource-id: wallet
      principle-attribute: preferred_username
    mvc:
      versioned-context-path: /api/v1


storage:
  endpoint: http://localhost:9000
  accessKey: nBirvv7Qn5pawcPgEMVt
  secretKey: ****************************************
  prefix: uploads
  bucket: wallet

price:
  cron: 0 0 23 * * *
  prices:
    energy:
      url: https://tge.pl/energia-elektryczna-otf
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazOtfPriceService
    energy-rdn:
      url: https://tge.pl/energia-elektryczna-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazRdnPriceService
    energy-rdn-fixing2:
      url: https://tge.pl/energia-elektryczna-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.FixingTwoRdnPriceService
    gas-otf:
      url: https://tge.pl/gaz-otf
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazOtfPriceService
    property-rights:
      url: https://tge.pl/prawa-majatkowe-rpm
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.PropertyRightsPriceService
    gas-rdn:
      url: https://tge.pl/gaz-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazRdnPriceService
    gas-rdb:
      url: https://tge.pl/gaz-rdb
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.GazRdbPriceService
    wibor1r:
      url: https://stooq.pl/q/d/l/?s=plopln1y&i=d
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.WiborCsvPriceService