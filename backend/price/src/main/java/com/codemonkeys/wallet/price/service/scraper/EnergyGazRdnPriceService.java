/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service.scraper;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceDate;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.price.vo.PriceValue;
import com.codemonkeys.wallet.domain.price.vo.PriceVolume;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.service.AbstractPriceService;
import com.codemonkeys.wallet.price.service.TextNormalizer;
import com.codemonkeys.wallet.price.validation.RecordNameValidator;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.jsoup.nodes.Element;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/** Scraper service for energy and gas RDN data. */
@Service
public class EnergyGazRdnPriceService extends AbstractPriceService {

  public EnergyGazRdnPriceService(
      PriceRepository priceRepository,
      ApplicationEventPublisher eventPublisher,
      PriceProperties priceProperties,
      HolidayChecker holidayChecker,
      RecordNameValidator recordNameValidator) {
    super(priceRepository, eventPublisher, priceProperties, holidayChecker, recordNameValidator);
  }

  @Override
  protected String getRowSelector() {
    return ".wyniki-table-indeksy table.footable tbody tr";
  }

  @Override
  protected Price parseRowToScrapedContract(Element row) {
    String price = TextNormalizer.normalize(row.select("td:nth-child(3)").text().trim());
    String volume = TextNormalizer.normalize(row.select("td:nth-child(5)").text().trim());
    String contract = TextNormalizer.normalize(row.select("td:first-child b").text().trim());

    if (!price.equals("-") && !price.isEmpty() && !volume.equals("") && !volume.isEmpty()) {
      return Price.builder()
          .date(PriceDate.of(LocalDate.now().plusDays(1))) // Setting date to the next day
          .contract(PriceName.of(contract))
          .volume(PriceVolume.of(new BigDecimal(volume.replace(",", "."))))
          .value(PriceValue.of(new BigDecimal(price.replace(",", "."))))
          .build();
    }
    return null;
  }
}
