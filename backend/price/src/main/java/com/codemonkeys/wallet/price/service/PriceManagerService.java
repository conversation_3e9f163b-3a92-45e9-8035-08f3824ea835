/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service;

import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.config.PriceProperties.PriceConfig;
import com.codemonkeys.wallet.price.validation.RecordNameValidator;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.aop.support.DefaultIntroductionAdvisor;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.retry.annotation.AnnotationAwareRetryOperationsInterceptor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/** Service responsible for managing scraping tasks based on configurations. */
@Slf4j
@RequiredArgsConstructor
@Service
public class PriceManagerService {

  private final BeanFactory beanFactory;
  private final PriceProperties priceProperties;
  private final PriceRepository priceRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final HolidayChecker holidayChecker;
  private final RecordNameValidator recordNameValidator;

  /** Scheduled method to trigger scraping based on the cron configuration. */
  @Scheduled(cron = "${price.cron}")
  public void scheduledScrape() {
    long t0 = System.currentTimeMillis();
    long before = priceRepository.count();
    Map<String, PriceConfig> prices = priceProperties.getPrices();
    if (prices == null || prices.isEmpty()) {
      log.error("Scrapers configuration is null or empty");
      return;
    }
    for (Map.Entry<String, PriceConfig> entry : prices.entrySet()) {
      PriceConfig config = entry.getValue();
      if (config.isEnabled()) {
        try {
          PriceService priceService = getPriceService(config.getType());
          priceService.scrape(config);
        } catch (Exception e) {
          log.error("Failed to instantiate scraper for class: {}", config.getType(), e);
        }
      }
    }

    long after = priceRepository.count();
    long delta = after - before;
    long millis = System.currentTimeMillis() - t0;

    if (delta > 0) {
      String details = String.format("Prices import: +%d rows, duration=%d ms", delta, millis);
      // eventPublisher.publishEvent(new LogEvent(this, EventType.PRICES_RECALCULATED, details));
    } else {
      log.info("Prices import finished – brak nowych danych ({} ms)", millis);
    }
  }

  /**
   * Creates a proxied instance of a {@link PriceService} scraper with retry capabilities enabled.
   *
   * <p>The method dynamically instantiates the scraper class based on its fully qualified name,
   * injects required dependencies via constructor, and wraps the instance in a proxy that supports
   * {@code @Retryable} operations. This enables automatic retries on failures such as {@code
   * IOException}, {@code ParseException}, or custom exceptions like {@code
   * InsufficientRecordsException}.
   *
   * @param className the fully qualified class name of the scraper service
   * @return a proxied instance of {@link PriceService} with retry mechanism applied
   * @throws Exception if the class cannot be loaded, instantiated, or dependency injection fails
   */
  private PriceService getPriceService(String className) throws Exception {

    // 1. scraper
    Class<?> clazz = Class.forName(className);
    Object target =
        clazz
            .getConstructor(
                PriceRepository.class,
                ApplicationEventPublisher.class,
                PriceProperties.class,
                HolidayChecker.class,
                RecordNameValidator.class)
            .newInstance(
                priceRepository,
                eventPublisher,
                priceProperties,
                holidayChecker,
                recordNameValidator);

    // 2. interceptor retry
    AnnotationAwareRetryOperationsInterceptor retryAdvice =
        new AnnotationAwareRetryOperationsInterceptor();
    retryAdvice.setBeanFactory(beanFactory);

    // 3. box of IntroductionAdvisor
    DefaultIntroductionAdvisor advisor = new DefaultIntroductionAdvisor(retryAdvice);

    // 4. proxy
    ProxyFactory pf = new ProxyFactory(target);
    pf.setProxyTargetClass(true);
    pf.addAdvisor(advisor);

    return (PriceService) pf.getProxy();
  }
}
