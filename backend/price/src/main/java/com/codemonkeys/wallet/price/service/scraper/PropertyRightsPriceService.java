/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service.scraper;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceDate;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.price.vo.PriceValue;
import com.codemonkeys.wallet.domain.price.vo.PriceVolume;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.service.AbstractPriceService;
import com.codemonkeys.wallet.price.service.TextNormalizer;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;
import org.jsoup.nodes.Element;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/** Scraper service for property rights data. */
@Service
public class PropertyRightsPriceService extends AbstractPriceService {

  private static final Map<String, String> LABEL_TO_PRICE_NAME =
      Map.of(
          "TGEozea", "PMOZE_A",
          "TGEozebio", "PMOZE-BIO",
          "TGEeff", "PMEF_F");

  public PropertyRightsPriceService(
      PriceRepository priceRepository,
      ApplicationEventPublisher eventPublisher,
      PriceProperties priceProperties,
      HolidayChecker holidayChecker) {
    super(priceRepository, eventPublisher, priceProperties, holidayChecker);
  }

  @Override
  protected String getRowSelector() {
    return "table.footable tbody tr";
  }

  @Override
  protected Price parseRowToScrapedContract(Element row) {
    String label = TextNormalizer.normalize(row.select("td:first-child div.label").text().trim());
    String priceStr = TextNormalizer.normalize(row.select("td:nth-child(2)").text().trim());
    String volumeStr = TextNormalizer.normalize(row.select("td:nth-child(4)").text().trim());

    if (LABEL_TO_PRICE_NAME.containsKey(label)
        && !priceStr.equals("-")
        && !priceStr.isEmpty()
        && !volumeStr.equals("")
        && !volumeStr.isEmpty()) {
      return Price.builder()
          .date(PriceDate.of(LocalDate.now()))
          .contract(PriceName.of(LABEL_TO_PRICE_NAME.get(label)))
          .volume(PriceVolume.of(new BigDecimal(volumeStr.replace(",", "."))))
          .value(PriceValue.of(new BigDecimal(priceStr.replace(",", "."))))
          .build();
    }
    return null;
  }
}
