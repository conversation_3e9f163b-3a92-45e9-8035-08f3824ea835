/* (C)2024-2025 */
package com.codemonkeys.wallet.price.config;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "price")
@Data
public class PriceProperties {
  private String cron;
  private Map<String, PriceConfig> prices;
  private RetryConfig retry;

  @Data
  public static class PriceConfig {
    private String url;
    private boolean enabled;
    private String type;
    private ExpectedRecordsConfig expectedRecords;
    private ExpectedContractsConfig expectedContracts;
  }

  @Data
  public static class RetryConfig {
    private int maxAttempts;
    private long delay;
    private double multiplier;
  }

  @Data
  public static class ExpectedRecordsConfig {
    private Map<DayOfWeek, Integer> days;
    private Integer holiday;
  }

  @Data
  public static class ExpectedContractsConfig {
    private Map<String, List<ContractRule>> days;
  }

  @Data
  public static class ContractRule {
    private String regex;
    private String exact;
    private Integer min = 1;
  }
}
