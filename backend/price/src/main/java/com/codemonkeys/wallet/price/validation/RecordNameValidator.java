/* (C)2025 */
package com.codemonkeys.wallet.price.validation;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.price.config.PriceProperties;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RecordNameValidator {

  private final PriceProperties props;

  public void validate(String id, LocalDate date, List<Price> prices) {

    PriceProperties.PriceConfig cfg = props.getPrices().get(id);
    if (cfg == null || cfg.getExpectedContracts() == null) return;

    // rules
    Map<String, List<PriceProperties.ContractRule>> map = cfg.getExpectedContracts().getDays();
    List<PriceProperties.ContractRule> rules =
        map.getOrDefault(date.getDayOfWeek().name(), map.getOrDefault("ANY", List.of()));
    if (rules.isEmpty()) return;

    if (prices == null || prices.isEmpty())
      return; /*validate only if there is data present — this way, we skip weekends without cluttering the yml*/

    Collection<String> names = prices.stream().map(p -> p.getContract().getName()).toList();

    // valid
    List<String> problems = new ArrayList<>();
    int totalExpectedMin = 0;
    int matched = 0;

    for (PriceProperties.ContractRule rule : rules) {
      String pattern =
          rule.getExact() != null ? STR."^\{Pattern.quote(rule.getExact())}$" : rule.getRegex();

      long found = names.stream().filter(n -> Pattern.matches(pattern, n)).count();
      matched += found;

      int min = Optional.ofNullable(rule.getMin()).orElse(1);
      totalExpectedMin += min;

      if (found < min) {
        problems.add(
            String.format("brakujące → wzorzec=%s, min=%d, znaleziono=%d", pattern, min, found));
      }
    }

    if (!problems.isEmpty()) {
      throw new RecordNameMismatchException(
          String.format("Brak dzisiejszych kontraktów dla %s: %s", id, String.join("; ", problems)),
          totalExpectedMin,
          matched);
    }
  }
}
