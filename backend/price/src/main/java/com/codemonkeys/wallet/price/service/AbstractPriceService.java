/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.email.event.EmailTechnicalEvent;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

/** Abstract base class for scraper services providing common functionality. */
@Slf4j
@RequiredArgsConstructor
@Service
public abstract class AbstractPriceService implements PriceService {

  protected final PriceRepository priceRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final PriceProperties priceProperties;
  private final HolidayChecker holidayChecker;

  @Retryable(
      value = {IOException.class, ParseException.class},
      maxAttemptsExpression = "#{priceProperties.retry.maxAttempts}",
      backoff =
          @Backoff(
              delayExpression = "#{priceProperties.retry.delay}",
              multiplierExpression = "#{priceProperties.retry.multiplier}"))
  @Override
  public void scrape(PriceProperties.PriceConfig config) throws IOException, ParseException {
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      String content = fetchContent(httpClient, config.getUrl());
      if (content != null) {
        long startTime = System.currentTimeMillis();
        List<Price> scrapedPrices = parseContent(content);
        long endTime = System.currentTimeMillis();
        log.info(
            "Scraping completed for {} in {} ms, total records: {}",
            config.getUrl(),
            (endTime - startTime),
            scrapedPrices.size());
        List<Price> finalScrapedPrices = validateAndRescrapeIfNecessary(config, scrapedPrices);
        saveScrapedContracts(finalScrapedPrices);
        saveScrapedContracts(scrapedPrices);
      }
    } catch (IOException | ParseException e) {
      String errorMessage = STR."Błąd pobierania danych dla URL: \{config.getUrl()}";
      log.error(errorMessage, e);
      throw e;
    }
  }

  /**
   * Validates the number of scraped records against the expected count. If the number of records is
   * insufficient, attempts to rescrape the data and returns the additional records.
   *
   * @param config the configuration for the scraping process, containing the URL and expected
   *     record counts.
   * @param scrapedPrices the list of initially scraped records.
   * @return a list of final records, which could be the original list if sufficient or additional
   *     records from the rescraping process if the initial list was insufficient.
   */
  private List<Price> validateAndRescrapeIfNecessary(
      PriceProperties.PriceConfig config, List<Price> scrapedPrices) {
    int expectedCount = getExpectedRecordCount(config);

    if (scrapedPrices.size() != expectedCount) {
      log.warn(
          "Liczba rekordów ({}) dla {} nie spełnia oczekiwań ({}). "
              + "Ponowna próba pobrania wszystkich rekordów.",
          scrapedPrices.size(),
          config.getUrl(),
          expectedCount);

      List<Price> additionalPrices = attemptRescrape(config);
      handleInsufficientRecords(additionalPrices, expectedCount, config);
      return additionalPrices;
    }
    return scrapedPrices;
  }

  /**
   * Handles the case when the number of records after a rescrape is still insufficient. Logs an
   * error and notifies the administrator via email about the issue.
   *
   * @param additionalPrices the list of records obtained after rescraping.
   * @param expectedCount the expected number of records for the scraping process.
   * @param config the configuration for the scraping process, containing the URL and expected
   *     record counts.
   */
  private void handleInsufficientRecords(
      List<Price> additionalPrices, int expectedCount, PriceProperties.PriceConfig config) {

    if (additionalPrices.size() < expectedCount) {
      Map<String, Object> emailVariables =
          Map.of(
              "url", config.getUrl(),
              "expectedRecords", config.getExpectedRecords().getDays().toString(),
              "receivedRecords", additionalPrices.size(),
              "day", LocalDate.now().getDayOfWeek().toString(),
              "checkDate", LocalDate.now().toString());

      log.error("Niewystarczająca liczba rekordów. Dane: {}", emailVariables);
      notifyAdmin("Niewystarczająca liczba rekordów", emailVariables);
    }
  }

  /**
   * Attempts to rescrape data from the provided configuration URL. Parses the content of the page
   * into a list of records. Logs any errors encountered during the rescrape.
   *
   * @param config the configuration for the scraping process, containing the URL to fetch data
   *     from.
   * @return a list of records obtained from the rescraping process. Returns an empty list if the
   *     rescrape fails.
   */
  private List<Price> attemptRescrape(PriceProperties.PriceConfig config) {
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      String content = fetchContent(httpClient, config.getUrl());
      if (content != null) {
        List<Price> additionalPrices = parseContent(content);
        return additionalPrices;
      }
    } catch (IOException | ParseException e) {
      log.error(
          "Błąd podczas ponownego pobierania rekordów dla {}: {}",
          config.getUrl(),
          e.getMessage(),
          e);
    }
    return List.of();
  }

  /**
   * Retrieves the expected record count for the given scraper configuration based on today's date.
   *
   * <p>If today is a holiday and a holiday-specific count is configured, that value is returned.
   * Otherwise, the expected record count for the current day of the week is returned.
   *
   * @param config the scraper configuration containing expected record counts
   * @return the expected number of records for the current date
   * @throws IllegalStateException if the expected records configuration is missing or if no record
   *     count is defined for today's day
   */
  private int getExpectedRecordCount(PriceProperties.PriceConfig config) {
    var expectedRecords =
        Optional.ofNullable(config.getExpectedRecords())
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        STR."No expected records configured for scraper with URL: \{
                            config.getUrl()}"));
    LocalDate today = LocalDate.now();

    return holidayChecker.isHoliday(today) && expectedRecords.getHoliday() != null
        ? expectedRecords.getHoliday()
        : Optional.ofNullable(expectedRecords.getDays().get(today.getDayOfWeek()))
            .orElseThrow(
                () ->
                    new IllegalStateException(
                        STR."No expected record count configured for day: \{
                            today.getDayOfWeek()}"));
  }

  /**
   * Fetches the content of the page from the given URL.
   *
   * @param httpClient the HTTP client to use
   * @param url the URL to fetch content from
   * @return the content of the page as a String
   * @throws IOException if an I/O error occurs
   * @throws ParseException if a parsing error occurs
   */
  private String fetchContent(CloseableHttpClient httpClient, String url)
      throws IOException, ParseException {
    HttpGet request = createHttpGet(url);
    try (CloseableHttpResponse response = httpClient.execute(request)) {
      int statusCode = response.getCode();
      if (statusCode != 200) {
        String errorMessage = STR."Niepoprawna odpowiedź HTTP. Status: \{statusCode}";
        log.error(errorMessage);
      }

      HttpEntity entity = response.getEntity();
      if (entity == null) {
        String errorMessage = STR."Brak treści odpowiedzi dla URL: \{url}";
        log.error(errorMessage);
      }

      return EntityUtils.toString(entity);
    }
  }

  /**
   * Creates an HTTP GET request with the appropriate headers.
   *
   * @param url the URL for the request
   * @return an instance of HttpGet
   */
  private HttpGet createHttpGet(String url) {
    HttpGet request = new HttpGet(url);
    request.addHeader(
        "User-Agent",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko)"
            + " Chrome/58.0.3029.110 Safari/537.3");
    request.addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
    request.addHeader("Accept-Language", "en-US,en;q=0.5");
    request.addHeader("Connection", "keep-alive");
    request.addHeader("Referer", "http://www.google.com");
    return request;
  }

  /**
   * Parses the content of the page into a list of scraped contracts.
   *
   * @param content the content of the page
   * @return a list of scraped contracts
   */
  private List<Price> parseContent(String content) {
    List<Price> scrapedPrices = new ArrayList<>();
    Document doc = Jsoup.parse(content);
    Elements rows = doc.select(getRowSelector());

    if (rows.isEmpty()) {
      String errorMessage = STR."Brak danych wierszy dla selektora: \{getRowSelector()}";
      log.error(errorMessage);
    }

    for (Element row : rows) {
      try {
        Price scrapedPrice = parseRowToScrapedContract(row);
        if (scrapedPrice != null) {
          scrapedPrices.add(scrapedPrice);
          log.info("Scraped Contract: {}", scrapedPrice);
        }
      } catch (NumberFormatException e) {
        log.warn("Failed to parse contract data from row: {}", row, e);
      }
    }
    log.info("Total contracts scraped: {}", scrapedPrices.size());
    return scrapedPrices;
  }

  /**
   * Saves the scraped contracts to the repository.
   *
   * @param scrapedPrices the list of scraped contracts
   */
  private void saveScrapedContracts(List<Price> scrapedPrices) {
    for (Price price : scrapedPrices) {
      if (!priceRepository.existsByDateAndContract(price.getDate(), price.getContract())) {
        priceRepository.save(price);
      } else {
        log.info(
            "Record already exists for date: {} and contract: {}",
            price.getDate(),
            price.getContract());
      }
    }
  }

  /**
   * Returns the CSS selector for rows containing contract data.
   *
   * @return the CSS selector
   */
  protected abstract String getRowSelector();

  /**
   * Parses a row element into a ScrapedContract instance.
   *
   * @param row the row element
   * @return a ScrapedContract instance or null if parsing fails
   */
  protected abstract Price parseRowToScrapedContract(Element row);

  /**
   * Handles recovery logic in case of an {@link IOException} during the scraping process. This
   * method is called after the retry mechanism exhausts all retry attempts. It logs the error and
   * sends a technical email to notify the administrator about the failure.
   *
   * @param e the {@link IOException} that occurred.
   * @param config the {@link PriceProperties.PriceConfig} containing the configuration of the
   *     scraping process.
   */
  @Recover
  public void recover(IOException e, PriceProperties.PriceConfig config) {
    Map<String, Object> emailVariables =
        Map.of(
            "url",
            config.getUrl(),
            "errorType",
            "Błąd pobierania danych",
            "errorMessage",
            STR."Nie udało się pobrać danych z URL: \{config.getUrl()}",
            "checkDate",
            LocalDate.now().toString());

    log.error("Błąd pobierania danych dla URL: {}", config.getUrl(), e);
    notifyAdmin("Błąd pobierania danych", emailVariables);
  }

  /**
   * Handles recovery logic in case of a {@link ParseException} during the scraping process. This
   * method is invoked after the retry mechanism exhausts all retry attempts. It logs the error and
   * sends a technical email to notify the administrator about the failure.
   *
   * @param e the {@link ParseException} that occurred.
   * @param config the {@link PriceProperties.PriceConfig} containing the configuration of the
   *     scraping process.
   */
  @Recover
  public void recover(ParseException e, PriceProperties.PriceConfig config) {
    Map<String, Object> emailVariables =
        Map.of(
            "url",
            config.getUrl(),
            "errorType",
            "Błąd parasomnia danych",
            "errorMessage",
            STR."Błąd parsowania danych z URL: \{config.getUrl()}",
            "checkDate",
            LocalDate.now().toString());

    log.error("Błąd parsowania danych dla URL: {}", config.getUrl(), e);
    notifyAdmin("Błąd parsowania danych", emailVariables);
  }

  /**
   * Sends a technical email to notify the administrator about critical issues such as failures in
   * the scraping process. The email content includes the provided subject and message.
   *
   * @param subject the subject of the email to be sent.
   * @param emailVariables the message body of the email.
   */
  public void notifyAdmin(String subject, Map<String, Object> emailVariables) {
    EmailTechnicalEvent event =
        new EmailTechnicalEvent(this, null, subject, "technical-template", emailVariables);
    eventPublisher.publishEvent(event);
  }
}
