/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service.scraper;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceDate;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.price.vo.PriceValue;
import com.codemonkeys.wallet.domain.price.vo.PriceVolume;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.service.AbstractPriceService;
import com.codemonkeys.wallet.price.service.TextNormalizer;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.jsoup.nodes.Element;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/** Scraper service for energy and gas OTF data. */
@Service
public class EnergyGazOtfPriceService extends AbstractPriceService {

  public EnergyGazOtfPriceService(
      PriceRepository priceRepository,
      ApplicationEventPublisher eventPublisher,
      PriceProperties priceProperties,
      HolidayChecker holidayChecker) {
    super(priceRepository, eventPublisher, priceProperties, holidayChecker);
  }

  @Override
  protected String getRowSelector() {
    return "div.table-responsive.wyniki-footable-kontrakty-terminowe-0 table tbody tr";
  }

  @Override
  protected Price parseRowToScrapedContract(Element row) {
    String dkr = TextNormalizer.normalize(row.select("td.col-settlement_price-val").text().trim());
    String contracts =
        TextNormalizer.normalize(row.select("td.col-contract_size-val").text().trim());
    String instrumentName =
        TextNormalizer.normalize(row.select("td.col-instrument_name").text().trim());

    if (!dkr.equals("-") && !dkr.isEmpty() && !contracts.equals("") && !contracts.isEmpty()) {
      return Price.builder()
          .date(PriceDate.of(LocalDate.now()))
          .contract(PriceName.of(instrumentName))
          .volume(PriceVolume.of(new BigDecimal(contracts.replace(",", "."))))
          .value(PriceValue.of(new BigDecimal(dkr.replace(",", "."))))
          .build();
    }
    return null;
  }
}
