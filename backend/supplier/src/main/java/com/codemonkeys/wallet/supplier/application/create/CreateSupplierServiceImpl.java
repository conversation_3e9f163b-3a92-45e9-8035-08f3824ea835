/* (C)2024-2025 */
package com.codemonkeys.wallet.supplier.application.create;

import com.codemonkeys.wallet.common.framework.crud.create.CreateServiceImpl;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactConfiguration;
import com.codemonkeys.wallet.common.framework.domain.vo.Email;
import com.codemonkeys.wallet.common.framework.domain.vo.PersonName;
import com.codemonkeys.wallet.common.framework.domain.vo.PhoneNumber;
import com.codemonkeys.wallet.common.framework.logger.SecureLogger;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.contact.application.crud.common.TenantIdContactService;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.SupplierContact;
import com.codemonkeys.wallet.domain.supplier.SupplierContactRepository;
import com.codemonkeys.wallet.domain.supplier.SupplierRepository;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierName;
import com.codemonkeys.wallet.supplier.application.common.CreateUpdateSupplierResponse;
import com.codemonkeys.wallet.supplier.application.common.SupplierMapper;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** Service implementation for creating Supplier entities, including handling Supplier contacts. */
@Service
public class CreateSupplierServiceImpl
    extends CreateServiceImpl<
        CreateSupplierRequest,
        CreateUpdateSupplierResponse,
        Supplier,
        SupplierId,
        SupplierRepository,
        SupplierMapper,
        CreateSupplierValidator> {

  private static final Logger log = LoggerFactory.getLogger(CreateSupplierServiceImpl.class);
  private final SupplierMapper supplierMapper;
  private final SupplierContactRepository supplierContactRepository;
  private final CustomerRepository customerRepository;
  private final TenantIdContactService tenantIdContactService;

  /** Constructs a new CreateSupplierServiceImpl with the specified dependencies. */
  @Autowired
  public CreateSupplierServiceImpl(
      SupplierRepository repository,
      SupplierMapper mapper,
      CreateSupplierValidator validator,
      SupplierMapper supplierMapper,
      SupplierContactRepository supplierContactRepository,
      CustomerRepository customerRepository,
      TenantIdContactService tenantIdContactService) {
    super(repository, mapper, validator);
    this.supplierMapper = supplierMapper;
    this.supplierContactRepository = supplierContactRepository;
    this.customerRepository = customerRepository;
    this.tenantIdContactService = tenantIdContactService;
  }

  /** Creates a new Supplier entity along with its associated contacts. */
  @Transactional
  public Supplier createSupplierWithContacts(CreateSupplierRequest request) {
    SecureLogger.debug(log, "Starting to create supplier with contacts");

    validateSupplierName(request.getName());

    Supplier supplier = supplierMapper.toDomain(request);
    supplier = repository.save(supplier);
    SecureLogger.debug(log, "Supplier saved: {}", supplier);

    saveSupplierContacts(supplier, request.getContacts());

    return supplier;
  }

  /** Validates if a supplier with the specified name already exists. */
  private void validateSupplierName(String name) {
    boolean exists = repository.existsByName(SupplierName.of(name));
    if (exists) {
      String reason = I18n.translate("validations.supplier.name.not.unique", name);
      throw new ValidationFailedException(
          reason, List.of(WalletValidationMessage.of("name", reason)));
    }
  }

  /** Saves the contacts associated with the supplier and creates relationships with customers. */
  private void saveSupplierContacts(
      Supplier supplier, List<CreateSupplierContactRequest> contacts) {
    for (CreateSupplierContactRequest contactRequest : contacts) {
      SecureLogger.debug(log, "Processing contact: {}", contactRequest);
      Set<Customer> customers = findCustomersByIds(contactRequest.getCustomerIds());
      for (Customer customer : customers) {
        SecureLogger.debug(log, "Processing customer: {}", customer);
        SupplierContact contact =
            supplierContactRepository
                .findBySupplierAndEmailAndCustomer(
                    supplier, Email.of(contactRequest.getEmail()), customer)
                .orElseGet(
                    () -> {
                      SupplierContact newContact =
                          SupplierContact.create(
                              PersonName.of(
                                  contactRequest.getFirstName(), contactRequest.getLastName()),
                              supplier,
                              PhoneNumber.of(contactRequest.getPhoneNumber()),
                              Email.of(contactRequest.getEmail()),
                              ContactConfiguration.of(false, false, false, false, false, false),
                              customer);

                      newContact.setTenantId(
                          tenantIdContactService.determineTenantId(Set.of(customer)));

                      return newContact;
                    });
        SupplierContact savedContact = supplierContactRepository.save(contact);
        SecureLogger.debug(log, "Saved contact: {}", savedContact);
      }
    }
  }

  private Set<Customer> findCustomersByIds(List<UUID> customerIds) {
    return customerRepository
        .findAllById(customerIds.stream().map(CustomerId::of).collect(Collectors.toList()))
        .stream()
        .collect(Collectors.toSet());
  }
}
