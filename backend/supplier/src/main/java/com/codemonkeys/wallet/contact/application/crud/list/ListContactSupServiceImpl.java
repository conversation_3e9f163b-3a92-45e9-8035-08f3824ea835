/* (C)2024-2025 */
package com.codemonkeys.wallet.contact.application.crud.list;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListServiceImpl;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.SpecHelper;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactId;
import com.codemonkeys.wallet.common.framework.shared.dto.FilterField;
import com.codemonkeys.wallet.domain.supplier.SupplierContact;
import com.codemonkeys.wallet.domain.supplier.SupplierContactRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * Service implementation for listing Supplier Contacts. Extends {@link ProjectionListServiceImpl}
 * to handle listing Supplier Contacts with pagination and filtering.
 */
@Slf4j
@Service
public class ListContactSupServiceImpl
    extends ProjectionListServiceImpl<
        ListContactSupRequest,
        ListContactSupResponse,
        SupplierContact,
        ContactId,
        SupplierContactRepository,
        ListContactSupMapper> {

  /**
   * Constructs a new ListContactSupServiceImpl with the necessary dependencies.
   *
   * @param repository the repository for SupplierContact entities.
   * @param mapper the mapper for converting SupplierContact entities to ListContactSupResponse
   *     DTOs.
   */
  public ListContactSupServiceImpl(
      SupplierContactRepository repository, ListContactSupMapper mapper) {
    super(repository, mapper, ListContactSupResponse.class);
  }

  @Override
  protected Specification<SupplierContact> prepareSpecification(ListContactSupRequest request) {
    Specification<SupplierContact> spec = (root, query, cb) -> cb.conjunction();
    FilterField supplierNameFilter =
        request.getFilters().stream()
            .filter(f -> "supplierName".equals(f.getId()))
            .findFirst()
            .orElse(null);

    if (supplierNameFilter != null) {
      String filterValue = supplierNameFilter.getValue().toString().toLowerCase();
      spec =
          spec.and(
              (root, query, cb) ->
                  cb.like(cb.lower(root.join("supplier").get("name")), "%" + filterValue + "%"));
      request.getFilters().remove(supplierNameFilter);
    }
    return spec.and(SpecHelper.filterBy(request.getFilters()));
  }

  /**
   * Lists Supplier Contacts with pagination and filtering.
   *
   * @param request the request containing pagination and filtering parameters.
   * @return a page of ListContactSupResponse DTOs.
   */
  @Override
  public Page<ListContactSupResponse> list(ListContactSupRequest request) {
    Pageable pageable = PageRequest.of(request.getPage(), request.getLimit());
    Page<SupplierContact> entityPage = repository.findAll(prepareSpecification(request), pageable);
    return mapper.toDto(entityPage);
  }
}
