/* (C)2024-2025 */
package com.codemonkeys.wallet.contact.application.crud.list;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListMapper;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactId;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.supplier.SupplierContact;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ListContactSupMapper
    implements ProjectionListMapper<SupplierContact, ContactId, ListContactSupResponse> {

  @Override
  public ListContactSupResponse toDto(SupplierContact entity) {
    Set<String> customerNames =
        entity.getCustomers().stream()
            .map(customer -> customer.getName().getName())
            .collect(Collectors.toSet());

    return new ListContactSupResponseImpl(
        entity.getName().getFullName(),
        entity.getNumber(),
        entity.getEmail(),
        entity.getSupplier().getName().getName(),
        String.join(", ", customerNames),
        entity.getId().toString(),
        entity.getSupplier().getId().toString());
  }

  /** Converts a {@link SupplierContact} entity into multiple DTOs, one per customer. */
  public List<ListContactSupResponse> toDtoList(SupplierContact entity) {
    List<ListContactSupResponse> dtos = new ArrayList<>();

    for (Customer customer : entity.getCustomers()) {
      dtos.add(
          new ListContactSupResponseImpl(
              entity.getName().getFullName(),
              entity.getNumber(),
              entity.getEmail(),
              entity.getSupplier().getName().getName(),
              customer.getName().getName(),
              entity.getId().toString(),
              entity.getSupplier().getId().toString()));
    }

    return dtos;
  }

  /** Converts a paginated list of {@link SupplierContact} entities into DTOs. */
  @Override
  public Page<ListContactSupResponse> toDto(Page<SupplierContact> entities) {
    List<ListContactSupResponse> dtos = new ArrayList<>();

    for (SupplierContact entity : entities) {
      dtos.addAll(toDtoList(entity));
    }

    return new PageImpl<>(dtos, entities.getPageable(), dtos.size());
  }
}
