/* (C)2024-2025 */
package com.codemonkeys.wallet.supplier.application.delete;

import com.codemonkeys.wallet.common.framework.crud.delete.DeleteByIdServiceImpl;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.SupplierRepository;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service implementation for deleting suppliers by ID. Handles the logic for verifying the
 * existence of the supplier and performing the deletion.
 */
@Slf4j
@Service
public class DeleteSupplierServiceImpl
    extends DeleteByIdServiceImpl<
        SupplierId,
        DeleteSupplierResponse,
        Supplier,
        SupplierRepository,
        DeleteSupplierMapper,
        DeleteSupplierIdValidator> {

  /**
   * Constructs a new DeleteSupplierServiceImpl with the specified repository, validator, and
   * mapper.
   *
   * @param repository the repository handling supplier data storage.
   * @param validator the validator for supplier deletion requests.
   * @param mapper the mapper for converting deletion results to DTOs.
   */
  public DeleteSupplierServiceImpl(
      SupplierRepository repository,
      DeleteSupplierIdValidator validator,
      DeleteSupplierMapper mapper) {
    super(repository, validator, mapper);
  }
}
