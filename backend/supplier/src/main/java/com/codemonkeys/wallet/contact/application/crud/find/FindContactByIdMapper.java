/* (C)2024-2025 */
package com.codemonkeys.wallet.contact.application.crud.find;

import com.codemonkeys.wallet.common.framework.crud.findbyid.FindByIdMapper;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactId;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationResult;
import com.codemonkeys.wallet.domain.supplier.SupplierContact;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * Mapper for converting SupplierContact to FindContactByIdResponse DTOs, handling both successful
 * finds and cases where the contact does not exist.
 */
@Service
public class FindContactByIdMapper
    implements FindByIdMapper<SupplierContact, ContactId, FindContactByIdResponse> {

  /**
   * Converts a SupplierContact entity to a DTO for successful find operations.
   *
   * @param contact the SupplierContact entity to be converted.
   * @return a DTO representing the successfully found contact.
   */
  @Override
  public FindContactByIdResponse toDto(SupplierContact contact) {
    return new FindContactByIdResponse(
        new ValidationResult(true, List.of()),
        contact.getId().getId().toString(),
        contact.getSupplier().getId().getId(),
        contact.getCustomers().stream()
            .map(customer -> customer.getId().getId().toString())
            .collect(Collectors.toList()));
  }

  /**
   * Creates a DTO representing a failed find operation due to validation errors.
   *
   * @param er the invalid result containing details about the failure.
   * @return a DTO indicating a failed find operation with error messages.
   */
  @Override
  public FindContactByIdResponse toDto(InvalidResult er) {
    return new FindContactByIdResponse(
        new ValidationResult(false, er.getMessages()), null, null, null);
  }
}
