/* (C)2024-2025 */
package com.codemonkeys.wallet;

import com.codemonkeys.wallet.common.storage.StorageConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.TestPropertySource;

@SpringBootApplication
@EntityScan(basePackages = {"com.codemonkeys.wallet"})
@EnableJpaRepositories(basePackages = {"com.codemonkeys.wallet"})
@EnableConfigurationProperties(value = StorageConfiguration.class)
@TestPropertySource("classpath:application.properties")
public class CommonTestApplication {
  public static void main(String[] args) {
    SpringApplication.run(CommonTestApplication.class, args);
  }
}
