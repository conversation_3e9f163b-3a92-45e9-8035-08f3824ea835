/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.attachments.application.crud.common;

import com.azure.core.http.HttpClient;
import com.azure.core.http.netty.NettyAsyncHttpClientBuilder;
import com.azure.core.util.BinaryData;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.blob.models.BlobErrorCode;
import com.azure.storage.blob.models.BlobStorageException;
import com.azure.storage.common.StorageSharedKeyCredential;
import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.storage.AzureStorageConfiguration;
import com.codemonkeys.wallet.common.storage.Storage;
import java.io.IOException;
import java.io.InputStream;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Optional;
import javax.naming.OperationNotSupportedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for handling attachment storage operations using Azure Blob Storage. Implements
 * {@link Storage} to manage upload, download, and deletion of {@link Attachment} objects using
 * Azure Blob Storage client.
 */
@Service(value = "azureStorage")
@Slf4j
public class AzureAttachmentStorage implements Storage<AttachmentId, Attachment> {

  private final BlobServiceClient blobServiceClient;
  private final BlobContainerClient containerClient;
  private final AzureStorageConfiguration storageConfiguration;

  /**
   * Constructs a new {@code AzureAttachmentStorage} service with the specified storage
   * configuration.
   *
   * @param storageConfiguration the Azure storage configuration settings.
   */
  public AzureAttachmentStorage(AzureStorageConfiguration storageConfiguration) {
    this.storageConfiguration = storageConfiguration;
    this.blobServiceClient = createBlobServiceClient();
    this.containerClient = blobServiceClient.getBlobContainerClient(
        storageConfiguration.getContainerName());

    // Create container if it doesn't exist
    if (!containerClient.exists()) {
      try {
        containerClient.create();
        log.info("Created Azure blob container: {}", storageConfiguration.getContainerName());
      } catch (BlobStorageException e) {
        if (e.getErrorCode() != BlobErrorCode.CONTAINER_ALREADY_EXISTS) {
          log.error("Failed to create container: {}", e.getMessage());
        }
      }
    }
  }

  /**
   * Creates a BlobServiceClient based on the available authentication method.
   *
   * @return configured BlobServiceClient
   */
  protected BlobServiceClient createBlobServiceClient() {
    // 1. Create a custom HTTP client with a longer timeout
    HttpClient httpClient = new NettyAsyncHttpClientBuilder()
        .responseTimeout(Duration.ofMinutes(5)) // Increase timeout to 5 minutes
        .build();

    // 2. Build the BlobServiceClient with the custom HTTP client
    BlobServiceClientBuilder builder = new BlobServiceClientBuilder()
        .httpClient(httpClient); // Use the custom client

    // Your existing authentication logic
    if (storageConfiguration.useConnectionString()) {
      return builder.connectionString(storageConfiguration.getConnectionString()).buildClient();
    } else if (storageConfiguration.useAccountKey()) {
      StorageSharedKeyCredential credential =
          new StorageSharedKeyCredential(
              storageConfiguration.getAccountName(), storageConfiguration.getAccountKey());
      return builder
          .endpoint(storageConfiguration.getEndpoint())
          .credential(credential)
          .buildClient();
    } else if (storageConfiguration.useSasToken()) {
      return builder
          .endpoint(storageConfiguration.getEndpoint())
          .sasToken(storageConfiguration.getSasToken())
          .buildClient();
    } else {
      throw new IllegalArgumentException(
          "No valid Azure Storage authentication method configured. "
              + "Please provide either connection string, account key, or SAS token.");
    }
  }

  /**
   * Formats the prefix for storing attachments based on the current date.
   *
   * @param attachment the attachment to be stored.
   * @return the formatted prefix used for the attachment path.
   */
  private String formatPrefix(Attachment attachment) {
    LocalDate today = LocalDate.now();
    return String.format(
        storageConfiguration.getPrefix(),
        String.format(
            "%s/%s/%s/%s",
            today.getYear(),
            today.getMonthValue(),
            today.getDayOfMonth(),
            attachment.getName().getValue()));
  }

  /**
   * Uploads an {@link Attachment} to Azure Blob Storage.
   *
   * @param attachment the attachment to be uploaded.
   * @return an optional containing the uploaded attachment, or an empty optional if the upload
   * failed.
   */
  @Override
  public Optional<Attachment> upload(Attachment attachment) {
    if (attachment.getMultipartFile().isEmpty()) {
      return Optional.empty();
    }

    try {
      String blobName = attachment.getPath().getValue();
      BlobClient blobClient = containerClient.getBlobClient(blobName);

      // Use the upload method that takes the stream and its length
      try (InputStream inputStream = attachment.getMultipartFile().getInputStream()) {
        blobClient.upload(
            inputStream,
            attachment.getMultipartFile().getSize(), // Provide the content length
            true); // overwrite if exists
      }

      log.info("Successfully uploaded attachment to Azure: {}", blobName);
      return Optional.of(attachment);
    } catch (IOException e) {
      log.error("Failed to read attachment file: {}", e.getMessage());
      return Optional.empty();
    } catch (BlobStorageException e) {
      log.error("Failed to upload attachment to Azure: {}", e.getMessage());
      return Optional.empty();
    } catch (Exception e) {
      log.error("Unexpected error during attachment upload: {}", e.getMessage(),
          e); // Log the full exception
      return Optional.empty();
    }
  }

  /**
   * Unsupported operation for downloading files using prefix and bucket.
   *
   * @throws OperationNotSupportedException when this method is called, as it is not supported.
   */
  @Override
  public void download(String prefix, String bucket) throws OperationNotSupportedException {
    throw new OperationNotSupportedException("This method is not supported.");
  }

  /**
   * Prepares an attachment for download by returning its {@link InputStream} from Azure Blob
   * Storage.
   *
   * @param attachmentPath the path of the attachment to be downloaded.
   * @return an optional containing the input stream of the file, or an empty optional if the file
   * is not found.
   */
  public Optional<InputStream> prepareFileForDownload(String attachmentPath) {
    try {
      BlobClient blobClient = containerClient.getBlobClient(attachmentPath);

      if (!blobClient.exists()) {
        log.error(
            "File with path {} not found in container {}",
            attachmentPath,
            storageConfiguration.getContainerName());
        return Optional.empty();
      }

      InputStream fileStream = blobClient.openInputStream();
      return Optional.of(fileStream);
    } catch (BlobStorageException e) {
      if (e.getErrorCode() == BlobErrorCode.BLOB_NOT_FOUND) {
        log.error(
            "Blob with path {} not found in container {}: {}",
            attachmentPath,
            storageConfiguration.getContainerName(),
            e.getMessage());
      } else {
        log.error("Error while accessing blob: {}", e.getMessage());
      }
      return Optional.empty();
    } catch (Exception e) {
      log.error("Error while finding or downloading attachment: {}", e.getMessage());
      return Optional.empty();
    }
  }

  /**
   * Deletes an attachment from Azure Blob Storage.
   *
   * @param attachment the attachment to be deleted.
   */
  @Override
  public void delete(Attachment attachment) {
    try {
      String blobName = attachment.getPath().getValue();
      BlobClient blobClient = containerClient.getBlobClient(blobName);

      if (blobClient.exists()) {
        blobClient.delete();
        log.info("Successfully deleted attachment from Azure: {}", blobName);
      } else {
        log.warn("Attempted to delete non-existent blob: {}", blobName);
      }
    } catch (BlobStorageException e) {
      log.error("Failed to delete attachment from Azure: {}", e.getMessage());
    } catch (Exception e) {
      log.error("Unexpected error during attachment deletion: {}", e.getMessage());
    }
  }
}