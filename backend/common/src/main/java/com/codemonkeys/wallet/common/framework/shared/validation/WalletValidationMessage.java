/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.shared.validation;

public class WalletValidationMessage implements ValidationMessage {
  String field;
  String reason;

  private WalletValidationMessage(String field, String reason) {
    this.reason = reason;
    this.field = field;
  }

  public static WalletValidationMessage of(String source, String reason) {
    return new WalletValidationMessage(source, reason);
  }

  @Override
  public String getField() {
    return field;
  }

  @Override
  public String getReason() {
    return reason;
  }
}
