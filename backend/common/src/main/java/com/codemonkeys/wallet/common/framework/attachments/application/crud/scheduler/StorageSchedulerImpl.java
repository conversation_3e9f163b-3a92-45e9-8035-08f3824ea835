/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.attachments.application.crud.scheduler;

import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.AzureAttachmentStorage;
import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.MinioAttachmentStorage;
import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.AttachmentRepository;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.framework.domain.vo.ModuleType;
import com.codemonkeys.wallet.common.storage.Storage;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/** Storage scheduler service for Minio */
@Slf4j
@RequiredArgsConstructor
@Service
public class StorageSchedulerImpl implements StorageScheduler {

  private final Storage<AttachmentId, Attachment> storage;
  private final AttachmentRepository attachmentRepository;

  /**
   * Scheduled method to clean up attachments with the type {@link ModuleType#EMPTY}. This method is
   * executed based on the cron expression defined in the application's configuration under the
   * property `attachment.cron`.
   *
   * <p>The method fetches all attachments of type {@link ModuleType#EMPTY} from the repository,
   * then attempts to delete the corresponding files from the storage. If the deletion of the file
   * or database record fails, the attachment type is updated to {@link ModuleType#ERROR}, and the
   * error is logged.
   */
  @Scheduled(cron = "${attachment.cron}")
  @Override
  public void scheduledClean() {
    List<Attachment> attachments = attachmentRepository.findAllByType(ModuleType.EMPTY);
    attachments.forEach(
        attachment -> {
          try {
            storage.delete(attachment);
            attachmentRepository.delete(attachment);
          } catch (Exception e) {
            attachment.setType(ModuleType.ERROR);
            attachmentRepository.save(attachment);
            log.error("Failed to delete attachment {}", attachment.getId(), e);
          }
        });
    log.info("Empty type attachments clean up complete.");
  }
}
