package com.codemonkeys.wallet.common.framework.logger;

import java.util.Arrays;
import org.owasp.encoder.Encode;
import org.slf4j.Logger;

public class SecureLogger {

  public static void debug(Logger logger, String message, Object... params) {
    Object[] encodedParams = SecureLogger.encodeParameters(params);
    logger.debug(message, encodedParams);
  }

  public static void info(Logger logger, String message, Object... params) {
    Object[] encodedParams = SecureLogger.encodeParameters(params);
    logger.info(message, encodedParams);
  }

  public static void warn(Logger logger, String message, Object... params) {
    Object[] encodedParams = encodeParameters(params);
    logger.warn(message, encodedParams);
  }

  public static void error(Logger logger, String message, Object... params) {
    Object[] encodedParams = encodeParameters(params);
    logger.error(message, encodedParams);
  }

  private static Object[] encodeParameters(Object... params) {
    return Arrays.stream(params).map(SecureLogger::encodeParameter).toArray();
  }

  private static Object encodeParameter(Object param) {
    if (param == null) {
      return null;
    }
    String stringValue = param.toString();
    return Encode.forHtml(Encode.forJavaScript(stringValue));
  }
}
