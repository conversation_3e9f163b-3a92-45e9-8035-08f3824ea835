/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.attachments.application.crud.download;

import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.AttachmentStorage;
import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.AttachmentRepository;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DownloadAttachmentService {

  private final AttachmentStorage storage;
  private final AttachmentRepository attachmentRepository;

  @Autowired
  public DownloadAttachmentService(
      AttachmentStorage storage, AttachmentRepository attachmentRepository) {
    this.storage = storage;
    this.attachmentRepository = attachmentRepository;
  }

  /**
   * Prepares the file for download and sends it via the HTTP response.
   *
   * @param id The ID of the attachment.
   * @param attachmentPath The path where the attachment is stored.
   * @param response The HTTP response to write the file to.
   */
  public void prepareFileForDownload(
      AttachmentId id, String attachmentPath, HttpServletResponse response) {
    try {
      Optional<InputStream> fileStream = getFileStream(id, attachmentPath);
      sendFileToResponse(fileStream, id, response);
    } catch (ValidationFailedException e) {
      throw new ValidationFailedException(I18n.translate("validations.failed"), e.getMessages());
    }
  }

  /**
   * Prepares the file for download and sends it via the HTTP response.
   *
   * @param id The ID of the attachment.
   * @param response The HTTP response to write the file to.
   */
  public void prepareFileForDownload(AttachmentId id, HttpServletResponse response) {
    try {
      Attachment attachment =
          attachmentRepository.findById(id).orElseThrow(EntityNotFoundException::new);
      Optional<InputStream> fileStream = getFileStream(id, attachment.getPath().getValue());
      sendFileToResponse(fileStream, id, response);
    } catch (ValidationFailedException e) {
      throw new ValidationFailedException(I18n.translate("validations.failed"), e.getMessages());
    }
  }

  /**
   * Retrieves the file stream for the given attachment.
   *
   * @param id The ID of the attachment.
   * @param attachmentPath The path where the attachment is stored.
   * @return The file stream wrapped in an Optional.
   */
  private Optional<InputStream> getFileStream(AttachmentId id, String attachmentPath) {
    Optional<InputStream> fileStream = storage.prepareFileForDownload(attachmentPath);
    if (fileStream.isEmpty()) {
      throw new ValidationFailedException(
          I18n.translate("validations.attachment.notFound"),
          List.of(
              WalletValidationMessage.of(
                  "attachmentId",
                  I18n.translate("validations.attachment.notFound", id.getId().toString()))));
    }
    return fileStream;
  }

  /**
   * Sends the file stream to the HTTP response.
   *
   * @param fileStream The file stream to send.
   * @param id The ID of the attachment.
   * @param response The HTTP response.
   */
  private void sendFileToResponse(
      Optional<InputStream> fileStream, AttachmentId id, HttpServletResponse response) {
    fileStream.ifPresentOrElse(
        stream -> {
          try {
            configureResponseHeaders(response, id.getId().toString());
            stream.transferTo(response.getOutputStream());
            response.flushBuffer();
          } catch (IOException e) {
            throw new ValidationFailedException(
                I18n.translate("validations.file.downloadError"),
                List.of(
                    WalletValidationMessage.of(
                        "fileStream",
                        I18n.translate("validations.file.downloadError", id.getId().toString()))));
          }
        },
        () -> {
          throw new ValidationFailedException(
              I18n.translate("validations.file.notFound"),
              List.of(
                  WalletValidationMessage.of(
                      "fileStream",
                      I18n.translate("validations.file.notFound", id.getId().toString()))));
        });
  }

  /**
   * Configures the response headers for downloading a file.
   *
   * @param response The HTTP response.
   * @param fileName The name of the file to be downloaded.
   */
  private void configureResponseHeaders(HttpServletResponse response, String fileName) {
    response.setContentType("application/octet-stream");
    response.setHeader("Content-Disposition", STR."attachment; filename=\"\{fileName}\"");
  }
}
