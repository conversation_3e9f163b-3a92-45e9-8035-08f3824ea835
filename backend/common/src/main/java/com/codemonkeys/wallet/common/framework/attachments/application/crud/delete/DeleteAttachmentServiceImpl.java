/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.attachments.application.crud.delete;

import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.AttachmentMapper;
import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.AttachmentStorage;
import com.codemonkeys.wallet.common.framework.crud.delete.DeleteByIdServiceImpl;
import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.AttachmentRepository;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service implementation for deleting attachments by their ID. This class extends {@link
 * DeleteByIdServiceImpl} and provides logic to handle both the database deletion and the removal of
 * the file from storage.
 */
@Service
public class DeleteAttachmentServiceImpl
    extends DeleteByIdServiceImpl<
        AttachmentId,
        DeleteAttachmentResponse,
        Attachment,
        AttachmentRepository,
        AttachmentMapper,
        DeleteAttachmentValidator> {
  AttachmentStorage storage;

  /**
   * Constructor for {@link DeleteAttachmentServiceImpl}.
   *
   * @param repository the repository for managing {@link Attachment} entities.
   * @param mapper the mapper for converting entities to DTOs.
   * @param validator the validator for validating the delete operation.
   * @param storage the storage service responsible for handling file deletion.
   */
  @Autowired
  public DeleteAttachmentServiceImpl(
      AttachmentRepository repository,
      AttachmentMapper mapper,
      DeleteAttachmentValidator validator,
      AttachmentStorage storage) {
    super(repository, validator, mapper);
    this.storage = storage;
  }

  /**
   * Deletes an attachment by its ID, first removing it from the database, and if successful,
   * deleting the file from storage.
   *
   * @param attachmentId the {@link AttachmentId} of the attachment to be deleted.
   * @return a {@link DeleteAttachmentResponse} containing the result of the operation.
   * @throws ValidationFailedException if the attachment with the given ID is not found.
   */
  @Override
  public DeleteAttachmentResponse deleteById(AttachmentId attachmentId) {
    Attachment attachment =
        repository
            .findById(attachmentId)
            .orElseThrow(() -> new ValidationFailedException("tst", Lists.newArrayList()));
    DeleteAttachmentResponse response = super.deleteById(attachmentId);
    if (response.isValid()) {
      storage.delete(attachment);
    }
    return response;
  }
}
