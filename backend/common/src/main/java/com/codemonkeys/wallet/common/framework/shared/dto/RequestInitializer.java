/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.shared.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RequestInitializer {

  private static final Map<String, String> FIELD_MAPPINGS =
      Map.of(
          "supplierName", "supplier.name",
          "customerNames", "customers.name",
          "fullName", "name");

  /**
   * Initializes pagination parameters for the given request.
   *
   * @param request the request object to initialize pagination for
   * @param params the parameters containing pagination settings (e.g., "page", "limit")
   * @param <T> the type of the request object extending ListRequest
   */
  public static <T extends ListRequest> void initializePagination(
      T request, Map<String, String> params) {
    try {
      request.setPage(Integer.parseInt(params.getOrDefault("page", "0")));
      request.setLimit(Integer.parseInt(params.getOrDefault("limit", "10")));
    } catch (NumberFormatException e) {
      log.warn("Invalid pagination parameters. Setting to default values.");
      request.setPage(0);
      request.setLimit(10);
    }
  }

  /**
   * Initializes sorting parameters for the given request.
   *
   * @param request the request object to initialize sorting for
   * @param params the parameters containing sorting settings (e.g., "sort" with format
   *     "field:asc/desc")
   * @param <T> the type of the request object extending ListRequest
   */
  public static <T extends ListRequest> void initializeSorting(
      T request, Map<String, String> params) {
    request.setSorting(new ArrayList<>());
    String sortParam = params.get("sort");
    if (sortParam != null) {
      String[] sortParts = sortParam.split(":");
      boolean isDesc = "desc".equalsIgnoreCase(sortParts[1]);
      request.getSorting().add(new SortField(sortParts[0], isDesc));
    }
  }

  /**
   * Initializes a global filter for the given request.
   *
   * @param request the request object to initialize the global filter for
   * @param params the parameters containing the global filter value (e.g., "globalFilter")
   * @param <T> the type of the request object extending ListRequest
   */
  public static <T extends ListRequest> void initializeGlobalFilter(
      T request, Map<String, String> params) {
    String globalFilterParam = params.get("globalFilter");
    if (globalFilterParam != null) {
      request.setGlobalFilter(globalFilterParam);
    }
  }

  /**
   * Initializes filters for the given request based on the provided parameters.
   *
   * @param request the request object to initialize filters for
   * @param params the parameters containing filter criteria
   * @param <T> the type of the request object extending ListRequest
   */
  public static <T extends ListRequest> void initializeFilters(
      T request, Map<String, String> params) {
    List<FilterField> filters = new ArrayList<>();

    params.forEach(
        (key, value) -> {
          if (isFilterKey(key)) {
            String mappedPath = FIELD_MAPPINGS.getOrDefault(key, key);
            String joinPath = extractJoinPath(mappedPath);
            String fieldPath = extractFieldPath(mappedPath);

            filters.add(new FilterField(fieldPath, value, "STRING", joinPath));
          }
        });

    request.setFilters(filters);
  }

  /**
   * Determines if the given key is a valid filter key (not pagination or sorting related).
   *
   * @param key the key to check
   * @return true if the key is a valid filter key, false otherwise
   */
  private static boolean isFilterKey(String key) {
    return !"page".equals(key) && !"limit".equals(key) && !"sort".equals(key);
  }

  /**
   * Extracts the join path from the given mapped path, if applicable.
   *
   * @param mappedPath the mapped path to extract the join path from
   * @return the join path, or null if no join path exists
   */
  private static String extractJoinPath(String mappedPath) {
    return mappedPath.contains(".") ? mappedPath.split("\\.", 2)[0] : null;
  }

  /**
   * Extracts the field path from the given mapped path.
   *
   * @param mappedPath the mapped path to extract the field path from
   * @return the field path
   */
  private static String extractFieldPath(String mappedPath) {
    return mappedPath.contains(".") ? mappedPath.split("\\.", 2)[1] : mappedPath;
  }
}
