/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.attachments.application.crud.common;

import com.codemonkeys.wallet.common.framework.attachments.application.crud.create.CreateAttachmentResponse;
import com.codemonkeys.wallet.common.framework.attachments.application.crud.delete.DeleteAttachmentResponse;
import com.codemonkeys.wallet.common.framework.crud.createwithattachment.CreateWithAttachmentMapper;
import com.codemonkeys.wallet.common.framework.crud.delete.DeleteMapper;
import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentPath;
import com.codemonkeys.wallet.common.framework.domain.vo.Name;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.common.storage.StorageConfiguration;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * Mapper for handling the creation and deletion of attachments. Implements {@link
 * CreateWithAttachmentMapper} and {@link DeleteMapper} to manage conversion between {@link
 * MultipartFile[]} and {@link Attachment}, and to generate appropriate responses.
 */
@Service
@AllArgsConstructor(onConstructor = @__(@Autowired))
public class AttachmentMapper
    implements CreateWithAttachmentMapper<
            Attachment, AttachmentId, MultipartFile[], CreateAttachmentResponse>,
        DeleteMapper<DeleteAttachmentResponse> {
  StorageConfiguration configuration;

  /**
   * Converts an array of {@link MultipartFile} into a list of {@link Attachment} domain objects.
   *
   * @param request the array of multipart files to be converted into attachments.
   * @return a list of attachment domain objects.
   */
  @Override
  public List<Attachment> toDomain(MultipartFile[] request) {
    return Arrays.stream(request).map(attachmentFromMultipartFile()).toList();
  }

  /**
   * Creates a function to map each {@link MultipartFile} to an {@link Attachment}.
   *
   * @return a function that takes a {@link MultipartFile} and returns an {@link Attachment}.
   */
  private @NotNull Function<MultipartFile, Attachment> attachmentFromMultipartFile() {
    return file -> {
      AttachmentId id = AttachmentId.of(UUID.randomUUID());
      Name name =
          Name.of(file.getOriginalFilename() != null ? file.getOriginalFilename() : file.getName());
      Name storageName = Name.of(UUID.randomUUID().toString());
      Name bucketName = Name.of(configuration.getBucket());
      AttachmentPath ap = AttachmentPath.of(configuration.getPrefix(), storageName.getValue());
      return Attachment.of(id, name, ap, storageName, bucketName, file);
    };
  }

  /**
   * Converts a list of attachments into a {@link CreateAttachmentResponse} response.
   *
   * @param entity the list of attachments.
   * @return a response containing the created attachments and validation result.
   */
  @Override
  public CreateAttachmentResponse toResponse(List<Attachment> entity) {
    List<Attachment> ids = entity.stream().toList();
    return CreateAttachmentResponse.of(ids, ValidResult.of());
  }

  /**
   * Converts an invalid result into a {@link CreateAttachmentResponse} response.
   *
   * @param errors the invalid result to be returned in the response.
   * @return a response containing the validation errors.
   */
  @Override
  public CreateAttachmentResponse toResponse(InvalidResult errors) {
    return CreateAttachmentResponse.of(null, errors);
  }

  /**
   * Returns an empty {@link DeleteAttachmentResponse} indicating a successful deletion.
   *
   * @return an empty deletion response.
   */
  @Override
  public DeleteAttachmentResponse toEmptyDto() {
    return DeleteAttachmentResponse.of(ValidResult.of());
  }

  /**
   * Converts validation errors into a {@link DeleteAttachmentResponse}.
   *
   * @param errors the validation errors.
   * @return a response containing the deletion errors.
   */
  @Override
  public DeleteAttachmentResponse toDto(InvalidResult errors) {
    return DeleteAttachmentResponse.of(errors);
  }
}
