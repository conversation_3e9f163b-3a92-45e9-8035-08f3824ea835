/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.shared.translations;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

@Configuration
public class CentralMessageSourceConfig {

  @Bean
  public MessageSource messageSource() {
    ReloadableResourceBundleMessageSource messageSource =
        new ReloadableResourceBundleMessageSource();
    messageSource.setBasenames(
        "classpath:/i18n/customer/customer-messages",
        "classpath:/i18n/supplier/supplier-messages",
        "classpath:/i18n/agreement/agreement-messages",
        "classpath:/i18n/wallets/wallets-messages",
        "classpath:/i18n/audit-log/audit-log-messages",
        "classpath:/i18n/analytics/analytics-messages");
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(3600);
    messageSource.setAlwaysUseMessageFormat(true);
    return messageSource;
  }
}
