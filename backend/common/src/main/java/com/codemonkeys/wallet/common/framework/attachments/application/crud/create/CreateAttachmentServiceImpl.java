/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.attachments.application.crud.create;

import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.AttachmentMapper;
import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.AzureAttachmentStorage;
import com.codemonkeys.wallet.common.framework.attachments.application.crud.common.MinioAttachmentStorage;
import com.codemonkeys.wallet.common.framework.crud.createwithattachment.CreateWithAttachmentServiceImpl;
import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.AttachmentRepository;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.storage.Storage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * Service implementation for creating attachments. This class extends {@link
 * CreateWithAttachmentServiceImpl} and manages the process of storing and validating uploaded
 * files.
 */
@Service
public class CreateAttachmentServiceImpl
    extends CreateWithAttachmentServiceImpl<
        MultipartFile[],
        CreateAttachmentResponse,
        Attachment,
        AttachmentId,
        AttachmentRepository,
        AttachmentMapper,
        CreateAttachmentValidator> {

  /**
   * Constructor for {@link CreateAttachmentServiceImpl}.
   *
   * @param repository the repository for managing {@link Attachment} entities.
   * @param mapper the mapper for converting entities to DTOs.
   * @param validator the validator to validate attachment creation.
   * @param storage the storage service responsible for handling file uploads.
   */
  @Autowired
  public CreateAttachmentServiceImpl(
      AttachmentRepository repository,
      AttachmentMapper mapper,
      CreateAttachmentValidator validator,
      Storage<AttachmentId, Attachment> storage) {
    super(repository, mapper, validator, storage);
  }
}
