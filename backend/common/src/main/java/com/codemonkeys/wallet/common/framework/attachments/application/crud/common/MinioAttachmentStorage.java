/* (C)2024-2025 */
package com.codemonkeys.wallet.common.framework.attachments.application.crud.common;

import com.codemonkeys.wallet.common.framework.domain.Attachment;
import com.codemonkeys.wallet.common.framework.domain.vo.AttachmentId;
import com.codemonkeys.wallet.common.storage.Storage;
import com.codemonkeys.wallet.common.storage.MinioStorageConfiguration;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.errors.ErrorResponseException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.Optional;
import javax.naming.OperationNotSupportedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for handling attachment storage operations. Implements {@link Storage} to manage upload,
 * download, and deletion of {@link Attachment} objects using a MinIO client.
 */

@Slf4j
public class MinioAttachmentStorage implements Storage<AttachmentId, Attachment> {
  MinioClient storageClient;
  MinioStorageConfiguration minioStorageConfiguration;

  /**
   * Constructs a new {@code AttachmentStorage} service with the specified storage configuration.
   *
   * @param minioStorageConfiguration the storage configuration settings.
   */
  @Autowired
  public MinioAttachmentStorage(MinioStorageConfiguration minioStorageConfiguration) {
    // storageClient =
    //    MinioClient.builder()
    //        .endpoint("http://127.0.0.1:9000")
    //        .credentials("BVafeSUBWPzQiZWLKIKf", "7zihASLhe7WmmNV7cfCBVa7vJcloNQbDUU7xVJTj")
    //        .build();
    this.minioStorageConfiguration = minioStorageConfiguration;
    storageClient =
        MinioClient.builder()
            .endpoint(minioStorageConfiguration.getEndpoint())
            .credentials(minioStorageConfiguration.getAccessKey(), minioStorageConfiguration.getSecretKey())
            .build();
  }

  /**
   * Formats the prefix for storing attachments based on the current date.
   *
   * @param attachment the attachment to be stored.
   * @return the formatted prefix used for the attachment path.
   */
  private String formatPrefix(Attachment attachment) {
    LocalDate today = LocalDate.now();
    return String.format(
        minioStorageConfiguration.getPrefix(),
        "%s/%s/%s/%s",
        today.getYear(),
        today.getMonthValue(),
        today.getDayOfMonth(),
        attachment.getName().getValue());
  }

  /**
   * Uploads an {@link Attachment} to the storage.
   *
   * @param attachment the attachment to be uploaded.
   * @return an optional containing the uploaded attachment, or an empty optional if the upload
   *     failed.
   */
  @Override
  public Optional<Attachment> upload(Attachment attachment) {
    if (attachment.getMultipartFile().isEmpty()) {
      return Optional.empty();
    }
    String prefix = formatPrefix(attachment);
    try {
      PutObjectArgs args =
          PutObjectArgs.builder()
              .bucket(minioStorageConfiguration.getBucket())
              .object(attachment.getPath().getValue())
              .stream(
                  attachment.getMultipartFile().getInputStream(),
                  attachment.getMultipartFile().getInputStream().available(),
                  -1)
              .build();
      storageClient.putObject(args);
      return Optional.of(attachment);
    } catch (Exception e) {
      log.info("{}", e.getMessage());
      System.out.println(e.getMessage());
      return Optional.empty();
    }
  }

  /**
   * Unsupported operation for downloading files using prefix and bucket.
   *
   * @throws OperationNotSupportedException when this method is called, as it is not supported.
   */
  @Override
  public void download(String prefix, String bucket) throws OperationNotSupportedException {
    throw new OperationNotSupportedException("This method is not supported.");
  }

  /**
   * Prepares an attachment for download by returning its {@link InputStream} from storage.
   *
   * @param attachmentPath the path of the attachment to be downloaded.
   * @return an optional containing the input stream of the file, or an empty optional if the file
   *     is not found.
   */
  public Optional<InputStream> prepareFileForDownload(String attachmentPath) {
    try {
      String fullObjectPath = attachmentPath;
      InputStream fileStream =
          storageClient.getObject(
              GetObjectArgs.builder()
                  .bucket(minioStorageConfiguration.getBucket())
                  .object(fullObjectPath)
                  .build());
      return Optional.of(fileStream);
    } catch (ErrorResponseException e) {
      log.error(
          "File with path {} not found in bucket {}: {}",
          attachmentPath,
          minioStorageConfiguration.getBucket(),
          e.getMessage());
      return Optional.empty();
    } catch (Exception e) {
      log.error("Error while finding or downloading attachment: {}", e.getMessage());
      return Optional.empty();
    }
  }

  /**
   * Deletes an attachment from the storage.
   *
   * @param attachment the attachment to be deleted.
   */
  @Override
  public void delete(Attachment attachment) {
    try {
      RemoveObjectArgs args =
          RemoveObjectArgs.builder()
              .bucket(minioStorageConfiguration.getBucket())
              .object(attachment.getPath().getValue())
              .build();
      storageClient.removeObject(args);
    } catch (Exception e) {
      log.info("{}", e.getMessage());
    }
  }
}
