/* (C)2024-2025 */
package com.codemonkeys.wallet.common.tests.testcontainers;

import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.event.ContextClosedEvent;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.lifecycle.Startables;

// Reference: https://maciejwalkowiak.com/blog/testcontainers-spring-boot-setup/
public final class PostgresTestcontainersInitializer
    implements ApplicationContextInitializer<ConfigurableApplicationContext> {

  static final String POSTGRES_LATEST = "postgres:latest";
  static final String USERNAME = "postgres";
  static final String PASSWORD = "postgres";
  static PostgreSQLContainer<?> postgres =
      new PostgreSQLContainer<>(POSTGRES_LATEST)
          .withUsername(USERNAME)
          .withPassword(PASSWORD)
          .withInitScript("sql/i.sql");

  static {
    Startables.deepStart(postgres).join();
  }

  @Override
  public void initialize(ConfigurableApplicationContext context) {

    context.addApplicationListener(
        (ApplicationListener<ContextClosedEvent>) event -> postgres.stop());

    postgres.start();

    TestPropertyValues.of(
            "spring.datasource.url=" + postgres.getJdbcUrl(),
            "spring.datasource.username=" + postgres.getUsername(),
            "spring.datasource.password=" + postgres.getPassword())
        .applyTo(context.getEnvironment());
  }
}
