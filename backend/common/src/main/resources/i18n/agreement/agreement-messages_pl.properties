validations.name.agreement={0} jest zbyt dÅugÄ nazwÄ!
validations.failed.agreement=BÅÄd walidacji dla inputu name.
validations.agreement.name.unique=Umowa o tej nazwie juÅ¼ istnieje.
validations.agreement.exists=Umowa dla danego okresu, noÅnika, klienta i sprzedawcy istnieje!
error.agreement.does.not.exist=Umowa nie istnieje.
error.agreement.empty.list=Pusta lista umÃ³w.
error.financial.instrument.not.found=Kontrakt o ID {0} nie zostaÅ znaleziony.
error.authorized.buyer.not.found=Autoryzowany kupujÄcy o ID {0} nie zostaÅ znaleziony.
error.agreement.supplier.not.found=Dostawca umowy o ID {0} nie zostaÅ znaleziony.
validation.agreement.contract.removal=Nie moÅ¼na zapisaÄ umowy, poniewaÅ¼ usuniÄto kontrakty, dla ktÃ³rych istniejÄ dodane transze portfela
validation.agreementGroup.notFound=Grupa zakupowa zawiera powiÄzane umowy i nie moÅ¼e zostaÄ usuniÄta.