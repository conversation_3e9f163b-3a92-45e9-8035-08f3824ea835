validations.tax-number=Invalid tax number for a given country.
validations.name={0} is too long name!
validations.failed=Validation failed for input data.
validations.customer.name.unique={0} is not unique customer name.
validations.customer.tax-number.unique={0} is not unique tax number.
validations.group.exists=Group does not exist
validations.group.name.unique=Group name must be unique
validations.year.format=Year must be a 4-digit number
validations.year.required=Year must be provided
validations.customers.in.other.groups=Some customers are already in a different group: {0}
validations.customers.not.provided=No customers provided
validations.phone=Invalid phone number
validations.email=Invalid email
error.customer.id.null=Customer ID cannot be null
error.customer.not.found=Customer not found: {0}
error.customer.has.agreements=Failed to delete customer because he has active agreements.
error.customer.has.wallets=Failed to delete customer because he has active wallets.
error.group.not.found=Group with ID {0} not found
error.group.does.not.exist=Group does not exist
error.contact.email.not.unique={0} is not unique customer contact email.
error.contact.phone.not.unique={0} is not unique customer contact phone.