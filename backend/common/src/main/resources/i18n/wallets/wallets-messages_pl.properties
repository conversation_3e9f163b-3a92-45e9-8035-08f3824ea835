validations.wallet.name.unique=Nazwa portfela musi być unikalna
error.tranche.not.found=Transzy o identyfikatorze {0} nie znaleziono
error.element.not.found=Elementu o identyfikatorze {0} nie znaleziono
error.agreement.not.found=Umowy o identyfikatorze {0} nie znaleziono
error.wallet.does.not.exist=Portfela o identyfikatorze {0} nie istnieje
error.recommendation.does.not.exist=Rekomendacji o identyfikatorze {0} nie istnieje
error.customer.not.found=Klient nie znaleziony
error.customer.contact.not.found=Nie znaleziono głównego kontaktu dla klienta
error.recommendation.failed.send=Nie udało się wysłać rekomendacji
error.recommendation.failed.retry=Nie udało się ponownie wysłać rekomendacji
error.recommendation.invalid.status.send=Nie można wysłać rekomendacji w statusie: {0}
error.recommendation.invalid.status.retry=Nie można ponownie wysłać rekomendacji w statusie: {0}
error.recommendation.invalid.status.add.to.wallet=Nie można dodać rekomendacji do portfela w statusie: {0}
error.recommendation.invalid.status.mark.as.accepted=Nie można oznaczyć rekomendacji jako zaakceptowanej w statusie: {0}
error.recommendation.invalid.status.mark.as.completed=Nie można oznaczyć rekomendacji jako zakończonej w statusie: {0}
error.recommendation.invalid.status.mark.as.not.completed=Nie można oznaczyć rekomendacji jako niezakończonej w statusie: {0}
error.recommendation.invalid.status.mark.as.ordered=Nie można oznaczyć rekomendacji jako zamówionej w statusie: {0}
error.recommendation.invalid.status.mark.as.rejected=Nie można oznaczyć rekomendacji jako odrzuconej w statusie: {0}
validations.contract.notFound=Kontrakt {0} nie został znaleziony w umowie
validations.entity.not.found=Nie znaleziono wymaganego kontraktu w umowie niezbędnego do wygenerowania eksportu.
error.recommendation.invalid.price.format=Nieprawidłowy format ceny. Proszę podać poprawną liczbę dziesiętną.
validations.purchaseMethod.invalid=Metoda zakupu "{0}" nie jest prawidłowa dla wybranego kontraktu "{1}".
validations.contract.price.not.found=Brak ostatniej ceny dla kontraktu: {0}
validations.contract.deadlineOutOfRange=Data wykonania transzy nie mieści się w zakresie dat początku i końca zakupów wskazanych w kontrakcie na umowie
validations.contract.portfolioVolumeExceedsLimit=Przekroczono 100% wolumenu.
validations.contract.recommendationVolumeExceedsLimit=Na liście rekomendacji istnieją niedodane do portfela rekomendacje, po uwzględnieniu których nastąpi przekroczenie 100% realizacji portfela.
validations.contract.invalidVolumeFormat=Niepoprawny format wolumenu. Proszę wprowadzić poprawną wartość liczbową.
validations.elements.wf=W portfelu brakuje elementu "Zielone współczynnik - WF", ktory wymagany jest ze wzgledu na wybrany sposob liczenia zielonych certyfikatow.
validations.elements.cj=W portfelu brakuje elementu "Zielone współczynnik - CJ (Rok portfela-1)", ktory wymagany jest ze wzgledu na wybrany sposob liczenia zielonych certyfikatow.
validations.elements.costs=W portfelu brakuje elementu "Koszty" dla głównego medium.
validations.rights.elements.costs=W portfelu brakuje elementu "Koszty", ktory wymagany jest ze wzgledu na wybrany sposob liczenia zielonych certyfikatow.
validations.elements.margin=W portfelu brakuje elementu "Marża", ktory wymagany jest ze wzgledu na wybrany sposob liczenia zielonych certyfikatow.
validations.elements.excise=W portfelu brakuje elementu "Akcyza" dla głównego medium.
validations.rights.elements.excise=W portfelu brakuje elementu "Akcyza" dla zielonych certyfikatów, ktory wymagany jest ze wzgledu na wybrany sposob liczenia zielonych certyfikatow.
validations.contract.exceedsMaxTranches=W portfelach nie spełniono następujących warunków zakupowych: Liczba transz
validations.wallet.notFound=Brak portfela
validations.tranche.idsRequired=Brak portfela
validations.wallet.volumeTimeUnitLimit=Przekroczono 100% wolumenu w jednostce: {0}
validations.media.volumeMultipleMismatch=Wolumen nie wielokrotny
validations.media.volumeLessThanMin=Nie spełniono warunków zakupowych umowy dotyczących wolumenu minimalnego wynoszącego {0}%.
validation.media.volumeAboveMax=Nie spełniono warunków zakupowych umowy dotyczących wolumenu maksymalnego wynoszącego {0}%.
validations.wallet.confirmedPrice=Wprowadzone zmiany wpłynęłyby na potwierdzone ceny. Cofnij potwierdzenie cen dla miesięcy, dla których chcesz zmodyfikować przeliczenie cen.
validations.wallet.confirmedRealisation=Wprowadzone zmiany wpłynęłyby na potwierdzone realizację. Cofnij potwierdzenie realizacji dla miesięcy, dla których chcesz zmodyfikować przeliczenie cen.
validations.elements.discount=Element "cena uslugi" oraz "cena obnizki" sa obowiazkowe dla wybranego sposobu liczenia zielonych.
GAS=gaz
ENERGY=energia
element.type.net=Cena TGE
element.type.costs=Koszty
element.type.excise=Akcyza
element.type.profile=Profil
element.type.profile_percent=Profil (%)
element.type.green_property_rights=Zielone certyfikaty
element.type.blue_property_rights=Błękitne certyfikaty
element.type.white_property_rights=Białe certyfikaty
element.type.total_property_rights=Prawa majątkowe razem
element.type.total=Cena końcowa
element.type.cj=Zielone współczynnik - CJ
element.type.wf=Zielone współczynnik - WF
element.type.margin=Marża
order.type.customer=Klient kupuje
order.type.enea_trade=EneaTrade24
order.type.poi=POI
order.type.portal_fortum=Portal Fortum
order.type.portal_telnom=Telnom
order.type.supplier_email=E-mail do sprzedawcy
order.type.attachment_email=E-mail z załącznikiem
order.type.other=Inne
error.contract.not.found.for.agreement=Nie znaleziono kontraktu: {0} w umowie {1}
error.contract.not.found.for.agreement2=Nie znaleziono kontraktu: {0} w umowie
error.contract.not.found.for.wallet=Nie znaleziono kontraktu: {0} w portfelu {1}
error.contract.not.found.for.wallet2=Nie znaleziono kontraktu: {0} w portfelu {1}
error.wallet.not.found=Nie znaleziono portfela o ID {0}.
error.wallet.not.found2=Nie znaleziono portfela o ID.
error.wallet.not.found.for.agreement=Nie znaleziono portfela dla umowy o identyfikatorze {0}
error.wallet.not.found.for.agreement2=Nie znaleziono portfela dla umowy o identyfikatorze
element.type.correction_factor=Współczynnik korekty
element.type.tge_correction_factor=Współczynnik korekty TGE
element.type.hidden_correction_factor=Ukryty współczynnik korekty
element.type.multiplier=Mnożnik
element.type.wibor=WIBOR
element.type.last_cj=Ostatnie CJ
element.type.m_parameter=Parametr M
element.type.industrial_status_discount=Rabat statusu przemysłowego
element.type.green_property_rights_costs=Koszty zielonych praw majątkowych
element.type.green_property_rights_hidden_correction_factor=Ukryty współczynnik korekty (zielone prawa)
element.type.supplier_service_price=Cena usługi sprzedawcy
element.type.supplier_discount_price=Upust sprzedawcy
realisation.confirmation.no_products=Brak produktów do potwierdzenia.