validations.wallet.name.unique=Wallet name must be unique
error.tranche.not.found=Tranches with id {0} not found
error.element.not.found=Elements with id {0} not found
error.agreement.not.found=Agreement with id {0} not found
error.wallet.does.not.exist=Wallet with id {0} does not exist
error.recommendation.does.not.exist=Recommendaction with id {0} does not exist
error.customer.not.found=Customer not found
error.customer.contact.not.found=Primary contact not found for customer
error.recommendation.failed.send=Failed to send recommendation
error.recommendation.failed.retry=Failed to retry sending recommendation
error.recommendation.invalid.status.send=Cannot send recommendation in status: {0}
error.recommendation.invalid.status.retry=Cannot retry sending recommendation in status: {0}
error.recommendation.invalid.status.add.to.wallet=Cannot add recommendation to wallet in status: {0}
error.recommendation.invalid.status.mark.as.accepted=Cannot mark recommendation as accepted in status: {0}
error.recommendation.invalid.status.mark.as.completed=Cannot mark recommendation as completed in status: {0}
error.recommendation.invalid.status.mark.as.not.completed=Cannot mark recommendation as not completed in status: {0}
error.recommendation.invalid.status.mark.as.ordered=Cannot mark recommendation as ordered in status: {0}
error.recommendation.invalid.status.mark.as.rejected=Cannot mark recommendation as rejected in status: {0}
validations.contract.notFound=Contract {0} not found
error.recommendation.invalid.price.format=Invalid price format. Please provide a valid decimal number.
error.wallet.not.found.for.agreement=Wallet not found for agreement with id {0}
validations.purchaseMethod.invalid=Purchase method "{0}" is not valid for the selected contract "{1}".
validations.contract.price.not.found=No recent price available for contract: {0}
validations.elements.wf=Wallet is missing element WF due to chosen green property rights calculation method.
validations.elements.cj=Wallet is missing element CJ due to chosen green property rights calculation method.
validations.elements.costs=Wallet is missing element COSTS due to chosen green property rights calculation method.
validations.rights.elements.costs=Wallet is missing element COSTS due to chosen green property rights calculation method.
validations.elements.excise=Wallet is missing element EXCISE for main media.
validations.rights.elements.excise=Wallet is missing element EXCISE for green property rights due to chosen green property rights calculation method.
validations.elements.margin=Wallet is missing element MARGIN due to chosen green property rights calculation method.
validations.contract.deadlineOutOfRange=The tranche execution date is not within the purchase start and end date range specified in the contract assigned to the agreement.
validations.contract.portfolioVolumeExceedsLimit=The 100% volume limit has been exceeded in the following portfolios.
validations.contract.recommendationVolumeExceedsLimit=There are recommendations on the list that have not been added to the portfolio, and their inclusion would exceed 100% of the portfolio?s target.
validations.contract.invalidVolumeFormat=Invalid volume format. Please enter a valid numeric value.
validations.contract.exceedsMaxTranches=The following purchasing conditions were not met in the wallets: Number of tranches.
error.contract.not.found.for.wallet=No contract found for: {0} in wallet {1}
error.contract.not.found.for.agreement=No contract found for: {0} in agreement {1}
validations.wallet.notFound=The wallet not found
validations.tranche.idsRequired=The wallet not found
validations.media.volumeMultipleMismatch=Volume multiple mismatch
validations.media.volumeLessThanMin=The purchasing conditions of the agreement regarding the minimum volume of {0}% were not met.
validation.media.volumeAboveMax=The purchasing conditions of the agreement regarding the maximum volume of {0}% were not met.
validations.wallet.confirmedPrice=The changes introduced would affect the confirmed prices. Revoke the price confirmations for the months for which you want to modify the price calculation.
validations.wallet.confirmedRealisation=The changes introduced would affect the confirmed realisation. Revoke the realisation confirmations for the months for which you want to modify the realisation calculation.
validations.entity.not.found=The required contract within the agreement could not be found to generate the export.
validations.elements.discount=Service discount and service price and mandatory.
GAS=gaz
ENERGY=energia
element.type.net=TGE Price
element.type.costs=Costs
element.type.excise=Excise
element.type.profile=Profile
element.type.profile_percent=Profile (%)
element.type.green_property_rights=Green property rights
element.type.blue_property_rights=Blue certificates
element.type.white_property_rights=White certificates
element.type.total_property_rights=Total property rights
element.type.total=Final price
element.type.cj=Green coefficient - CJ
element.type.wf=Green coefficient - WF
element.type.margin=Margin
order.type.customer=Customer buys
order.type.enea_trade=EneaTrade24
order.type.poi=POI
order.type.portal_fortum=Portal Fortum
order.type.portal_telnom=Telnom
order.type.supplier_email=Supplier email
order.type.attachment_email=Email with attachment
order.type.other=Other

error.wallet.not.found=Wallet with ID {0} not found.

element.type.correction_factor=Correction factor
element.type.tge_correction_factor=TGE correction factor
element.type.hidden_correction_factor=Hidden correction factor
element.type.multiplier=Multiplier
element.type.wibor=WIBOR
element.type.last_cj=Last CJ
element.type.m_parameter=Parameter M
element.type.industrial_status_discount=Industrial status discount
element.type.green_property_rights_costs=Green property rights costs
element.type.green_property_rights_hidden_correction_factor=Hidden correction factor (green property rights)
element.type.supplier_service_price=Supplier service price
element.type.supplier_discount_price=Supplier discount price
realisation.confirmation.no_products=No products available for confirmation.