/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.profile;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.DefaultElementPriceHandler;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.PriceCommand;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProfileCostPriceHandler extends DefaultElementPriceHandler<ProfileCostCommand> {
  @Override
  public Wallet handle(ProfileCostCommand command) {
    return defaultWork(command.getWallet(), getAssociatedElements());
  }

  @Override
  protected Wallet doWork(Wallet wallet, List<PriceCommand> requests) {
    return defaultWork(wallet, getAssociatedElements());
  }

  @Override
  protected List<ElementType> getAssociatedElements() {
    return List.of(ElementType.PROFILE);
  }
}
