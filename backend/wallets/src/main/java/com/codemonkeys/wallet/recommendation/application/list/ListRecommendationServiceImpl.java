/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.list;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListService;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListServiceImpl;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.SpecHelper;
import com.codemonkeys.wallet.common.framework.shared.dto.FilterField;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.RecommendationRepository;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.domain.recommendation.vo.RecommendationId;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ListRecommendationServiceImpl
    extends ProjectionListServiceImpl<
        ListRecommendationRequest,
        ListRecommendationResponse,
        Recommendation,
        RecommendationId,
        RecommendationRepository,
        ListRecommendationMapper>
    implements ProjectionListService<ListRecommendationRequest, ListRecommendationResponse> {

  public ListRecommendationServiceImpl(
      RecommendationRepository repository, ListRecommendationMapper mapper) {
    super(repository, mapper, ListRecommendationResponse.class);
  }

  /**
   * Handles the listing of recommendations by delegating to the repository and mapper.
   *
   * @param request the request object containing parameters for listing recommendations.
   * @return a {@link Page} of {@link ListRecommendationResponse} DTOs.
   */
  @Override
  public Page<ListRecommendationResponse> list(ListRecommendationRequest request) {
    return super.list(request);
  }

  /**
   * Prepares the specification for filtering recommendations based on the provided request.
   *
   * @param request the request object containing filtering parameters.
   * @return a {@link Specification} for filtering recommendations.
   */
  @Override
  protected Specification<Recommendation> prepareSpecification(ListRecommendationRequest request) {
    Specification<Recommendation> spec = createBaseSpecification(request.isIncludeArchived());

    if (request.getStatuses() != null && !request.getStatuses().isEmpty()) {
      spec = spec.and(filterByStatuses(request.getStatuses()));
    }
    FilterField supplierNameFilter =
        request.getFilters().stream()
            .filter(f -> "supplierName".equals(f.getId()))
            .findFirst()
            .orElse(null);

    if (supplierNameFilter != null) {
      String filterValue = supplierNameFilter.getValue().toString().toLowerCase();

      spec =
          spec.and(
              (root, query, cb) ->
                  cb.like(cb.lower(root.get("supplierName")), "%" + filterValue + "%"));
      request.getFilters().remove(supplierNameFilter);
    }
    // ----------------------------------------------------------------------
    FilterField authorizedBuyersFilter =
        request.getFilters().stream()
            .filter(f -> "authorizedBuyers".equals(f.getId()))
            .findFirst()
            .orElse(null);

    if (authorizedBuyersFilter != null) {
      String filterValue = authorizedBuyersFilter.getValue().toString().toLowerCase();
      spec =
          spec.and(
              (root, query, cb) ->
                  cb.like(cb.lower(root.join("authorizedBuyers")), "%" + filterValue + "%"));
    }
    cleanFilters(request);
    if (supplierNameFilter != null) {
      request.getFilters().remove(supplierNameFilter);
    }
    if (authorizedBuyersFilter != null) {
      request.getFilters().remove(authorizedBuyersFilter);
    }
    return spec.and(SpecHelper.filterBy(request.getFilters()));
  }

  /**
   * Cleans the filters by removing fields that are not needed for database filtering. Specifically,
   * it removes 'includeArchived' and 'status' filters, as these are handled separately in the logic
   * and do not directly correspond to columns in the database.
   *
   * @param request the {@link ListRecommendationRequest} containing the filters to be cleaned. This
   *     method modifies the list of filters in place, removing unnecessary entries.
   */
  private void cleanFilters(ListRecommendationRequest request) {
    if (request.getFilters() != null) {
      request
          .getFilters()
          .removeIf(
              filter ->
                  "includeArchived".equals(filter.getId()) || "status".equals(filter.getId()));
    }
  }

  /**
   * Filters recommendations by their statuses.
   *
   * @param statuses the set of statuses to filter by.
   * @return a {@link Specification} for filtering recommendations by status.
   */
  private Specification<Recommendation> filterByStatuses(Set<RecommendationStatus> statuses) {
    return (root, query, cb) -> root.get("status").in(statuses);
  }

  /**
   * Creates the base specification to include or exclude archived recommendations.
   *
   * @param includeArchived whether to include archived recommendations.
   * @return a {@link Specification} for filtering by the deadline date.
   */
  private Specification<Recommendation> createBaseSpecification(boolean includeArchived) {
    if (includeArchived) {
      return (root, query, cb) -> cb.conjunction();
    } else {
      // pokazuj wszystkie rekomendacje z terminem decyzji od 14 dni wstecz aż do przyszłości
      return (root, query, cb) -> {
        LocalDateTime twoWeeksAgo = LocalDateTime.now().minusDays(14);
        return cb.greaterThanOrEqualTo(root.get("deadline"), twoWeeksAgo);
      };
    }
  }
}
