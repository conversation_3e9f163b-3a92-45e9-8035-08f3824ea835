/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.statusflow;

import com.codemonkeys.wallet.common.framework.domain.vo.CommunicationLanguage;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class EmailTemplateDataPreparer {

  private static final DecimalFormat DECIMAL_FORMAT;

  static {
    DecimalFormatSymbols symbols = new DecimalFormatSymbols(Locale.getDefault());
    symbols.setDecimalSeparator(',');
    symbols.setGroupingSeparator(' ');
    DECIMAL_FORMAT = new DecimalFormat("#,##0.00", symbols);
  }

  public Map<String, Object> prepareTemplateData(
      Recommendation recommendation, CustomerContact contact) {
    Map<String, Object> variables = new HashMap<>();
    CommunicationLanguage language = contact.getLanguage();
    variables.put("recommendationId", recommendation.getId().getId());
    variables.put("customerName", recommendation.getCustomerName());
    variables.put("carrier", getCarrierTypeName(recommendation.getCarrier(), language));
    variables.put("contract", recommendation.getContract());
    variables.put("volume", formatDecimal(recommendation.getVolume()));
    variables.put("price", formatDecimal(recommendation.getPrice()));
    variables.put(
        "deadline",
        recommendation
            .getDeadline()
            .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
    variables.put("recommendationComment", recommendation.getEmailTemplateComment());
    return variables;
  }

  public String determineSubject(CustomerContact contact, Recommendation recommendation) {
    String customerName = recommendation.getCustomerName();
    return "EN".equals(contact.getLanguage().name())
        ? String.format("Prepared for you: Purchase Recommendation (%s)", customerName)
        : String.format("Przygotowano dla Państwa rekomendację zakupu (%s)", customerName);
  }

  public String determineTemplateName(CustomerContact contact) {
    return "EN".equals(contact.getLanguage().name())
        ? "recommendation-template-eng"
        : "recommendation-template-pl";
  }

  private String formatDecimal(String value) {
    try {
      return DECIMAL_FORMAT.format(Double.parseDouble(value));
    } catch (NumberFormatException e) {
      return value;
    }
  }

  private String getCarrierTypeName(String carrier, CommunicationLanguage language) {
    return switch (carrier) {
      case "EE" -> (language == CommunicationLanguage.EN) ? "Energy" : "Energia";
      case "G" -> (language == CommunicationLanguage.EN) ? "Gas" : "Gaz";
      case "PM" -> (language == CommunicationLanguage.EN) ? "Property Rights" : "Prawa Majątkowe";
      default -> carrier;
    };
  }
}
