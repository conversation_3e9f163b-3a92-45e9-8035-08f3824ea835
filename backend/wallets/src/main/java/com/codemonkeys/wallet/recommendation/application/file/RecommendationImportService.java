/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.file;

import com.codemonkeys.wallet.common.framework.attachments.application.crud.create.CreateAttachmentResponse;
import com.codemonkeys.wallet.common.framework.attachments.application.crud.create.CreateAttachmentServiceImpl;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.recommendation.application.create.CreateRecommendationRequest;
import com.codemonkeys.wallet.recommendation.application.create.CreateRecommendationServiceImpl;
import com.codemonkeys.wallet.recommendation.application.file.export.RecommendationImportErrorFileExporter;
import com.codemonkeys.wallet.recommendation.application.file.parser.FileParser;
import com.codemonkeys.wallet.recommendation.application.file.utils.ErrorMessages;
import com.codemonkeys.wallet.recommendation.application.file.utils.FileUploadError;
import com.codemonkeys.wallet.recommendation.application.file.validators.AgreementValidator;
import com.codemonkeys.wallet.recommendation.application.file.validators.RecommendationValidator;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/** Service for processing file uploads. */
@Service
@RequiredArgsConstructor
public class RecommendationImportService {

  private final FileParser fileParser;
  private final AgreementValidator agreementValidator;
  private final CreateRecommendationServiceImpl createRecommendationService;
  private final RecommendationValidator recommendationValidator;
  private final CreateAttachmentServiceImpl createAttachmentService;
  private final RecommendationImportErrorFileExporter errorFileExporter;
  private final List<RecommendationImportRequest> erroneousRecords = new ArrayList<>();

  /**
   * Processes the uploaded file.
   *
   * @param file the uploaded file
   * @return the response containing the results of the file processing
   * @throws Exception if an error occurs during file processing
   */
  public RecommendationImportResponse processFile(MultipartFile file) throws Exception {
    String filename = file.getOriginalFilename();
    if (isInvalidFileFormat(filename)) {
      return new RecommendationImportResponse(ErrorMessages.INVALID_FILE_FORMAT);
    }

    InputStream inputStream = file.getInputStream();
    List<FileUploadError> errors = new ArrayList<>();
    List<RecommendationImportRequest> recommendationDTOs = parseFile(inputStream, errors);

    // Continue processing even if there are parsing errors
    List<CreateRecommendationRequest> createRequests =
        validateAndCreateRequests(recommendationDTOs, errors);
    // Process valid records
    createRecommendations(createRequests, errors);

    RecommendationImportResponse response =
        new RecommendationImportResponse(createRequests.size(), errors);

    // If errors occurred, generate an attachment with the erroneous records and set its download
    if (!response.getErrors().isEmpty()) {
      String errorFileId = generateErrorAttachmentId();
      response.setErrorFileId(errorFileId);
    }

    return response;
  }

  /**
   * Exports erroneous records to an XLSX file, creates an attachment, and returns its download URL.
   *
   * @return the download URL for the erroneous records attachment, or null if creation fails.
   * @throws Exception if an error occurs during export or attachment creation.
   */
  private String generateErrorAttachmentId() throws Exception {
    byte[] errorFileBytes = errorFileExporter.exportErrorFile(getErroneousRecords());
    MultipartFile errorAttachment =
        new MockMultipartFile(
            "files",
            "bledne_rekordy.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            errorFileBytes);

    CreateAttachmentResponse attachmentResponse =
        createAttachmentService.create(new MultipartFile[] {errorAttachment});
    if (attachmentResponse.isValid()
        && attachmentResponse.getAttachments() != null
        && !attachmentResponse.getAttachments().isEmpty()) {
      return attachmentResponse.getAttachments().get(0).getId().getId().toString();
    }
    return null;
  }

  /**
   * Returns the list of erroneous import records that failed validation.
   *
   * @return a list of RecommendationImportRequest objects that did not pass validation.
   */
  public List<RecommendationImportRequest> getErroneousRecords() {
    return erroneousRecords;
  }

  /**
   * Checks if the file format is invalid.
   *
   * @param filename the name of the file
   * @return true if the file format is invalid, false otherwise
   */
  private boolean isInvalidFileFormat(String filename) {
    return filename == null || (!filename.endsWith(".xls") && !filename.endsWith(".xlsx"));
  }

  /**
   * Parses the file and returns a list of file upload requests.
   *
   * @param inputStream the input stream of the file
   * @param errors the list of file upload errors
   * @return the list of file upload requests
   * @throws Exception if an error occurs during file parsing
   */
  private List<RecommendationImportRequest> parseFile(
      InputStream inputStream, List<FileUploadError> errors) throws Exception {
    return fileParser.parseFile(inputStream, errors);
  }

  /**
   * Validates a list of file upload requests and creates a corresponding list of recommendation
   * requests. This method handles both cases where an agreement group is provided and where only a
   * single agreement ID is present. It processes each request, validates the business logic, and
   * adds any validation errors to the provided error list.
   *
   * @param recommendationDTOs the list of file upload requests to validate and process
   * @param errors the list of file upload errors to which any validation errors will be added
   * @return a list of successfully validated and created {@link CreateRecommendationRequest}
   *     objects
   */
  private List<CreateRecommendationRequest> validateAndCreateRequests(
      List<RecommendationImportRequest> recommendationDTOs, List<FileUploadError> errors) {
    List<CreateRecommendationRequest> createRequests = new ArrayList<>();
    erroneousRecords.clear();

    for (int i = 0; i < recommendationDTOs.size(); i++) {
      RecommendationImportRequest dto = recommendationDTOs.get(i);
      int initialSize = createRequests.size();

      // Obsługa grupy umów
      if (isAgreementGroupPresent(dto)) {
        processAgreementGroup(dto, errors, i + 1, createRequests);
      } else {
        // Obsługa pojedynczego ID umowy
        resolveContractIdFromHumanReadableId(dto, errors, i + 1);
        if (dto.getContractId() == null) {
          erroneousRecords.add(dto);
          continue;
        }
        processSingleAgreement(dto, errors, i + 1, createRequests);
      }
      if (createRequests.size() == initialSize) {
        erroneousRecords.add(dto);
      }
    }
    return createRequests;
  }

  /**
   * Resolves the contract ID (UUID) from the human-readable agreement ID and updates the DTO.
   *
   * @param dto the RecommendationImportRequest DTO to update.
   * @param errors the list of errors to which any validation errors will be added.
   * @param rowIndex the index of the current row being processed, used for error reporting.
   */
  private void resolveContractIdFromHumanReadableId(
      RecommendationImportRequest dto, List<FileUploadError> errors, int rowIndex) {
    if (dto.getHumanReadableAgreementId() != null) {
      try {
        dto.setContractId(
            agreementValidator.findAgreementIdByHumanReadableId(dto.getHumanReadableAgreementId()));
      } catch (IllegalArgumentException e) {
        errors.add(new FileUploadError(rowIndex, e.getMessage()));
      }
    }
  }

  /**
   * Checks if the provided file upload request contains an agreement group.
   *
   * @param dto the file upload request to check
   * @return true if the agreement group field is present and not empty, false otherwise
   */
  private boolean isAgreementGroupPresent(RecommendationImportRequest dto) {
    return dto.getAgreementGroup() != null && !dto.getAgreementGroup().isEmpty();
  }

  /**
   * Processes a file upload request that contains an agreement group. It retrieves all agreements
   * associated with the group, validates the business logic, and adds the corresponding
   * recommendation requests.
   *
   * @param dto the file upload request containing the agreement group
   * @param errors the list of file upload errors to which any validation errors will be added
   * @param rowIndex the index of the current row being processed, used for error reporting
   * @param createRequests the list to which successfully validated recommendation requests will be
   *     added
   */
  private void processAgreementGroup(
      RecommendationImportRequest dto,
      List<FileUploadError> errors,
      int rowIndex,
      List<CreateRecommendationRequest> createRequests) {

    Map<UUID, Long> agreementsMap =
        agreementValidator.findAgreementsByGroup(dto.getAgreementGroup());

    if (agreementsMap.isEmpty()) {
      errors.add(
          new FileUploadError(
              rowIndex,
              String.format(
                  "%s %s", ErrorMessages.AGREEMENT_GROUP_NOT_FOUND, dto.getAgreementGroup())));
      return;
    }

    Map<UUID, List<AgreementData>> agreementsByCustomer =
        mapAgreementsToCustomers(agreementsMap, errors, rowIndex);

    processAgreementsForCustomers(agreementsByCustomer, dto, errors, rowIndex, createRequests);
  }

  /**
   * Maps agreements to their respective customers.
   *
   * <p>Retrieves the associated customer for each agreement. If a customer is found, the agreement
   * is grouped under the customer's ID. If a customer is missing, an error is recorded.
   *
   * @param agreementsMap A map of agreement IDs and their corresponding human-readable IDs.
   * @param errors A list to store validation errors.
   * @param rowIndex The index of the row in the uploaded file.
   * @return A map where the key is the customer ID and the value is a list of agreements linked to
   *     that customer.
   */
  private Map<UUID, List<AgreementData>> mapAgreementsToCustomers(
      Map<UUID, Long> agreementsMap, List<FileUploadError> errors, int rowIndex) {

    Map<UUID, List<AgreementData>> agreementsByCustomer = new HashMap<>();

    agreementsMap.forEach(
        (agreementId, humanReadableId) -> {
          Optional<Customer> customerOpt =
              agreementValidator.findCustomerByAgreementId(agreementId);

          if (customerOpt.isPresent()) {
            UUID customerId = UUID.fromString(customerOpt.get().getId().getId());
            agreementsByCustomer
                .computeIfAbsent(customerId, k -> new ArrayList<>())
                .add(new AgreementData(agreementId, humanReadableId));
          } else {
            errors.add(
                new FileUploadError(
                    rowIndex,
                    String.format(
                        "%s ID umowy: %s", ErrorMessages.CUSTOMER_NOT_FOUND, humanReadableId)));
          }
        });

    return agreementsByCustomer;
  }

  /**
   * Processes agreements for each customer and validates them.
   *
   * <p>Iterates over the agreements grouped by customer, assigns the agreement details to the
   * provided DTO, and validates them. If validation fails, an error is added.
   *
   * @param agreementsByCustomer A map containing customers and their associated agreements.
   * @param dto The recommendation import request being processed.
   * @param errors A list to store validation errors.
   * @param rowIndex The index of the row in the uploaded file.
   * @param createRequests A list to store successfully validated recommendation requests.
   */
  private void processAgreementsForCustomers(
      Map<UUID, List<AgreementData>> agreementsByCustomer,
      RecommendationImportRequest dto,
      List<FileUploadError> errors,
      int rowIndex,
      List<CreateRecommendationRequest> createRequests) {

    agreementsByCustomer
        .values()
        .forEach(
            agreements ->
                agreements.forEach(
                    agreement -> {
                      dto.setContractId(agreement.agreementId());
                      dto.setHumanReadableAgreementId(agreement.humanReadableId());

                      if (!validateAndProcess(dto, errors, rowIndex, createRequests)) {
                        errors.add(
                            new FileUploadError(
                                rowIndex,
                                String.format(
                                    "%s ID umowy: %s",
                                    ErrorMessages.CUSTOMER_NOT_FOUND,
                                    agreement.humanReadableId())));
                      }
                    }));
  }

  private record AgreementData(UUID agreementId, Long humanReadableId) {}

  /**
   * Processes a file upload request that contains a single agreement ID. It validates the business
   * logic and adds the corresponding recommendation request if valid.
   *
   * @param dto the file upload request containing the single agreement ID
   * @param errors the list of file upload errors to which any validation errors will be added
   * @param rowIndex the index of the current row being processed, used for error reporting
   * @param createRequests the list to which successfully validated recommendation requests will be
   *     added
   */
  private void processSingleAgreement(
      RecommendationImportRequest dto,
      List<FileUploadError> errors,
      int rowIndex,
      List<CreateRecommendationRequest> createRequests) {
    if (!validateAndProcess(dto, errors, rowIndex, createRequests)) {
      String humanReadableId =
          dto.getHumanReadableAgreementId() != null
              ? dto.getHumanReadableAgreementId().toString()
              : dto.getContractId().toString();
      errors.add(
          new FileUploadError(
              rowIndex,
              String.format("%s ID umowy: %s", ErrorMessages.CUSTOMER_NOT_FOUND, humanReadableId)));
    }
  }

  /**
   * Validates the business logic for a given file upload request, checks for the presence of a
   * customer associated with the agreement ID, and adds the recommendation request if valid.
   *
   * @param dto the file upload request to validate and process
   * @param errors the list of file upload errors to which any validation errors will be added
   * @param rowIndex the index of the current row being processed, used for error reporting
   * @param createRequests the list to which successfully validated recommendation requests will be
   *     added
   * @return true if the validation and processing were successful, false otherwise
   */
  private boolean validateAndProcess(
      RecommendationImportRequest dto,
      List<FileUploadError> errors,
      int rowIndex,
      List<CreateRecommendationRequest> createRequests) {
    UUID agreementId = dto.getContractId();
    if (recommendationValidator.validateBusinessAndAddErrors(dto, errors, rowIndex)) {
      return false;
    }
    Optional<Customer> customerOpt = agreementValidator.findCustomerByAgreementId(agreementId);
    if (customerOpt.isPresent()) {
      createRequests.add(createRequest(dto));
      return true;
    } else {
      return false;
    }
  }

  /**
   * Creates recommendations from the create recommendation requests.
   *
   * @param createRequests the list of create recommendation requests
   * @param errors the list of file upload errors
   */
  private void createRecommendations(
      List<CreateRecommendationRequest> createRequests, List<FileUploadError> errors) {
    for (CreateRecommendationRequest createRequest : createRequests) {
      try {
        createRecommendationService.create(createRequest);
      } catch (Exception e) {
        errors.add(
            new FileUploadError(0, ErrorMessages.ERROR_CREATING_RECOMMENDATION + e.getMessage()));
      }
    }
  }

  /**
   * Creates a CreateRecommendationRequest from the provided data.
   *
   * @param dto the file upload request
   * @return the create recommendation request
   */
  private @NotNull CreateRecommendationRequest createRequest(RecommendationImportRequest dto) {
    Optional<Contract> contractOpt =
        agreementValidator.findContractByAgreementIdAndContract(
            dto.getContractId(), dto.getContract());

    String mediaType = null;
    if (contractOpt.isPresent()) {
      Contract contract = contractOpt.get();
      mediaType = agreementValidator.getContractType(contract);
    }
    List<UUID> contractIds = List.of(dto.getContractId());
    return new CreateRecommendationRequest(
        contractIds,
        dto.getContract(),
        mediaType,
        dto.getDeadline(),
        dto.getPrice(),
        dto.getVolume(),
        PriceReference.valueOf(dto.getPurchaseMethod()),
        null,
        false,
        false,
        dto.getEmailComment(),
        "",
        RecommendationStatus.NEW,
        dto.getAgreementGroup(),
        dto.getTimeUnit());
  }
}
