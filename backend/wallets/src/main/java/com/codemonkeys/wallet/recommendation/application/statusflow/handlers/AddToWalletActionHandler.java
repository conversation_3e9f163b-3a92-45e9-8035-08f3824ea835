/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.statusflow.handlers;

import com.codemonkeys.wallet.common.ValidationContext;
import com.codemonkeys.wallet.common.ValidationService;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationAction;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.domain.wallets.*;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.create.CreateRecommendationRequest;
import com.codemonkeys.wallet.recommendation.application.statusflow.RecommendationActionHandler;
import jakarta.transaction.Transactional;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler responsible for processing the "ADD_TO_WALLET" action on a recommendation. This handler
 * validates the recommendation status, retrieves the relevant agreement and associated wallets,
 * creates tranches, and updates the recommendation's status.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AddToWalletActionHandler implements RecommendationActionHandler {

  private final WalletRepository walletRepository;
  private final TranchesRepository tranchesRepository;
  private final AgreementRepository agreementRepository;
  private final ContractRepository contractRepository;
  private final ValidationService validationService;
  private final RecalculationService recalculationService;

  /**
   * Returns the specific action that this handler processes.
   *
   * @return {@link RecommendationAction#ADD_TO_WALLET} action.
   */
  @Override
  public RecommendationAction getAction() {
    return RecommendationAction.ADD_TO_WALLET;
  }

  /**
   * Handles the "ADD_TO_WALLET" action on the provided recommendation. This method performs the
   * following steps:
   *
   * <ul>
   *   <li>Validates the recommendation's status.
   *   <li>Parses and validates the price.
   *   <li>Finds the corresponding agreement.
   *   <li>Finds wallets associated with the agreement.
   *   <li>Processes each wallet by adding a tranche.
   *   <li>Updates the recommendation's status to {@link RecommendationStatus#ADDED_TO_WALLET}.
   * </ul>
   *
   * @param recommendation The recommendation to process.
   * @return {@code true} if the action was successfully completed, {@code false} otherwise.
   * @throws IllegalStateException if any validation fails or required data is not found.
   */
  @Override
  @Transactional
  public boolean handle(Recommendation recommendation) {
    validateRecommendationStatus(recommendation);
    BigDecimal price = validateAndParsePrice(recommendation, recommendation.getPrice());
    Agreement agreement = findAgreement(recommendation);
    List<Wallet> wallets = findWalletsForAgreement(agreement);
    wallets.forEach(wallet -> processWallet(wallet, recommendation, price));
    recommendation.changeStatus(RecommendationStatus.ADDED_TO_WALLET);
    return true;
  }

  /**
   * Validates and parses the price provided in the recommendation.
   *
   * @param recommendation The recommendation containing the price.
   * @param price The price as a string.
   * @return The parsed {@link BigDecimal} price.
   * @throws IllegalStateException if the price is invalid.
   */
  private BigDecimal validateAndParsePrice(Recommendation recommendation, String price) {
    try {
      return new BigDecimal(price);
    } catch (NumberFormatException e) {
      handleException(recommendation, "error.recommendation.invalid.price.format", e);
      throw e;
    }
  }

  /**
   * Validates that the recommendation is in the correct status to be added to the wallet.
   *
   * @param recommendation The recommendation to validate.
   * @throws IllegalStateException if the recommendation status is not {@link
   *     RecommendationStatus#COMPLETED}.
   */
  private void validateRecommendationStatus(Recommendation recommendation) {
    if (recommendation.getStatus() != RecommendationStatus.COMPLETED) {
      throw new IllegalStateException(
          I18n.translate(
              "error.recommendation.invalid.status.add.to.wallet", recommendation.getStatus()));
    }
  }

  /**
   * Finds the agreement associated with the recommendation.
   *
   * @param recommendation The recommendation containing the contract ID.
   * @return The found {@link Agreement}.
   * @throws IllegalStateException if the agreement is not found.
   */
  private Agreement findAgreement(Recommendation recommendation) {
    return agreementRepository
        .findById(AgreementId.of(recommendation.getContractId()))
        .orElseThrow(
            () ->
                new IllegalStateException(
                    I18n.translate("error.agreement.not.found", recommendation.getContractId())));
  }

  /**
   * Finds wallets associated with the given agreement.
   *
   * @param agreement The agreement for which wallets should be found.
   * @return A list of {@link Wallet} associated with the agreement.
   * @throws IllegalStateException if no wallets are found.
   */
  private List<Wallet> findWalletsForAgreement(Agreement agreement) {
    List<Wallet> wallets = walletRepository.findByAgreement_Id(agreement.getId());
    if (wallets.isEmpty()) {
      throw new IllegalStateException(
          I18n.translate("error.wallet.not.found.for.agreement", agreement.getId()));
    }
    return wallets;
  }

  /**
   * Finds the matching contract for the given wallet and recommendation based on the agreement ID
   * and the contract name in the recommendation.
   *
   * @param wallet The wallet associated with the agreement.
   * @param recommendation The recommendation containing the contract name.
   * @return The matching {@link Contract}.
   * @throws IllegalStateException if no matching contract is found.
   */
  private Contract findMatchingContract(Wallet wallet, Recommendation recommendation) {
    List<Contract> contracts = contractRepository.findByAgreementId(wallet.getAgreement().getId());

    return contracts.stream()
        .filter(contract -> contract.getName().getValue().equals(recommendation.getContract()))
        .findFirst()
        .orElseThrow(
            () ->
                new IllegalStateException(
                    I18n.translate(
                        "error.contract.name.does.not.match.contract",
                        new Object[] {recommendation.getContract()})));
  }

  /**
   * Processes a wallet by adding a tranche with the details provided in the recommendation.
   *
   * @param wallet The wallet to which the tranche will be added.
   * @param recommendation The recommendation containing tranche details.
   * @param price The price to be associated with the tranche.
   */
  private void processWallet(Wallet wallet, Recommendation recommendation, BigDecimal price) {
    TimeUnit timeUnit = TimeUnit.valueOf(recommendation.getTimeUnit().toUpperCase());
    Contract matchingContract = findMatchingContract(wallet, recommendation);
    CreateRecommendationRequest createRequest = createRecommendationRequestFrom(recommendation);
    validationService.validate(ValidationContext.ADD_TO_WALLET_ACTION, createRequest);
    Tranche tranche =
        new Tranche(
            TrancheId.of(UUID.randomUUID()),
            wallet,
            recommendation.getDeadline().toLocalDate(),
            timeUnit,
            new BigDecimal(recommendation.getVolume()),
            price,
            recommendation.getPurchaseMethod(),
            matchingContract,
            false);

    wallet.addTranche(tranche);
    tranchesRepository.save(tranche);
    walletRepository.save(wallet);
    recalculationService.recalculate(wallet);
    log.info("Added tranche {} to wallet {}", tranche.getId(), wallet.getId());
  }

  /**
   * Creates a new {@link CreateRecommendationRequest} based on the provided {@link Recommendation}.
   *
   * @param recommendation the {@link Recommendation} entity from which to create the request
   * @return a {@link CreateRecommendationRequest} containing data from the given recommendation
   */
  private CreateRecommendationRequest createRecommendationRequestFrom(
      Recommendation recommendation) {
    return new CreateRecommendationRequest(
        List.of(recommendation.getContractId()),
        recommendation.getContract(),
        recommendation.getCarrier(),
        recommendation.getDeadline().toLocalDate(),
        recommendation.getPrice(),
        recommendation.getVolume(),
        recommendation.getPurchaseMethod(),
        recommendation.getSupplierName(),
        recommendation.isRequiresCustomerAcceptance(),
        recommendation.isSendRecommendation(),
        recommendation.getEmailTemplateComment(),
        recommendation.getExecutor(),
        recommendation.getStatus(),
        recommendation.getAgreementGroup(),
        recommendation.getTimeUnit());
  }

  /**
   * Handles exceptions that occur during the processing of a recommendation. Sets the
   * recommendation status to {@link RecommendationStatus#ERROR} and logs the exception.
   *
   * @param recommendation The recommendation being processed.
   * @param errorMessageKey The key for the error message.
   * @param e The exception that was thrown.
   * @throws IllegalStateException wrapping the original exception.
   */
  private void handleException(Recommendation recommendation, String errorMessageKey, Exception e) {
    recommendation.changeStatus(RecommendationStatus.ERROR);
    log.error("Error processing recommendation {}: {}", recommendation.getId(), e.getMessage(), e);
    throw new IllegalStateException(
        String.format("%s: %s", I18n.translate(errorMessageKey), e.getMessage()), e);
  }
}
