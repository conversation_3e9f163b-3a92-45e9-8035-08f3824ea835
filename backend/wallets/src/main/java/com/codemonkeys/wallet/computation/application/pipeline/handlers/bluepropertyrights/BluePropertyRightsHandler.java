/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.bluepropertyrights;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.DefaultElementPriceHandler;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BluePropertyRightsHandler
    extends DefaultElementPriceHandler<BluePropertyRightsCommand> {

  @Override
  public Wallet handle(BluePropertyRightsCommand command) {
    return defaultWork(command.getWallet(), getAssociatedElements());
  }

  @Override
  protected List<ElementType> getAssociatedElements() {
    return List.of(ElementType.BLUE_PROPERTY_RIGHTS);
  }
}
