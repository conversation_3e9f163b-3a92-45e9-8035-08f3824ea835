/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.simulation.fullpurchase;

import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationHelper;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationRequest;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** Manages the extraction and restoration of property rights tranches from a simulation request. */
@Slf4j
@Service
@RequiredArgsConstructor
public class PropertyRightsTrancheManager {

  private final RecommendationHelper recommendationHelper;

  /**
   * Extracts and removes property rights tranches from the simulation request.
   *
   * @param request the simulation request.
   * @return a list of extracted property rights tranches.
   */
  public List<WalletSimulationRequest.CreateTrancheRequest> extractPropertyRightsTranches(
      WalletSimulationRequest request) {
    Agreement agreement;
    try {
      agreement = recommendationHelper.findAgreementOrThrow(request.getAgreement());
    } catch (Exception e) {
      log.warn(
          "No agreement found for ID {}. Skipping property rights extraction.",
          request.getAgreement());
      return List.of();
    }

    if (agreement.getContracts() == null || agreement.getContracts().isEmpty()) {
      log.info("Agreement contains no contracts. Skipping property rights extraction.");
      return List.of();
    }
    Set<UUID> prContractIds =
        agreement.getContracts().stream()
            .filter(Contract::isPropertyRightsContract)
            .map(c -> UUID.fromString(c.getId().getId()))
            .collect(Collectors.toSet());
    List<WalletSimulationRequest.CreateTrancheRequest> prTranches =
        request.getTranches().stream()
            .filter(tranche -> prContractIds.contains(tranche.getContract()))
            .collect(Collectors.toList());
    request.getTranches().removeIf(tranche -> prContractIds.contains(tranche.getContract()));
    log.info("Extracted {} property rights tranche(s) from the request.", prTranches.size());
    return prTranches;
  }

  /**
   * Restores property rights tranches to the simulation request.
   *
   * @param request the simulation request.
   * @param prTranches the list of property rights tranches to restore.
   */
  public void restorePropertyRightsTranches(
      WalletSimulationRequest request,
      List<WalletSimulationRequest.CreateTrancheRequest> prTranches) {
    if (!prTranches.isEmpty()) {
      request.getTranches().addAll(prTranches);
      log.info("Restored {} property rights tranche(s) to the request.", prTranches.size());
    }
  }
}
