/* (C)2024-2025 */
package com.codemonkeys.wallet.common;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.CalculationStrategyValidatorFactory;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.GreenPropertyRightsValidationStrategy;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.RecommendationRepository;
import com.codemonkeys.wallet.domain.wallets.TranchesRepository;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.wallet.application.common.WalletMapper;
import com.codemonkeys.wallet.wallet.application.create.CreateWalletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Helper class that provides common validation methods for agreements and contracts. The {@code
 * ValidationHelper} class includes utility methods for finding and validating agreements and
 * contracts, ensuring that requested entities exist and meet specific criteria.
 */
@Component
@RequiredArgsConstructor
public class Validation {

  private final AgreementRepository agreementRepository;
  private final RecommendationRepository recommendationRepository;
  private final TranchesRepository trancheRepository;
  private final ContractRepository contractRepository;
  private final WalletRepository walletRepository;
  private final WalletMapper walletMapper;
  private final CalculationStrategyValidatorFactory factory;
  private final ElementValidationService elementValidationService;

  /**
   * Finds an {@link Agreement} by its contract ID or throws a {@link ValidationFailedException} if
   * the agreement is not found.
   *
   * @param contractId the UUID of the contract.
   * @return the found {@link Agreement}.
   * @throws ValidationFailedException if no agreement is found for the given contract ID.
   */
  public Agreement findAgreementOrThrow(UUID contractId) {
    return agreementRepository
        .findById(AgreementId.of(contractId))
        .orElseThrow(
            () -> ValidationFailedException.of("contractId", "validations.contract.notFound"));
  }

  /**
   * Finds a {@link Contract} by its contract name within a given {@link Agreement}, or throws a
   * {@link ValidationFailedException} if no matching contract is found.
   *
   * @param agreement the agreement that contains the contracts.
   * @param contractName the name of the contract to find.
   * @return the found {@link Contract}.
   * @throws ValidationFailedException if no contract matches the given contract name within the
   *     agreement.
   */
  public Contract findContractOrThrow(Agreement agreement, String contractName) {
    return agreement.getContracts().stream()
        .filter(c -> c.getName().getValue().equalsIgnoreCase(contractName))
        .findFirst()
        .orElseThrow(
            () -> ValidationFailedException.of("contract", "validations.contract.notFound"));
  }

  /**
   * Validates that a specified purchase method matches one of the contract's {@link
   * PriceReference}s. Throws a {@link ValidationFailedException} if the purchase method is invalid
   * or not associated with the contract's allowed price references.
   *
   * @param contract the contract whose price reference is being validated.
   * @param purchaseMethod the purchase method to validate.
   * @throws ValidationFailedException if the purchase method is invalid or not allowed for the
   *     contract.
   */
  public void validatePriceReferenceOrThrow(Contract contract, String purchaseMethod) {
    try {
      PriceReference reference = PriceReference.valueOf(purchaseMethod.trim());
      if (!contract.getPriceReference().contains(reference)) {
        throw ValidationFailedException.of("purchaseMethod", "validations.purchaseMethod.invalid");
      }
    } catch (IllegalArgumentException e) {
      throw ValidationFailedException.of("purchaseMethod", "validations.purchaseMethod.invalid");
    }
  }

  /**
   * Validates if the provided deadline is within the contract's purchase date range.
   *
   * @param contract the contract associated with the recommendation.
   * @param deadline the deadline to validate.
   * @throws ValidationFailedException if the deadline is outside the allowed range.
   */
  public void validateDeadlineWithinContractDates(Contract contract, LocalDateTime deadline) {
    LocalDateTime contractStart = contract.getAverageCalculation().getStartDate().atStartOfDay();
    LocalDateTime contractEnd = contract.getAverageCalculation().getEndDate().atTime(23, 59);

    if (deadline.isBefore(contractStart) || deadline.isAfter(contractEnd)) {
      throw ValidationFailedException.of("deadline", "validations.contract.deadlineOutOfRange");
    }
  }

  /**
   * Validates that the total volume of tranches in portfolios associated with a specific contract
   * within an agreement does not exceed 100%. If the calculated volume exceeds this threshold, a
   * {@link ValidationFailedException} is thrown.
   *
   * @param agreementId the identifier of the agreement to which the contract belongs
   * @param contractName the name of the contract whose tranche volumes are being validated
   * @throws ValidationFailedException if the total tranche volume for the specified contract
   *     exceeds 100%
   */
  public void validateTotalVolumeInPortfolios(
      AgreementId agreementId, String contractName, BigDecimal additionalVolume) {
    List<ContractId> contractIds =
        contractRepository.findIdsByAgreementIdAndName(agreementId, contractName);
    BigDecimal currentTrancheVolume = trancheRepository.sumSizeByContractIds(contractIds);
    BigDecimal totalVolume =
        (currentTrancheVolume != null ? currentTrancheVolume : BigDecimal.ZERO)
            .add(additionalVolume);
    if (totalVolume.compareTo(BigDecimal.valueOf(100)) > 0) {
      throw ValidationFailedException.of(
          "volume", "validations.contract.portfolioVolumeExceedsLimit");
    }
  }

  /**
   * Validates that the addition of a new recommendation volume, combined with existing
   * recommendation and tranche volumes for a specific contract within an agreement, does not exceed
   * 100%. If the combined volume surpasses this threshold, a {@link ValidationFailedException} is
   * thrown.
   *
   * @param agreementId the identifier of the agreement to which the contract belongs
   * @param additionalVolume the volume of the new recommendation to be added
   * @param contractName the name of the contract for which the recommendation volume is being
   *     validated
   * @throws ValidationFailedException if the total volume (existing recommendations and tranches
   *     plus the new recommendation) exceeds 100%
   */
  public void validateNewRecommendationVolumeWithinLimit(
      AgreementId agreementId, BigDecimal additionalVolume, String contractName) {
    List<ContractId> contractIds =
        contractRepository.findIdsByAgreementIdAndName(agreementId, contractName);

    BigDecimal currentRecommendationVolume =
        recommendationRepository
            .findByAgreementIdAndContractName(UUID.fromString(agreementId.getId()), contractName)
            .stream()
            .map(this::convertVolumeToBigDecimal)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal currentTrancheVolume = trancheRepository.sumSizeByContractIds(contractIds);

    BigDecimal totalVolume =
        currentRecommendationVolume
            .add(currentTrancheVolume != null ? currentTrancheVolume : BigDecimal.ZERO)
            .add(additionalVolume);

    if (totalVolume.compareTo(BigDecimal.valueOf(100)) > 0) {
      throw ValidationFailedException.of(
          "volume", "validations.contract.recommendationVolumeExceedsLimit");
    }
  }

  /**
   * Converts the volume of a given {@link Recommendation} from a string format to a {@link
   * BigDecimal}. If the volume format is invalid and cannot be converted, a {@link
   * ValidationFailedException} is thrown.
   *
   * @param recommendation the {@link Recommendation} containing the volume to be converted
   * @return the volume as a {@link BigDecimal} if successfully parsed
   * @throws ValidationFailedException if the volume format is invalid or cannot be parsed as a
   *     {@link BigDecimal}
   */
  private BigDecimal convertVolumeToBigDecimal(Recommendation recommendation) {
    try {
      return new BigDecimal(recommendation.getVolume());
    } catch (NumberFormatException e) {
      throw ValidationFailedException.of("volume", "validations.contract.invalidVolumeFormat");
    }
  }

  /**
   * Validates that the total volume of tranches in portfolios associated with a specific wallet
   * does not exceed 100%. If the calculated volume exceeds this threshold, a {@link
   * ValidationFailedException} is thrown.
   *
   * @param walletId the identifier of the wallet to which the contract belongs
   * @param contractName the name of the contract whose tranche volumes are being validated
   * @param additionalVolume additional volume to be considered in the validation
   * @throws ValidationFailedException if the total tranche volume for the specified contract
   *     exceeds 100%
   */
  public void validateTotalVolumeInPortfolios(
      WalletId walletId, String contractName, BigDecimal additionalVolume) {
    List<ContractId> contractIds =
        contractRepository.findIdsByWalletIdAndName(walletId, contractName);
    BigDecimal currentTrancheVolume = trancheRepository.sumSizeByContractIds(contractIds);
    BigDecimal totalVolume =
        (currentTrancheVolume != null ? currentTrancheVolume : BigDecimal.ZERO)
            .add(additionalVolume);

    if (totalVolume.compareTo(BigDecimal.valueOf(100)) > 0) {
      throw ValidationFailedException.of(
          "volume", "validations.contract.portfolioVolumeExceedsLimit");
    }
  }

  /**
   * Validates that the new recommendation volume, when added to existing volumes, does not exceed
   * 100 for the specified wallet and contract.
   *
   * @param walletId The ID of the wallet.
   * @param additionalVolume The volume to be added for the new recommendation.
   * @param contractName The name of the related contract.
   * @throws IllegalStateException if the wallet is not found.
   * @throws ValidationFailedException if the total volume exceeds the limit.
   */
  public void validateNewRecommendationVolumeWithinLimit(
      WalletId walletId, BigDecimal additionalVolume, String contractName) {
    Wallet wallet =
        walletRepository
            .findById(walletId)
            .orElseThrow(() -> new IllegalStateException("Wallet not found for ID: " + walletId));

    Agreement agreement = wallet.getAgreement();

    List<Recommendation> recommendations =
        recommendationRepository.findByAgreementIdAndContractName(
            UUID.fromString(agreement.getId().getId()), contractName);

    BigDecimal currentRecommendationVolume =
        recommendations.stream()
            .map(this::convertVolumeToBigDecimal)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    List<ContractId> contractIds =
        contractRepository.findIdsByWalletIdAndName(walletId, contractName);
    BigDecimal currentTrancheVolume = trancheRepository.sumSizeByContractIds(contractIds);

    BigDecimal totalVolume =
        currentRecommendationVolume
            .add(currentTrancheVolume != null ? currentTrancheVolume : BigDecimal.ZERO)
            .add(additionalVolume);

    if (totalVolume.compareTo(BigDecimal.valueOf(100)) > 0) {
      throw ValidationFailedException.of(
          "volume", "validations.contract.recommendationVolumeExceedsLimit");
    }
  }

  /**
   * Validates that all necessary elements exist in the wallet request based on the specified green
   * property rights calculation type. The validation is performed by retrieving and applying the
   * appropriate validation strategy for the calculation type.
   *
   * @param request The request object containing wallet creation details to be validated
   * @throws ValidationFailedException if the required elements for the specified calculation type
   *     are missing or invalid
   * @throws IllegalArgumentException if an unsupported green property rights calculation type is
   *     provided
   */
  public void validateNecessaryElementsExistenceForGreenPropertyRightsCalculationType(
      CreateWalletRequest request) {
    Wallet wallet = walletMapper.toDomain(request);
    if (wallet.getGreenPropertyRightCalculationType() != null) {
      GreenPropertyRightsValidationStrategy strategy =
          factory.getStrategy(wallet.getGreenPropertyRightCalculationType());
      if (strategy != null) {
        strategy.validateOrThrow(wallet);
      }
    }
  }

  /**
   * Validates that all necessary elements exist in the wallet request for energy media type. The
   * validation is performed by applying the appropriate validation service for energy wallets.
   *
   * @param request The request object containing wallet creation details to be validated
   * @throws ValidationFailedException if the required elements for energy media type are missing or
   *     invalid
   */
  public void validateNecessaryElementsExistenceForEnergy(CreateWalletRequest request) {
    Wallet wallet = walletMapper.toDomain(request);
    if (wallet.getMediaType().equals(MediaType.ENERGY)) {
      elementValidationService.validateOrThrow(wallet);
    }
  }
}
