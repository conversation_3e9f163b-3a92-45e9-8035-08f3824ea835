/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.enea.EneaCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa.EnergaComplexCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa.EnergaSimpleCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.eon.EONCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.noop.NoOpCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.pge.PGECalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.tiew.TIEWCalculationStrategy;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.wallets.enums.GreenPropertyRightCalculationType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Factory class responsible for creating and providing appropriate calculation strategies for
 * different types of Green Property Rights calculations. This factory manages various strategy
 * implementations and returns the correct one based on the specified calculation type.
 */
@Service
public class CalculationStrategyFactory {
  private final EneaCalculationStrategy eneaCalculationStrategy;
  private final EONCalculationStrategy eonCalculationStrategy;
  private final EnergaComplexCalculationStrategy energaComplexCalculationStrategy;
  private final EnergaSimpleCalculationStrategy energaSimpleCalculationStrategy;
  private final PGECalculationStrategy pgeCalculationStrategy;
  private final TIEWCalculationStrategy tiewCalculationStrategy;
  private final NoOpCalculationStrategy noOpCalculationStrategy;

  /**
   * Constructs a new CalculationStrategyFactory with all the required calculation strategies.
   *
   * @param priceRepository Repository for accessing price data, required by some strategies
   */
  @Autowired
  public CalculationStrategyFactory(PriceRepository priceRepository) {
    this.eneaCalculationStrategy = new EneaCalculationStrategy();
    this.eonCalculationStrategy = new EONCalculationStrategy();
    this.energaComplexCalculationStrategy = new EnergaComplexCalculationStrategy(priceRepository);
    this.energaSimpleCalculationStrategy = new EnergaSimpleCalculationStrategy();
    this.pgeCalculationStrategy = new PGECalculationStrategy();
    this.tiewCalculationStrategy = new TIEWCalculationStrategy();
    this.noOpCalculationStrategy = new NoOpCalculationStrategy();
  }

  /**
   * Returns the appropriate calculation strategy based on the specified Green Property Right
   * calculation type.
   *
   * @param type The type of Green Property Right calculation to be performed
   * @return The corresponding calculation strategy implementation
   */
  public GreenPropertyRightsCalculationStrategy getStrategy(
      GreenPropertyRightCalculationType type) {
    return switch (type) {
      case ENEA -> eneaCalculationStrategy;
      case EON -> eonCalculationStrategy;
      case ENERGA_COMPLEX -> energaComplexCalculationStrategy;
      case ENERGA_SIMPLE -> energaSimpleCalculationStrategy;
      case PGE -> pgeCalculationStrategy;
      case TIEW -> tiewCalculationStrategy;
      case OTHER -> noOpCalculationStrategy;
    };
  }
}
