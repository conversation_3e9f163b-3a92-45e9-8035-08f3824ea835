/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.annualpurchaseoverview;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.AggregateRootListService;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListService;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * Service class for generating an annual purchase overview of wallets based on provided criteria.
 *
 * <p>This service extends {@link AggregateRootListService} and implements {@link
 * ProjectionListService} to support operations for retrieving wallet aggregates with applied
 * filtering based on annual purchase criteria.
 *
 * <p>The filtering is performed based on the following conditions:
 *
 * <ul>
 *   <li>If the {@code media} field of the criteria is {@link Media#GREEN_PROPERTY_RIGHTS}, then:
 *       <ul>
 *         <li>The wallet's media type is filtered to {@link MediaType#ENERGY}.
 *         <li>A JSONB property (extracted from {@code agreement.propertyRights} with key
 *             "purchaseModel") must equal "TRANCHE".
 *       </ul>
 *   <li>If the {@code media} field of the criteria is {@link Media#ENERGY}, then the wallet's media
 *       type is filtered to {@link MediaType#ENERGY}.
 *   <li>If the {@code media} field of the criteria is {@link Media#GAS}, then the wallet's media
 *       type is filtered to {@link MediaType#GAS}.
 *   <li>If a {@code year} is provided in the criteria, then wallets are filtered to match that
 *       year.
 * </ul>
 *
 * @see AggregateRootListService
 * @see ProjectionListService
 */
@Service
public class AnnualPurchaseOverviewService
    extends AggregateRootListService<
        AnnualPurchaseOverviewRequest,
        Wallet,
        Wallet,
        WalletId,
        WalletRepository,
        AnnualPurchaseOverviewMapper>
    implements ProjectionListService<AnnualPurchaseOverviewRequest, Wallet> {

  /**
   * Constructs a new {@code AnnualPurchaseOverviewService} with the specified repository and
   * mapper.
   *
   * @param repository the repository used for accessing {@link Wallet} data.
   * @param mapper the mapper used for transforming data between entities and DTOs.
   */
  @Autowired
  public AnnualPurchaseOverviewService(
      WalletRepository repository, AnnualPurchaseOverviewMapper mapper) {
    super(repository, mapper);
  }

  /**
   * Prepares a JPA {@link Specification} for {@link Wallet} based on the provided {@link
   * AnnualPurchaseOverviewRequest} criteria.
   *
   * <p>The specification applies the following filters:
   *
   * <ul>
   *   <li>If the {@code media} field in the criteria is {@link Media#GREEN_PROPERTY_RIGHTS}, then:
   *       <ul>
   *         <li>The wallet's media type must be {@link MediaType#ENERGY}.
   *         <li>The result of the JSONB extraction from {@code agreement.propertyRights} (using the
   *             key "purchaseModel") must equal "TRANCHE".
   *       </ul>
   *   <li>If the {@code media} field in the criteria is {@link Media#ENERGY}, then the wallet's
   *       media type is filtered to {@link MediaType#ENERGY}.
   *   <li>If the {@code media} field in the criteria is {@link Media#GAS}, then the wallet's media
   *       type is filtered to {@link MediaType#GAS}.
   *   <li>If a {@code year} is provided in the criteria, then the wallet's year (accessed via
   *       {@code year.year}) is matched against the provided year.
   * </ul>
   *
   * @param criteria the criteria containing the filtering parameters.
   * @return a {@link Specification} for querying {@link Wallet} entities based on the specified
   *     criteria.
   */
  @Override
  protected Specification<Wallet> prepareSpecification(AnnualPurchaseOverviewRequest criteria) {
    return (root, query, criteriaBuilder) -> {
      List<Predicate> predicates = new ArrayList<>();
      if (criteria.getMedia() != null && criteria.getMedia().equals(Media.GREEN_PROPERTY_RIGHTS)) {
        predicates.add(criteriaBuilder.equal(root.get("mediaType"), MediaType.ENERGY));
        predicates.add(
            criteriaBuilder.equal(
                criteriaBuilder.function(
                    "jsonb_extract_path_text",
                    String.class,
                    root.get("agreement").get("propertyRights"),
                    criteriaBuilder.literal("purchaseModel")),
                "TRANCHE"));
      }

      if (criteria.getMedia() != null && criteria.getMedia().equals(Media.ENERGY)) {
        predicates.add(criteriaBuilder.equal(root.get("mediaType"), MediaType.ENERGY));
      }

      if (criteria.getMedia() != null && criteria.getMedia().equals(Media.GAS)) {
        predicates.add(criteriaBuilder.equal(root.get("mediaType"), MediaType.GAS));
      }
      if (criteria.getYear() != null) {
        predicates.add(criteriaBuilder.equal(root.get("year").get("year"), criteria.getYear()));
      }
      if (criteria.getWallet() != null) {
        predicates.add(
            criteriaBuilder.like(
                criteriaBuilder.lower(
                    root.get("agreement").get("customer").get("name").get("name")),
                "%" + criteria.getWallet().toLowerCase() + "%"));
      }
      if (criteria.getSupplier() != null) {
        predicates.add(
            criteriaBuilder.like(
                criteriaBuilder.lower(
                    root.get("agreement").get("supplier").get("name").get("name")),
                "%" + criteria.getSupplier().toLowerCase() + "%"));
      }

      return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
    };
  }
}
