/* (C)2024-2025 */
package com.codemonkeys.wallet.product.application.pipeline.handlers.marketmean;

import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.wallets.Product;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletPrice;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.ProductType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.ProductId;
import com.codemonkeys.wallet.product.application.pipeline.MathUtil;
import com.codemonkeys.wallet.product.application.pipeline.handlers.ProductHandler;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GasMarketMeanHandler extends ProductHandler<GasMarketMeanCommand> {
  @Autowired PriceRepository priceRepository;

  public GasMarketMeanHandler(PriceRepository priceRepository, PriceRepository priceRepository1) {
    super(priceRepository);
    this.priceRepository = priceRepository1;
  }

  @Override
  public Wallet handle(GasMarketMeanCommand command) {
    Wallet wallet = command.getWallet();
    List<TimeUnit> months = TimeUnit.Y.getMonths();
    Map<TimeUnit, List<Tranche>> tranches = command.getWallet().monthlyTranches();
    List<Product> result = Lists.newArrayList();
    for (TimeUnit month : months) {
      List<Tranche> monthTranches =
          Stream.ofNullable(tranches.get(month))
              .flatMap(List::stream)
              .filter(t -> Objects.equals(t.getContract().getClass(), GasContract.class))
              .toList();
      if (!monthTranches.isEmpty()) {
        BigDecimal size = MathUtil.sum(monthTranches, Tranche::getSize);
        Optional<Contract> mContract =
            replaceSpotContract(
                getMonthContract(monthTranches), wallet.getAgreement(), month, wallet.getYear());
        List<Tranche> mTranches =
            getTranches(monthTranches, t -> t.getContract().isMonthOrSpotContract());
        BigDecimal mSize = MathUtil.sum(mTranches, Tranche::getSize);
        BigDecimal mPercent = mSize.divide(size, MathContext.DECIMAL64);
        Optional<Contract> qContract = getQuarterContract(monthTranches);
        List<Tranche> qTranches =
            getTranches(monthTranches, t -> t.getContract().isQuarterContract());
        BigDecimal qSize = MathUtil.sum(qTranches, Tranche::getSize);
        BigDecimal qPercent = qSize.divide(size, MathContext.DECIMAL64);
        Optional<Contract> yContract = getYearContract(monthTranches);
        List<Tranche> yTranches = getTranches(monthTranches, t -> t.getContract().isYearContract());
        BigDecimal ySize = MathUtil.sum(yTranches, Tranche::getSize);
        BigDecimal yPercent = ySize.divide(size, MathContext.DECIMAL64);
        BigDecimal monthMean = mean(monthTranches, mContract);
        BigDecimal monthValue = mPercent.multiply(monthMean);
        BigDecimal quarterMean = mean(monthTranches, qContract);
        BigDecimal quarterValue = qPercent.multiply(quarterMean);
        BigDecimal yearMean = mean(monthTranches, yContract);
        BigDecimal yearValue = yPercent.multiply(yearMean);
        BigDecimal mean = monthValue.add(quarterValue).add(yearValue);
        Media media = getMedia(monthTranches);
        if (media != null) {
          result.add(
              new Product(
                  ProductId.randomId(),
                  command.getWallet(),
                  ProductType.MARKET_MEAN,
                  month,
                  mean,
                  media));
          WalletPrice wp = wallet.getPrice(month, ElementType.NET);
          if (wp != null) {
            result.add(
                new Product(
                    ProductId.randomId(),
                    command.getWallet(),
                    ProductType.CLIENT_MEAN,
                    month,
                    wp.getValue(),
                    media));
            BigDecimal benchmark = mean.subtract(wp.getValue());
            result.add(
                new Product(
                    ProductId.randomId(),
                    command.getWallet(),
                    ProductType.BENCHMARK_RESULT,
                    month,
                    benchmark,
                    media));
          }
        }
      }
    }
    wallet.addProducts(result);
    return wallet;
  }
}
