/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.enea;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.CalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.GreenPropertyRightsCalculationStrategy;
import com.codemonkeys.wallet.domain.wallets.PriceMetadata;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public class EneaCalculationStrategy extends CalculationStrategy
    implements GreenPropertyRightsCalculationStrategy {

  @Override
  public PriceMetadata getMetadata() {
    return metadata;
  }

  @Override
  public Optional<BigDecimal> price(List<Tranche> tranches) {
    if (tranches.isEmpty()) {
      return Optional.empty();
    }

    Wallet wallet = tranches.getFirst().getWallet();
    handleDutyMetadata(wallet.getYear().getYearAsInt());
    BigDecimal basePrice = basePrice(tranches, Tranche::product);
    BigDecimal priceWithExcise = applyExcise(basePrice, wallet);
    BigDecimal priceWithCosts = applyCosts(priceWithExcise, wallet);
    BigDecimal priceWithDuty = applyDuty(priceWithCosts, wallet.getYear().getYearAsInt());
    return Optional.of(applyIndustrialStatusDiscount(priceWithDuty, wallet));
  }
}
