/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.file.validators;

import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.file.utils.ErrorMessages;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.stereotype.Component;

/** Validator for file data. */
@Component
public class FileValidator {

  /**
   * Validates the Agreement Group ID by checking if the cell is either null or contains a valid
   * string. Allows the cell to be empty.
   *
   * @param cell the cell containing the Agreement Group ID
   * @return the validated Agreement Group ID as a String, or null if the cell is empty
   * @throws Exception if the cell is not a valid string
   */
  public String validateAgreementGroupId(Cell cell) throws Exception {
    if (cell == null || cell.getCellType() == CellType.BLANK) {
      return null;
    }
    if (cell.getCellType() != CellType.STRING) {
      throw new Exception(ErrorMessages.INVALID_AGREEMENT_GROUP_ID);
    }
    String cellValue = cell.getStringCellValue().trim();
    if (cellValue.isEmpty()) {
      return null;
    }
    return cellValue;
  }

  /**
   * Validates the Human Readable Agreement ID. Ensures the value is non-null, contains only digits,
   * and converts it to a Long.
   *
   * @param value the extracted value from the cell (String or Numeric)
   * @return the validated Agreement ID as a Long
   * @throws Exception if the value is null, empty, or not a valid numeric format
   */
  public Long validateHumanReadableAgreementId(Object value) throws Exception {
    if (value == null || value.toString().trim().isEmpty()) {
      throw new Exception(ErrorMessages.INVALID_AGREEMENT_ID);
    }

    if (value instanceof Double) {
      double numValue = (Double) value;
      if (numValue % 1 == 0) {
        return (long) numValue;
      }
      throw new Exception(ErrorMessages.INVALID_AGREEMENT_ID_FORMAT);
    }

    if (value instanceof Long) {
      return (Long) value;
    }

    String cellValue = value.toString().trim();

    if (!cellValue.matches("\\d+")) {
      throw new Exception(ErrorMessages.INVALID_AGREEMENT_ID_FORMAT);
    }

    return Long.parseLong(cellValue);
  }

  /**
   * Validates the deadline by checking if the cell contains a valid date in the format YYYY-MM-DD.
   *
   * @param cell the cell containing the deadline
   * @return the validated deadline as a LocalDate
   * @throws Exception if the validation fails
   */
  public LocalDate validateDeadline(Cell cell) throws Exception {
    if (cell == null || cell.getCellType() == CellType.BLANK) {
      throw new Exception(STR."\{ErrorMessages.INVALID_DEADLINE} - Komórka jest pusta.");
    }

    try {
      if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
        return cell.getLocalDateTimeCellValue().toLocalDate();
      } else if (cell.getCellType() == CellType.STRING) {
        String cellValue = cell.getStringCellValue().trim();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(cellValue, formatter);
      } else {
        throw new Exception(STR."\{ErrorMessages.INVALID_DEADLINE} - Typ komórki nieobsługiwany.");
      }
    } catch (DateTimeParseException e) {
      throw new Exception(
          STR."\{
              ErrorMessages
                  .INVALID_DEADLINE} - Nieprawidłowy format daty. Oczekiwany format: YYYY-MM-DD.");
    }
  }

  /**
   * Validates the contract by checking if the cell is not null and is of type STRING.
   *
   * @param cell the cell containing the contract
   * @return the validated contract as a String
   * @throws Exception if the validation fails
   */
  public String validateContract(Cell cell) throws Exception {
    if (cell == null || cell.getCellType() != CellType.STRING) {
      throw new Exception(ErrorMessages.INVALID_CONTRACT);
    }
    return cell.getStringCellValue().trim();
  }

  /**
   * Validates the time unit by checking if the cell is not null and contains a valid TimeUnit.
   *
   * @param cell the cell containing the time unit
   * @return the validated time unit as a String (the name of the TimeUnit enum)
   * @throws Exception if the validation fails
   */
  public String validateTimeUnit(Cell cell) throws Exception {
    if (cell == null || cell.getCellType() == CellType.BLANK) {
      return "";
    }

    String cellValue;
    if (cell.getCellType() == CellType.STRING) {
      cellValue = cell.getStringCellValue().trim().toUpperCase();
    } else if (cell.getCellType() == CellType.NUMERIC) {
      cellValue = String.valueOf((int) cell.getNumericCellValue());
    } else {
      throw new Exception(STR."\{ErrorMessages.INVALID_TIME_UNIT} - Nieprawidłowy typ danych.");
    }

    if (cellValue.isEmpty()) {
      return "";
    }

    try {
      return TimeUnit.valueOf(cellValue).name();
    } catch (IllegalArgumentException e) {
      throw new Exception(STR."\{ErrorMessages.INVALID_TIME_UNIT}: \{cellValue}");
    }
  }

  /**
   * Validates the volume.
   *
   * @param cell the cell containing the volume
   * @return the validated volume
   * @throws Exception if the validation fails
   */
  public String validateVolume(Cell cell) throws Exception {
    if (cell == null
        || (cell.getCellType() != CellType.STRING && cell.getCellType() != CellType.NUMERIC)) {
      throw new Exception(ErrorMessages.INVALID_VOLUME);
    }
    String volumeStr =
        cell.getCellType() == CellType.STRING
            ? cell.getStringCellValue().trim().replace(',', '.')
            : String.valueOf(cell.getNumericCellValue());

    if (!volumeStr.matches("\\d+(\\.\\d+)?")) {
      throw new Exception(ErrorMessages.INVALID_VOLUME_FORMAT);
    }
    double volume = Double.parseDouble(volumeStr);
    if (volume > 100) {
      throw new Exception(
          STR."\{ErrorMessages.INVALID_VOLUME} - Maksymalna dozwolona wartość to 100.");
    }

    return volumeStr;
  }

  /**
   * Validates the price by checking if the cell is not null and returns the price as a string.
   * Supports both numeric and string cell types.
   *
   * @param cell the cell containing the price
   * @return the validated price as a String
   * @throws Exception if the cell is null, blank, or if the value is empty after trimming
   */
  public String validatePrice(Cell cell) throws Exception {
    if (cell == null || cell.getCellType() == CellType.BLANK) {
      throw new Exception(STR."\{ErrorMessages.INVALID_PRICE} - Komórka jest pusta.");
    }
    String priceStr;
    if (cell.getCellType() == CellType.NUMERIC) {
      double price = cell.getNumericCellValue();
      if (price >= 10000) {
        throw new Exception(
            STR."\{ErrorMessages.INVALID_PRICE} - Maksymalna dozwolona wartość to 10,000.");
      }
      priceStr = String.valueOf(price).trim();
    } else if (cell.getCellType() == CellType.STRING) {
      priceStr = cell.getStringCellValue().trim();
    } else {
      throw new Exception(STR."\{ErrorMessages.INVALID_PRICE} - Typ komórki nieobsługiwany.");
    }

    if (priceStr.isEmpty()) {
      throw new Exception(STR."\{ErrorMessages.INVALID_PRICE} - Cena nie może być pusta.");
    }
    return priceStr;
  }

  /**
   * Validates the purchase method by checking if the cell is not null and is of type STRING.
   *
   * @param cell the cell containing the purchase method
   * @return the validated purchase method as a String
   * @throws Exception if the validation fails
   */
  public String validatePurchaseMethod(Cell cell) throws Exception {
    if (cell == null || cell.getCellType() != CellType.STRING) {
      throw new Exception(ErrorMessages.INVALID_PURCHASE_METHOD);
    }
    return cell.getStringCellValue().trim();
  }

  /**
   * Validates the email comment by checking if the cell is not null and returns a trimmed string.
   * Returns an empty string if the cell is blank.
   *
   * @param cell the cell containing the email comment
   * @return the validated email comment as a String, or an empty string if the cell is blank
   */
  public String validateEmailComment(Cell cell) {
    if (cell == null || cell.getCellType() == CellType.BLANK) {
      return "";
    }
    return cell.getStringCellValue().trim();
  }
}
