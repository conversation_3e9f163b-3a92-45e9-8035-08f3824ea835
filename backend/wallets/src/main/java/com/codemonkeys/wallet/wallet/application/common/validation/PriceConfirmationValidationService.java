/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.common.validation;

import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.ElementRepository;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.ElementId;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service for validating price confirmation related operations. This service provides methods to
 * verify updates, removals, and additions of elements in the context of price confirmations,
 * ensuring that changes are not made to confirmed prices.
 */
@Service
public class PriceConfirmationValidationService {
  private static final String PRICE_CHANGE_NOT_ALLOWED = "validations.wallet.confirmedPrice";
  private final WalletRepository walletRepository;
  private final ElementRepository elementRepository;

  @Autowired
  public PriceConfirmationValidationService(
      WalletRepository walletRepository, ElementRepository elementRepository) {
    this.walletRepository = walletRepository;
    this.elementRepository = elementRepository;
  }

  /**
   * Verifies if an element update is allowed based on the price confirmation status.
   *
   * <p>This method checks if the time unit has been changed and if the wallet has a confirmed price
   * for the new time unit. It also checks if the wallet has a confirmed price for the old time unit
   * and if the value of the element has been changed. If any of these conditions are met, a {@link
   * ValidationFailedException} is thrown.
   *
   * @param elementId The ID of the element to be updated.
   * @param newTimeUnit The new time unit for the element.
   * @param newValue The new value for the element.
   * @throws ValidationFailedException if the price change is not allowed.
   * @throws EntityNotFoundException if the element is not found.
   */
  public void verifyElementUpdate(ElementId elementId, TimeUnit newTimeUnit, BigDecimal newValue) {
    Element element =
        elementRepository.findById(elementId).orElseThrow(EntityNotFoundException::new);
    TimeUnit oldTimeUnit = element.getTimeUnit();
    Wallet wallet = element.getWallet();
    if (!oldTimeUnit.equals(newTimeUnit)) {
      if (wallet.hasConfirmedPrice(newTimeUnit)) {
        throwValidationFailedException(PRICE_CHANGE_NOT_ALLOWED);
      }
    }
    if (wallet.hasConfirmedPrice(oldTimeUnit) && !element.getValue().equals(newValue)) {
      throwValidationFailedException(PRICE_CHANGE_NOT_ALLOWED);
    }
  }

  /**
   * Verifies if an element removal is allowed based on the price confirmation status.
   *
   * <p>This method checks if the wallet has a confirmed price for the time unit of the element to
   * be removed. If the price is confirmed, a {@link ValidationFailedException} is thrown.
   *
   * @param elementId The ID of the element to be removed.
   * @throws ValidationFailedException if the price change is not allowed.
   * @throws EntityNotFoundException if the element is not found.
   */
  public void verifyElementRemoval(ElementId elementId) {
    Element element =
        elementRepository.findById(elementId).orElseThrow(EntityNotFoundException::new);
    TimeUnit timeUnit = element.getTimeUnit();
    Wallet wallet = element.getWallet();
    if (wallet.hasConfirmedPrice(timeUnit)) {
      throwValidationFailedException(PRICE_CHANGE_NOT_ALLOWED);
    }
  }

  /**
   * Verifies if an element addition is allowed based on the price confirmation status.
   *
   * <p>This method checks if the wallet has a confirmed price for the time unit to which the
   * element is being added. If the price is confirmed, a {@link ValidationFailedException} is
   * thrown.
   *
   * @param walletId The ID of the wallet to which the element is being added.
   * @param timeUnit The time unit for the new element.
   * @throws ValidationFailedException if the price change is not allowed.
   * @throws EntityNotFoundException if the wallet is not found.
   */
  public void verifyElementAddition(WalletId walletId, TimeUnit timeUnit) {
    Wallet wallet = walletRepository.findById(walletId).orElseThrow(EntityNotFoundException::new);
    if (wallet.hasConfirmedPrice(timeUnit)) {
      throwValidationFailedException(PRICE_CHANGE_NOT_ALLOWED);
    }
  }

  private void throwValidationFailedException(String message) {
    throw new ValidationFailedException(I18n.translate(message), Lists.newArrayList());
  }
}
