/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.tranches.update;

import com.codemonkeys.wallet.common.framework.crud.update.UpdateMapper;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UpdateTrancheMapper
    implements UpdateMapper<Tranche, TrancheId, UpdateTrancheRequest, UpdateTrancheResponse> {

  private final ContractRepository contractRepository;
  private final WalletRepository walletRepository;

  @Override
  public Tranche toDomain(Tranche toBeUpdated, UpdateTrancheRequest request) {

    Contract contract = contractRepository.getReferenceById(request.getContract());
    Wallet wallet = walletRepository.getReferenceById(request.getWalletId());
    Tranche updated =
        new Tranche(
            request.getId(),
            wallet,
            request.getExecutionDate(),
            request.getTimeUnit(),
            request.getVolume(),
            request.getPrice(),
            request.getPriceReference(),
            contract,
            false,
            request.isInterventionPurchase());

    toBeUpdated.update(updated);
    return updated;
  }

  @Override
  public UpdateTrancheResponse toResponse(Tranche entity) {
    return UpdateTrancheResponse.of(entity.getId(), ValidResult.of(), entity);
  }

  @Override
  public UpdateTrancheResponse toResponse(InvalidResult errors) {
    return UpdateTrancheResponse.of(null, errors, null);
  }
}
