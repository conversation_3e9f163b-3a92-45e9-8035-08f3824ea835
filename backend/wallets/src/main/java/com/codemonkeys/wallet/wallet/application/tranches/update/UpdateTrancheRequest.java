/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.tranches.update;

import com.codemonkeys.wallet.common.framework.shared.dto.Request;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateTrancheRequest implements Request {
  private TrancheId id;
  private ContractId contract;
  private TimeUnit timeUnit;
  private LocalDate executionDate;
  private PriceReference priceReference;
  private BigDecimal volume;
  private BigDecimal price;
  private WalletId walletId;
  private boolean interventionPurchase;
}
