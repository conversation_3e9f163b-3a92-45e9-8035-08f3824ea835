/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.priceconfirmation;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListMapper;
import com.codemonkeys.wallet.domain.wallets.PriceConfirmation;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
public class PriceConfirmationMapper implements ProjectionListMapper<Wallet, WalletId, Wallet> {

  @Override
  public Wallet toDto(Wallet entity) {
    return entity;
  }

  @Override
  public Page<Wallet> toDto(Page<Wallet> entities) {
    return entities;
  }

  public Page<PriceConfirmation> toPagedPriceConfirmation(
      Page<Wallet> entities, PriceConfirmationRequest request) {
    return entities.map(Wallet::priceConfirmation);
  }
}
