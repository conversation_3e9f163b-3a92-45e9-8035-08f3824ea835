/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.elements.update;

import com.codemonkeys.wallet.common.framework.crud.update.UpdateByIdServiceImpl;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.ElementRepository;
import com.codemonkeys.wallet.domain.wallets.vo.ElementId;
import com.codemonkeys.wallet.wallet.application.common.validation.PriceConfirmationValidationService;
import org.springframework.stereotype.Service;

@Service
public class UpdateElementServiceImpl
    extends UpdateByIdServiceImpl<
        UpdateElementRequest,
        UpdateElementResponse,
        Element,
        ElementId,
        ElementRepository,
        UpdateElementMapper,
        UpdateElementValidator> {
  private final RecalculationService recalculationService;
  private final PriceConfirmationValidationService priceConfirmationValidationService;

  public UpdateElementServiceImpl(
      ElementRepository repository,
      UpdateElementMapper mapper,
      UpdateElementValidator validator,
      RecalculationService recalculationService,
      PriceConfirmationValidationService priceConfirmationValidationService) {
    super(repository, mapper, validator);
    this.recalculationService = recalculationService;
    this.priceConfirmationValidationService = priceConfirmationValidationService;
  }

  @Override
  public UpdateElementResponse updateById(ElementId id, UpdateElementRequest request) {
    priceConfirmationValidationService.verifyElementUpdate(
        request.getId(), request.getTimeUnit(), request.getValue());
    UpdateElementResponse response = super.updateById(id, request);
    recalculationService.recalculate(request.getWallet().getId());
    return response;
  }
}
