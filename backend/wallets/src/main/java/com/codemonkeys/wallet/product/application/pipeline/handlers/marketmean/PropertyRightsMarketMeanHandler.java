/* (C)2024-2025 */
package com.codemonkeys.wallet.product.application.pipeline.handlers.marketmean;

import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.PropertyRightContract;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.results.Result;
import com.codemonkeys.wallet.domain.wallets.MonthResults;
import com.codemonkeys.wallet.domain.wallets.MonthTranches;
import com.codemonkeys.wallet.domain.wallets.Product;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletPrice;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.ProductType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.ProductId;
import com.codemonkeys.wallet.product.application.pipeline.MathUtil;
import com.codemonkeys.wallet.product.application.pipeline.handlers.ProductHandler;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PropertyRightsMarketMeanHandler
    extends ProductHandler<PropertyRightsMarketMeanCommand> {

  @Autowired PriceRepository priceRepository;

  public PropertyRightsMarketMeanHandler(
      PriceRepository priceRepository, PriceRepository priceRepository1) {
    super(priceRepository);
    this.priceRepository = priceRepository1;
  }

  @Override
  public Wallet handle(PropertyRightsMarketMeanCommand command) {
    Wallet wallet = command.getWallet();
    List<TimeUnit> months = TimeUnit.Y.getMonths();
    MonthTranches tranches = command.getWallet().getMonthTranches();
    List<Product> result = Lists.newArrayList();
    for (TimeUnit month : months) {
      MonthResults monthResults = wallet.getMonthResults(Media.GREEN_PROPERTY_RIGHTS);
      List<Tranche> monthTranches = tranches.get(month, PropertyRightContract.class);
      if (!monthTranches.isEmpty()) {
        BigDecimal size = MathUtil.sum(monthTranches, Tranche::getSize);
        Optional<Contract> mContract =
            getMonthContract(monthTranches, t -> t.getContract().isPropertyRightsMonthContract());
        List<Tranche> mTranches =
            getTranches(monthTranches, t -> t.getContract().isPropertyRightsMonthContract());
        BigDecimal mSize = MathUtil.sum(mTranches, Tranche::getSize);
        BigDecimal mPercent = mSize.divide(size, MathContext.DECIMAL64);
        Optional<Contract> yContract =
            getYearContract(monthTranches, t -> t.getContract().isPropertyRightsYearContract());
        List<Tranche> yTranches =
            getTranches(monthTranches, t -> t.getContract().isPropertyRightsYearContract());
        BigDecimal ySize = MathUtil.sum(yTranches, Tranche::getSize);
        BigDecimal yPercent = ySize.divide(size, MathContext.DECIMAL64);
        BigDecimal monthMean = mean(monthTranches, mContract);
        BigDecimal monthValue = mPercent.multiply(monthMean);
        BigDecimal yearMean = mean(yTranches, yContract);
        BigDecimal yearValue = yPercent.multiply(yearMean);
        BigDecimal mean = monthValue.add(yearValue);
        Media media = getMedia(monthTranches);
        if (media != null) {
          result.add(
              new Product(
                  ProductId.randomId(),
                  command.getWallet(),
                  ProductType.MARKET_MEAN,
                  month,
                  mean,
                  media));
          BigDecimal basePrice = basePrice(monthTranches, Tranche::product);
          WalletPrice wp = wallet.getPrice(month, ElementType.GREEN_PROPERTY_RIGHTS);
          if (basePrice != null) {
            result.add(
                new Product(
                    ProductId.randomId(),
                    command.getWallet(),
                    ProductType.CLIENT_MEAN,
                    month,
                    basePrice,
                    media));
            BigDecimal benchmark = mean.subtract(basePrice);
            result.add(
                new Product(
                    ProductId.randomId(),
                    command.getWallet(),
                    ProductType.BENCHMARK_RESULT,
                    month,
                    benchmark,
                    media));
          } else if (wp != null) {
            result.add(
                new Product(
                    ProductId.randomId(),
                    command.getWallet(),
                    ProductType.CLIENT_MEAN,
                    month,
                    wp.getValue(),
                    media));
            BigDecimal benchmark = mean.subtract(wp.getValue());
            Optional<Result> resultOptional = monthResults.get(month);
            if (resultOptional.isPresent()) {
              benchmark = wp.getValue().subtract(resultOptional.get().getValue());
            }
            result.add(
                new Product(
                    ProductId.randomId(),
                    command.getWallet(),
                    ProductType.BENCHMARK_RESULT,
                    month,
                    benchmark,
                    media));
          }
        }
      }
    }
    wallet.addProducts(result);
    return wallet;
  }
}
