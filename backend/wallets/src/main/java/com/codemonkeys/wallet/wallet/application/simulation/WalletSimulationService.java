/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.simulation;

import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.wallet.application.find.FindWalletByIdServiceImpl;
import com.codemonkeys.wallet.wallet.application.tranches.common.TrancheMapper;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WalletSimulationService {

  private final WalletSimulationMapper walletMapper;
  private final RecalculationService recalculationService;
  private final FindWalletByIdServiceImpl findWalletByIdService;
  private final PriceRepository priceRepository;
  private final CoverageValidationService coverageValidationService;
  private final TrancheMapper trancheMapper;

  /**
   * Finds a wallet by its ID and simulates its recalculation. This method first retrieves the
   * wallet and then recalculates it using the recalculation service.
   *
   * @param walletId the unique identifier of the wallet to be found and simulated.
   * @return a {@link WalletSimulationDto} containing the recalculated wallet data.
   */
  public WalletSimulationDto findAndSimulateWalletById(WalletId walletId) {
    Wallet wallet = findWalletByIdService.findWalletById(walletId);
    Wallet recalculatedWallet = recalculationService.simulateRecalculation(wallet);
    return new WalletSimulationDto(recalculatedWallet);
  }

  /**
   * Simulates a wallet recalculation based on the provided request data. Converts the wallet
   * request DTO into the domain model and recalculates the wallet.
   *
   * @param walletRequest the details of the wallet to be simulated, including its tranches, prices,
   *     and elements.
   * @return a {@link WalletSimulationDto} containing the recalculated wallet data.
   */
  public WalletSimulationDto simulateWallet(WalletSimulationRequest walletRequest) {
    coverageValidationService.validateNotExceeding100Percent(walletRequest);
    Wallet base = findWalletByIdService.findWalletById(WalletId.of(walletRequest.getWalletId()));
    Wallet simulation = new Wallet(base);
    simulation.getTranches().clear();
    walletRequest.getTranches().forEach(t -> simulation.addTranche(trancheMapper.toDomain(t)));
    Wallet recalculated = recalculationService.simulateRecalculation(simulation);
    return new WalletSimulationDto(recalculated);
  }

  /**
   * Finds the latest price for a given contract name by fetching the most recent entry from the
   * price repository. The prices are ordered by date in descending order, ensuring that the most
   * recent price is returned.
   *
   * @param contractName the name of the contract to fetch the latest price for.
   * @return a {@link PriceValueDto} containing the value of the latest price for the given
   *     contract.
   */
  public PriceValueDto findLastPriceForContract(String contractName) {
    Optional<Price> lastPrice =
        priceRepository.findLatestPriceByContract(PriceName.of(contractName).getName());
    if (lastPrice.isEmpty()) {
      String message = I18n.translate("validations.contract.price.not.found", contractName);
      throw new ValidationFailedException(
          message, List.of(WalletValidationMessage.of("contract", message)));
    }
    if (lastPrice.get().getValue() == null) {
      String message = I18n.translate("validations.contract.price.not.found", contractName);
      throw new ValidationFailedException(
          message, List.of(WalletValidationMessage.of("contract", message)));
    }
    return new PriceValueDto(lastPrice.get().getValue().getValue());
  }
}
