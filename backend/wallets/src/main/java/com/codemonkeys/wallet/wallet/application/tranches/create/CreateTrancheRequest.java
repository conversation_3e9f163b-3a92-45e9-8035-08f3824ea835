/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.tranches.create;

import com.codemonkeys.wallet.common.framework.shared.dto.Request;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateTrancheRequest implements Request {

  private boolean formType;
  private List<UUID> walletIds;
  private List<UUID> contractIds;
  private String contract;
  private TimeUnit timeUnit;
  private LocalDate executionDate;
  private PriceReference priceReference;
  private BigDecimal volume;
  private BigDecimal price;
  private boolean interventionPurchase;

  public List<AgreementId> getAgreementsIds() {
    return Optional.ofNullable(contractIds).orElse(List.of()).stream()
        .map(AgreementId::of)
        .toList();
  }

  public List<WalletId> getWalletIds() {
    return walletIds.stream().map(WalletId::of).toList();
  }
}
