/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.profile;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.DefaultElementPriceHandler;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletPrice;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProfilePercentPriceHandler
    extends DefaultElementPriceHandler<ProfilePercentCostCommand> {

  private Wallet process(Wallet wallet) {
    List<Element> elements = wallet.getElements(ElementType.PROFILE_PERCENT);
    for (Element element : elements) {
      List<TimeUnit> months = element.getTimeUnit().getMonths();
      for (TimeUnit month : months) {
        // TODO: Zastanowić się nad API z optionalem - purystyka
        WalletPrice net = wallet.getPrice(month, ElementType.NET);
        if (net != null) {
          BigDecimal fraction =
              element.getValue().divide(BigDecimal.valueOf(100L), 5, RoundingMode.HALF_UP);
          BigDecimal value = net.getValue().multiply(fraction);

          List<WalletPrice> profilePrices = createPrice(wallet, ElementType.PROFILE, month, value);
          wallet.addPrices(profilePrices);
        }
      }
    }
    return wallet;
  }

  @Override
  public Wallet handle(ProfilePercentCostCommand command) {
    return process(command.getWallet());
  }

  @Override
  protected List<ElementType> getAssociatedElements() {
    return List.of(ElementType.PROFILE_PERCENT);
  }
}
