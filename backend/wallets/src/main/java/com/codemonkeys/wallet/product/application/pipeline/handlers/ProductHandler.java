/* (C)2024-2025 */
package com.codemonkeys.wallet.product.application.pipeline.handlers;

import an.awesome.pipelinr.Command;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.contract.PropertyRightContract;
import com.codemonkeys.wallet.domain.contract.vo.ContractName;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.Product;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.ProductType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.ProductId;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class ProductHandler<T extends ProductCommand>
    implements Command.Handler<T, Wallet> {
  protected static final int PRICE_DECIMAL_SCALE = 2;
  PriceRepository priceRepository;

  public ProductHandler(PriceRepository priceRepository) {
    this.priceRepository = priceRepository;
  }

  public Optional<Contract> replaceSpotContract(
      Optional<Contract> contractOptional, Agreement agreement, TimeUnit tm, Year year) {
    if (contractOptional.isPresent() && contractOptional.get().isSpotContract()) {
      ContractName cn =
          ContractName.of(
              year,
              contractOptional.get().getAgreement().getMediaType(),
              ContractType.M,
              tm.getOrder());
      List<Contract> ccs =
          agreement.getContracts().stream().filter(c -> c.getName().equals(cn)).toList();

      return ccs.stream().findFirst();
    }
    return contractOptional;
  }

  protected @NotNull Optional<Contract> getQuarterContract(List<Tranche> monthTranches) {
    return monthTranches.stream()
        .filter(tranche -> tranche.getContract().isQuarterContract())
        .findFirst()
        .map(Tranche::getContract);
  }

  protected @NotNull Optional<Contract> getMonthContract(List<Tranche> monthTranches) {
    return monthTranches.stream()
        .filter(tranche -> tranche.getContract().isMonthOrSpotContract())
        .findFirst()
        .map(Tranche::getContract);
  }

  protected @NotNull Optional<Contract> getMonthContract(
      List<Tranche> monthTranches, Predicate<Tranche> predicate) {
    return monthTranches.stream().filter(predicate).findFirst().map(Tranche::getContract);
  }

  protected @NotNull Optional<Contract> getYearContract(List<Tranche> monthTranches) {
    return monthTranches.stream()
        .filter(tranche -> tranche.getContract().isYearContract())
        .findFirst()
        .map(Tranche::getContract);
  }

  protected Media getMedia(List<Tranche> monthTranches) {
    if (!monthTranches.isEmpty()) {
      return switch (monthTranches.getFirst().getContract()) {
        case EnergyContract energyContract -> {
          yield Media.ENERGY;
        }
        case GasContract gasContract -> {
          yield Media.GAS;
        }
        case PropertyRightContract propertyRightContract -> {
          String name = propertyRightContract.getName().getValue();
          if (name.contains("PMOZE_A")) {
            yield Media.GREEN_PROPERTY_RIGHTS;
          }
          if (name.contains("PMEF_F")) {
            yield Media.WHITE_PROPERTY_RIGHTS;
          }
          if (name.contains("PMOZE_BIO")) {
            yield Media.BLUE_PROPERTY_RIGHTS;
          }
          throw new IllegalStateException(
              STR."Unexpected value: \{monthTranches.getFirst().getContract()}");
        }
        default ->
            throw new IllegalStateException(
                STR."Unexpected value: \{monthTranches.getFirst().getContract()}");
      };
    } else return null;
  }

  protected List<Tranche> getTranches(
      List<Tranche> monthTranches, Predicate<? super Tranche> predicate) {
    return monthTranches.stream().filter(predicate).toList();
  }

  protected BigDecimal mean(List<Tranche> tranches, Optional<Contract> contractOptional) {
    if (tranches.isEmpty() || contractOptional.isEmpty()) {
      return BigDecimal.ZERO;
    }
    Contract contract = contractOptional.get();
    List<Price> prices =
        priceRepository.findByContractAndDateBetween(
            PriceName.of(contract.getName().getStockName()),
            contract.getAverageCalculation().getStartDate(),
            contract.getAverageCalculation().getEndDate());
    BigDecimal price =
        prices.stream()
            .map(p -> p.getValue().getValue().multiply(p.getVolume().getVolume()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal volume =
        prices.stream()
            .map(p -> p.getVolume().getVolume())
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    if (volume.compareTo(BigDecimal.ZERO) > 0) {
      price = price.divide(volume, MathContext.DECIMAL64);
    }

    return price.setScale(25, RoundingMode.HALF_UP);
  }

  protected List<Product> boughtVolumeInTimeUnit(
      List<Tranche> tranches,
      BigDecimal volume,
      ProductType productType,
      Media media,
      TimeUnit month,
      Wallet wallet) {
    List<Product> result = Lists.newArrayList();
    BigDecimal contractedVolume =
        tranches.stream().map(Tranche::getSize).reduce(BigDecimal.ZERO, BigDecimal::add);
    result.add(
        new Product(ProductId.randomId(), wallet, productType, month, contractedVolume, media));
    return result;
  }

  protected @NotNull Wallet handleVolumeProducts(Wallet wallet, Class<? extends Contract> type) {
    Map<TimeUnit, List<Tranche>> tranches = wallet.monthlyTranches();
    List<Product> result = Lists.newArrayList();
    List<TimeUnit> months = TimeUnit.Y.getMonths();
    for (TimeUnit month : months) {
      BigDecimal volume = wallet.getAgreementReferenceVolume(month);
      List<Tranche> monthTranches =
          Stream.ofNullable(tranches.get(month))
              .flatMap(List::stream)
              .filter(t -> Objects.equals(t.getContract().getClass(), type))
              .toList();
      Media media = getMedia(monthTranches);
      if (!monthTranches.isEmpty() && media != null) {
        List<Product> monthVolume =
            boughtVolumeInTimeUnit(
                monthTranches.stream().filter(t -> t.getContract().isMonthContract()).toList(),
                volume,
                ProductType.VOLUME_M,
                media,
                month,
                wallet);
        List<Product> quarterVolume =
            boughtVolumeInTimeUnit(
                monthTranches.stream().filter(t -> t.getContract().isQuarterContract()).toList(),
                volume,
                ProductType.VOLUME_Q,
                media,
                month,
                wallet);
        List<Product> yearVolume =
            boughtVolumeInTimeUnit(
                monthTranches.stream().filter(t -> t.getContract().isYearContract()).toList(),
                volume,
                ProductType.VOLUME_Y,
                media,
                month,
                wallet);
        List<Product> spotVolume =
            boughtVolumeInTimeUnit(
                monthTranches.stream().filter(t -> t.getContract().isSpotContract()).toList(),
                volume,
                ProductType.VOLUME_SPOT,
                media,
                month,
                wallet);
        List<Product> overallVolume =
            boughtVolumeInTimeUnit(monthTranches, volume, ProductType.VOLUME, media, month, wallet);
        result.addAll(overallVolume);
        result.addAll(spotVolume);
        result.addAll(monthVolume);
        result.addAll(quarterVolume);
        result.addAll(yearVolume);
      }
    }
    wallet.addProducts(result);
    return wallet;
  }

  protected BigDecimal basePrice(
      List<Tranche> tranches, Function<Tranche, BigDecimal> priceCalculator) {
    if (priceCalculator == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    return tranches.stream()
        .map(priceCalculator)
        .reduce(BigDecimal.ZERO, BigDecimal::add)
        .setScale(PRICE_DECIMAL_SCALE, RoundingMode.HALF_UP);
  }
}
