/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.list;

import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import java.time.LocalDate;

public interface ListWalletResponse {
  WalletId getId();

  String getMediaType();

  LocalDate getStartDate();

  Year getYear();

  AgreementResponse getAgreement();

  String getDescription();

  String getName();

  interface AgreementResponse {
    String getId();

    CustomerResponse getCustomer();

    interface CustomerResponse {
      String getName();
    }

    SupplierResponse getSupplier();

    interface SupplierResponse {
      String getName();
    }
  }
}
