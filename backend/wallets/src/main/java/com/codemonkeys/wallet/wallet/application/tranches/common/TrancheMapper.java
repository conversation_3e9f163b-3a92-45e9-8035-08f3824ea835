/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.tranches.common;

import com.codemonkeys.wallet.common.framework.crud.create.CreateMapper;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.wallet.application.tranches.create.CreateTrancheRequest;
import java.math.BigDecimal;
import org.springframework.stereotype.Service;

/**
 * Mapper class for mapping between {@link Tranche} domain entities and DTOs like {@link
 * CreateTrancheRequest} and {@link CreateTrancheResponse}.
 */
@Service
public class TrancheMapper
    implements CreateMapper<Tranche, TrancheId, CreateTrancheRequest, CreateTrancheResponse> {

  /**
   * Maps the {@link CreateTrancheRequest} to a {@link Tranche} domain entity.
   *
   * @param request the request containing tranche data
   * @return the mapped {@link Tranche} entity
   */
  @Override
  public Tranche toDomain(CreateTrancheRequest request) {
    return new Tranche(
        TrancheId.randomId(),
        null,
        request.getExecutionDate(),
        request.getTimeUnit(),
        new BigDecimal(String.valueOf(request.getVolume())),
        new BigDecimal(String.valueOf(request.getPrice())),
        request.getPriceReference(),
        null,
        false);
  }

  /**
   * Converts a {@link Tranche} entity to a {@link CreateTrancheResponse}.
   *
   * @param entity the {@link Tranche} entity
   * @return the response containing the tranche details and validation result
   */
  @Override
  public CreateTrancheResponse toResponse(Tranche entity) {
    return CreateTrancheResponse.of(entity.getId(), ValidResult.of(), entity);
  }

  /**
   * Converts an {@link InvalidResult} to a {@link CreateTrancheResponse}.
   *
   * @param errors the validation errors
   * @return the response containing the validation errors
   */
  @Override
  public CreateTrancheResponse toResponse(InvalidResult errors) {
    return CreateTrancheResponse.of(null, errors, null);
  }
}
