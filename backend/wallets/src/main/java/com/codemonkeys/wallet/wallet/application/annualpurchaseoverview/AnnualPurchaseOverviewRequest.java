/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.annualpurchaseoverview;

import com.codemonkeys.wallet.common.framework.shared.dto.ListRequest;
import com.codemonkeys.wallet.common.framework.shared.dto.RequestInitializer;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Request class for retrieving annual purchase overviews. Extends {@link ListRequest} to provide
 * pagination, sorting, and filtering capabilities while adding specific parameters for annual
 * purchase overview queries.
 *
 * @see ListRequest
 * @see RequestInitializer
 */
@Data
@AllArgsConstructor
public class AnnualPurchaseOverviewRequest extends ListRequest {

  private Media  mediaType;
  private String year;
  private String customerName;
  private String supplierName;
  private String description;

  /**
   * Constructs a new AnnualPurchaseOverviewRequest from a map of request parameters. Initializes
   * both common list request properties and specific filters for annual purchase overviews.
   *
   * <p>The following parameters are supported:
   *
   * <ul>
   *   <li>media - Required. The type of media to filter by (must be a valid {@link Media} enum
   *       value)
   *   <li>year - Optional. The year to filter results by
   *   <li>wallet - Optional. The wallet identifier to filter results by
   *   <li>page - Optional. The page number for pagination (inherited from ListRequest)
   *   <li>limit - Optional. The page size for pagination (inherited from ListRequest)
   *   <li>sort - Optional. The sorting parameters (inherited from ListRequest)
   *   <li>filter - Optional. The global filter parameters (inherited from ListRequest)
   * </ul>
   *
   * @param params a map of request parameters used to initialize the request
   * @throws IllegalArgumentException if the media parameter is missing or invalid
   */
  public AnnualPurchaseOverviewRequest(Map<String, String> params) {
    super();
    RequestInitializer.initializePagination(this, params);
    initializeSpecificFilters(params);
    RequestInitializer.initializeFilters(this, params);
    RequestInitializer.initializeSorting(this, params);
    RequestInitializer.initializeGlobalFilter(this, params);
  }

  /**
   * Initializes the specific filters for annual purchase overview requests from the provided
   * parameters. This includes setting the media type, year, and wallet filters.
   *
   * @param p the map of request parameters
   * @throws IllegalArgumentException if the media parameter value is not a valid {@link Media} enum
   *     value
   */
  private void initializeSpecificFilters(Map<String,String> p){
    this.mediaType    = Media.valueOf(p.get("mediaType"));
    this.year         = p.get("year");
    this.customerName = p.get("customerName");
    this.supplierName = p.get("supplierName");
    this.description  = p.get("description");
  }
}
