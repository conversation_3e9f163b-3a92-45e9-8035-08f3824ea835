/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.priceconfirmation;

import com.codemonkeys.wallet.common.framework.shared.dto.ListRequest;
import com.codemonkeys.wallet.common.framework.shared.dto.RequestInitializer;
import com.codemonkeys.wallet.domain.customer.CustomerSegment;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class PriceConfirmationRequest extends ListRequest {

  private List<Media> mediaTypes = List.of();
  private List<String> years = List.of();
  private List<String> supplierNames = List.of();
  private String customerName;
  private String description;
  private List<CustomerSegment> customerSegments = List.of();
  private boolean pricesTge = false;

  public PriceConfirmationRequest(Map<String, String> params) {
    super();
    RequestInitializer.initializePagination(this, params);
    initializeSpecificFilters(params);
    RequestInitializer.initializeFilters(this, params);
    RequestInitializer.initializeSorting(this, params);
    RequestInitializer.initializeGlobalFilter(this, params);
  }

  private void initializeSpecificFilters(Map<String, String> p) {
    this.mediaTypes = splitOrEmpty(p.get("mediaType")).stream().map(Media::valueOf).toList();
    this.years = splitOrEmpty(p.get("year"));
    this.supplierNames = splitOrEmpty(p.get("supplierName"));
    this.customerName = p.get("customerName");
    this.description = p.get("description");
    this.customerSegments =
        splitOrEmpty(p.get("customerSegment")).stream().map(CustomerSegment::valueOf).toList();
    this.pricesTge = Boolean.parseBoolean(p.getOrDefault("pricesTge", "false"));
  }

  private static List<String> splitOrEmpty(String raw) {
    return raw == null || raw.isBlank()
        ? List.of()
        : Arrays.stream(raw.split(",")).map(String::trim).filter(s -> !s.isEmpty()).toList();
  }
}
