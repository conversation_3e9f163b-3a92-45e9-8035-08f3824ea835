/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.create;

import com.codemonkeys.wallet.common.ValidationContext;
import com.codemonkeys.wallet.common.ValidationService;
import com.codemonkeys.wallet.common.framework.crud.create.CreateServiceImpl;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.RecommendationRepository;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.domain.recommendation.vo.RecommendationId;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.common.CreateUpdateRecommendationResponse;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationHelper;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationMapper;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service implementation for creating new recommendations. Extends {@link CreateServiceImpl} to
 * handle business logic for creating recommendations.
 */
@Slf4j
@Service
public class CreateRecommendationServiceImpl
    extends CreateServiceImpl<
        CreateRecommendationRequest,
        CreateUpdateRecommendationResponse,
        Recommendation,
        RecommendationId,
        RecommendationRepository,
        RecommendationMapper,
        CreateRecommendationValidator> {

  private final ValidationService validationService;
  private final RecommendationHelper recommendationHelper;

  /**
   * Constructs a new {@code CreateRecommendationServiceImpl} with the specified dependencies.
   *
   * @param repository the repository used to persist recommendations.
   * @param mapper the mapper used to convert entities to DTOs.
   * @param validator the validator used to validate incoming requests.
   * @param validationService the helper used for recommendation-related operations.
   */
  @Autowired
  public CreateRecommendationServiceImpl(
      RecommendationRepository repository,
      RecommendationMapper mapper,
      CreateRecommendationValidator validator,
      ValidationService validationService,
      RecommendationHelper recommendationHelper) {
    super(repository, mapper, validator);
    this.validationService = validationService;
    this.recommendationHelper = recommendationHelper;
  }

  /**
   * Creates a new recommendation based on the request.
   *
   * @param request the request containing the recommendation details.
   * @return the response containing the created recommendation details.
   */
  @Transactional
  @Override
  public CreateUpdateRecommendationResponse create(CreateRecommendationRequest request) {
    updateTimeUnitFromRecommendationUpload(request);
    validationService.validate(ValidationContext.RECOMMENDATION, request);
    List<Recommendation> recommendations =
        request.getContractId().stream()
            .map(contractId -> createRecommendation(request, String.valueOf(contractId)))
            .collect(Collectors.toList());

    repository.saveAll(recommendations);

    return mapper.toResponse(recommendations.get(0));
  }

  /**
   * Creates a new recommendation based on the provided request and agreement ID. This method
   * retrieves the agreement and contract, determines the carrier type, and assigns the appropriate
   * recommendation status before building the recommendation object.
   *
   * @param request the recommendation request containing necessary details
   * @param agreementId the ID of the agreement associated with the recommendation
   * @return the created {@link Recommendation} object
   */
  private Recommendation createRecommendation(
      CreateRecommendationRequest request, String agreementId) {
    UUID agreementUUID = UUID.fromString(agreementId);
    Agreement agreement = recommendationHelper.findAgreementOrThrow(agreementUUID);
    Contract contract = recommendationHelper.findContractOrThrow(agreement, request.getContract());
    String carrier = recommendationHelper.getCarrierTypeName(contract);
    RecommendationStatus status = determineRecommendationStatus(agreement);

    return buildRecommendation(
        request, agreement, agreementUUID, carrier, calculateDeadline(request, contract), status);
  }

  /**
   * Updates the time unit in the recommendation request if it is missing. This method retrieves the
   * corresponding contract from the agreement and extracts the time unit from the contract name.
   *
   * @param request the recommendation request to be updated
   */
  private void updateTimeUnitFromRecommendationUpload(
      @NotNull CreateRecommendationRequest request) {
    if (request.getTimeUnit() == null || request.getTimeUnit().isEmpty()) {
      Agreement agreement =
          recommendationHelper.findAgreementOrThrow(request.getContractId().get(0));

      agreement.getContracts().stream()
          .filter(c -> c.getName().getValue().equalsIgnoreCase(request.getContract()))
          .findFirst()
          .ifPresent(
              contract -> {
                TimeUnit timeUnit = contract.getName().getTimeUnit();
                if (timeUnit != null) {
                  request.setTimeUnit(timeUnit.name());
                }
              });
    }
  }

  /**
   * Calculates the deadline for the recommendation based on the request and contract.
   *
   * @param request the request containing the deadline details.
   * @param contract the contract associated with the recommendation.
   * @return the calculated deadline.
   */
  private LocalDateTime calculateDeadline(CreateRecommendationRequest request, Contract contract) {
    return recommendationHelper.calculateDeadline(
        contract,
        String.valueOf(request.getPurchaseMethod()),
        request.getDeadline().atStartOfDay());
  }

  /**
   * Determines the status of the recommendation based on the request.
   *
   * @param agreement the request containing recommendation details.
   * @return the determined recommendation status.
   */
  private RecommendationStatus determineRecommendationStatus(Agreement agreement) {
    return agreement.isSendRecommendation()
        ? RecommendationStatus.NEW
        : RecommendationStatus.ACCEPTED_ORDER_PENDING;
  }

  /**
   * Retrieves and translates the order type from `media` or `propertyRights` in the agreement. If
   * only the type exists, it returns the type. If a value also exists, it returns "type - value".
   * The results are joined with commas if both sections contain data.
   *
   * @param agreement the Agreement object from which the order type information is retrieved.
   * @return A formatted string containing the order type (optionally with value) or an empty string
   *     if no data is available.
   */
  private String getOrderTypeFromAgreement(Agreement agreement) {
    return Stream.of(agreement.getMedia(), agreement.getPropertyRights())
        .filter(Objects::nonNull)
        .map(CreateContractRequest.ContractParameters::getOrderTypeParameters)
        .filter(Objects::nonNull)
        .map(
            orderType -> {
              if (orderType.getType() == null) {
                return null;
              }
              String type =
                  I18n.translate(STR."order.type.\{orderType.getType().name().toLowerCase()}");
              return orderType.getValue() != null ? STR."\{type} - \{orderType.getValue()}" : type;
            })
        .filter(Objects::nonNull)
        .collect(Collectors.joining(", "));
  }

  /**
   * Builds the {@link Recommendation} object based on the provided parameters.
   *
   * @param request the request containing recommendation details.
   * @param agreement the agreement associated with the recommendation.
   * @param agreementUUID the UUID of the agreement.
   * @param carrier the carrier type name.
   * @param deadline the calculated deadline.
   * @param status the status of the recommendation.
   * @return the created recommendation entity.
   */
  private Recommendation buildRecommendation(
      CreateRecommendationRequest request,
      Agreement agreement,
      UUID agreementUUID,
      String carrier,
      LocalDateTime deadline,
      RecommendationStatus status) {

    return new Recommendation(
        RecommendationId.randomId(),
        UUID.fromString(agreement.getCustomer().getId().getId()),
        agreement.getCustomer().getName().toString(),
        agreementUUID,
        request.getContract(),
        carrier,
        deadline,
        request.getPrice() != null ? request.getPrice() : "",
        request.getVolume(),
        request.getPurchaseMethod(),
        agreement.getSupplier().getName().getName(),
        agreement.isRequiresCustomerAcceptance(),
        agreement.isSendRecommendation(),
        request.getEmailTemplateComment(),
        request.getExecutor(),
        status,
        request.getAgreementGroup(),
        request.getTimeUnit(),
        new ArrayList<>(agreement.getAuthorizedBuyers()),
        getOrderTypeFromAgreement(agreement),
        agreement.getHumanReadableAgreementId().toString());
  }
}
