/* (C)2025 */
package com.codemonkeys.wallet.recommendation.application.statusflow.handlers;

import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationAction;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.email.event.EmailEvent;
import com.codemonkeys.wallet.recommendation.application.statusflow.EmailTemplateDataPreparer;
import com.codemonkeys.wallet.recommendation.application.statusflow.RecommendationBatchActionHandler;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SendBatchActionHandler extends AbstractRecommendationHandler
    implements RecommendationBatchActionHandler {

  public SendBatchActionHandler(
      CustomerRepository customerRepository,
      EmailTemplateDataPreparer emailTemplateDataPreparer,
      ApplicationEventPublisher eventPublisher) {
    super(customerRepository, emailTemplateDataPreparer, eventPublisher);
  }

  @Override
  public RecommendationAction getAction() {
    return RecommendationAction.SEND;
  }

  @Override
  public boolean handle(Recommendation recommendation) {
    throw new UnsupportedOperationException(
        "Single recommendation handling is not supported in batch handler");
  }

  /**
   * Processes a batch of recommendations grouped by contractId (agreement). Sends one email per
   * contact and agreement, combining multiple contracts into one message.
   *
   * @param recommendations list of recommendations to send
   * @return true if all emails were sent successfully; false otherwise
   */
  @Override
  public boolean handleBatch(List<Recommendation> recommendations) {
    Map<UUID, List<Recommendation>> byContractId =
        recommendations.stream().collect(Collectors.groupingBy(Recommendation::getContractId));
    boolean globalSuccess = true;
    int emails = 0;

    for (List<Recommendation> recsForContract : byContractId.values()) {
      boolean success = processContractRecommendations(recsForContract);
      if (!success) {
        globalSuccess = false;
      } else {
        emails++;
      }
    }
    log.info("Batch finished. Emails sent={} success={}", emails, globalSuccess);
    return globalSuccess;
  }

  /**
   * Sends one email per contact for all recommendations under a given contractId (agreement).
   *
   * @param recs list of recommendations belonging to the same agreement
   * @return true if all emails for the contract were sent successfully
   */
  private boolean processContractRecommendations(List<Recommendation> recs) {
    List<String> uniqueComments = mergeUniqueComments(recs);
    Customer customer = fetchCustomer(recs.get(0), CustomerId.of(recs.get(0).getCustomerId()));
    List<CustomerContact> contacts =
        validateCustomerConfiguration(getContacts(recs.get(0), customer));
    if (contacts.isEmpty()) {
      log.warn("Brak kontaktów dla contractId={}", recs.get(0).getContractId());
      return false;
    }

    contacts.forEach(contact -> sendEmail(contact, recs, uniqueComments));
    updateRecommendationStatuses(recs);
    return true;
  }

  /**
   * Collects and returns distinct, sorted comments from recommendations.
   *
   * @param recommendations list of recommendations
   * @return list of unique, sorted comments
   */
  private List<String> mergeUniqueComments(List<Recommendation> recommendations) {
    return recommendations.stream()
        .map(Recommendation::getEmailTemplateComment)
        .filter(Objects::nonNull)
        .distinct()
        .sorted()
        .toList();
  }

  /**
   * Sends an email with a list of recommendations and merged comments to a contact.
   *
   * @param contact the contact to send the email to
   * @param recs list of recommendations to include in the email
   * @param comments merged comments to include in the email body
   */
  private void sendEmail(
      CustomerContact contact, List<Recommendation> recs, List<String> comments) {
    Recommendation rep = recs.getFirst();
    Map<String, Object> vars = emailTemplateDataPreparer.prepareTemplateData(rep, contact);
    List<Map<String, Object>> rows =
        recs.stream().map(r -> emailTemplateDataPreparer.prepareTemplateData(r, contact)).toList();

    vars.put("recommendations", rows);
    vars.put("recommendationComment", String.join("\n", comments));
    log.info(
        "Preparing email to {} with {} recommendations",
        contact.getEmail().getValue(),
        rows.size());

    eventPublisher.publishEvent(
        new EmailEvent(
            this,
            contact.getEmail().getValue(),
            emailTemplateDataPreparer.determineSubject(contact, rep),
            emailTemplateDataPreparer.determineTemplateName(contact),
            vars));

    log.info(
        "E-mail sent to {} (contractId={}, rows={})",
        contact.getEmail().getValue(),
        rep.getContractId(),
        rows.size());
  }

  /**
   * Updates the status of all given recommendations after successful processing.
   *
   * @param recommendations the list of recommendations to update
   */
  private void updateRecommendationStatuses(List<Recommendation> recommendations) {
    recommendations.forEach(
        rec ->
            rec.changeStatus(
                rec.isRequiresCustomerAcceptance()
                    ? RecommendationStatus.SEND
                    : RecommendationStatus.ACCEPTED_ORDER_PENDING));

    log.info("Updated status for {} recommendations", recommendations.size());
  }
}
