/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.simulation.fullpurchase;

import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationHelper;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** Adds missing coverage (up to 100%) for property rights contracts using PMOZE_A contracts. */
@Slf4j
@Service
@RequiredArgsConstructor
public class FullPurchasePropertyRights {

  private final PriceRepository priceRepository;
  private final RecommendationHelper recommendationHelper;

  /**
   * Tops up monthly coverage with PMOZE_A if property rights contracts exist.
   *
   * @param request wallet simulation input
   */
  public void addPropertyRightsTranche(WalletSimulationRequest request) {
    Agreement agreement = recommendationHelper.findAgreementOrThrow(request.getAgreement());
    if (agreement == null) {
      log.warn(
          "No agreement found for ID: {} -> skipping property rights fill", request.getAgreement());
      return;
    }

    // 2) Check if there is any property rights contract at all
    boolean hasAnyPropertyRights =
        agreement.getContracts().stream().anyMatch(Contract::isPropertyRightsContract);
    if (!hasAnyPropertyRights) {
      log.info("Agreement {} has no property rights contracts -> skipping fill", agreement.getId());
      return;
    }

    // Find all PMOZE_A-like contracts
    List<Contract> pmozeAContracts = findAllPmozeAContracts(agreement);

    // 4) Create a map of Contract UUID -> Contract objects for quick lookups
    Map<UUID, Contract> idToContract =
        agreement.getContracts().stream()
            .collect(Collectors.toMap(c -> UUID.fromString(c.getId().getId()), c -> c));

    // 5) Fetch the price for "PMOZE_A" from the PriceRepository
    BigDecimal priceValue = getPriceValue("PMOZE_A");
    List<WalletSimulationRequest.CreateTrancheRequest> originalTranches =
        new ArrayList<>(request.getTranches());

    // oblicz pokrycie dla każdego miesiąca M1-M12, respektując szerokie zakresy (Y, Qx)
    Map<TimeUnit, BigDecimal> monthCoverage = initializeEmptyMonthCoverage();
    for (WalletSimulationRequest.CreateTrancheRequest tranche : originalTranches) {
      Contract contract = idToContract.get(tranche.getContract());
      if (contract == null || !contract.isPropertyRightsContract()) continue;

      TimeUnit timeUnit = tranche.getTimeUnit();
      if (timeUnit == null) continue;

      List<TimeUnit> coveredMonths = timeUnit.getMonths();
      for (TimeUnit month : coveredMonths) {
        BigDecimal prev = monthCoverage.getOrDefault(month, BigDecimal.ZERO);
        BigDecimal added = tranche.getSize();
        BigDecimal newValue = prev.add(added);
        monthCoverage.put(month, newValue);
        log.debug(
            "→ Tranche {}% [{}] covers {} → new total: {}%", added, timeUnit, month, newValue);
      }
    }

    // uzupełnij brakujące pokrycia tylko tam, gdzie jest < 100%
    for (TimeUnit month : TimeUnit.values()) {
      if (!month.isMonth()) continue;

      BigDecimal current = monthCoverage.getOrDefault(month, BigDecimal.ZERO);
      BigDecimal missing = BigDecimal.valueOf(100).subtract(current);
      if (missing.compareTo(BigDecimal.ZERO) > 0) {
        Contract bestMatch = findBestFittingPmozeAContract(pmozeAContracts, month);
        WalletSimulationRequest.CreateTrancheRequest newTranche =
            new WalletSimulationRequest.CreateTrancheRequest(
                LocalDate.now(),
                UUID.fromString(bestMatch.getId().getId()),
                month,
                missing,
                priceValue,
                PriceReference.DKR,
                true);
        request.getTranches().add(newTranche);
        log.info(
            "✓ {} → current={}%, missing={}%, using {}",
            month.name(), current, missing, bestMatch.getName().getValue());
      } else {
        log.info("✓ {} fully covered ({}%)", month.name(), current);
      }
    }
  }

  private Map<TimeUnit, BigDecimal> initializeEmptyMonthCoverage() {
    Map<TimeUnit, BigDecimal> map = new EnumMap<>(TimeUnit.class);
    for (TimeUnit t : TimeUnit.values()) {
      if (t.isMonth()) map.put(t, BigDecimal.ZERO);
    }
    return map;
  }

  private List<Contract> findAllPmozeAContracts(Agreement agreement) {
    List<Contract> matching =
        agreement.getContracts().stream()
            .filter(Contract::isPropertyRightsContract)
            .filter(c -> c.getName().getValue().startsWith("PMOZE_A"))
            .toList();

    if (matching.isEmpty()) {
      throw new ValidationFailedException(
          "Nie istnieje kontrakt PMOZE_A w umowie",
          List.of(
              WalletValidationMessage.of("symulacja", "Nie istnieje kontrakt PMOZE_A w umowie")));
    }

    return matching;
  }

  /** Gets latest price for given contract name. Throws if missing. */
  private BigDecimal getPriceValue(String contractName) {
    return priceRepository
        .findLatestPriceByContract(contractName)
        .orElseThrow(
            () ->
                new ValidationFailedException(
                    STR."No price found for \{contractName}",
                    List.of(
                        WalletValidationMessage.of(
                            "simulation", STR."No price entry for \{contractName}"))))
        .getValue()
        .getValue();
  }

  private Contract findBestFittingPmozeAContract(List<Contract> contracts, TimeUnit month) {
    return contracts.stream()
        .filter(c -> c.getName().getTimeUnit().getMonths().contains(month))
        .sorted(Comparator.comparingInt(c -> c.getName().getTimeUnit().getMonths().size()))
        .findFirst()
        .orElseThrow(
            () ->
                new ValidationFailedException(
                    STR."Brak kontraktu PMOZE_A pokrywającego miesiąc \{month}",
                    List.of(
                        WalletValidationMessage.of("symulacja", STR."Brak PMOZE_A dla \{month}"))));
  }

  /**
   * A simple record for pairing a request tranche and its matching contract.
   *
   * @param <A> the type of the first item
   * @param <B> the type of the second item
   */
  private record Tuple<A, B>(A first, B second) {}
}
