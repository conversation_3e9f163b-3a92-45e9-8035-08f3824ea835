/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.middleware;

import an.awesome.pipelinr.Command;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

// TODO: Wrzuciłbym do commona i reużywał w product/price pipelinie
@Slf4j
@Component
public class LoggingMiddleware implements Command.Middleware {
  @Override
  public <R, C extends Command<R>> R invoke(C command, Next<R> next) {
    log.debug("Przed wykonaniem: {}", command.getClass().getSimpleName());
    R response = next.invoke();
    log.debug("Po wykonaniu: {}", command.getClass().getSimpleName());
    return response;
  }
}
