/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.net;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.PriceHandler;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.wallets.MonthTranches;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletPrice;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NetEnergyHandler extends PriceHandler<NetEnergyCommand> {

  private Wallet process(Wallet wallet) {
    if (wallet.getMediaType() == MediaType.ENERGY) {
      List<TimeUnit> months = TimeUnit.Y.getMonths();
      MonthTranches monthlyTranches = wallet.getMonthTranches(EnergyContract.class);
      for (TimeUnit month : months) {
        List<Tranche> tranches = monthlyTranches.get(month);
        if (tranches != null) {
          BigDecimal unitPrice = calculateUnitPrice(tranches);
          if (unitPrice.compareTo(BigDecimal.ZERO) > 0) {
            List<WalletPrice> walletPrices = createPrice(wallet, ElementType.NET, month, unitPrice);
            wallet.addPrices(walletPrices);
          }
        }
      }
    }
    return wallet;
  }

  @Override
  public Wallet handle(NetEnergyCommand command) {
    Wallet wallet = command.getWallet();
    return process(wallet);
  }

  private BigDecimal calculateUnitPrice(List<Tranche> tranches) {
    BigDecimal size =
        tranches.stream().map(Tranche::getFractionalSize).reduce(BigDecimal.ZERO, BigDecimal::add);
    // BigDecimal price =
    //    tranches.stream()
    //        .map(t -> t.getPrice().multiply(t.getFractionalSize()))
    //        .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal price = BigDecimal.ZERO;
    for (Tranche t : tranches) {
      BigDecimal tmp = t.getPrice().multiply(t.getFractionalSize());
      price = price.add(tmp);
    }
    if (size.equals(BigDecimal.ZERO)) {
      // throw new OperationNotSupportedException("Size should not be 0");
      return BigDecimal.ZERO;
    }
    return price.divide(size, RoundingMode.HALF_UP);
  }

  @Override
  protected List<ElementType> getAssociatedElements() {
    return List.of(ElementType.NET);
  }
}
