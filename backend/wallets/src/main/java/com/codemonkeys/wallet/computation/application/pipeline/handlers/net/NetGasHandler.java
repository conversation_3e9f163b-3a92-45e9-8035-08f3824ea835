/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.net;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.PriceCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.PriceHandler;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.wallets.MonthTranches;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletPrice;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <PERSON><PERSON> responsible for processing gas wallet net price calculations. This component calculates
 * net prices for gas wallets on a monthly basis.
 *
 * <p>The handler:
 *
 * <ul>
 *   <li>Only processes wallets with media type GAS
 *   <li>Calculates unit prices for each month based on wallet tranches
 *   <li>Creates and adds net price entries to the wallet when valid prices are calculated
 * </ul>
 *
 * <p>The price calculation uses a weighted average approach based on tranche sizes and prices.
 *
 * @see PriceHandler
 * @see NetGasCommand
 * @see ElementType#NET
 */
@Slf4j
@Component
public class NetGasHandler extends PriceHandler<NetGasCommand> {

  /**
   * Processes a wallet to calculate and add net gas prices. Only processes wallets with media type
   * GAS. For each month, calculates unit prices from tranches and adds the resulting prices to the
   * wallet.
   *
   * @param wallet The wallet to process
   * @return The processed wallet with updated prices
   */
  private Wallet process(Wallet wallet) {
    if (wallet.getMediaType() == MediaType.GAS) {
      List<TimeUnit> months = TimeUnit.Y.getMonths();
      MonthTranches monthlyTranches = wallet.getMonthTranches(GasContract.class);
      for (TimeUnit month : months) {
        List<Tranche> tranches = monthlyTranches.get(month);
        if (tranches != null) {
          BigDecimal unitPrice = calculateUnitPrice(tranches);
          if (unitPrice.compareTo(BigDecimal.ZERO) > 0) {
            List<WalletPrice> walletPrices = createPrice(wallet, ElementType.NET, month, unitPrice);
            wallet.addPrices(walletPrices);
          }
        }
      }
    }
    return wallet;
  }

  /**
   * Handles a NetGasCommand by processing its associated wallet.
   *
   * @param command The command containing the wallet to process
   * @return The processed wallet with updated prices
   */
  @Override
  public Wallet handle(NetGasCommand command) {
    Wallet wallet = command.getWallet();
    return process(wallet);
  }

  /**
   * Performs the core processing work for the handler. Delegates to the process method for actual
   * price calculations.
   *
   * @param wallet The wallet to process
   * @param requests List of price commands (not used in this implementation)
   * @return The processed wallet with updated prices
   */
  @Override
  protected Wallet doWork(Wallet wallet, List<PriceCommand> requests) {
    return process(wallet);
  }

  /**
   * Calculates the weighted average unit price from a list of tranches. The weight is based on the
   * fractional size of each tranche.
   *
   * @param tranches List of tranches to calculate price from
   * @return The weighted average unit price, or zero if total size is zero
   */
  private BigDecimal calculateUnitPrice(List<Tranche> tranches) {
    BigDecimal size =
        tranches.stream().map(Tranche::getFractionalSize).reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal price = BigDecimal.ZERO;
    for (Tranche t : tranches) {
      BigDecimal tmp = t.getPrice().multiply(t.getFractionalSize());
      price = price.add(tmp);
    }
    if (size.equals(BigDecimal.ZERO)) {
      return BigDecimal.ZERO;
    }

    return price.divide(size, RoundingMode.HALF_UP);
  }

  /**
   * Returns the element types associated with this handler.
   *
   * @return A list containing only ElementType.NET
   */
  @Override
  protected List<ElementType> getAssociatedElements() {
    return List.of(ElementType.NET);
  }
}
