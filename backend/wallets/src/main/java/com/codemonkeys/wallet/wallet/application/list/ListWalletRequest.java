/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.list;

import com.codemonkeys.wallet.common.framework.logger.SecureLogger;
import com.codemonkeys.wallet.common.framework.shared.dto.ListRequest;
import com.codemonkeys.wallet.common.framework.shared.dto.RequestInitializer;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@EqualsAndHashCode(callSuper = false)
public class ListWalletRequest extends ListRequest {

  private static final Logger log = LoggerFactory.getLogger(ListWalletRequest.class);

  private Integer contractYear;

  /**
   * Constructs a new ListWalletRequest based on provided parameters.
   *
   * @param params the request parameters for filtering and pagination.
   */
  public ListWalletRequest(Map<String, String> params) {
    super();
    RequestInitializer.initializePagination(this, params);
    initializeSpecificFilters(params);
    RequestInitializer.initializeFilters(this, params);
    RequestInitializer.initializeSorting(this, params);
  }

  /**
   * Initializes specific filters like contractYear from the request parameters.
   *
   * @param params the request parameters.
   */
  private void initializeSpecificFilters(Map<String, String> params) {
    this.contractYear = parseContractYear(params.get("contractYear"));
  }

  /**
   * Parses the "contractYear" parameter from the request and returns its value, or null if the
   * parameter is not present or invalid.
   *
   * @param contractYearParam the contract year parameter from the request.
   * @return the parsed contract year, or null if invalid.
   */
  private Integer parseContractYear(String contractYearParam) {
    try {
      return contractYearParam != null ? Integer.parseInt(contractYearParam) : null;
    } catch (NumberFormatException e) {
      SecureLogger.warn(log, "Invalid contractYear parameter: {}. Skipping filter by year.", contractYearParam);
      return null;
    }
  }
}
