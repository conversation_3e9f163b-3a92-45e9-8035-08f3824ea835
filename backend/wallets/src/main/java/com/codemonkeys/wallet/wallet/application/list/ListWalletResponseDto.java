/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.list;

import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementGroupName;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListWalletResponseDto {
  private WalletId id;
  private String mediaType;
  private LocalDate startDate;
  private Year year;
  private AgreementResponse agreement;
  private String description;
  private String name;
  private BigDecimal averagePrice;
  private BigDecimal annualPurchaseRealization;

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class AgreementResponse {
    private String id;
    private CustomerResponse customer;
    private SupplierResponse supplier;
    private AgreementGroupResponse agreementGroup;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerResponse {
      private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupplierResponse {
      private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgreementGroupResponse {
      private AgreementGroupName name;
    }
  }
}
