/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.export;

import com.codemonkeys.wallet.common.framework.security.Permission;
import com.codemonkeys.wallet.common.framework.security.Permissions;
import com.codemonkeys.wallet.wallet.application.export.common.ExportWalletConstants;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping(ExportWalletConstants.PATH)
@RestController
@Slf4j
@AllArgsConstructor(onConstructor_ = @Autowired)
public class ExportWalletController {

  private final ExportWalletService simulateWalletService;

  /**
   * Exports the wallet simulation data provided in the request to an Excel file.
   *
   * @param walletRequest The request containing wallet simulation data to be exported.
   * @return A {@link ResponseEntity} containing the generated Excel file as a {@link
   *     ByteArrayResource}. Returns an HTTP 500 error if an exception occurs during file
   *     generation.
   */
  @PostMapping
  @Permission(Permissions.SIMULATE_WALLET)
  public ResponseEntity<ByteArrayResource> exportSimulationToExcel(
      @RequestBody WalletSimulationRequest walletRequest) {
    try {
      byte[] excelFile = simulateWalletService.processAndGenerateExcel(walletRequest);
      ByteArrayResource resource = new ByteArrayResource(excelFile);

      return ResponseEntity.ok()
          .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=simulation.xlsx")
          .contentType(MediaType.APPLICATION_OCTET_STREAM)
          .body(resource);
    } catch (Exception e) {
      log.error("Error generating Excel file: {}", e.getMessage());
      return ResponseEntity.internalServerError().build();
    }
  }
}
