/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.CalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.GreenPropertyRightsCalculationStrategy;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public class EnergaSimpleCalculationStrategy extends CalculationStrategy
    implements GreenPropertyRightsCalculationStrategy {

  @Override
  public Optional<BigDecimal> price(List<Tranche> tranches) {
    if (tranches.isEmpty()) {
      return Optional.empty();
    }
    Wallet wallet = tranches.getLast().getWallet();
    BigDecimal basePrice = basePrice(tranches, Tranche::product);
    BigDecimal priceWithCosts = applyCosts(basePrice, wallet);
    BigDecimal priceWithExcise = applyExcise(priceWithCosts, wallet);
    BigDecimal finalPrice = applyDuty(priceWithExcise, wallet.getYear().getYearAsInt());
    return Optional.of(applyIndustrialStatusDiscount(finalPrice, wallet));
  }
}
