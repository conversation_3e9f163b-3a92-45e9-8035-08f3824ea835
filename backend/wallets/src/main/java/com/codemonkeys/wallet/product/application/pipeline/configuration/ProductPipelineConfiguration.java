/* (C)2024-2025 */
package com.codemonkeys.wallet.product.application.pipeline.configuration;

import an.awesome.pipelinr.CommandHandlers;
import an.awesome.pipelinr.Notification;
import an.awesome.pipelinr.Pipeline;
import an.awesome.pipelinr.Pipelinr;
import com.codemonkeys.wallet.computation.application.pipeline.middleware.LoggingMiddleware;
import com.codemonkeys.wallet.product.application.pipeline.handlers.ProductHandler;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class ProductPipelineConfiguration {
  private final LoggingMiddleware loggingMiddleware;

  @Autowired
  public ProductPipelineConfiguration(LoggingMiddleware loggingMiddleware) {
    this.loggingMiddleware = loggingMiddleware;
  }

  @Bean(name = "productPipeline")
  Pipeline pipeline(
      ObjectProvider<ProductHandler> commandHandlers,
      ObjectProvider<Notification.Handler> notificationHandlers,
      LoggingMiddleware loggingMiddleware
  ) {
    Pipeline p =
        new Pipelinr()
            .with((CommandHandlers) () -> commandHandlers.stream().map(handler -> handler))
            .with((CommandHandlers) () -> notificationHandlers.stream().map(handler -> (an.awesome.pipelinr.Command.Handler) handler))
        .with(() -> Stream.of(loggingMiddleware));
    log.info("{}", commandHandlers);
    return p;
  }
}
