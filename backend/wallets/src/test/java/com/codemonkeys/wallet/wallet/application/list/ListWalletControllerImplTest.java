/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.list;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Collections;
import java.util.HashMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.ResponseEntity;

public class ListWalletControllerImplTest {

  private ListWalletServiceImpl listWalletService;
  private ListWalletControllerImpl listWalletController;

  @BeforeEach
  void setUp() {
    listWalletService = mock(ListWalletServiceImpl.class);
    listWalletController = new ListWalletControllerImpl(listWalletService);
  }

  @Test
  void testList() throws JsonProcessingException {
    ListWalletRequest request = new ListWalletRequest(new HashMap<>());
    Page<ListWalletResponseDto> page = new PageImpl<>(Collections.emptyList());

    when(listWalletService.customList(request)).thenReturn(page);

    ResponseEntity<Page<ListWalletResponseDto>> response =
        listWalletController.list(new HashMap<>());

    assertEquals(page, response.getBody());
    verify(listWalletService, times(1)).customList(request);
  }
}
