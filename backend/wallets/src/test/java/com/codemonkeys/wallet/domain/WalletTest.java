/* (C)2024-2025 */
package com.codemonkeys.wallet.domain;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.wallets.Product;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.ProductType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.ProductId;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class WalletTest {

  @Mock private Agreement agreement;

  private Wallet wallet;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

    wallet =
        new Wallet(
            MediaType.ENERGY,
            agreement,
            LocalDate.now(),
            Year.of(2024),
            new HashSet<>(),
            new HashSet<>(),
            "Test Wallet",
            null);
  }

  @Test
  void hasConfirmedRealisation_shouldReturnFalse_whenNoProductsExist() {
    // When
    boolean result = wallet.hasConfirmedRealisation(TimeUnit.M1, Media.ENERGY);

    // Then
    assertFalse(result, "Should return false when no products exist");
  }

  @Test
  void hasConfirmedRealisation_shouldReturnFalse_whenProductsExistButNoneConfirmed() {
    // Given
    Product product =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M1,
            BigDecimal.TEN,
            Media.ENERGY);
    product.setConfirmed(false);
    wallet.addProduct(product);

    // When
    boolean result = wallet.hasConfirmedRealisation(TimeUnit.M1, Media.ENERGY);

    // Then
    assertFalse(result, "Should return false when products exist but none are confirmed");
  }

  @Test
  void hasConfirmedRealisation_shouldReturnTrue_whenAllProductsAreConfirmed() {
    // Given
    Product product1 =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M1,
            BigDecimal.TEN,
            Media.ENERGY);
    product1.setConfirmed(true);

    Product product2 =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M1,
            BigDecimal.ONE,
            Media.ENERGY);
    product2.setConfirmed(true);

    wallet.addProduct(product1);
    wallet.addProduct(product2);

    // When
    boolean result = wallet.hasConfirmedRealisation(TimeUnit.M1, Media.ENERGY);

    // Then
    assertTrue(result, "Should return true when at least one product is confirmed");
  }

  @Test
  void hasConfirmedRealisation_shouldCheckIfAllMonthsInTimeUnitAndConfirmedAndFail() {
    // Given
    Product product1 =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M1,
            BigDecimal.TEN,
            Media.ENERGY);
    product1.setConfirmed(false);

    Product product2 =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M2,
            BigDecimal.ONE,
            Media.ENERGY);
    product2.setConfirmed(true);

    wallet.addProduct(product1);
    wallet.addProduct(product2);

    // When
    boolean resultForM1 = wallet.hasConfirmedRealisation(TimeUnit.M1, Media.ENERGY);
    boolean resultForM2 = wallet.hasConfirmedRealisation(TimeUnit.M2, Media.ENERGY);
    boolean resultForQ1 = wallet.hasConfirmedRealisation(TimeUnit.Q1, Media.ENERGY);

    // Then
    assertFalse(resultForM1, "M1 should return false as its product is not confirmed");
    assertTrue(resultForM2, "M2 should return true as its product is confirmed");
    assertFalse(
        resultForQ1,
        "Q1 should return true as it contains M1, M2, M3 and M2 has a confirmed product");
  }

  @Test
  void hasConfirmedRealisation_shouldCheckIfAllMonthsInTimeUnitAndConfirmedAndSuccess() {
    // Given
    Product product1 =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M1,
            BigDecimal.TEN,
            Media.ENERGY);
    product1.setConfirmed(true);

    Product product2 =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M2,
            BigDecimal.ONE,
            Media.ENERGY);
    product2.setConfirmed(true);

    Product product3 =
        new Product(
            ProductId.randomId(),
            wallet,
            ProductType.VOLUME,
            TimeUnit.M3,
            BigDecimal.ONE,
            Media.ENERGY);
    product3.setConfirmed(true);
    wallet.addProduct(product1);
    wallet.addProduct(product2);
    wallet.addProduct(product3);

    // When
    boolean resultForM1 = wallet.hasConfirmedRealisation(TimeUnit.M1, Media.ENERGY);
    boolean resultForM2 = wallet.hasConfirmedRealisation(TimeUnit.M2, Media.ENERGY);
    boolean resultForM3 = wallet.hasConfirmedRealisation(TimeUnit.M3, Media.ENERGY);
    boolean resultForQ1 = wallet.hasConfirmedRealisation(TimeUnit.Q1, Media.ENERGY);

    // Then
    assertTrue(resultForM1, "M1 should return false as its product is not confirmed");
    assertTrue(resultForM2, "M2 should return true as its product is confirmed");
    assertTrue(resultForM3, "M2 should return true as its product is confirmed");
    assertTrue(
        resultForQ1,
        "Q1 should return true as it contains M1, M2, M3 and M2 has a confirmed product");
  }
}
