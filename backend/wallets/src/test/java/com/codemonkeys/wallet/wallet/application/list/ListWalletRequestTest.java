/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.list;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

public class ListWalletRequestTest {

  @Test
  void testConstructor() {
    Map<String, String> params = new HashMap<>();
    params.put("page", "2");
    params.put("limit", "10");

    ListWalletRequest request = new ListWalletRequest(params);

    assertEquals(2, request.getPage());
    assertEquals(10, request.getLimit());
  }

  /*    @Test
  void testInvalidPage() {
      Map<String, String> params = new HashMap<>();
      params.put("page", "invalid");

      ListWalletRequest request = new ListWalletRequest(params);

      assertEquals(1, request.getPage());
  }*/

  @Test
  void testInvalidLimit() {
    Map<String, String> params = new HashMap<>();
    params.put("limit", "invalid");

    ListWalletRequest request = new ListWalletRequest(params);

    assertEquals(1, request.getLimit());
  }

  @Test
  void testGetZeroBasedPage() {
    Map<String, String> params = new HashMap<>();
    params.put("page", "3");

    ListWalletRequest request = new ListWalletRequest(params);

    // assertEquals(2, request.getZeroBasedPage());
  }
}
