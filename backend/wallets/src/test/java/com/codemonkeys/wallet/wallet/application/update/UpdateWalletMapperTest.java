/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.update;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.lenient;

import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.wallet.factories.WalletTestFactory;
import java.util.List;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

@ExtendWith(MockitoExtension.class)
class UpdateWalletMapperTest {

  @InjectMocks private UpdateWalletMapper mapper;

  @Mock private MessageSource messageSource;

  @BeforeEach
  void setUp() {
    I18n.setMessageSource(messageSource);
    lenient()
        .when(messageSource.getMessage("validations.wallet.name.unique", null, Locale.getDefault()))
        .thenReturn("Wallet name must be unique");
  }

  @Test
  void toResponseShouldReturnValidResponseWhenWalletIsProvided() {
    Wallet wallet = WalletTestFactory.Valid.createExampleWallet();
    UpdateWalletResponse response = mapper.toResponse(wallet);

    assertNotNull(response);
    assertTrue(response.isValid());
    assertEquals(wallet, response.getWallet());
  }

  @Test
  void toResponseShouldReturnInvalidResponseWhenErrorsAreProvided() {
    String validationMessage = I18n.translate("validations.wallet.name.unique");
    InvalidResult errors =
        InvalidResult.of(List.of(WalletValidationMessage.of("name", validationMessage)));
    UpdateWalletResponse response = mapper.toResponse(errors);

    assertNotNull(response);
    assertFalse(response.isValid());
    assertNull(response.getWallet());
    assertTrue(
        response.getValidation().getMessages().stream()
            .anyMatch(msg -> msg.getReason().equals(validationMessage)));
  }
}
