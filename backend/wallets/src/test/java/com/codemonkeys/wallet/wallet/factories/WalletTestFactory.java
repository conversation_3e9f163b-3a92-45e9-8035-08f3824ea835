/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.factories;

import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.wallet.application.create.CreateWalletRequest;
import java.util.HashSet;

public class WalletTestFactory {

  public static class Valid {
    public static Wallet createExampleWallet() {
      return fromCreateWalletRequest(WalletRequestTestFactory.Valid.createExampleWalletRequest());
    }

    public static Wallet fromCreateWalletRequest(CreateWalletRequest request) {
      return new Wallet(
          request.getMediaType(),
          null,
          request.getStartDate(),
          request.getYear(),
          new HashSet<>(), // elements
          new HashSet<>(), // prices
          request.getDescription(),
          request.getGreenPropertyRightCalculationType());
    }
  }
}
