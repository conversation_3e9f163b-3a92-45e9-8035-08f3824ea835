/* (C)2025 */
package com.codemonkeys.wallet.simulation.fullpurchase;

import com.codemonkeys.wallet.analytical.AnalyticalCalculationService;
import com.codemonkeys.wallet.analytical.response.AnalyticalResponse;
import com.codemonkeys.wallet.common.DashboardTrancheMapper;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.find.DashboardTrancheDto;
import com.codemonkeys.wallet.simulation.DashboardSimulationRequest;
import com.codemonkeys.wallet.simulation.DashboardSimulationResponse;
import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class DashboardFullSimulationMapper {

  private final ContractRepository contractRepository;
  private final AgreementRepository agreementRepository;
  private final DashboardTrancheMapper trancheMapper;
  private final AnalyticalCalculationService analyticalCalculationService;

  /**
   * Maps DashboardSimulationRequest to Wallet domain object. Builds Wallet with tranches, elements,
   * and related agreement.
   *
   * @param request simulation input request
   * @return mapped Wallet domain object
   */
  public Wallet toDomain(DashboardSimulationRequest request) {
    Set<Tranche> tranches =
        request.getTranches().stream().map(this::mapToTranche).collect(Collectors.toSet());

    Set<Element> elements =
        request.getElements().stream().map(this::mapToElement).collect(Collectors.toSet());

    Agreement agreement =
        agreementRepository
            .findById(AgreementId.of(request.getAgreement()))
            .orElseThrow(EntityNotFoundException::new);

    return new Wallet(
        agreement.getMediaType(),
        agreement,
        request.getStartDate(),
        Year.of(request.getYear().getYear()),
        tranches,
        elements,
        null,
        null);
  }

  /**
   * Maps Wallet domain object to DashboardSimulationResponse DTO. Includes analytical calculation
   * and mapped tranches.
   *
   * @param wallet Wallet domain object
   * @return response DTO containing tranches and analytics
   */
  public DashboardSimulationResponse toDto(Wallet wallet) {
    AnalyticalResponse analyticalData =
        analyticalCalculationService.calculateAnalyticalTable(wallet);

    List<DashboardTrancheDto> trancheDtos =
        wallet.getTranches().stream()
            .filter(
                tranche ->
                    tranche.getContract() == null
                        || !tranche.getContract().isPropertyRightsContract())
            .map(trancheMapper::mapTranche)
            .collect(Collectors.toList());

    return new DashboardSimulationResponse(wallet, trancheDtos, analyticalData);
  }

  /**
   * Maps a single tranche request to Tranche domain object. Resolves contract from repository and
   * sets tranche metadata.
   *
   * @param req tranche request data
   * @return Tranche domain object
   */
  public Tranche mapToTranche(DashboardSimulationRequest.CreateTrancheRequest req) {
    Contract contract =
        contractRepository
            .findById(ContractId.of(req.getContract()))
            .orElseThrow(EntityNotFoundException::new);

    Tranche tranche =
        new Tranche(
            TrancheId.randomId(),
            null,
            req.getExecutionDate(),
            req.getTimeUnit(),
            req.getSize(),
            req.getPrice(),
            req.getPriceReference(),
            contract,
            req.isVirtual());

    tranche.setRecommendation(req.isRecommendation());
    return tranche;
  }

  /**
   * Maps a single element request to Element domain object.
   *
   * @param e element request data
   * @return Element domain object
   */
  private Element mapToElement(DashboardSimulationRequest.CreateElementRequest e) {
    return new Element(null, e.getType(), e.getTimeUnit(), e.getValue(), Media.ENERGY);
  }
}
