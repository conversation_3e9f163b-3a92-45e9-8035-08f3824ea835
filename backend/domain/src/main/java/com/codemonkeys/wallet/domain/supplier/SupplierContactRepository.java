/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.supplier;

import com.codemonkeys.wallet.common.framework.domain.vo.ContactId;
import com.codemonkeys.wallet.common.framework.domain.vo.Email;
import com.codemonkeys.wallet.common.framework.shared.persistence.CommonRepository;
import com.codemonkeys.wallet.common.framework.shared.persistence.ExcludedFromTenantFilter;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import jakarta.transaction.Transactional;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SupplierContactRepository
    extends CommonRepository<SupplierContact, ContactId>,
        ExcludedFromTenantFilter,
        JpaSpecificationExecutor<SupplierContact> {

  @Transactional
  @Modifying
  @Query("DELETE FROM SupplierContact sc WHERE sc.supplier.id = :supplierId")
  void deleteAllBySupplierId(SupplierId supplierId);

  Optional<SupplierContact> findBySupplierAndEmail(Supplier supplier, Email email);

  Optional<SupplierContact> findBySupplierAndEmailAndCustomer(
      Supplier supplier, Email email, Customer customer);
}
