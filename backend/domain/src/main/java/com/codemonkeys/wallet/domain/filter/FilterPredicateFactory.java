/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.filter;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.FilterPredicateFactoryInterface;
import com.codemonkeys.wallet.common.framework.logger.SecureLogger;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FilterPredicateFactory implements FilterPredicateFactoryInterface {

  private static final Logger log = LoggerFactory.getLogger(FilterPredicateFactory.class);

  private static final Set<String> BASIC_TYPES =
      Set.of("String", "UUID", "LocalDateTime", "Boolean");
  private final ValueObjectPredicate valueObjectPredicate;

  @Override
  public Predicate createPredicate(Path<?> path, Object value, CriteriaBuilder criteriaBuilder) {
    if (value == null) {
      return criteriaBuilder.isNull(path);
    }

    if (path.getJavaType().isEnum()) {
      SecureLogger.info(log, "Processing Enum: {}, value: {}", path.getJavaType(), value);
      return handleEnumPredicate(path, value, criteriaBuilder);
    }
    String typeName = path.getJavaType().getSimpleName();
    if (BASIC_TYPES.contains(typeName)) {
      return switch (typeName) {
        case "String" -> handleStringPredicate(path, value, criteriaBuilder);
        case "UUID" -> handleUUIDPredicate(path, value, criteriaBuilder);
        case "Boolean" -> handleBooleanPredicate(path, value, criteriaBuilder);
        case "LocalDateTime" ->
            handleLocalDateTimePredicate(path, value.toString(), criteriaBuilder);
        default -> criteriaBuilder.equal(path, value);
      };
    }

    return valueObjectPredicate.handleValueObjectPredicate(path, value, criteriaBuilder);
  }

  private Predicate handleStringPredicate(Path<?> path, Object value, CriteriaBuilder cb) {
    return cb.like(cb.lower((Path<String>) path), "%" + value.toString().toLowerCase() + "%");
  }

  private Predicate handleUUIDPredicate(Path<?> path, Object value, CriteriaBuilder cb) {
    try {
      UUID uuid = UUID.fromString(value.toString());
      return cb.equal(path, uuid);
    } catch (IllegalArgumentException e) {
      SecureLogger.error(log, "Invalid UUID value: {}", value);
      return cb.conjunction();
    }
  }

  private Predicate handleEnumPredicate(Path<?> path, Object value, CriteriaBuilder cb) {
    try {
      Class<? extends Enum> enumClass = (Class<? extends Enum>) path.getJavaType();

      /// Check if the value is a string that can be converted to a number
      if (value instanceof String && ((String) value).matches("\\d+")) {
        value = Integer.parseInt((String) value);
      }

      if (value instanceof Number) {
        int ordinalValue = ((Number) value).intValue();
        SecureLogger.info(log, "Enum path: {}, value: {}, detected as ORDINAL", path, value);

        Enum[] enumConstants = enumClass.getEnumConstants();
        if (enumConstants == null || ordinalValue < 0 || ordinalValue >= enumConstants.length) {
          SecureLogger.error(log,
              "Invalid ordinal value: {} for enum type: {}",
              ordinalValue,
              enumClass.getSimpleName());
          return cb.conjunction();
        }
        Enum enumValue = enumConstants[ordinalValue];
        return cb.equal(path, enumValue);
      } else {
        Enum enumValue = Enum.valueOf(enumClass, value.toString().trim().toUpperCase());
        SecureLogger.info(log, "Enum path: {}, value: {}, detected as STRING", path, value);
        return cb.equal(path, enumValue);
      }
    } catch (IllegalArgumentException e) {
      SecureLogger.error(log, "Invalid Enum value: {} for type: {}", value, path.getJavaType().getSimpleName());
      return cb.conjunction();
    }
  }

  private Predicate handleLocalDateTimePredicate(
      Path<?> path, String dateValue, CriteriaBuilder cb) {
    try {
      DateTimeFormatter formatter;
      LocalDateTime startDate;
      LocalDateTime endDate;

      if (dateValue.length() == 7 && dateValue.matches("\\d{2}-\\d{4}")) {
        // MM-yyyy
        formatter = DateTimeFormatter.ofPattern("MM-yyyy");
        YearMonth ym = YearMonth.parse(dateValue, formatter);
        startDate = ym.atDay(1).atStartOfDay();
        endDate = ym.atEndOfMonth().atTime(23, 59, 59, 999_999_999);

      } else if (dateValue.length() == 4 && dateValue.matches("\\d{4}")) {
        // yyyy
        int year = Integer.parseInt(dateValue);
        startDate = LocalDate.of(year, 1, 1).atStartOfDay();
        endDate = LocalDate.of(year, 12, 31).atTime(23, 59, 59, 999_999_999);

      } else if (dateValue.length() == 8) {
        formatter = DateTimeFormatter.ofPattern("dd-MM-yy");
        startDate = LocalDate.parse(dateValue, formatter).atStartOfDay();
        endDate = startDate.plusDays(1).minusNanos(1);

        // dd-MM-yy HH (11 znaków)
      } else if (dateValue.length() == 11) {
        formatter = DateTimeFormatter.ofPattern("dd-MM-yy HH");
        startDate = LocalDateTime.parse(dateValue, formatter).withMinute(0).withSecond(0);
        endDate = startDate.plusHours(1).minusNanos(1);

        // dd-MM-yy HH:mm (14 znaków)
      } else if (dateValue.length() == 14) {
        formatter = DateTimeFormatter.ofPattern("dd-MM-yy HH:mm");
        startDate = LocalDateTime.parse(dateValue, formatter).withSecond(0);
        endDate = startDate.plusMinutes(1).minusNanos(1);

        // 10 znaków: może być dd-MM-yyyy lub yyyy-MM-dd
      } else if (dateValue.length() == 10) {
        // czy znak na pozycji 4 to '-'
        if (dateValue.charAt(4) == '-') {
          formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
          startDate = LocalDate.parse(dateValue, formatter).atStartOfDay();
        } else {
          formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
          startDate = LocalDate.parse(dateValue, formatter).atStartOfDay();
        }
        endDate = startDate.plusDays(1).minusNanos(1);

        // dd-MM-yyyy HH (13 znaków) lub yyyy-MM-dd HH
      } else if (dateValue.length() == 13) {
        if (dateValue.charAt(4) == '-') {
          // yyyy-MM-dd HH
          formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
        } else {
          // dd-MM-yyyy HH
          formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH");
        }
        startDate = LocalDateTime.parse(dateValue, formatter).withMinute(0).withSecond(0);
        endDate = startDate.plusHours(1).minusNanos(1);

        // dd-MM-yyyy HH:mm (16 znaków) lub yyyy-MM-dd HH:mm
      } else if (dateValue.length() == 16) {
        if (dateValue.charAt(4) == '-') {
          // yyyy-MM-dd HH:mm
          formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        } else {
          formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm");
        }
        startDate = LocalDateTime.parse(dateValue, formatter).withSecond(0);
        endDate = startDate.plusMinutes(1).minusNanos(1);
      } else {
        // dd-MM-yyyy HH:mm:ss lub yyyy-MM-dd HH:mm:ss
        if (dateValue.length() == 19 && dateValue.charAt(4) == '-') {
          // yyyy-MM-dd HH:mm:ss
          formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        } else {
          formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        }
        startDate = LocalDateTime.parse(dateValue, formatter);
        endDate = startDate.plusSeconds(1).minusNanos(1);
      }

      Path<LocalDateTime> datePath = (Path<LocalDateTime>) path;
      return cb.between(datePath, startDate, endDate);
    } catch (Exception e) {
      SecureLogger.error(log, "Invalid LocalDateTime value: {}", dateValue);
      return cb.conjunction();
    }
  }

  private Predicate handleBooleanPredicate(Path<?> path, Object value, CriteriaBuilder cb) {
    boolean boolValue = Boolean.parseBoolean(value.toString());
    return cb.equal(path, boolValue);
  }
}
