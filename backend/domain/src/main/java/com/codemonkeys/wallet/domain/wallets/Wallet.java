/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.results.Result;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.GreenPropertyRightCalculationType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.ProductType;
import com.codemonkeys.wallet.domain.wallets.enums.RealisationType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.SortComparator;
import org.hibernate.annotations.Type;
import org.jetbrains.annotations.NotNull;
import org.jmolecules.ddd.types.AggregateRoot;
import org.springframework.data.util.Pair;

@Getter
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "WALLETS")
@Slf4j
public class Wallet extends IdentifiableAggregateRoot<WalletId>
    implements AggregateRoot<Wallet, WalletId> {

  private static final BigDecimal VOLUME_MAX = BigDecimal.valueOf(100);
  @EmbeddedId private WalletId id;

  @SortComparator(TrancheComparator.class)
  @OneToMany(
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY,
      mappedBy = "wallet")
  private SortedSet<Tranche> tranches = new TreeSet<>(new TrancheComparator());

  @OneToMany(
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY,
      mappedBy = "wallet")
  private Set<Element> elements = new HashSet<>();

  @OneToMany(
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY,
      mappedBy = "wallet")
  private Set<WalletPrice> prices = new HashSet<>();

  @OneToMany(
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY,
      mappedBy = "wallet")
  private Set<Product> products = new HashSet<>();

  @OneToMany(
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY,
      mappedBy = "wallet")
  private Set<Result> results = new HashSet<>();

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "media_type", nullable = false)
  private MediaType mediaType;

  @NotNull
  @Column(name = "start_date", nullable = false)
  private LocalDate startDate;

  @NotNull
  @Column(name = "year", nullable = false)
  private Year year;

  @Setter
  @Column(name = "description", length = 150)
  private String description;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "agreement_id", nullable = false)
  private Agreement agreement;

  @Setter
  @Enumerated(EnumType.STRING)
  @Column(name = "green_property_right_calculation_type")
  private GreenPropertyRightCalculationType greenPropertyRightCalculationType;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  @Setter
  @Column(name = "analytical_settings", columnDefinition = "jsonb")
  @Type(JsonType.class)
  private JsonNode analyticalSettings;

  public Wallet(Wallet wallet) {
    this.id = WalletId.randomId();
    this.mediaType = wallet.getMediaType();
    this.startDate = wallet.getStartDate();
    this.year = wallet.getYear();
    this.description = wallet.getDescription();
    this.agreement = wallet.getAgreement();
    this.greenPropertyRightCalculationType = wallet.getGreenPropertyRightCalculationType();

    this.tranches = new TreeSet<>(new TrancheComparator());
    for (Tranche t : wallet.getTranches()) {
      this.tranches.add(new Tranche(t));
    }

    this.elements = new HashSet<>();
    for (Element element : wallet.getElements()) {
      this.elements.add(new Element(element));
    }

    this.prices = new HashSet<>();
    for (WalletPrice price : wallet.getPrices()) {
      this.prices.add(new WalletPrice(price));
    }

    this.products = new HashSet<>();
    for (Product product : wallet.getProducts()) {
      this.products.add(new Product(product));
    }
  }

  public Wallet(
      // @NonNull WalletName name,
      @NonNull MediaType mediaType,
      Agreement agreement,
      LocalDate startDate,
      Year year,
      Set<Tranche> tranches,
      Set<Element> elements,
      String description,
      GreenPropertyRightCalculationType type) {
    this.id = WalletId.randomId();
    // this.name = name;
    this.mediaType = mediaType;
    this.startDate = startDate;
    this.year = year;
    this.description = description;
    this.agreement = agreement;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
    if (tranches != null) {
      tranches.forEach(t -> t.setWallet(this));
      this.tranches = new TreeSet<>(new TrancheComparator());
      this.tranches.addAll(tranches);
    } else {
      this.tranches = new TreeSet<>(new TrancheComparator());
    }
    if (elements != null) {
      elements.forEach(t -> t.setWallet(this));
      this.elements = elements;
    } else {
      this.elements = new HashSet<>();
    }
    this.greenPropertyRightCalculationType = type;
  }

  private static @NotNull Predicate<WalletPrice> byTypeAndTimeUnit(WalletPrice wp) {
    return p -> p.getType().equals(wp.getType()) && p.getTimeUnit().equals(wp.getTimeUnit());
  }

  private static @NotNull Predicate<Product> byTypeAndTimeUnitAndMedia(Product product) {
    return p ->
        p.getType().equals(product.getType())
            && p.getTimeUnit().equals(product.getTimeUnit())
            && p.getMedia().equals(product.getMedia());
  }

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    if (agreement != null) {
      this.tenantId =
          Optional.ofNullable(TenantContext.getTenantId())
              .filter(id -> !id.isEmpty())
              .orElseGet(
                  () ->
                      Optional.ofNullable(agreement.getTenantId())
                          .orElseThrow(() -> new IllegalStateException("Tenant ID must be set.")));
    } else {
      throw new IllegalStateException("Agreement must be set in Wallet.");
    }
  }

  @NotNull
  private Map<ContractType, BigDecimal> volumeBoughtByContractType(
      TimeUnit month, MonthProducts products, Media media) {
    Map<ContractType, BigDecimal> count = Maps.newHashMap();
    Map<RealisationType, List<Pair<ContractType, ProductType>>> tmp = Maps.newHashMap();
    List<Pair<ContractType, ProductType>> pairs = getPairs(media);
    for (Pair<ContractType, ProductType> pair : pairs) {
      count.put(
          pair.getFirst(),
          products
              .get(month, media, pair.getSecond())
              .map(Product::getValue)
              .orElse(BigDecimal.ZERO));
    }
    return count;
  }

  private List<Pair<ContractType, ProductType>> getPairs(Media media) {
    return switch (media) {
      case ENERGY ->
          List.of(
              Pair.of(ContractType.Y, ProductType.VOLUME_Y),
              Pair.of(ContractType.Q, ProductType.VOLUME_Q),
              Pair.of(ContractType.M, ProductType.VOLUME_M),
              Pair.of(ContractType.TGe24, ProductType.VOLUME_SPOT));
      case GAS ->
          List.of(
              Pair.of(ContractType.Y, ProductType.VOLUME_Y),
              Pair.of(ContractType.Q, ProductType.VOLUME_Q),
              Pair.of(ContractType.M, ProductType.VOLUME_M),
              Pair.of(ContractType.TGEgasID, ProductType.VOLUME_SPOT),
              Pair.of(ContractType.TGEgasDA, ProductType.VOLUME_SPOT));
      case GREEN_PROPERTY_RIGHTS -> List.of(Pair.of(ContractType.PMOZE_A, ProductType.VOLUME));
      default -> List.of();
    };
  }

  @Override
  public WalletId getId() {
    return id;
  }

  public void addProduct(Product product) {
    // Zabezpieczenie przed podwojnym dodaniem produktu ze wzgledu na potwierdzanie cen
    Optional<Product> maybeProduct =
        getProducts().stream().filter(byTypeAndTimeUnitAndMedia(product)).findAny();
    if (maybeProduct.isEmpty()) {
      product.setWallet(this);
      this.products.add(product);
    }
  }

  public void addProducts(List<Product> products) {
    for (Product product : products) {
      // Zabezpieczenie przed podwojnym dodaniem produktu ze wzgledu na potwierdzanie cen
      Optional<Product> maybeProduct =
          getProducts().stream().filter(byTypeAndTimeUnitAndMedia(product)).findAny();
      if (maybeProduct.isEmpty()) {
        product.setWallet(this);
        this.products.add(product);
      }
    }
    // products.forEach(p -> p.setWallet(this));
    // this.products.addAll(products);
  }

  public void addTranche(Tranche tranche) {
    tranche.setWallet(this);
    this.tranches.add(tranche);
  }

  public void removeTranche(TrancheId trancheId) {
    tranches.removeIf(t -> t.getId().equals(trancheId));
  }

  public void addTranches(List<Tranche> tranches) {
    tranches.forEach(t -> t.setWallet(this));
    this.tranches.addAll(tranches);
  }

  public void addElement(Element element) {
    element.setWallet(this);
    this.elements.add(element);
  }

  public void addElements(List<Element> elements) {
    elements.forEach(e -> e.setWallet(this));
    this.elements.addAll(elements);
  }

  public void addPrice(WalletPrice price) {
    price.setWallet(this);
    this.prices.add(price);
  }

  public void setAgreement(Agreement agreement) {
    // TODO?: agreement.addWallet(this);
    this.agreement = agreement;
  }

  public void addPrices(List<WalletPrice> price) {
    for (WalletPrice wp : price) {
      Optional<WalletPrice> maybePrice = prices.stream().filter(byTypeAndTimeUnit(wp)).findAny();
      if (maybePrice.isEmpty()) {
        wp.setWallet(this);
        this.prices.add(wp);
      }
    }
  }

  public Optional<WalletPrice> getPrice(ElementType type) {
    return prices.stream().filter(wp -> wp.getType().equals(type)).findFirst();
  }

  public List<WalletPrice> getPrices(ElementType type) {
    return prices.stream().filter(wp -> wp.getType().equals(type)).toList();
  }

  public List<WalletPrice> getPrices(TimeUnit timeUnit) {
    return prices.stream().filter(wp -> wp.getTimeUnit().equals(timeUnit)).toList();
  }

  public List<WalletPrice> getPrices(TimeUnit timeUnit, ElementType type) {
    return prices.stream()
        .filter(wp -> wp.getTimeUnit().equals(timeUnit) && wp.getType().equals(type))
        .toList();
  }

  public WalletPrice getPrice(TimeUnit timeUnit, ElementType type) {
    for (WalletPrice price : getPrices(timeUnit)) {
      if (price.getType().equals(type)) {
        return price;
      }
    }
    return null;
  }

  public List<WalletPrice> getPrices(TimeUnit timeUnit, List<ElementType> types) {
    return prices.stream()
        .filter(wp -> wp.getTimeUnit().equals(timeUnit) && types.contains(wp.getType()))
        .toList();
  }

  public List<Element> getElements(ElementType type) {
    return elements.stream().filter(e -> e.getType().equals(type)).toList();
  }

  public Optional<Element> getElement(ElementType type) {
    return elements.stream().filter(e -> e.getType().equals(type)).findFirst();
  }

  public Optional<Element> getElement(ElementType type, TimeUnit tu) {
    return elements.stream()
        .filter(e -> e.getType().equals(type) && e.getTimeUnit().equals(tu))
        .findFirst();
  }

  public List<Element> getElements(TimeUnit timeUnit, Media type) {
    return elements.stream()
        .filter(e -> e.getTimeUnit().equals(timeUnit) && e.getMedia().equals(type))
        .toList();
  }

  public List<Element> getElements(ElementType type, TimeUnit timeUnit) {
    return elements.stream()
        .filter(e -> e.getTimeUnit().equals(timeUnit) && e.getType().equals(type))
        .toList();
  }

  // todo: remove after refactor to getMonthElements
  public Map<TimeUnit, List<Element>> getMonthlyElements(Media type) {
    Map<TimeUnit, List<Element>> result = new HashMap<>();
    List<TimeUnit> processableTimeUnits =
        Arrays.asList(TimeUnit.Y, TimeUnit.Q1, TimeUnit.Q2, TimeUnit.Q3, TimeUnit.Q4);
    List<Element> elements2 =
        elements.stream().filter(e -> e.getMedia().equals(type)).collect(Collectors.toList());
    for (Element element : elements2) {
      if (processableTimeUnits.contains(element.getTimeUnit())) {
        mapTimeUnitToMonthResult(result, element);
      } else {
        addTrancheToMonthResult(result, element.getTimeUnit(), element);
      }
    }
    return result;
  }

  // todo: remove after refactor to getMonthElements
  private void mapTimeUnitToMonthResult(Map<TimeUnit, List<Element>> result, Element elem) {
    List<TimeUnit> months = elem.getTimeUnit().getMonths();
    for (TimeUnit month : months) {
      addTrancheToMonthResult(result, month, elem);
    }
  }

  private void addTrancheToMonthResult(
      Map<TimeUnit, List<Element>> result, TimeUnit timeUnit, Element ele) {
    result.computeIfAbsent(timeUnit, key -> new ArrayList<>()).add(ele);
  }

  public List<Element> getElements(List<ElementType> types) {
    return elements.stream().filter(e -> types.contains(e.getType())).toList();
  }

  public List<Tranche> getTranches(TimeUnit timeUnit) {
    return tranches.stream()
        .filter(
            t -> t.getTimeUnit().equals(timeUnit) || t.getTimeUnit().getMonths().contains(timeUnit))
        .toList();
  }

  public List<Product> getProducts(TimeUnit timeUnit) {
    return products.stream().filter(t -> t.getTimeUnit().equals(timeUnit)).toList();
  }

  public Map<TimeUnit, List<Product>> monthlyProducts() {
    Map<TimeUnit, List<Product>> result = new HashMap<>();
    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      result.put(month, getProducts(month));
    }
    return result;
  }

  @JsonIgnore
  public MonthProducts getMonthProducts() {
    return new MonthProducts(products.stream().toList());
  }

  @JsonIgnore
  public MonthPrices getMonthPrices() {
    return new MonthPrices(prices.stream().toList());
  }

  @JsonIgnore
  public MonthProducts getMonthProducts(Media media) {
    return new MonthProducts(
        products.stream().filter(p -> Objects.equals(p.getMedia(), media)).toList());
  }

  @JsonIgnore
  public MonthResults getMonthResults() {
    return new MonthResults(results.stream().toList());
  }

  @JsonIgnore
  public MonthResults getMonthResults(Media media) {
    return new MonthResults(
        results.stream().filter(p -> Objects.equals(p.getMediaType(), media)).toList());
  }

  public Map<TimeUnit, List<Tranche>> monthlyTranches() {
    Map<TimeUnit, List<Tranche>> result = new HashMap<>();
    List<TimeUnit> processableTimeUnits =
        Arrays.asList(TimeUnit.Y, TimeUnit.Q1, TimeUnit.Q2, TimeUnit.Q3, TimeUnit.Q4);
    for (Tranche tranche : tranches) {
      if (processableTimeUnits.contains(tranche.getTimeUnit())) {
        mapTimeUnitToMonthResult(result, tranche);
      } else {
        addTrancheToMonthResult(result, tranche.getTimeUnit(), tranche);
      }
    }
    return result;
  }

  private void mapTimeUnitToMonthResult(Map<TimeUnit, List<Tranche>> result, Tranche tranche) {
    List<TimeUnit> months = tranche.getTimeUnit().getMonths();
    for (TimeUnit month : months) {
      addTrancheToMonthResult(result, month, tranche);
    }
  }

  private void addTrancheToMonthResult(
      Map<TimeUnit, List<Tranche>> result, TimeUnit timeUnit, Tranche tranche) {
    result.computeIfAbsent(timeUnit, key -> new ArrayList<>()).add(tranche);
  }

  @JsonIgnore
  public MonthTranches getMonthTranches() {
    return new MonthTranches(tranches.stream().toList());
  }

  @JsonIgnore
  public MonthTranches getMonthTranches(Class<?> contractClassType) {
    List<Tranche> tts =
        tranches.stream()
            .filter(t -> t.getContract().getClass().equals(contractClassType))
            .toList();
    return new MonthTranches(tts);
  }

  public BigDecimal getAgreementReferenceVolume(TimeUnit timeUnit) {
    return getAgreement().getVolumes().getTimeUnitValue(timeUnit);
  }

  public String getCustomerName() {
    return this.getAgreement().getCustomer().getName().getName();
  }

  public String getName() {
    return getDescription() != null
        ? String.format(
            "%s - %s - %s - %s - %s",
            getCustomerName(),
            getYear().getYear(),
            I18n.translate(getMediaType().name()),
            agreement.getSupplier().getName().getName(),
            getDescription())
        : String.format(
            "%s - %s - %s - %s",
            getCustomerName(),
            getYear().getYear(),
            I18n.translate(getMediaType().name()),
            agreement.getSupplier().getName().getName());
  }

  public boolean doesTransactionViolateTimeUnitVolumeLimit(
      BigDecimal toBeBoughtVolume, TimeUnit timeUnit, Class<?> type) {
    if (!timeUnit.isMonth()) {
      List<TimeUnit> unitsToVerify = timeUnit.getMonths();
      boolean result = true;
      for (TimeUnit unitToVerify : unitsToVerify) {
        result &=
            doesTransactionViolateTimeUnitVolumeLimitForMonth(toBeBoughtVolume, unitToVerify, type);
      }
      return result;
    } else {
      return doesTransactionViolateTimeUnitVolumeLimitForMonth(toBeBoughtVolume, timeUnit, type);
    }
  }

  public boolean doesTransactionViolateTimeUnitVolumeLimitForMonth(
      BigDecimal toBeBoughtVolume, TimeUnit timeUnit, Class<?> type) {
    MonthTranches monthTranches = getMonthTranches();
    List<Tranche> tranches = monthTranches.get(timeUnit, type);
    BigDecimal timeUnitVolume =
        tranches.stream().map(Tranche::getSize).reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal finalVolume = timeUnitVolume;
    finalVolume = finalVolume.add(toBeBoughtVolume);
    return finalVolume.compareTo(VOLUME_MAX) > 0;
  }

  private Map<TimeUnit, RealisationDetail> getMediaRealisationDetail(Media media) {
    MonthProducts mediaProducts = getMonthProducts(media);
    MonthResults mediaResults = getMonthResults();
    Map<TimeUnit, RealisationDetail> details = Maps.newHashMap();
    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      Optional<Result> resultOpt = mediaResults.get(month);
      BigDecimal volume =
          mediaProducts
              .get(month, media, ProductType.VOLUME)
              .map(Product::getValue)
              .orElse(BigDecimal.ZERO);
      BigDecimal client =
          mediaProducts
              .get(month, media, ProductType.CLIENT_MEAN)
              .map(Product::getValue)
              .orElse(BigDecimal.ZERO);
      BigDecimal marketMean =
          resultOpt.isPresent()
              ? resultOpt.get().getValue()
              : mediaProducts
                  .get(month, media, ProductType.MARKET_MEAN)
                  .map(Product::getValue)
                  .orElse(BigDecimal.ZERO);
      BigDecimal benchmark =
          mediaProducts
              .get(month, media, ProductType.BENCHMARK_RESULT)
              .map(Product::getValue)
              .orElse(BigDecimal.ZERO);
      Map<ContractType, BigDecimal> count = volumeBoughtByContractType(month, mediaProducts, media);
      boolean confirmed = mediaProducts.isConfirmed(month, media);
      details.put(
          month,
          new RealisationDetail(
              volume, count, client, resultOpt.isPresent(), marketMean, benchmark, confirmed));
    }
    return details;
  }

  private RealisationDetails getRealisationDetails(List<Media> realisationMedia) {
    RealisationDetails realisationDetail = new RealisationDetails();
    for (Media media : realisationMedia) {
      Map<TimeUnit, RealisationDetail> data = getMediaRealisationDetail(media);
      realisationDetail.put(media, data);
    }
    return realisationDetail;
  }

  public RealisationDetails getRealisationDetails() {
    List<Media> mediaTypes =
        Arrays.stream(RealisationType.values()).map(RealisationType::toMedia).toList();
    return getRealisationDetails(mediaTypes);
  }

  /**
   * Constructs an annual purchase overview for the given media.
   *
   * <p>This method creates an {@link AnnualPurchaseOverview} instance and populates it with
   * customer name, media information, and the year. It then iterates over all months in the year
   * (as defined by {@code TimeUnit.Y.getMonths()}), and for each month, it calculates the monthly
   * volume using {@link #getMonthlyVolume(TimeUnit, MonthProducts, Media, int)}. If the month is
   * prior to the starting month of the data, a placeholder "-" is used. Otherwise, the monthly
   * volume is retrieved from the corresponding {@link MonthProducts}.
   *
   * @param media the media type for which the purchase overview is generated
   * @return an {@link AnnualPurchaseOverview} populated with customer, media, year, and monthly
   *     volume details.
   */
  public AnnualPurchaseOverview annualPurchaseOverview(Media media) {
    AnnualPurchaseOverview overview = new AnnualPurchaseOverview();
    overview.put("walletId", getId().getId());
    overview.put("customer", getName());
    final int startMonth = getStartDate().getMonthValue();
    final MonthProducts monthProducts = getMonthProducts(media);
    final LocalDate agreementEndDate = this.getAgreement().getEndDate();
    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      LocalDate monthDate = LocalDate.of(getYear().getYearAsInt(), month.getOrder(), 1);
      if (monthDate.isAfter(agreementEndDate)) {
        overview.put(month.toString(), "-");
        continue;
      }

      overview.put(month.toString(), getMonthlyVolume(month, monthProducts, media, startMonth));
    }
    return overview;
  }

  /**
   * Retrieves the monthly volume for the specified month.
   *
   * <p>If the month's order (as determined by {@link TimeUnit#getOrder()}) is less than the
   * provided start month, the method returns a placeholder "-" to indicate that the month falls
   * outside the active period. Otherwise, it attempts to retrieve the product of type {@link
   * ProductType#VOLUME} from the given {@link MonthProducts} for the specified media. If a product
   * is found, its value is returned as a string; if not, the method returns "0".
   *
   * @param month the month for which the volume is to be calculated
   * @param monthProducts the collection of monthly product data
   * @param media the media type used to retrieve the corresponding product
   * @param startMonth the month (as an integer) from which the volume data should be considered
   * @return a string representing the monthly volume, "-" if the month is before the start month,
   *     or "0" if no volume data is available
   */
  private String getMonthlyVolume(
      TimeUnit month, MonthProducts monthProducts, Media media, int startMonth) {
    if (month.getOrder() < startMonth) {
      return "-";
    }
    return monthProducts
        .get(month, media, ProductType.VOLUME)
        .map(product -> product.getValue().toString())
        .orElse("0");
  }

  public PriceConfirmation priceConfirmation() {
    PriceConfirmation overview = new PriceConfirmation();
    overview.put("walletId", getId().getId());
    overview.put("customer", getName());
    final MonthPrices monthPrices = getMonthPrices();
    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      Optional<WalletPrice> totalOptional = monthPrices.get(month, ElementType.TOTAL);
      totalOptional.ifPresentOrElse(
          walletPrice ->
              overview.put(
                  month.toString(),
                  new PriceConfirmationEntry(
                      walletPrice.getValue(),
                      walletPrice.getConfirmed(),
                      walletPrice.getConfirmed())),
          () ->
              overview.put(
                  month.toString(), new PriceConfirmationEntry(BigDecimal.ZERO, false, false)));
    }
    return overview;
  }

  public boolean hasConfirmedPrice(TimeUnit timeUnit) {
    boolean hasConfirmedPrice =
        timeUnit.getMonths().stream()
            .map(month -> getMonthPrices().get(month, ElementType.TOTAL))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .anyMatch(WalletPrice::getConfirmed);
    return hasConfirmedPrice;
  }

  /**
   * Checks if any product for the given time unit has its realisation confirmed.
   *
   * <p>This method checks all months associated with the given time unit and returns true if any
   * product in those months has its confirmed property set to true.
   *
   * @param timeUnit The time unit to check for confirmed realisation.
   * @return true if any product in the given time unit has confirmed realisation, false otherwise.
   */
  public boolean hasConfirmedRealisation(TimeUnit timeUnit, Media media) {
    return timeUnit.getMonths().stream()
        .anyMatch(month -> getMonthProducts().isConfirmed(month, media));
  }
}
