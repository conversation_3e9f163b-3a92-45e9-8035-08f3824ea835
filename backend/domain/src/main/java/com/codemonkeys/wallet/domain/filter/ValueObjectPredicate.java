/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.filter;

import com.codemonkeys.wallet.common.framework.logger.SecureLogger;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.jmolecules.ddd.types.Identifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ValueObjectPredicate {

  private static final Logger log = LoggerFactory.getLogger(ValueObjectPredicate.class);

  public Predicate handleValueObjectPredicate(Path<?> path, Object value, CriteriaBuilder cb) {
    try {
      SecureLogger.info(log, "Received value: {}, path: {}", value, path.getJavaType().getSimpleName());

      // Handle special ValueObject types
      Predicate predicate = handleSpecialValueObjects(path, value, cb);
      if (predicate != null) {
        return predicate;
      }

      // Handle Identifier types (e.g., NewsletterId, CustomerId)
      if (isIdentifierType(path)) {
        return handleIdentifier(path, value, cb);
      }

      // Check for 'id' field presence
      predicate = handleIdField(path, value, cb);
      if (predicate != null) {
        return predicate;
      }

      // Handle remaining fields via reflection
      return handleReflectionBasedPredicate(path, value, cb);

    } catch (Exception e) {
      SecureLogger.error(log, "Error processing ValueObject: {}", e.getMessage());
      return cb.conjunction();
    }
  }

  private Predicate handleSpecialValueObjects(Path<?> path, Object value, CriteriaBuilder cb) {
    return switch (path.getJavaType().getSimpleName()) {
      case "CustomerName", "SupplierName" ->
          cb.like(cb.lower(path.get("name")), STR."%\{value.toString().toLowerCase()}%");
      case "Year" -> cb.equal(path.get("year"), value.toString());
      case "PersonName" -> handlePersonNamePredicate(path, value, cb);
      default -> handleValueMethod(path, value, cb);
    };
  }

  private Predicate handlePersonNamePredicate(Path<?> path, Object value, CriteriaBuilder cb) {
    Path<String> firstNamePath = path.get("firstName");
    Path<String> lastNamePath = path.get("lastName");

    String searchValue = STR."%\{value.toString().toLowerCase()}%";
    Predicate fullNamePredicate =
        cb.like(cb.lower(cb.concat(cb.concat(firstNamePath, " "), lastNamePath)), searchValue);
    return fullNamePredicate;
  }

  private Predicate handleValueMethod(Path<?> path, Object value, CriteriaBuilder cb) {
    try {
      Method getValueMethod = path.getJavaType().getMethod("getValue");
      Path<String> valuePath = path.get("value");
      return cb.like(cb.lower(valuePath), STR."%\{value.toString().toLowerCase()}%");
    } catch (NoSuchMethodException e) {
      SecureLogger.info(log,
          "No 'getValue' method found for {}, trying other fields...",
          path.getJavaType().getSimpleName());
      return null;
    }
  }

  private boolean isIdentifierType(Path<?> path) {
    return Identifier.class.isAssignableFrom(path.getJavaType());
  }

  private Predicate handleIdentifier(Path<?> path, Object value, CriteriaBuilder cb) {
    try {
      Method valueOfMethod = path.getJavaType().getMethod("valueOf", String.class);
      Object identifierInstance = valueOfMethod.invoke(null, value.toString());
      return cb.equal(path, identifierInstance);
    } catch (Exception e) {
      SecureLogger.error(log, "Error processing identifier: {}", e.getMessage());
      return cb.conjunction();
    }
  }

  private Predicate handleIdField(Path<?> path, Object value, CriteriaBuilder cb) {
    try {
      Method getIdMethod = value.getClass().getMethod("getId");
      Object idValue = getIdMethod.invoke(value);

      if (idValue != null) {
        return cb.equal(path.get("id"), idValue);
      } else {
        SecureLogger.error(log, "ValueObject has null id: {}", value);
        return cb.conjunction();
      }
    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
      SecureLogger.info(log,
          "No 'id' field found for {}, skipping ID check...", value.getClass().getSimpleName());
      return null;
    }
  }

  private Predicate handleReflectionBasedPredicate(Path<?> path, Object value, CriteriaBuilder cb) {
    for (Method method : value.getClass().getMethods()) {
      if (method.getName().startsWith("get")) {
        try {
          Object fieldValue = method.invoke(value);
          if (fieldValue != null) {
            String fieldName = method.getName().substring(3).toLowerCase();
            if (fieldValue instanceof String) {
              return cb.like(
                  cb.lower(path.get(fieldName)), STR."%\{fieldValue.toString().toLowerCase()}%");
            } else {
              return cb.equal(path.get(fieldName), fieldValue);
            }
          }
        } catch (Exception e) {
          SecureLogger.error(log, "Error reflecting field: {}", e.getMessage());
        }
      }
    }
    return cb.equal(path, value);
  }
}
