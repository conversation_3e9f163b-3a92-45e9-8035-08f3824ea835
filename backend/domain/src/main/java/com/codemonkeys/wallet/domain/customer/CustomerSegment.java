/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.customer;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public enum CustomerSegment {
  @JsonProperty("0")
  LOCAL_GOVERNMENT_UNIT,
  @JsonProperty("1")
  INDUSTRY,
  @JsonProperty("2")
  REAL_ESTATE,
  @JsonProperty("3")
  SHOPPING_CHAINS;

  public String getValue() {
    return this.name().toString();
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public static CustomerSegment fromJson(String value) {
    return switch (value) {
      case "0" -> LOCAL_GOVERNMENT_UNIT;
      case "1" -> INDUSTRY;
      case "2" -> REAL_ESTATE;
      case "3" -> SHOPPING_CHAINS;
      default -> throw new IllegalArgumentException(STR."Nieznana warto<PERSON> segmentu: \{value}");
    };
  }
}
