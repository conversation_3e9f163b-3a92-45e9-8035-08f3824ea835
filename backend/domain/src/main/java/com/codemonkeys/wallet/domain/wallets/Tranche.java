/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.event.TrancheEntityListener;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.jmolecules.ddd.types.AggregateRoot;

@Getter
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@EntityListeners(TrancheEntityListener.class)
@Table(name = "WALLET_TRANCHES")
@JsonIgnoreProperties(value = {"hibernateLazyInitializer"})
public class Tranche extends IdentifiableAggregateRoot<TrancheId>
    implements AggregateRoot<Tranche, TrancheId> {

  @EmbeddedId private TrancheId id;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    this.tenantId =
        Optional.ofNullable(TenantContext.getTenantId())
            .filter(id -> !id.isEmpty())
            .orElseGet(
                () -> {
                  if (wallet == null
                      || wallet.getAgreement() == null
                      || wallet.getAgreement().getTenantId() == null) {
                    throw new IllegalStateException(
                        "Tenant ID must be set. Ensure Wallet and Agreement are properly configured.");
                  }
                  return wallet.getAgreement().getTenantId();
                });
  }

  @JsonIgnoreProperties(
      value = {
        "tranches",
        "elements",
        "products",
        "prices",
        "agreement",
        "hibernateLazyInitializer",
        "handler",
        "results",
        "realisationDetails",
        "analyticalSettings"
      })
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "wallet_id", nullable = false)
  private Wallet wallet;

  private LocalDate executionDate;

  @JsonIgnoreProperties(value = {"agreement"})
  @ManyToOne
  private Contract contract;

  private TimeUnit timeUnit;
  private BigDecimal size;
  private BigDecimal price;

  @Enumerated(EnumType.STRING)
  @Column(name = "price_reference", nullable = false)
  private PriceReference priceReference;

  @Column(name = "email_sent", nullable = false)
  private boolean emailSent = false;

  @ColumnDefault("FALSE")
  @Column(name = "virtual", nullable = false)
  private boolean virtual = false;

  @Transient private boolean recommendation;

  public Tranche(
      @NonNull TrancheId id,
      Wallet wallet,
      LocalDate executionDate,
      TimeUnit timeUnit,
      BigDecimal size,
      BigDecimal price,
      PriceReference priceReference,
      Contract contract,
      boolean virtual) {
    this.id = id;
    this.wallet = wallet;
    this.executionDate = executionDate;
    this.timeUnit = timeUnit;
    this.size = size;
    this.price = price;
    this.priceReference = priceReference;
    this.contract = contract;
    this.virtual = virtual;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  public Tranche(Tranche other) {
    this.id = TrancheId.randomId();
    this.wallet = other.getWallet();
    this.executionDate = other.getExecutionDate();
    this.timeUnit = other.getTimeUnit();
    this.size = other.getSize();
    this.price = other.getPrice();
    this.priceReference = other.getPriceReference();
    this.contract = other.getContract();
    this.emailSent = other.isEmailSent();
    this.virtual = other.isVirtual();
  }

  public void update(Tranche updated) {
    this.wallet = updated.getWallet();
    this.executionDate = updated.getExecutionDate();
    this.timeUnit = updated.getTimeUnit();
    this.size = updated.getSize();
    this.price = updated.getPrice();
    this.priceReference = updated.getPriceReference();
    this.contract = updated.getContract();
  }

  @Override
  public TrancheId getId() {
    return id;
  }

  public Wallet getWallet() {
    return wallet;
  }

  public void setWallet(Wallet wallet) {
    this.wallet = wallet;
  }

  public void markEmailAsSent() {
    this.emailSent = true;
  }

  public boolean isVirtual() {
    return virtual;
  }

  public void setVirtual(boolean virtual) {
    this.virtual = virtual;
  }

  public boolean isRecommendation() {
    return recommendation;
  }

  public void setRecommendation(boolean recommendation) {
    this.recommendation = recommendation;
  }

  /**
   * Size is percentage base but for calculation we use fractional size, not percentage one.
   *
   * @return
   */
  public BigDecimal getFractionalSize() {
    return size.divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_UP);
  }

  public BigDecimal product() {
    return this.getFractionalSize().multiply(price);
  }

  /**
   * Get wallets year.
   *
   * @return year as int
   */
  public int getYear() {
    return getWallet().getYear().getYearAsInt();
  }
}
