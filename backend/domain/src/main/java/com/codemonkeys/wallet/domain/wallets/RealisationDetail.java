/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.domain.agreement.ContractType;
import java.math.BigDecimal;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Represents detailed information about the realization of contracts for a specific time period.
 *
 * <p>This class contains information about purchased volumes, contract-specific volumes, client and
 * market mean prices, and benchmark values for comparison purposes.
 */
@Data
@AllArgsConstructor
public class RealisationDetail {
  /** The total volume purchased for this time period */
  private BigDecimal purchased;

  /** Mapping of contract types to their respective purchased volumes */
  private Map<ContractType, BigDecimal> contracts;

  /** The mean price agreed with the client */
  private BigDecimal clientMean;

  /** Flag indicating whether the market mean has been replaced with actual data */
  private boolean marketMeanReplaced;

  /** The mean market price for this time period */
  private BigDecimal marketMean;

  /** The benchmark value for comparison purposes */
  private BigDecimal benchmark;

  private boolean confirmed;
}
