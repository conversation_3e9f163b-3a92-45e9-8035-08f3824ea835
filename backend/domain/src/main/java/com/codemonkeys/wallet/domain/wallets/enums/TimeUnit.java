/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets.enums;

import jakarta.persistence.Transient;
import java.time.Month;
import java.util.List;
import java.util.Set;

/**
 * Represents time units used for contract periods and financial calculations.
 *
 * <p>This enum defines various time periods including:
 *
 * <ul>
 *   <li>Individual months (M1-M12)
 *   <li>Quarters (Q1-Q4)
 *   <li>Year (Y)
 * </ul>
 *
 * <p>Each time unit maintains relationships with its component time units (e.g., quarters contain
 * months) and provides utility methods for time period conversions and comparisons.
 */
public enum TimeUnit {
  M1(List.of(), 1),
  M2(List.of(), 2),
  M3(List.of(), 3),
  M4(List.of(), 4),
  M5(List.of(), 5),
  M6(List.of(), 6),
  M7(List.of(), 7),
  M8(List.of(), 8),
  M9(List.of(), 9),
  M10(List.of(), 10),
  M11(List.of(), 11),
  M12(List.of(), 12),
  Q1(List.of(M1, M2, M3), 1),
  Q2(List.of(M4, M5, M6), 2),
  Q3(List.of(M7, M8, M9), 3),
  Q4(List.of(M10, M11, M12), 4),
  Y(List.of(M1, M2, M3, M4, M5, M6, M7, M8, M9, M10, M11, M12), 1);

  /** List of component time units (e.g., months within a quarter or year) */
  @Transient public List<TimeUnit> months;

  /** Ordinal position within its category (e.g., month number 1-12, quarter number 1-4) */
  public int order;

  /**
   * Constructs a TimeUnit with its component time units and ordinal position.
   *
   * @param months List of component time units contained within this time unit
   * @param order Ordinal position within its category
   */
  TimeUnit(List<TimeUnit> months, int order) {
    this.months = months;
    this.order = order;
  }

  /**
   * Converts a month number (1-12) to its corresponding quarter.
   *
   * @param monthNumber Month number (1-12)
   * @return The quarter (Q1-Q4) containing the specified month
   * @throws IllegalArgumentException if the month number is invalid
   */
  public static TimeUnit monthNumberToQuarter(Integer monthNumber) {
    return switch (monthNumber) {
      case 1, 2, 3 -> TimeUnit.Q1;
      case 4, 5, 6 -> TimeUnit.Q2;
      case 7, 8, 9 -> TimeUnit.Q3;
      case 10, 11, 12 -> TimeUnit.Q4;
      default ->
          throw new IllegalArgumentException(
              String.format("Illegal month number: %d", monthNumber));
    };
  }

  /**
   * Returns a set of time units relevant for broker dashboard size calculations for a given month.
   *
   * <p>The returned set includes the specified month, its corresponding quarter, and the year.
   *
   * @param month Month number (1-12)
   * @return Set containing the month, its quarter, and year time units
   * @throws IllegalArgumentException if the month number is invalid
   */
  public static Set<TimeUnit> getBrokerDashboardSizeUnitsForMonth(int month) {
    if (month < 1 || month > 12) {
      throw new IllegalArgumentException("Miesiąc musi być z zakresu 1-12");
    }
    return Set.of(TimeUnit.valueOf(STR."M\{month}"), monthNumberToQuarter(month), Y);
  }

  /**
   * Returns the list of months contained within this time unit.
   *
   * <p>For individual months, returns a list containing only itself. For quarters and year, returns
   * the list of component months.
   *
   * @return List of months contained in this time unit
   */
  public List<TimeUnit> getMonths() {
    if (months.isEmpty()) {
      return List.of(this);
    }
    return months;
  }

  /**
   * Returns the ordinal position of this time unit within its category.
   *
   * @return Ordinal position (e.g., month number 1-12, quarter number 1-4)
   */
  public int getOrder() {
    return order;
  }

  /**
   * Determines if this time unit represents a month (M1-M12).
   *
   * @return true if this time unit is a month, false otherwise
   */
  public boolean isMonth() {
    return this.ordinal() >= M1.ordinal() && this.ordinal() <= M12.ordinal();
  }

  /**
   * Determines if this time unit represents a quarter (Q1-Q4).
   *
   * @return true if this time unit is a quarter, false otherwise
   */
  public boolean isQuarter() {
    return this == Q1 || this == Q2 || this == Q3 || this == Q4;
  }

  /**
   * Determines if this time unit represents a year (Y).
   *
   * @return true if this time unit is a year, false otherwise
   */
  public boolean isYear() {
    return this == Y;
  }

  /**
   * Returns the numeric representation of this time unit.
   *
   * @return The order value as a number
   */
  public int asNumber() {
    return order;
  }

  /**
   * Converts this time unit to a java.time.Month.
   *
   * <p>For quarters and year, returns the first month of the period.
   *
   * @return The java.time.Month representation
   */
  public Month toMonth() {
    return switch (this) {
      case Q1, M1, Y -> Month.of(1);
      case M2 -> Month.of(2);
      case M3 -> Month.of(3);
      case Q2, M4 -> Month.of(4);
      case M5 -> Month.of(5);
      case M6 -> Month.of(6);
      case M7, Q3 -> Month.of(7);
      case M8 -> Month.of(8);
      case M9 -> Month.of(9);
      case M10, Q4 -> Month.of(10);
      case M11 -> Month.of(11);
      case M12 -> Month.of(12);
      default -> Month.of(1);
    };
  }

  /**
   * Returns the quarter containing this time unit.
   *
   * <p>For months, returns the containing quarter. For quarters, returns itself. For year, returns
   * Q1 by default.
   *
   * @return The quarter time unit
   */
  public TimeUnit getQuarter() {
    return switch (this) {
      case Q1, M1 -> Q1;
      case Q2, M4, M5, M6 -> Q2;
      case M7, Q3, M8, M9 -> Q3;
      case M10, Q4, M11, M12 -> Q4;
      default -> Q1;
    };
  }
}
