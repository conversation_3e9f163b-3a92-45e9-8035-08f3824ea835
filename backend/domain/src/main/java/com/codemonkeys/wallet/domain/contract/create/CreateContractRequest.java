/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.contract.create;

import com.codemonkeys.wallet.common.framework.domain.enums.FormType;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.enums.PurchaseModel;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.enums.FinalPurchaseDateType;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.contract.vo.*;
import java.time.LocalDate;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateContractRequest {
  FormType formType;
  List<UUID> customers;
  UUID group;
  MediaType mediaType;
  UUID supplier;
  PurchaseModel purchaseModel;
  LocalDate startDate;
  LocalDate endDate;
  LocalDate averageCalculationStartDate;
  ContractParameters media;
  ContractParameters propertyRights;
  List<Contract> contracts;
  List<String> authorizedBuyers;

  public Set<Integer> getYears() {
    Set<Integer> years = new HashSet<>();
    if (startDate.getYear() == endDate.getYear()) {
      years.add(startDate.getYear());
    }

    LocalDate iteratorDate = LocalDate.from(startDate);
    while (iteratorDate.getYear() < endDate.getYear()) {
      years.add(iteratorDate.getYear());
      iteratorDate = iteratorDate.plusYears(1);
    }

    // while (startDate.getYear() < endDate.getYear()) {
    //  years.add(startDate.getYear());
    //  startDate = startDate.plusYears(1);
    // }
    return years;
  }

  public void assignPurchaseModel() {
    this.media.setPurchaseModel(purchaseModel);
    /* this.propertyRights.setPurchaseModel(purchaseModel);*/
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class FinalPurchaseDate {
    FinalPurchaseDateType type;
    Integer value;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class ContractParameters {
    PurchaseModel purchaseModel;
    List<ContractType> availableProducts;
    List<PriceReference> priceReference;
    Map<PriceReference, OrderTimes.OrderTime> orderTime;
    ActionIfNot100 actionIfNot100;
    Quantity quantity;
    CreateContractRequest.FinalPurchaseDate finalPurchaseDate;
    Volume volume;
    OrderTypeParameters orderTypeParameters;
    RejectionReason rejectionReason;
    String comment;
  }
}
