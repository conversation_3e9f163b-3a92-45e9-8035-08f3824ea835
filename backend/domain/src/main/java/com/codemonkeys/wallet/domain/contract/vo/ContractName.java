/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.contract.vo;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.common.framework.logger.SecureLogger;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;

/**
 * Value object representing a contract name in the energy trading system.
 *
 * <p>This class encapsulates the logic for creating, parsing, and managing contract names
 * across different media types (energy and gas) and contract types. It supports various contract
 * naming conventions including:
 * <ul>
 *   <li>Base contracts (BASE_Y, BASE_Q, BASE_M)</li>
 *   <li>Gas contracts (GAS_BASE_Y, GAS_BASE_Q, GAS_BASE_M)</li>
 *   <li>Property rights contracts (PMOZE_A, PMOZE_BIO, PMEF_F)</li>
 *   <li>Spot contracts (TGe24, TGEgasDA, TGEgasID)</li>
 *   <li>Special contracts (SS, SW)</li>
 * </ul>
 *
 * <p>Contract names follow specific patterns based on their type:
 * <ul>
 *   <li>Yearly contracts: {@code BASE_Y-25} (for year 2025)</li>
 *   <li>Quarterly contracts: {@code BASE_Q-2-25} (Q2 2025)</li>
 *   <li>Monthly contracts: {@code BASE_M-06-25} (June 2025)</li>
 *   <li>Property rights: {@code PMOZE_A-06-25} (June 2025)</li>
 * </ul>
 *
 * <p>This class is immutable and thread-safe. It implements value object semantics
 * with proper equals/hashCode behavior provided by Lombok's {@code @Value} annotation.
 *
 * <AUTHOR> System
 * @since 1.0
 */
@Slf4j
@Value
@NoArgsConstructor(access = AccessLevel.PROTECTED, force = true)
public class ContractName {

  private static final Set<String> PROPERTY_RIGHTS_PREFIXES =
      Set.of("PMOZE_A", "PMOZE_BIO", "PMEF_F");
  @NonNull
  String value;

  /**
   * Private constructor for creating a contract name based on structured parameters.
   *
   * @param year      the contract year
   * @param mediaType the media type (ENERGY or GAS)
   * @param product   the contract type (Y, Q, M, PMOZE_A, etc.)
   * @param timeUnit  the time unit number (for monthly/quarterly contracts)
   */
  private ContractName(
      @NonNull Year year,
      @NonNull MediaType mediaType,
      @NonNull ContractType product,
      Integer timeUnit) {
    String yearDigits = year.toString().substring(2);
    String base =
        switch (mediaType) {
          case ENERGY -> "BASE";
          case GAS -> "GAS_BASE";
        };
    String name =
        switch (product) {
          case Y -> String.format("%s_Y-%s", base, yearDigits);
          case Q -> String.format("%s_Q-%01d-%s", base, timeUnit, yearDigits);
          case M -> String.format("%s_M-%02d-%s", base, timeUnit, yearDigits);
          case PMOZE_A -> {
            if (timeUnit != null) {
              yield String.format("%s-%02d-%s", ContractType.PMOZE_A, timeUnit, yearDigits);
            } else {
              yield String.format("%s", ContractType.PMOZE_A);
            }
          }
          case PMOZE_BIO -> {
            if (timeUnit != null) {
              yield String.format("%s-%02d-%s", ContractType.PMOZE_BIO, timeUnit, yearDigits);
            } else {
              yield String.format("%s", ContractType.PMOZE_BIO);
            }
          }
          case PMEF_F -> {
            if (timeUnit != null) {
              yield String.format("%s-%02d-%s", ContractType.PMEF_F, timeUnit, yearDigits);
            } else {
              yield String.format("%s", ContractType.PMEF_F);
            }
          }
          case SS -> String.format("%s_S-S-%s", base, yearDigits);
          case SW -> String.format("%s_S-W-%s", base, yearDigits);
          case TGe24 -> ContractType.TGe24.toString();
          case TGEgasDA -> ContractType.TGEgasDA.toString();
          case TGEgasID -> ContractType.TGEgasID.toString();
        };

    // this.year = Integer.valueOf(year.getYear());
    this.value = name;
  }

  /**
   * Constructor for creating a contract name from a string value.
   *
   * @param value the contract name string
   */
  public ContractName(@NonNull String value) {
    this.value = value;
  }

  /**
   * Factory method for creating a ContractName from a string value.
   *
   * @param value the contract name string
   * @return a new ContractName instance
   */
  public static ContractName of(@NonNull String value) {
    return new ContractName(value);
  }

  /**
   * Factory method for creating a ContractName from structured parameters.
   *
   * @param year      the contract year
   * @param mediaType the media type (ENERGY or GAS)
   * @param product   the contract type
   * @param timeUnit  the time unit number (for monthly/quarterly contracts)
   * @return a new ContractName instance
   */
  public static ContractName of(
      @NonNull Year year,
      @NonNull MediaType mediaType,
      @NonNull ContractType product,
      Integer timeUnit) {
    return new ContractName(year, mediaType, product, timeUnit);
  }

  /**
   * Factory method for creating a ContractName without a specific time unit.
   *
   * @param year      the contract year
   * @param mediaType the media type (ENERGY or GAS)
   * @param product   the contract type
   * @return a new ContractName instance
   */
  public static ContractName of(
      @NonNull Year year, @NonNull MediaType mediaType, @NonNull ContractType product) {
    return new ContractName(year, mediaType, product, null);
  }

  @JsonValue
  @Override
  public String toString() {
    return value;
  }

  private static final Pattern[] TIME_UNIT_PATTERNS = {
      // Matches the full, expected structure for a Monthly contract
      // e.g., "BASE_M-12-25" or "GAS_BASE_M-01-24"
      Pattern.compile("^(?:BASE|GAS_BASE)_M-(\\d{1,2})-\\d{2}$"),

      // Matches the full, expected structure for a Quarterly contract
      // e.g., "BASE_Q-1-25" or "GAS_BASE_Q-4-24"
      Pattern.compile("^(?:BASE|GAS_BASE)_Q-(\\d{1})-\\d{2}$"),

      // Matches the full, expected structure for a Yearly contract
      // e.g., "BASE_Y-25" or "GAS_BASE_Y-24"
      Pattern.compile("^(?:BASE|GAS_BASE)_Y-\\d{2}$")
  };

  private static final String[] TIME_UNIT_PREFIXES = {"M", "Q", "Y"};

  private static final List<String> SPOT_CONTRACT_TYPES = List.of(
      ContractType.TGe24.name(),
      ContractType.TGEgasDA.name(),
      ContractType.TGEgasID.name()
  );

  /**
   * Extracts the time unit from the contract name.
   *
   * <p>This method analyzes the contract name pattern to determine the appropriate
   * time unit (Y for yearly, Q1-Q4 for quarterly, M1-M12 for monthly).
   *
   * @return the time unit extracted from the contract name, or null if not determinable
   */
  public TimeUnit getTimeUnit() {
    if (value == null || value.isEmpty()) {
      return null;
    }

    TimeUnit patternBasedTimeUnit = extractTimeUnitFromPattern();
    if (patternBasedTimeUnit != null) {
      return patternBasedTimeUnit;
    }

    if (isSpotContractType()) {
      return TimeUnit.Y;
    }

    return parseTimeUnit(value);
  }

  /**
   * Extracts time unit from the contract name using regex patterns.
   *
   * @return the time unit if found, null otherwise
   */
  private TimeUnit extractTimeUnitFromPattern() {
    if (value.length() > 1000) {
      SecureLogger.warn(log, "{} contract name is too long", value);
      throw new IllegalArgumentException("%s contract name is too long".formatted(value));
    }
    for (int i = 0; i < TIME_UNIT_PATTERNS.length; i++) {
      Matcher matcher = TIME_UNIT_PATTERNS[i].matcher(value);
      if (matcher.matches()) {
        String prefix = TIME_UNIT_PREFIXES[i];
        if ("Y".equals(prefix)) {
          return TimeUnit.Y;
        }
        int unit = Integer.parseInt(matcher.group(1));
        return TimeUnit.valueOf(prefix + unit);
      }
    }
    return null;
  }

  /**
   * Checks if this contract name represents a spot contract type.
   *
   * @return true if this is a spot contract (TGe24, TGEgasDA, TGEgasID)
   */
  private boolean isSpotContractType() {
    return SPOT_CONTRACT_TYPES.contains(value);
  }


  /**
   * Parses a time unit string into a TimeUnit enum value for property rights contracts.
   *
   * <p>This method specifically handles property rights contract naming patterns:
   * <ul>
   *   <li>{@code "PMOZE_A-25"} → {@code TimeUnit.Y} (yearly)</li>
   *   <li>{@code "PMOZE_A-06-25"} → {@code TimeUnit.M6} (monthly)</li>
   *   <li>{@code "PMOZE_BIO-12-25"} → {@code TimeUnit.M12} (monthly)</li>
   *   <li>{@code "PMEF_F-01-25"} → {@code TimeUnit.M1} (monthly)</li>
   * </ul>
   *
   * <p>The parsing logic:
   * <ol>
   *   <li>Validates the contract has a valid property rights prefix</li>
   *   <li>Splits the string by dashes to analyze parts</li>
   *   <li>Returns {@code TimeUnit.Y} for contracts without month specification</li>
   *   <li>Parses the month part for 3-part contracts (prefix-month-year)</li>
   * </ol>
   *
   * @param value the contract name string to parse (e.g., "PMOZE_A-06-25")
   * @return the corresponding TimeUnit (Y for yearly, M1-M12 for monthly)
   * @throws IllegalArgumentException if the value is null or empty
   */
  public TimeUnit parseTimeUnit(String value) {
    if (value == null || value.isEmpty()) {
      throw new IllegalArgumentException("Value cannot be null or empty");
    }

    if (!hasValidPrefix(value)) {
      return TimeUnit.Y;
    }

    String[] parts = value.split("-");
    return switch (parts.length) {
      case 1, 2 -> TimeUnit.Y;
      case 3 -> parseMonthTimeUnit(parts[1]);
      default -> TimeUnit.Y;
    };
  }

  /**
   * Checks if the contract name has a valid property rights prefix.
   *
   * @param value the contract name to check
   * @return true if the name starts with a valid property rights prefix
   */
  private boolean hasValidPrefix(String value) {
    return PROPERTY_RIGHTS_PREFIXES.stream().anyMatch(value::startsWith);
  }

  /**
   * Parses a month part string into a TimeUnit enum value.
   *
   * <p>Handles various month formats and normalizes them to M1-M12 format.
   *
   * @param monthPart the month part to parse (e.g., "06", "M6", "6")
   * @return the corresponding monthly TimeUnit
   */
  private TimeUnit parseMonthTimeUnit(String monthPart) {
    if (!monthPart.startsWith("M") && monthPart.startsWith("0")) {
      monthPart = "M%s".formatted(monthPart.substring(1, 2));
    }
    if (!monthPart.startsWith("M") && !monthPart.startsWith("0")) {
      monthPart = "M%s".formatted(monthPart);
    }
    return TimeUnit.valueOf(monthPart);
  }

  /**
   * Extracts the stock name (base name) from the contract name.
   *
   * <p>For property rights contracts (PMOZE_A, PMOZE_BIO, PMEF_F), this returns
   * the part before the first dash. For other contracts, returns the full name.
   *
   * @return the stock name portion of the contract name
   */
  public String getStockName() {
    if (!value.contains("PMOZE_A-")
        && !value.contains("PMOZE_BIO-")
        && !value.contains("PMEF_F-")) {
      return value;
    } else {
      int idx = value.indexOf("-");
      return value.substring(0, idx);
    }
  }

  /**
   * Returns the sort rank for contract export ordering.
   *
   * <p>The lower the value, the earlier the contract appears in sorted lists.
   * Ordering priority:
   * <ul>
   *   <li>0: Yearly contracts (e.g., BASE_Y-25)</li>
   *   <li>1-4: Quarterly contracts (Q1-Q4)</li>
   *   <li>5-16: Monthly contracts (M1-M12)</li>
   *   <li>100+: Property rights contracts in order (TGe24, PMOZE_A, PMOZE_BIO, PMEF_F)</li>
   *   <li>MAX_VALUE: Unknown or invalid contracts</li>
   * </ul>
   *
   * @param contractName the contract name to get sort order for
   * @return the sort rank (lower values sort earlier)
   */
  public static int exportSortOrder(String contractName) {
    if (contractName == null || contractName.isBlank()) {
      return Integer.MAX_VALUE;
    }

    if (contractName.matches(".*_Y-\\d{2}$")) {
      return 0;
    }

    String[] parts = contractName.replace('_', '-').split("-");
    if (parts.length >= 3) {
      if ("Q".equals(parts[1])) {
        return Integer.parseInt(parts[2]); // 1-4
      }
      if ("M".equals(parts[1])) {
        int m = Integer.parseInt(parts[2]); // 1-12
        return 4 + m; // 5-16
      }
    }
    List<String> prOrder = List.of("TGe24", "PMOZE_A", "PMOZE_BIO", "PMEF_F");
    String baseName =
        contractName.contains("-")
            ? contractName.substring(0, contractName.indexOf('-'))
            : contractName;
    int prIdx = prOrder.indexOf(baseName);
    if (prIdx != -1) {
      return 100 + prIdx;
    }
    return Integer.MAX_VALUE;
  }
}
