/* (C)2025 */
package com.codemonkeys.wallet.pricingcharts.charts.export;

import com.codemonkeys.wallet.pricingcharts.common.PricingDataDto;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ChartsExportService {

  private static final String SHEET_NAME = "Kontrakt i wykres";
  private final ContractDataService contractDataService;
  private final ExcelSheetPopulator excelSheetPopulator;
  private final ExcelChartCreator excelChartCreator;

  /**
   * Exports chart data to an XLS file.
   *
   * @param contracts comma-separated contract names
   * @param startDate start date
   * @param endDate end date
   * @return XLS file as byte array
   * @throws IOException if file generation fails
   */
  public byte[] exportCharts(String contracts, LocalDate startDate, LocalDate endDate)
      throws IOException {
    validateDateRange(startDate, endDate);
    List<String> contractList = parseContracts(contracts);
    Map<String, List<PricingDataDto>> contractDataMap =
        contractDataService.getContractDataMap(contractList, startDate, endDate);
    contractDataService.validateContractsData(contractDataMap);

    try (Workbook workbook = new XSSFWorkbook();
        ByteArrayOutputStream xlsOut = new ByteArrayOutputStream()) {

      Sheet sheet = workbook.createSheet(SHEET_NAME);
      int lastRowIndex = excelSheetPopulator.populateSheet(sheet, contractDataMap);
      excelChartCreator.addChart(workbook, sheet, contractDataMap, lastRowIndex);

      workbook.write(xlsOut);
      return xlsOut.toByteArray();
    }
  }

  /**
   * Builds a ResponseEntity with the XLS file and appropriate headers.
   *
   * @param contracts comma-separated contract names
   * @param startDate start date
   * @param endDate end date
   * @return ResponseEntity with XLS file
   * @throws IOException if file generation fails
   */
  public ResponseEntity<byte[]> exportChartResponse(
      String contracts, LocalDate startDate, LocalDate endDate) throws IOException {
    byte[] fileContent = exportCharts(contracts, startDate, endDate);
    if (fileContent == null || fileContent.length == 0) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
    }

    String safeFileName = createSafeFileName(contracts);
    ContentDisposition disposition = ContentDisposition.attachment().filename(safeFileName).build();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentDisposition(disposition);
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.set("Access-Control-Expose-Headers", "Content-Disposition");

    return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
  }

  /**
   * Validates that the start date is not after the end date.
   *
   * @param startDate the start date
   * @param endDate the end date
   * @throws IllegalArgumentException if startDate is after endDate
   */
  private void validateDateRange(LocalDate startDate, LocalDate endDate) {
    if (startDate.isAfter(endDate)) {
      throw new IllegalArgumentException("Start date cannot be after end date.");
    }
  }

  /**
   * Parses a comma-separated string of contracts into a list.
   *
   * @param contracts the comma-separated contracts
   * @return a list of trimmed, non-empty contract names
   */
  private List<String> parseContracts(String contracts) {
    return Arrays.stream(contracts.split(",")).map(String::trim).filter(s -> !s.isEmpty()).toList();
  }

  /**
   * Creates a safe filename by replacing commas with underscores.
   *
   * @param contracts the raw contract string
   * @return a filename string for the exported chart file
   */
  private String createSafeFileName(String contracts) {
    return STR."chart_export_\{contracts.replaceAll(",", "_")}.xlsx";
  }
}
