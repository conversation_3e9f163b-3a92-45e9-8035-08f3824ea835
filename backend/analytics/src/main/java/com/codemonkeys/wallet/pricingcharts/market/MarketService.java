/* (C)2024-2025 */
package com.codemonkeys.wallet.pricingcharts.market;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MarketService {

  private final PriceRepository priceRepository;

  /**
   * Retrieves market data for the given date.
   *
   * @param today the reference date
   * @return a list of MarketDto objects representing market data
   */
  public List<MarketDto> getMarketData(LocalDate today) {
    LocalDate twoDaysAgo = today.minusDays(2);
    LocalDate yesterday = today.minusDays(1);

    Map<String, Price> twoDaysAgoPrices = getPricesByDate(twoDaysAgo);
    Map<String, Price> yesterdayPrices = getPricesByDate(yesterday);

    return yesterdayPrices.values().stream()
        .filter(price -> isValidContract(price.getContract().getName()))
        .map(
            yesterdayPrice -> {
              String contractName = yesterdayPrice.getContract().getName();
              BigDecimal previousPrice =
                  twoDaysAgoPrices.containsKey(contractName)
                      ? twoDaysAgoPrices.get(contractName).getValue().getValue()
                      : BigDecimal.ZERO;

              BigDecimal currentPrice = yesterdayPrice.getValue().getValue();

              return new MarketDto(
                  contractName,
                  previousPrice,
                  currentPrice,
                  calculateChangePercentage(previousPrice, currentPrice),
                  calculateMonthlyChangePercentage(contractName, today));
            })
        .sorted(Comparator.comparing(MarketDto::getContractName))
        .toList();
  }

  /**
   * Fetches prices by date and maps them by contract name.
   *
   * @param date the date for which to fetch prices
   * @return a map of contract name to Price
   */
  private Map<String, Price> getPricesByDate(LocalDate date) {
    return priceRepository.findByDate(date).stream()
        .collect(Collectors.toMap(price -> price.getContract().getName(), price -> price));
  }

  /**
   * Calculates the percentage change between two prices.
   *
   * @param previous the previous price
   * @param current the current price
   * @return the percentage change as a BigDecimal
   */
  private BigDecimal calculateChangePercentage(BigDecimal previous, BigDecimal current) {
    if (previous == null || previous.equals(BigDecimal.ZERO)) {
      return BigDecimal.ZERO;
    }
    return current
        .subtract(previous)
        .divide(previous, 4, BigDecimal.ROUND_HALF_UP)
        .multiply(BigDecimal.valueOf(100));
  }

  /**
   * Calculates the monthly change percentage for a given contract.
   *
   * @param contractName the name of the contract
   * @param today the reference date
   * @return the monthly change percentage as a BigDecimal
   */
  private BigDecimal calculateMonthlyChangePercentage(String contractName, LocalDate today) {
    LocalDate startOfMonth = today.minusMonths(1);

    List<Price> prices =
        priceRepository.findByContractAndDateBetween(
            PriceName.of(contractName), startOfMonth, today);

    if (prices.isEmpty()) {
      return BigDecimal.ZERO;
    }

    BigDecimal average =
        prices.stream()
            .map(price -> price.getValue().getValue())
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(BigDecimal.valueOf(prices.size()), 4, BigDecimal.ROUND_HALF_UP);

    BigDecimal lastPrice = prices.get(prices.size() - 1).getValue().getValue();

    return calculateChangePercentage(average, lastPrice);
  }

  /**
   * Checks if the contract does NOT contain unwanted patterns and is NOT on the blocked list.
   *
   * @param contractName the name of the contract to check
   * @return true if the contract should be included
   */
  private boolean isValidContract(String contractName) {
    List<String> blockedContracts = List.of("TGe15", "TGe9", "TGeBase", "TGeOffpeak", "TGePeak");

    if (blockedContracts.contains(contractName)) {
      return false;
    }

    return !(contractName.contains("S-W")
        || contractName.contains("W-")
        || contractName.contains("S-S"));
  }
}
