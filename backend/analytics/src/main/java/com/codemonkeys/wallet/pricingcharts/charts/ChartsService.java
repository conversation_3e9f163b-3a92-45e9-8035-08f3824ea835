/* (C)2024-2025 */
package com.codemonkeys.wallet.pricingcharts.charts;

import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.domain.contract.vo.ContractName;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.pricingcharts.common.AveragePriceDto;
import com.codemonkeys.wallet.pricingcharts.common.PricingDataDto;
import com.codemonkeys.wallet.pricingcharts.common.PricingDataMapper;
import com.codemonkeys.wallet.pricingcharts.common.PricingDataWithMaxDto;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ChartsService {

  private static final List<String> AVAILABLE_RANGES =
      Arrays.asList("5D", "1M", "3M", "6M", "1Y", "2Y", "MAX");
  private final PriceRepository priceRepository;
  private final PricingDataMapper priceMapper;

  /**
   * Retrieves pricing data for a contract based on a range period or date interval.
   *
   * @param contract the identifier of the contract
   * @param range the range period (e.g., "5D", "1M", etc.), optional
   * @param startDate the start date of the range, optional
   * @param endDate the end date of the range, optional
   * @return a list of pricing data
   */
  public List<PricingDataDto> getPrices(
      String contract, String range, LocalDate startDate, LocalDate endDate) {
    if (range != null) {
      return getPricesByContractAndRange(contract, range);
    } else if (startDate != null && endDate != null) {
      return getPricesByContractAndDateRange(contract, startDate, endDate);
    } else {
      String errorMessage = I18n.translate("error.range.or.dates.required");
      throw new IllegalArgumentException(errorMessage);
    }
  }

  /**
   * Retrieves pricing data for a specific contract within a date range.
   *
   * @param contract the identifier of the contract
   * @param startDate the start date of the range
   * @param endDate the end date of the range
   * @return a list of pricing data within the specified date range
   */
  public List<PricingDataDto> getPricesByContractAndDateRange(
      String contract, LocalDate startDate, LocalDate endDate) {
    validateDateRange(startDate, endDate);
    List<Price> prices =
        priceRepository.findByContractAndDateBetween(PriceName.of(contract), startDate, endDate);
    return priceMapper.toDtoList(prices);
  }

  /**
   * Retrieves pricing data for a specific contract based on a predefined range (e.g., "5D", "1M").
   *
   * @param contract the identifier of the contract
   * @param range the range period (e.g., "5D", "1M", etc.)
   * @return a list of pricing data for the specified range
   */
  public List<PricingDataDto> getPricesByContractAndRange(String contract, String range) {
    LocalDate today = LocalDate.now();
    LocalDate startDate;
    LocalDate endDate = today;

    startDate =
        switch (range.toUpperCase()) {
          case "5D" ->
              isApplicableContractType(contract)
                  ? calculateStartDateForLastFiveWorkingDays(today)
                  : today.minusDays(4);
          case "1M" -> {
            YearMonth previousMonth = YearMonth.from(today).minusMonths(1);
            yield previousMonth.atDay(1);
          }
          case "3M" -> today.minusMonths(3).withDayOfMonth(1);
          case "6M" -> today.minusMonths(6).withDayOfMonth(1);
          case "1Y" -> today.minusYears(1).withDayOfYear(1);
          case "2Y" -> today.minusYears(2).withDayOfYear(1);
          case "MAX" -> today.minusYears(5).withDayOfYear(1);
          default -> throw new IllegalArgumentException(STR."Unsupported range: \{range}");
        };

    return getPricesByContractAndDateRange(contract, startDate, endDate);
  }

  /**
   * Determines if a given contract is applicable based on its time unit.
   *
   * <p>The method extracts the {@link TimeUnit} from the contract name and checks whether it
   * represents a valid monthly, quarterly, or yearly contract.
   *
   * @param contract the contract string to evaluate
   * @return true if the contract's time unit is monthly, quarterly, or yearly; false otherwise
   */
  private boolean isApplicableContractType(String contract) {
    try {
      ContractName contractName = new ContractName(contract);
      TimeUnit timeUnit = contractName.getTimeUnit();
      return timeUnit != null && (timeUnit.isMonth() || timeUnit.isQuarter() || timeUnit.isYear());
    } catch (IllegalArgumentException e) {
      return false;
    }
  }

  /**
   * Calculates the start date for the last five working days (excluding weekends).
   *
   * @param today the current date
   * @return the earliest date among the last five working days
   */
  private LocalDate calculateStartDateForLastFiveWorkingDays(LocalDate today) {
    int workingDaysCount = 0;
    LocalDate currentDate = today;

    while (workingDaysCount < 5) {
      if (currentDate.getDayOfWeek() != DayOfWeek.SATURDAY
          && currentDate.getDayOfWeek() != DayOfWeek.SUNDAY) {
        workingDaysCount++;
      }
      currentDate = currentDate.minusDays(1);
    }

    return currentDate.plusDays(1);
  }

  /**
   * Calculates average spot prices for the specified contracts within a date range. Contracts with
   * no data are excluded.
   *
   * @param startDate the start date (inclusive)
   * @param endDate the end date (inclusive)
   * @param contracts a list of contracts to calculate averages for
   * @return a list of {@link AveragePriceDto} with averages for each contract with data
   * @throws IllegalArgumentException if startDate is after endDate
   */
  public List<AveragePriceDto> calculateAverageSpotPriceByContracts(
      LocalDate startDate, LocalDate endDate, List<String> contracts) {
    validateDateRange(startDate, endDate);
    return contracts.stream()
        .map(
            contract -> {
              List<Price> prices =
                  priceRepository.findByContractAndDateBetween(
                      PriceName.of(contract), startDate, endDate);

              if (prices.isEmpty()) return null;
              double average =
                  prices.stream()
                      .mapToDouble(price -> price.getValue().getValue().doubleValue())
                      .average()
                      .orElseThrow();

              return new AveragePriceDto(contract, average, prices.size());
            })
        .filter(Objects::nonNull)
        .toList();
  }

  /**
   * Validates the date range by ensuring the start date is not after the end date.
   *
   * @param startDate the start date
   * @param endDate the end date
   */
  private void validateDateRange(LocalDate startDate, LocalDate endDate) {
    if (startDate.isAfter(endDate)) {
      String errorMessage = I18n.translate("error.date.invalid.range");
      throw new IllegalArgumentException(errorMessage);
    }
  }

  /**
   * Retrieves available time ranges for a specific contract where data is present.
   *
   * @param contract the identifier of the contract
   * @return a list of available ranges for the given contract that have corresponding data
   */
  public List<String> getAvailableRangesForContract(String contract) {
    return AVAILABLE_RANGES.stream()
        .filter(range -> !getPricesByContractAndRange(contract, range).isEmpty())
        .toList();
  }

  /**
   * Retrieves pricing data along with calculated lower and upper limits for the given contract.
   *
   * @param contract the contract identifier
   * @param range the pricing data range (e.g., "5D", "1M") or null if using date range
   * @param startDate the start date for the pricing data
   * @param endDate the end date for the pricing data
   * @return a PricingDataWithMaxDto containing the pricing data and the calculated limits
   * @throws IllegalArgumentException if no pricing data is found or if data extraction fails
   */
  public PricingDataWithMaxDto getPricingDataWithMaxAndLimits(
      String contract, String range, LocalDate startDate, LocalDate endDate) {

    List<PricingDataDto> prices = fetchPricingData(contract, range, startDate, endDate);
    if (prices.isEmpty()) {
      return new PricingDataWithMaxDto(prices, Double.NaN, Double.NaN);
    }

    double localMin = extractLocalMin(prices, contract);
    double localMax = extractLocalMax(prices, contract);
    double[] limits = calculateLimits(contract, localMin, localMax);

    return new PricingDataWithMaxDto(prices, limits[0], limits[1]);
  }

  /**
   * Fetches pricing data for the specified contract using the provided range or date interval.
   *
   * @param contract the contract identifier
   * @param range the pricing data range or null if using a date interval
   * @param startDate the start date for the data retrieval
   * @param endDate the end date for the data retrieval
   * @return a list of PricingDataDto objects
   */
  private List<PricingDataDto> fetchPricingData(
      String contract, String range, LocalDate startDate, LocalDate endDate) {
    return getPrices(contract, range, startDate, endDate);
  }

  /**
   * Extracts the local minimum price from the provided pricing data.
   *
   * @param prices the list of pricing data
   * @param contract the contract identifier (used in error message)
   * @return the minimum price value as a double
   * @throws IllegalArgumentException if no minimum value is found
   */
  private double extractLocalMin(List<PricingDataDto> prices, String contract) {
    return prices.stream()
        .mapToDouble(dto -> dto.getValue().doubleValue())
        .min()
        .orElse(Double.NaN);
  }

  /**
   * Extracts the local maximum price from the provided pricing data.
   *
   * @param prices the list of pricing data
   * @param contract the contract identifier (used in error message)
   * @return the maximum price value as a double
   * @throws IllegalArgumentException if no maximum value is found
   */
  private double extractLocalMax(List<PricingDataDto> prices, String contract) {
    return prices.stream()
        .mapToDouble(dto -> dto.getValue().doubleValue())
        .max()
        .orElse(Double.NaN);
  }

  /**
   * Calculates the lower and upper price limits based on the contract type and local min/max
   * values.
   *
   * <p>Rules:
   *
   * <ul>
   *   <li>For PMOZE_A and PMOZE-BIO: upper limit = (MAX + 20), lower limit = (MIN - 20)
   *   <li>For PMEF_F: upper limit = (MAX + 200), lower limit = (MIN - 200)
   *   <li>For other contracts: upper limit = (MAX + 30), lower limit = (MIN - 30)
   * </ul>
   *
   * Limits are rounded to the nearest tens.
   *
   * @param contract the contract identifier
   * @param localMin the local minimum price value
   * @param localMax the local maximum price value
   * @return a double array where index 0 is the lower limit and index 1 is the upper limit
   */
  private double[] calculateLimits(String contract, double localMin, double localMax) {
    double lowerLimit;
    double upperLimit;

    if ("PMOZE_A".equalsIgnoreCase(contract) || "PMOZE-BIO".equalsIgnoreCase(contract)) {
      upperLimit = Math.ceil((localMax + 20) / 10) * 10;
      lowerLimit = Math.floor((localMin - 20) / 10) * 10;
    } else if ("PMEF_F".equalsIgnoreCase(contract)) {
      upperLimit = Math.ceil((localMax + 200) / 10) * 10;
      lowerLimit = Math.floor((localMin - 200) / 10) * 10;
    } else {
      upperLimit = Math.ceil((localMax + 30) / 10) * 10;
      lowerLimit = Math.floor((localMin - 30) / 10) * 10;
    }
    return new double[] {lowerLimit, upperLimit};
  }
}
