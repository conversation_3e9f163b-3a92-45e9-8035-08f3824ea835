/* (C)2025 */
package com.codemonkeys.wallet.pricingcharts.charts.export;

import com.codemonkeys.wallet.pricingcharts.common.PricingDataDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.openxmlformats.schemas.drawingml.x2006.main.*;
import org.springframework.stereotype.Component;

@Component
public class ExcelChartCreator {

  /**
   * Adds a chart to the provided sheet based on the contract data.
   *
   * @param workbook the Excel workbook
   * @param sheet the sheet to add the chart to
   * @param contractDataMap map of contract names to their pricing data
   * @param lastRowIndex the last row index of data
   */
  public void addChart(
      Workbook workbook,
      Sheet sheet,
      Map<String, List<PricingDataDto>> contractDataMap,
      int lastRowIndex) {
    Drawing<?> drawing = sheet.createDrawingPatriarch();
    ClientAnchor anchor = createChartAnchor(workbook);
    XSSFChart chart = ((XSSFDrawing) drawing).createChart(anchor);
    chart.setTitleText("Wykres dla wybranych kontraktów");
    chart.setTitleOverlay(false);

    CTChart ctChart = chart.getCTChart();
    ctChart.addNewDispBlanksAs().setVal(STDispBlanksAs.GAP);

    CTPlotArea plotArea = ctChart.getPlotArea();
    CTLineChart lineChart = plotArea.addNewLineChart();
    lineChart.addNewGrouping().setVal(STGrouping.STANDARD);
    lineChart.addNewVaryColors().setVal(false);

    String sheetName = sheet.getSheetName();
    String xAxisRange = String.format("'%s'!$A$2:$A$%d", sheetName, lastRowIndex + 1);
    List<String> contractList = new ArrayList<>(contractDataMap.keySet());
    addAllChartSeries(lineChart, sheetName, contractList, xAxisRange, lastRowIndex);
    configureAxes(lineChart, plotArea, contractDataMap);
    addYAxisTitle(plotArea, contractList);

    CTLegend legend = ctChart.addNewLegend();
    legend.addNewLegendPos().setVal(STLegendPos.T);
    legend.addNewOverlay().setVal(false);
  }

  /**
   * Creates and returns a ClientAnchor for chart placement.
   *
   * @param workbook the Excel workbook
   * @return a configured ClientAnchor
   */
  private ClientAnchor createChartAnchor(Workbook workbook) {
    ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
    anchor.setCol1(10);
    anchor.setRow1(1);
    anchor.setCol2(20);
    anchor.setRow2(30);
    return anchor;
  }

  /**
   * Iterates over the contract list and adds a chart series for each contract.
   *
   * @param lineChart the line chart
   * @param sheetName the sheet name
   * @param contractList list of contract names
   * @param xAxisRange the x-axis data range
   * @param lastRowIndex the last data row index
   */
  private void addAllChartSeries(
      CTLineChart lineChart,
      String sheetName,
      List<String> contractList,
      String xAxisRange,
      int lastRowIndex) {
    for (int i = 0; i < contractList.size(); i++) {
      addChartSeries(lineChart, sheetName, contractList.get(i), i, xAxisRange, lastRowIndex);
    }
  }

  /**
   * Configures the X and Y axes for the chart.
   *
   * @param lineChart the line chart
   * @param plotArea the chart plot area
   */
  private void configureAxes(
      CTLineChart lineChart,
      CTPlotArea plotArea,
      Map<String, List<PricingDataDto>> contractDataMap) {
    CTUnsignedInt xAxisId = lineChart.addNewAxId();
    xAxisId.setVal(123456);
    CTUnsignedInt yAxisId = lineChart.addNewAxId();
    yAxisId.setVal(654321);

    // Configure X-axis (category axis)
    CTCatAx catAx = plotArea.addNewCatAx();
    catAx.addNewAxId().setVal(123456);
    catAx.addNewScaling().addNewOrientation().setVal(STOrientation.MIN_MAX);
    catAx.addNewDelete().setVal(false);
    catAx.addNewAxPos().setVal(STAxPos.B);
    catAx.addNewCrossAx().setVal(654321);
    catAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

    // Configure Y-axis (value axis)
    CTValAx valAx = plotArea.addNewValAx();
    valAx.addNewAxId().setVal(654321);
    CTScaling scaling = valAx.addNewScaling();
    scaling.addNewOrientation().setVal(STOrientation.MIN_MAX);
    double adjustedMin = getAdjustedMinY(contractDataMap);
    scaling.addNewMin().setVal(adjustedMin);

    valAx.addNewDelete().setVal(false);
    valAx.addNewAxPos().setVal(STAxPos.L);
    valAx.addNewCrossAx().setVal(123456);
    valAx.addNewCrosses().setVal(STCrosses.AUTO_ZERO);
    valAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

    // Y Grid lines color #D1CFCF
    CTShapeProperties gridStyle = valAx.addNewMajorGridlines().addNewSpPr();
    CTLineProperties gridLine = gridStyle.addNewLn();
    gridLine.setW(6350); // ~0.75pt
    CTSolidColorFillProperties gridFill = gridLine.addNewSolidFill();
    CTSRgbColor gridColor = gridFill.addNewSrgbClr();
    gridColor.setVal(new byte[] {(byte) 0xD1, (byte) 0xCF, (byte) 0xCF});
  }

  /**
   * Adds a title to the Y-axis based on contract data.
   *
   * @param plotArea the chart plot area
   * @param contractList list of contract names
   */
  private void addYAxisTitle(CTPlotArea plotArea, List<String> contractList) {
    String yAxisLabel = "[PLN/MWh]";
    if (contractList.stream().anyMatch(c -> "PMEF_F".equalsIgnoreCase(c))) {
      yAxisLabel = "[PLN/toe]";
    }
    CTValAx valAx = plotArea.getValAxArray(0);
    CTTitle yAxisTitle = valAx.addNewTitle();
    yAxisTitle.addNewOverlay().setVal(false);
    CTTextBody yAxisTextBody = yAxisTitle.addNewTx().addNewRich();
    yAxisTextBody.addNewBodyPr();
    CTTextParagraph yAxisParagraph = yAxisTextBody.addNewP();
    CTRegularTextRun yAxisRun = yAxisParagraph.addNewR();
    yAxisRun.setT(yAxisLabel);
    yAxisParagraph.addNewPPr().addNewDefRPr();
  }

  /**
   * Adds a single chart series for the specified contract.
   *
   * @param lineChart the line chart
   * @param sheetName the sheet name
   * @param contract the contract name
   * @param seriesIndex the series index
   * @param xAxisRange the x-axis data range
   * @param lastRowIndex the last data row index
   */
  private void addChartSeries(
      CTLineChart lineChart,
      String sheetName,
      String contract,
      int seriesIndex,
      String xAxisRange,
      int lastRowIndex) {
    int colIndex = 1 + (2 * seriesIndex);
    String colLetter = getExcelColumnLetter(colIndex);
    String valuesRange =
        String.format("'%s'!$%s$2:$%s$%d", sheetName, colLetter, colLetter, lastRowIndex + 1);

    CTLineSer ser = lineChart.addNewSer();
    ser.addNewIdx().setVal(seriesIndex);
    ser.addNewOrder().setVal(seriesIndex);

    String seriesTitleCell = String.format("'%s'!$%s$1", sheetName, colLetter);
    CTSerTx tx = ser.addNewTx();
    tx.addNewStrRef().setF(seriesTitleCell);

    ser.addNewCat().addNewStrRef().setF(xAxisRange);
    ser.addNewVal().addNewNumRef().setF(valuesRange);

    // Line color
    CTShapeProperties shapeProperties = ser.addNewSpPr();
    CTLineProperties lineProperties = shapeProperties.addNewLn();
    lineProperties.setW(12700);
    lineProperties.addNewSolidFill().addNewSrgbClr().setVal(pickColorForSeries(seriesIndex));

    CTMarker marker = ser.addNewMarker();
    marker.addNewSymbol().setVal(STMarkerStyle.NONE);
  }

  /**
   * Returns the Excel column letter corresponding to a zero-based column index.
   *
   * @param columnIndex zero-based column index
   * @return the Excel column letter
   */
  private String getExcelColumnLetter(int columnIndex) {
    int temp = columnIndex;
    StringBuilder letter = new StringBuilder();
    while (temp >= 0) {
      letter.insert(0, (char) ('A' + (temp % 26)));
      temp = (temp / 26) - 1;
    }
    return letter.toString();
  }

  private double getAdjustedMinY(Map<String, List<PricingDataDto>> dataMap) {
    double min =
        dataMap.values().stream()
            .flatMap(List::stream)
            .mapToDouble(dto -> dto.getValue().doubleValue())
            .min()
            .orElse(0);

    if (min <= 0) return 0;
    double offset = Math.max(0.1, Math.min(5, 2.5));
    double adjusted = min - offset;

    return Math.floor(adjusted * 10) / 10.0;
  }

  /**
   * Generates an RGB color as a byte array for the given series index. Uses the golden ratio
   * conjugate to compute the hue for evenly distributed colors.
   *
   * @param seriesIndex the index of the series
   * @return a byte array containing the RGB values of the generated color
   */
  private static final byte[][] PALETTE = {
      {(byte)0xEA,(byte)0x56,(byte)0x0D},  // pomarańczowy
      {(byte)0x00,(byte)0x76,(byte)0xB4},  // niebieski
      {(byte)0x3C,(byte)0x9C,(byte)0x0C},  // zielony
      {(byte)0xD6,(byte)0x27,(byte)0x28},  // czerwony
      {(byte)0x94,(byte)0x67,(byte)0xBD},  // fiolet
      {(byte)0xFF,(byte)0xBF,(byte)0x00},  // bursztyn
      {(byte)0x2C,(byte)0xA0,(byte)0xAF},  // morski
      {(byte)0xB2,(byte)0xB2,(byte)0xB2},  // szary
      {(byte)0x8C,(byte)0x56,(byte)0x4A},  // brąz
      {(byte)0xE4,(byte)0x6B,(byte)0x6B}   // łosoś
  };

  private byte[] pickColorForSeries(int idx) {
    if (idx < PALETTE.length) {
      return PALETTE[idx];
    }
    float hue = (float) ((idx * 0.61803398875) % 1);
    int rgb = java.awt.Color.HSBtoRGB(hue, 0.75f, 0.85f);

    return new byte[] {
        (byte) ((rgb >> 16) & 0xFF),
        (byte) ((rgb >>  8) & 0xFF),
        (byte)  (rgb        & 0xFF)
    };
  }
}
