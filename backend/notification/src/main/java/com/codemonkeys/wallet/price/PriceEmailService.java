/* (C)2024-2025 */
package com.codemonkeys.wallet.price;

import static com.codemonkeys.wallet.domain.contract.vo.ContractName.SPOT_CONTRACTS;

import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerContactRepository;
import com.codemonkeys.wallet.domain.price.HistoricalPricesView;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.email.event.EmailEvent;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PriceEmailService {


  private final PriceRepository priceRepo;
  private final PriceTemplate template;
  private final ApplicationEventPublisher publisher;
  private final CustomerContactRepository contactRepo;
  private final PriceCalculator dtoFactory;
  String[] SPOT = SPOT_CONTRACTS.toArray(String[]::new);

  /**
   * Fetches price data, calculates percentage changes, and sends email notifications to all
   * customers subscribed to price alerts.
   */
  public void sendPriceEmails() {

    LocalDate today = LocalDate.now();

    /* 1. ceny dzisiejsze */
    List<Price> todayPrices = priceRepo.findByDate(today).stream()
        .filter(p -> isValidContract(p.getContract().getName()))
        .toList();

    if (todayPrices.isEmpty()) {
      log.warn("Brak notowań na {} – odpuszczam.", today);
      return;
    }

    /* 2. zapytanie z 4 datami */
    LocalDate dMinus1 = today.minusDays(1);
    LocalDate dMinus1M = today.minusMonths(1);
    LocalDate dMinus3M = today.minusMonths(3);
    LocalDate dMinus1Y = today.minusYears(1);

    Map<String, HistoricalPricesView> hist =
        priceRepo.findHistoricalPrices(dMinus1, dMinus1M, dMinus3M, dMinus1Y, SPOT)
            .stream()
            .collect(Collectors.toMap(HistoricalPricesView::getContractName, h -> h));

    /* 3. budujemy DTO bez dodatkowych map */
    List<PriceEmailDTO> dto = todayPrices.stream()
        .sorted(Comparator.comparing(p -> p.getContract().getName()))
        .map(p -> {
          HistoricalPricesView h = hist.get(p.getContract().getName());
          return dtoFactory.create(
              p,
              h != null ? h.getDailyPrice() : null,
              h != null ? h.getMonthlyPrice() : null,
              h != null ? h.getQuarterlyPrice() : null,
              h != null ? h.getYearlyPrice() : null);
        })
        .toList();

    /* 4. wysyłka */
    contactRepo.findAllDistinctPriceContacts()
        .forEach(c -> sendEmail(c, dto));
  }


  private void sendEmail(CustomerContact c, List<PriceEmailDTO> dto) {
    Map<String, Object> vars = Map.of("prices", dto);

    try {
      publisher.publishEvent(new EmailEvent(
          this,
          c.getEmail().getValue(),
          template.determineSubject(c),
          template.determineTemplateName(c),
          vars));
    } catch (Exception ex) {
      log.error("Failed to send price email to {}", c.getEmail().getValue(), ex);
    }
  }

  /**
   * Checks if the contract does NOT contain unwanted patterns and is NOT on the blocked list.
   *
   * @param name the name of the contract to check
   * @return true if the contract should be included
   */
  private boolean isValidContract(String name) {
    List<String> blocked = List.of("TGe15", "TGe9", "TGeBase", "TGeOffpeak", "TGePeak");
    if (name.matches("TGe24-H(1[0-9]|2[0-4]|[1-9])")) {
      return false;
    }
    if (blocked.contains(name)) {
      return false;
    }
    return !(name.contains("S-W") || name.contains("W-") || name.contains("S-S"));
  }
}