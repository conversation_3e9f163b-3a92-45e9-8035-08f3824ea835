/* (C)2024-2025 */
package com.codemonkeys.wallet.price;

import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.email.event.EmailEvent;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PriceEmailService {

  private final PriceRepository priceRepository;
  private final CustomerRepository customerRepository;
  private final PriceTemplate emailTemplateDataPreparer;
  private final ApplicationEventPublisher eventPublisher;

  /**
   * Fetches price data, calculates percentage changes, and sends email notifications to all
   * customers subscribed to price alerts.
   */
  public void sendPriceEmails() {
    // Fetch today's prices
    List<Price> currentPrices = priceRepository.findByDate(LocalDate.now());

    // Apply filtering to remove unwanted contract names
    currentPrices =
        currentPrices.stream()
            .filter(price -> isValidContract(price.getContract().getName()))
            .toList();

    // Fetch previous day prices and average prices for different periods
    LocalDate lastAvailableDate = findLastAvailableDate(LocalDate.now().minusDays(1));

    if (lastAvailableDate == null) {
      log.error("No data available for the last 30 days. Skipping email generation.");
      return;
    }

    Map<String, Price> previousPriceMap = getPreviousPrices();
    Map<String, BigDecimal> monthlyPriceMap =
        calculateAveragePriceForPeriod(LocalDate.now().minusMonths(1), lastAvailableDate);
    Map<String, BigDecimal> quarterlyPriceMap =
        calculateAveragePriceForPeriod(LocalDate.now().minusMonths(3), lastAvailableDate);
    Map<String, BigDecimal> yearlyPriceMap =
        calculateAveragePriceForPeriod(LocalDate.now().minusYears(1), lastAvailableDate);

    // Prepare email data
    List<PriceEmailDTO> priceEmailDTOs =
        emailTemplateDataPreparer.preparePriceEmailData(
            currentPrices, previousPriceMap, monthlyPriceMap, quarterlyPriceMap, yearlyPriceMap);

    // Fetch customers with price notification enabled and send emails
    List<Customer> customers = customerRepository.findAllWithPriceNotificationEnabledWithContacts();

    customers.forEach(customer -> sendEmailsToCustomerContacts(customer, priceEmailDTOs));
  }

  /**
   * Retrieves the price data from the previous day or the last available date.
   *
   * @return a map containing prices from the previous day or the last available date, mapped by
   *     contract name.
   */
  private Map<String, Price> getPreviousPrices() {
    LocalDate date = findLastAvailableDate(LocalDate.now().minusDays(1));

    if (date == null) {
      log.error("No previous prices available for the last 30 days.");
      return Map.of();
    }

    List<Price> prices = priceRepository.findByDate(date);
    return prices.stream()
        .collect(Collectors.toMap(price -> price.getContract().getName(), price -> price));
  }

  /**
   * Calculates the average price for a given period (e.g., month, quarter, year).
   *
   * @param startDate the start date of the period
   * @param endDate the end date of the period
   * @return a map containing the average prices per contract for the given period.
   */
  private Map<String, BigDecimal> calculateAveragePriceForPeriod(
      LocalDate startDate, LocalDate endDate) {
    List<Price> prices = priceRepository.findByDateBetween(startDate, endDate);

    if (prices.isEmpty()) {
      log.warn("No prices found between {} and {}", startDate, endDate);
      return Map.of();
    }

    return prices.stream()
        .collect(
            Collectors.groupingBy(
                price -> price.getContract().getName(),
                Collectors.mapping(
                    price -> price.getValue().getValue(),
                    Collectors.averagingDouble(BigDecimal::doubleValue))))
        .entrySet()
        .stream()
        .collect(
            Collectors.toMap(Map.Entry::getKey, entry -> BigDecimal.valueOf(entry.getValue())));
  }

  /**
   * Finds the last available date with price data starting from the given date.
   *
   * @param date the starting date
   * @return the last available date with price data
   */
  private LocalDate findLastAvailableDate(LocalDate date) {
    for (int attempts = 0; attempts < 30; attempts++) {
      if (!priceRepository.findByDate(date).isEmpty()) {
        return date;
      }
      date = date.minusDays(1);
    }

    log.warn("No data available within the last 30 days from {}", date);
    return null;
  }

  /**
   * Sends email notifications to all contacts of a given customer who have price alerts enabled.
   *
   * @param customer the customer whose contacts should receive the emails
   * @param priceEmailDTOs the price data to be included in the emails
   */
  private void sendEmailsToCustomerContacts(Customer customer, List<PriceEmailDTO> priceEmailDTOs) {
    customer.getContacts().stream()
        .filter(
            contact ->
                contact.getConfiguration().getPrices() && contact.getConfiguration().getEmail())
        .forEach(contact -> sendEmailToContact(contact, priceEmailDTOs));
  }

  /**
   * Sends an email to a single customer contact with the provided price data.
   *
   * @param contact the contact to send the email to
   * @param priceEmailDTOs the price data to be included in the email
   */
  private void sendEmailToContact(CustomerContact contact, List<PriceEmailDTO> priceEmailDTOs) {
    Map<String, Object> variables = new HashMap<>();
    variables.put("prices", priceEmailDTOs);

    String subject = emailTemplateDataPreparer.determineSubject(contact);
    String templateName = emailTemplateDataPreparer.determineTemplateName(contact);

    try {
      EmailEvent emailEvent =
          new EmailEvent(this, contact.getEmail().getValue(), subject, templateName, variables);
      eventPublisher.publishEvent(emailEvent);
    } catch (Exception e) {
      log.error("Failed to send email to contact: {}", contact.getEmail().getValue(), e);
    }
  }

  /**
   * Checks if the contract does NOT contain unwanted patterns and is NOT on the blocked list.
   *
   * @param contractName the name of the contract to check
   * @return true if the contract should be included
   */
  private boolean isValidContract(String contractName) {
    List<String> blockedContracts = List.of("TGe15", "TGe9", "TGeBase", "TGeOffpeak", "TGePeak");

    if (blockedContracts.contains(contractName)) {
      return false;
    }
    return !(contractName.contains("S-W")
        || contractName.contains("W-")
        || contractName.contains("S-S"));
  }
}
