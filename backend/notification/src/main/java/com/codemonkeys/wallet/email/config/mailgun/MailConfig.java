/* (C)2024-2025 */
package com.codemonkeys.wallet.email.config.mailgun;

import java.util.Properties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

@ConfigurationProperties(prefix = "spring.mail")
@ConditionalOnProperty(prefix = "email", name = "provider", havingValue = "smtp", matchIfMissing = true)
@Configuration
@Getter
@Setter
public class MailConfig {

  private String host;
  private int port;
  private String username;
  private String password;
  private boolean smtpAuth;
  private boolean startTlsEnable;
  private String mimeCharset;
  private boolean debug;

  @Bean
  public JavaMailSender javaMailSender() {
    JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
    mailSender.setHost(host);
    mailSender.setPort(port);
    mailSender.setUsername(username);
    mailSender.setPassword(password);

    Properties props = mailSender.getJavaMailProperties();
    props.put("mail.transport.protocol", "smtp");
    props.put("mail.smtp.auth", smtpAuth);
    props.put("mail.smtp.starttls.enable", startTlsEnable);
    props.put("mail.mime.charset", mimeCharset);
    props.put("mail.debug", debug);

    return mailSender;
  }
}
