/* (C)2024-2025 */
package com.codemonkeys.wallet.price;

import com.codemonkeys.wallet.domain.customer.CustomerContact;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PriceTemplate {

  /**
   * Determines the subject line to use for the price email, based on the customer's language.
   *
   * @param contact the customer contact with language preference
   * @return localized subject string ("Daily Energy Prices" or "Dzienne ceny energii")
   */
  public String determineSubject(CustomerContact contact) {
    return "EN".equalsIgnoreCase(contact.getLanguage().name())
        ? "Daily Energy Prices"
        : "Dzienne ceny energii";
  }

  /**
   * Determines which email template name to use for rendering the price email.
   *
   * @param contact the customer contact with language preference
   * @return template name string ("price-template-eng" or "price-template-pl")
   */
  public String determineTemplateName(CustomerContact contact) {
    return "EN".equalsIgnoreCase(contact.getLanguage().name())
        ? "price-template-eng"
        : "price-template-pl";
  }
}