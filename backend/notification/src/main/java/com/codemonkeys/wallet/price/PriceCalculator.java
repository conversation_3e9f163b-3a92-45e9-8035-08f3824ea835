package com.codemonkeys.wallet.price;

import com.codemonkeys.wallet.domain.price.Price;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PriceCalculator {

  public PriceEmailDTO create(
      Price currentPrice,
      BigDecimal previousValue,
      BigDecimal monthlyValue,
      BigDecimal quarterlyValue,
      BigDecimal yearlyValue) {

    BigDecimal curr = currentPrice.getValue().getValue();
    BigDecimal prev = previousValue != null ? previousValue : BigDecimal.ZERO;

    BigDecimal dailyDelta = diff(prev, curr);
    BigDecimal monthlyDelta = diff(monthlyValue, curr);
    BigDecimal quarterlyDelta = diff(quarterlyValue, curr);
    BigDecimal yearlyDelta = diff(yearlyValue, curr);

    String css = cssClass(dailyDelta);

    log.debug("DTO {} | prev={} | curr={} | Δd={} | Δm={} | Δq={} | Δy={}",
        currentPrice.getContract().getName(),
        prev, curr, dailyDelta, monthlyDelta, quarterlyDelta, yearlyDelta);

    return new PriceEmailDTO(
        currentPrice.getContract().getName(),
        prev,
        curr,
        dailyDelta,
        monthlyDelta,
        quarterlyDelta,
        yearlyDelta,
        css);
  }


  /**
   * Calculates the percentage difference between two values.
   *
   * @param prev the previous value (may be null)
   * @param curr the current value
   * @return percentage change between prev and curr, rounded to 2 decimal places
   */
  private BigDecimal diff(BigDecimal prev, BigDecimal curr) {
    if (prev == null || prev.compareTo(BigDecimal.ZERO) == 0) {
      return BigDecimal.ZERO;
    }
    return curr.subtract(prev)
        .divide(prev, 4, RoundingMode.HALF_UP)
        .multiply(BigDecimal.valueOf(100))
        .setScale(2, RoundingMode.HALF_UP);
  }

  private String cssClass(BigDecimal delta) {
    int sign = delta.compareTo(BigDecimal.ZERO);
    if (sign > 0) {
      return "positive";
    }
    if (sign < 0) {
      return "negative";
    }
    return "neutral";
  }
}