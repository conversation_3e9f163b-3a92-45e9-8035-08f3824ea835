/* (C)2024-2025 */
package com.codemonkeys.wallet.email.handler;

import com.codemonkeys.wallet.application.LogEvent;
import com.codemonkeys.wallet.domain.wallets.vo.EventType;
import com.codemonkeys.wallet.email.config.EmailTechnicalProperties;
import com.codemonkeys.wallet.email.event.EmailEvent;
import com.codemonkeys.wallet.email.event.EmailEventAttachment;
import com.codemonkeys.wallet.email.event.EmailTechnicalEvent;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Event handler for processing email-related events. Handles events for sending emails with or
 * without attachments and logs the results.
 */
@Component
@Slf4j
public class EmailHandler {

  private final EmailServiceHandler emailService;
  private final ApplicationEventPublisher eventPublisher;
  private final EmailTechnicalProperties emailOverrideProperties;

  /**
   * Constructs an {@code EmailHandler} with the provided email service and event publisher.
   *
   * @param emailService the service used to send emails.
   * @param eventPublisher the publisher used to log email-related events.
   */
  public EmailHandler(
      EmailServiceHandler emailService,
      ApplicationEventPublisher eventPublisher,
      EmailTechnicalProperties emailOverrideProperties) {
    this.emailService = emailService;
    this.eventPublisher = eventPublisher;
    this.emailOverrideProperties = emailOverrideProperties;
  }

  /**
   * Handles {@link EmailEvent} by sending a regular email without an attachment.
   *
   * @param event the email event containing the email details.
   */
  @EventListener
  public void handleEmailEvent(EmailEvent event) {
    if (event instanceof EmailTechnicalEvent) {
      log.info("Handling technical email event.");
      handleTechnicalEmail((EmailTechnicalEvent) event);
      return;
    }
    if (event instanceof EmailEventAttachment) {
      log.info("Ignoring event without attachment for event with attachment.");
      return;
    }
    try {
      emailService.sendEmail(
          event.getRecipient(), event.getSubject(), event.getTemplateName(), event.getVariables());
      logEventSuccess(event);
    } catch (Exception e) {
      logEventFailure(event, e);
    }
  }

  /**
   * Handles {@link EmailEventAttachment} by sending an email with an attachment.
   *
   * @param event the email event containing the email and attachment details.
   */
  @EventListener
  public void handleEmailEventWithAttachment(EmailEventAttachment event) {
    try {
      emailService.sendEmailWithAttachment(
          event.getRecipient(),
          event.getSubject(),
          event.getTemplateName(),
          event.getVariables(),
          event.getAttachment(),
          event.getAttachmentName());
      logEventSuccess(event);
    } catch (Exception e) {
      logEventFailure(event, e);
    }
  }

  /**
   * Handles the sending of technical emails. If email override addresses are enabled in the
   * configuration, the email will be sent to those addresses instead of the recipient specified in
   * the event. If override addresses are not enabled, the email will be sent to the original
   * recipient.
   *
   * @param event the {@link EmailTechnicalEvent} that contains details of the email to be sent.
   */
  private void handleTechnicalEmail(EmailTechnicalEvent event) {
    List<String> technicalRecipients =
        emailOverrideProperties.isEnabled()
            ? emailOverrideProperties.getRecipients()
            : List.of(event.getRecipient());

    technicalRecipients.forEach(
        recipient -> {
          try {
            emailService.sendEmail(
                recipient, event.getSubject(), event.getTemplateName(), event.getVariables());
            log.info("Technical email sent to: {}", recipient);
          } catch (Exception e) {
            log.error("Failed to send technical email to: {}", recipient, e);
            logEventFailure(event, e);
          }
        });

    logEventSuccess(event);
  }

  /**
   * Logs a successful email event and publishes a {@link LogEvent} with a success status.
   *
   * @param event the {@link EmailEvent} that was successfully processed.
   */
  private void logEventSuccess(EmailEvent event) {
    String details =
        String.format(
            "Email to: %s, Subject: %s, Success: true", event.getRecipient(), event.getSubject());
    eventPublisher.publishEvent(new LogEvent(this, EventType.EMAIL_SENT_SUCCESS, details));
  }

  /**
   * Logs a failed email event and publishes a {@link LogEvent} with a failure status. The failure
   * is logged and published along with the exception that caused the failure.
   *
   * @param event the {@link EmailEvent} that failed to be processed.
   * @param e the {@link Exception} that caused the failure.
   */
  private void logEventFailure(EmailEvent event, Exception e) {
    log.error("Failed to send email to {}", event.getRecipient(), e);
    String details =
        String.format(
            "Email to: %s, Subject: %s, Success: false", event.getRecipient(), event.getSubject());
    eventPublisher.publishEvent(new LogEvent(this, EventType.EMAIL_SENT_FAILURE, details));
  }
}
