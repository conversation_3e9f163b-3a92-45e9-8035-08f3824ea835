package com.codemonkeys.wallet.email.config.azure;

import com.azure.communication.email.EmailClient;
import com.azure.communication.email.EmailClientBuilder;
import com.azure.core.credential.AzureKeyCredential;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "acs")
@ConditionalOnProperty(prefix = "email", name = "provider", havingValue = "acs")
@Getter
@Setter
class AcsConfig {

  private String endpoint;
  private String accessKey;

  @Bean
  EmailClient emailClient() {
    return new EmailClientBuilder()
        .endpoint(endpoint)
        .credential(new AzureKeyCredential(accessKey))
        .buildClient();
  }
}