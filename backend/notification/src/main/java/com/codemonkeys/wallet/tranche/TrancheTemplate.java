/* (C)2024-2025 */
package com.codemonkeys.wallet.tranche;

import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.email.config.EmailTemplateProperties;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TrancheTemplate {

  private final EmailTemplateProperties emailTemplateProperties;

  /**
   * Prepares the data for the email template for multiple tranches.
   *
   * @param tranches the list of tranches for which the email data is being prepared.
   * @param contact the customer contact whose language will be used for determining wallet type.
   * @return a map of variables to be used in the email template.
   */
  public Map<String, Object> prepareTemplateDataForMultipleTranches(
      List<Tranche> tranches, CustomerContact contact) {
    Map<String, Object> variables = new HashMap<>();
    variables.put("tranches", tranches);
    variables.put(
        "walletType",
        determineWalletType(tranches.get(0).getWallet().getMediaType().name(), contact));
    variables.put("applicationUrl", emailTemplateProperties.getApplicationUrl());
    return variables;
  }

  /**
   * Determines the type of the wallet based on its media type and the contact's language.
   *
   * @param mediaType the media type of the wallet (ENERGY or GAS).
   * @param contact the customer contact whose language will be used to determine wallet type.
   * @return the corresponding wallet type as a string, based on the contact's language.
   */
  public String determineWalletType(String mediaType, CustomerContact contact) {
    String language = contact.getLanguage().name();

    if ("ENERGY".equals(mediaType)) {
      return "EN".equals(language) ? "electricity" : "energii elektrycznej";
    } else if ("GAS".equals(mediaType)) {
      return "EN".equals(language) ? "gas" : "gazu";
    } else {
      return "EN".equals(language) ? "unknown type" : "nieznanego typu";
    }
  }

  /**
   * Determines the subject of the email based on the contact's language.
   *
   * @param contact the customer contact for whom the email is being sent.
   * @return the subject of the email.
   */
  public String determineSubject(CustomerContact contact) {
    return "EN".equals(contact.getLanguage().name()) ? "New tranche added" : "Nowa transza dodana";
  }

  /**
   * Determines the template name to be used for the email based on the contact's language.
   *
   * @param contact the customer contact for whom the email is being sent.
   * @return the name of the template to be used for the email.
   */
  public String determineTemplateName(CustomerContact contact) {
    return "EN".equals(contact.getLanguage().name())
        ? "tranche-addition-template-eng"
        : "tranche-addition-template-pl";
  }
}
