/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.list;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListService;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListServiceImpl;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.SpecHelper;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * Service implementation for listing agreements. This class extends {@link
 * ProjectionListServiceImpl} and provides logic for filtering and paginating agreements.
 */
@Service
@Slf4j
public class ListAgreementServiceImpl
    extends ProjectionListServiceImpl<
        ListAgreementRequest,
        ListAgreementResponse,
        Agreement,
        AgreementId,
        AgreementRepository,
        ListAgreementMapper>
    implements ProjectionListService<ListAgreementRequest, ListAgreementResponse> {

  /**
   * Constructor for {@link ListAgreementServiceImpl}.
   *
   * @param repository the repository for managing {@link Agreement} entities.
   * @param mapper the mapper for converting {@link Agreement} entities into DTOs.
   */
  public ListAgreementServiceImpl(AgreementRepository repository, ListAgreementMapper mapper) {
    super(repository, mapper, ListAgreementResponse.class);
  }

  /**
   * Prepares a specification for filtering agreements based on the provided request.
   *
   * @param request the request object containing filtering parameters.
   * @return a {@link Specification} for filtering agreements.
   */
  @Override
  protected Specification<Agreement> prepareSpecification(ListAgreementRequest request) {
    Specification<Agreement> specification = SpecHelper.filterBy(request.getFilters());

    if (request.getContractYear() != null) {
      Integer year = request.getContractYear();

      specification =
          specification.and(
              (root, query, builder) ->
                  builder.equal(
                      builder.function(
                          "date_part",
                          Integer.class,
                          builder.literal("year"),
                          root.get("startDate")),
                      year));
    }
    cleanFilters(request);
    return specification;
  }

  private void cleanFilters(ListAgreementRequest request) {
    if (request.getFilters() != null) {
      request.getFilters().removeIf(filter -> "contractYear".equals(filter.getId()));
      request.getFilters().removeIf(filter -> "withArchive".equals(filter.getId()));
    }
  }

  /**
   * Lists agreements based on the provided request.
   *
   * @param request the request object containing pagination and filtering parameters.
   * @return a {@link Page} of {@link ListAgreementResponse} DTOs.
   */
  @Override
  public Page<ListAgreementResponse> list(ListAgreementRequest request) {
    return super.list(request);
  }
}
