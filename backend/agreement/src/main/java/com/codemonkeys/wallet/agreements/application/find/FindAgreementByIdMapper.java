/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.find;

import com.codemonkeys.wallet.common.framework.crud.findbyid.FindByIdMapper;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import org.springframework.stereotype.Service;

/**
 * Mapper for converting Agreement to FindAgreementByIdResponse DTOs, handling both successful finds
 * and cases where the agreement does not exist.
 */
@Service
public class FindAgreementByIdMapper
    implements FindByIdMapper<Agreement, AgreementId, FindAgreementByIdResponse> {

  /**
   * Converts an Agreement entity to a DTO for successful find operations.
   *
   * @param agreement the Agreement entity to be converted.
   * @return a DTO representing the successfully found agreement.
   */
  @Override
  public FindAgreementByIdResponse toDto(Agreement agreement) {
    return new FindAgreementByIdResponse(
        ValidResult.of(),
        agreement.getId(),
        agreement.getMediaType().toString(),
        agreement.getStartDate(),
        agreement.getEndDate(),
        agreement.getAverageCalculationStartDate(),
        agreement.getContracts(),
        // new HashSet<>(),
        agreement.getAuthorizedBuyers(),
        agreement.getCustomer(),
        agreement.getSupplier(),
        agreement.getVolumes(),
        agreement.getMedia(),
        agreement.getPropertyRights(),
        agreement.getDescription(),
        agreement.getAgreementGroup(),
        agreement.isRequiresCustomerAcceptance(),
        agreement.isSendRecommendation());
  }

  /**
   * Creates a DTO representing a failed find operation due to validation errors.
   *
   * @param invalidResult the invalid result containing details about the failure.
   * @return a DTO indicating a failed find operation with error messages.
   */
  @Override
  public FindAgreementByIdResponse toDto(InvalidResult invalidResult) {
    return new FindAgreementByIdResponse(invalidResult);
  }
}
