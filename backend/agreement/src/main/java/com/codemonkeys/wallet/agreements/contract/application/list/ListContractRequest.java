/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.contract.application.list;

import com.codemonkeys.wallet.common.framework.shared.dto.ListRequest;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import java.util.Map;
import java.util.UUID;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@Data
@Slf4j
public class ListContractRequest extends ListRequest {
  private AgreementId agreementId;
  private boolean applyFilter = false;

  /**
   * Constructor that initializes the request object based on the provided parameters.
   *
   * @param params a map of request parameters used to initialize the request.
   */
  public ListContractRequest(Map<String, String> params) {
    this.page = parsePage(params.get("page"));
    this.limit = parseLimit(params.get("limit"));
    this.agreementId = AgreementId.of(UUID.fromString(params.get("agreementId")));
    this.applyFilter = Boolean.parseBoolean(params.getOrDefault("applyFilter", "false"));
  }

  /**
   * Parses the page parameter from the request.
   *
   * @param pageParam the page parameter as a string.
   * @return the parsed page number, defaulting to 0 if invalid.
   */
  private int parsePage(String pageParam) {
    try {
      int page = Integer.parseInt(pageParam);
      return page < 0 ? 0 : page;
    } catch (NumberFormatException e) {
      log.warn("Invalid page parameter: {}. Defaulting to 1.", pageParam);
      return 0;
    }
  }

  /**
   * Parses the limit parameter from the request.
   *
   * @param limitParam the limit parameter as a string.
   * @return the parsed limit value, defaulting to 1 if invalid.
   */
  private int parseLimit(String limitParam) {
    try {
      return Integer.parseInt(limitParam);
    } catch (NumberFormatException e) {
      log.warn("Invalid limit parameter: {}. Defaulting to 1.", limitParam);
      return 1;
    }
  }

  public int getZeroBasedPage() {
    return this.page - 1;
  }

  /**
   * Converts the request object to a {@link Pageable} object for pagination purposes.
   *
   * @return a {@link Pageable} object representing the pagination settings.
   */
  public Pageable toPageable() {
    return PageRequest.of(this.page, this.limit);
  }
}
