/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.list;

import java.time.LocalDate;
import java.time.LocalDateTime;

public interface ListAgreementResponse {
  String getId();

  String getMediaType();

  LocalDate getStartDate();

  LocalDate getEndDate();

  LocalDateTime getCreatedAt();

  LocalDateTime getUpdatedAt();

  String getPurchaseModel();

  VolumesResponse getVolumes();

  String getStatus();

  String getDescription();

  SupplierResponse getSupplier();

  CustomerResponse getCustomer();

  AgreementGroupResponse getAgreementGroup();

  boolean isRequiresCustomerAcceptance();

  boolean isSendRecommendation();

  String getHumanReadableAgreementId();

  interface VolumesResponse {
    String getSummary();
  }

  interface SupplierResponse {
    String getId();

    String getName();
  }

  interface CustomerResponse {
    String getId();

    String getName();
  }

  interface AgreementGroupResponse {
    String getId();

    String getName();
  }
}
