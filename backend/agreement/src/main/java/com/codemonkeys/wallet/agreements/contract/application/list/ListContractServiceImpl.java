/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.contract.application.list;

import com.codemonkeys.wallet.agreements.contract.application.listAllOption.ContractSorter;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListService;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListServiceImpl;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import java.time.LocalDate;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ListContractServiceImpl
    extends ProjectionListServiceImpl<
        ListContractRequest,
        ListContractResponse,
        Contract,
        ContractId,
        ContractRepository,
        ListContractMapper>
    implements ProjectionListService<ListContractRequest, ListContractResponse> {

  private final ContractSorter contractSorter;

  /**
   * Constructor that injects the required dependencies.
   *
   * @param repository the repository used to access contracts.
   * @param mapper the mapper used to convert entities to DTOs.
   */
  public ListContractServiceImpl(
      ContractRepository repository, ListContractMapper mapper, ContractSorter contractSorter) {
    super(repository, mapper, ListContractResponse.class);
    this.contractSorter = contractSorter;
  }

  /**
   * Prepares the specification for filtering contracts based on the provided request.
   *
   * @param request the request object containing filtering information.
   * @return a {@link Specification} for filtering contracts.
   */
  @Override
  protected Specification<Contract> prepareSpecification(ListContractRequest request) {
    return (root, query, cb) -> cb.equal(root.get("agreementId"), request.getAgreementId());
  }

  /**
   * Retrieves a paginated list of contracts with optional filtering.
   *
   * <p>If {@code applyFilter} in the request is {@code true}, only eligible contracts are included.
   *
   * @param request the request containing filtering and pagination details.
   * @return a {@link Page} of {@link ListContractResponse} DTOs.
   */
  @Override
  public Page<ListContractResponse> list(ListContractRequest request) {
    Page<Contract> contracts =
        repository.findAllByAgreementIdOrderByNameDesc(
            request.getAgreementId(), request.toPageable());
    List<Contract> sortedContracts = contractSorter.sort(contracts);
    if (request.isApplyFilter()) {
      sortedContracts =
          sortedContracts.stream().filter(c -> c.purchasableAt(LocalDate.now())).toList();
    }
    return mapper.toDto(
        new PageImpl<>(sortedContracts, contracts.getPageable(), sortedContracts.size()));
  }
}
