/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.create.service;

import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.calendar.ProductCalendar;
import com.codemonkeys.wallet.domain.calendar.ProductCalendarRepository;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.contract.vo.ContractName;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultFinalDateService implements ContractFinalDateService {
  ProductCalendarRepository productCalendarRepository;
  PredicateService predicateService;

  @Autowired
  public DefaultFinalDateService(
      ProductCalendarRepository productCalendarRepository, PredicateService predicateService) {
    this.productCalendarRepository = productCalendarRepository;
    this.predicateService = predicateService;
  }

  @Override
  public LocalDate getFinalDate(
      CreateContractRequest.FinalPurchaseDate finalPurchaseDate, ContractName name, Year year) {
    return switch (finalPurchaseDate.getType()) {
      case WORK_DAY_BEFORE_LAST_TRADING ->
          getDateForWorkDayBeforeLastTrading(finalPurchaseDate, name, year);
      case WORK_DAY_BEFORE_DELIVERY ->
          getDateForWorkDayBeforeDelivery(finalPurchaseDate, name, year);
      case CALENDAR_DAY_BEFORE_LAST_TRADING ->
          getDateForCalendarDayBeforeLastTrading(finalPurchaseDate, name, year);
      case CALENDAR_DAY_BEFORE_DELIVERY ->
          getDateForCalendarDayBeforeDelivery(finalPurchaseDate, name, year);
      case DAY_OF_MONTH_BEFORE_DELIVERY ->
          getDateForDayOfMonthBeforeDelivery(finalPurchaseDate, name, year);
      default ->
          throw new IllegalStateException("Unexpected value: " + finalPurchaseDate.getType());
    };
  }

  /**
   * Calculates the final date based on the day of the month before delivery.
   *
   * @param finalPurchaseDate the final purchase date details
   * @param name the contract name
   * @param year the year of the contract
   * @return the calculated final date
   */
  private LocalDate getDateForDayOfMonthBeforeDelivery(
      CreateContractRequest.FinalPurchaseDate finalPurchaseDate, ContractName name, Year year) {
    int dayOfMonth = finalPurchaseDate.getValue();
    ProductCalendar productCalendar = productCalendarRepository.findByInstrument(name.getValue());
    if (productCalendar != null) {
      LocalDate result = productCalendar.getFirstDeliveryDate();
      result = result.minusMonths(1);
      result = result.withDayOfMonth(dayOfMonth);
      return result;
    } else if (!name.getValue().equals(ContractType.TGe24.name())) {
      Month month = name.getTimeUnit().toMonth();
      YearMonth ym = YearMonth.of(year.getYearAsInt(), month.getValue());
      return LocalDate.of(
          Integer.parseInt(year.getYear()), name.getTimeUnit().toMonth(), ym.lengthOfMonth());
    } else {
      return LocalDate.of(Integer.parseInt(year.getYear()), Month.DECEMBER, 31);
    }
  }

  /**
   * Calculates the final date based on a number of calendar days before the delivery date.
   *
   * @param finalPurchaseDate the final purchase date details
   * @param name the contract name
   * @param year the year of the contract
   * @return the calculated final date
   */
  private LocalDate getDateForCalendarDayBeforeDelivery(
      CreateContractRequest.FinalPurchaseDate finalPurchaseDate, ContractName name, Year year) {
    int daysBefore = finalPurchaseDate.getValue();
    ProductCalendar productCalendar = productCalendarRepository.findByInstrument(name.getValue());
    if (productCalendar != null) {
      return valueForCalendarDays(productCalendar.getFirstDeliveryDate(), daysBefore);
    } else {
      return LocalDate.of(Integer.parseInt(year.getYear()), Month.DECEMBER, 31);
    }
  }

  /**
   * Calculates the final date based on a number of calendar days before the last trading day.
   *
   * @param finalPurchaseDate the final purchase date details
   * @param name the contract name
   * @param year the year of the contract
   * @return the calculated final date
   */
  private LocalDate getDateForCalendarDayBeforeLastTrading(
      CreateContractRequest.FinalPurchaseDate finalPurchaseDate, ContractName name, Year year) {
    int daysBefore = finalPurchaseDate.getValue();
    ProductCalendar productCalendar = productCalendarRepository.findByInstrument(name.getValue());
    if (productCalendar != null) {
      return valueForCalendarDays(productCalendar.getLastTradingDay(), daysBefore);
    } else {
      return LocalDate.of(Integer.parseInt(year.getYear()), Month.DECEMBER, 31);
    }
  }

  /**
   * Calculates the final date based on a number of workdays before the delivery date.
   *
   * @param finalPurchaseDate the final purchase date details
   * @param name the contract name
   * @param year the year of the contract
   * @return the calculated final date
   */
  private LocalDate getDateForWorkDayBeforeDelivery(
      CreateContractRequest.FinalPurchaseDate finalPurchaseDate, ContractName name, Year year) {
    int daysBefore = finalPurchaseDate.getValue();
    ProductCalendar productCalendar = productCalendarRepository.findByInstrument(name.getValue());
    if (productCalendar != null) {
      return valueForWorkDays(productCalendar.getFirstDeliveryDate(), daysBefore);
    } else {
      return LocalDate.of(Integer.parseInt(year.getYear()), Month.DECEMBER, 31);
    }
  }

  /**
   * Calculates the final date based on a number of workdays before the last trading day.
   *
   * @param finalPurchaseDate the final purchase date details
   * @param name the contract name
   * @param year the year of the contract
   * @return the calculated final date
   */
  private LocalDate getDateForWorkDayBeforeLastTrading(
      CreateContractRequest.FinalPurchaseDate finalPurchaseDate, ContractName name, Year year) {
    int daysBefore = finalPurchaseDate.getValue();
    ProductCalendar productCalendar = productCalendarRepository.findByInstrument(name.getValue());
    if (productCalendar != null) {
      return valueForWorkDays(productCalendar.getLastTradingDay(), daysBefore);
    } else {
      return LocalDate.of(Integer.parseInt(year.getYear()), Month.DECEMBER, 31);
    }
  }

  /**
   * Calculates a date by subtracting a specified number of calendar days from a starting date.
   *
   * @param start the starting date
   * @param days the number of days to subtract
   * @return the calculated date
   */
  private LocalDate valueForCalendarDays(LocalDate start, int days) {
    LocalDate result = start;
    while (days > 0) {
      result = result.minusDays(1);
      days--;
    }
    return result;
  }

  /**
   * Calculates a date by subtracting a specified number of workdays from a starting date.
   *
   * @param start the starting date
   * @param days the number of workdays to subtract
   * @return the calculated date
   */
  private LocalDate valueForWorkDays(LocalDate start, int days) {
    LocalDate result = start;
    while (days > 0) {
      result = result.minusDays(1);
      boolean workday = predicateService.isWorkDay(result);
      if (workday) {
        days--;
      }
    }
    return result;
  }
}
