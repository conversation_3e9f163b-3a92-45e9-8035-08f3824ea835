/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.list;

import com.codemonkeys.wallet.common.framework.shared.dto.ListRequest;
import com.codemonkeys.wallet.common.framework.shared.dto.RequestInitializer;
import java.util.Map;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class ListAgreementRequest extends ListRequest {

  private Integer contractYear;

  public ListAgreementRequest(Map<String, String> params) {
    super();
    RequestInitializer.initializePagination(this, params);
    initializeSpecificFilters(params);
    RequestInitializer.initializeFilters(this, params);
    RequestInitializer.initializeSorting(this, params);
    RequestInitializer.initializeGlobalFilter(this, params);
  }

  private void initializeSpecificFilters(Map<String, String> params) {
    this.contractYear = parseContractYear(params.get("contractYear"));
  }

  private Integer parseContractYear(String contractYearParam) {
    try {
      return contractYearParam != null ? Integer.parseInt(contractYearParam) : null;
    } catch (NumberFormatException e) {
      log.warn("Invalid contractYear parameter: {}. Skipping filter by year.", contractYearParam);
      return null;
    }
  }
}
