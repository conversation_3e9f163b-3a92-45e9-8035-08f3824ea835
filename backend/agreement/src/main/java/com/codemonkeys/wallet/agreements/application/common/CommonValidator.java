/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.common;

import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementRequest;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationMessage;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementNumber;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.wallets.TranchesRepository;
import jakarta.persistence.EntityNotFoundException;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import net.xyzsd.dichotomy.Result;

public class CommonValidator {

  /**
   * Validates the existence of an agreement with the given ID in the repository.
   *
   * @param agreementRepository the repository to check for the existence of the agreement.
   * @param agreementId the ID of the agreement to validate.
   * @return a Result indicating whether the validation passed (agreement exists) or failed
   *     (agreement does not exist).
   */
  public static Result<ValidResult, InvalidResult> validateExistence(
      AgreementRepository agreementRepository, AgreementId agreementId) {
    if (agreementId == null || !agreementRepository.existsById(agreementId)) {
      String errorMessage = I18n.translate("error.agreement.does.not.exist", agreementId.getId());
      List<ValidationMessage> errors =
          List.of(WalletValidationMessage.of("agreementId", errorMessage));
      return Result.ofErr(InvalidResult.of(errors));
    }
    return Result.ofOK(ValidResult.of());
  }

  public static Result<ValidResult, InvalidResult> validateExistence(
      AgreementRepository agreementRepository, List<AgreementId> agreementIdList) {

    if (agreementIdList.isEmpty()) {
      String errorMessage = I18n.translate("error.agreement.empty.list");
      return Result.ofErr(
          InvalidResult.of(List.of(WalletValidationMessage.of("agreementList", errorMessage))));
    }

    List<WalletValidationMessage> errors =
        agreementIdList.stream()
            .filter(Objects::nonNull)
            .filter(agreementId -> !agreementRepository.existsById(agreementId))
            .map(
                agreementId -> {
                  String errorMessage =
                      I18n.translate("error.agreement.does.not.exist", agreementId.getId());
                  return WalletValidationMessage.of("agreementId", errorMessage);
                })
            .toList();

    if (!errors.isEmpty()) {
      return Result.ofErr(InvalidResult.of(errors));
    }

    return Result.ofOK(ValidResult.of());
  }

  /**
   * Validates the uniqueness of an agreement name in the repository.
   *
   * @param agreementRepository the repository to check for the uniqueness of the agreement name.
   * @param name the name of the agreement to validate.
   * @return a Result indicating whether the validation passed (name is unique) or failed (name
   *     already exists).
   */
  public static Result<ValidResult, InvalidResult> validateUniqueName(
      AgreementRepository agreementRepository, AgreementNumber name) {
    return Result.ofOK(ValidResult.of());
  }

  public static Result<ValidResult, InvalidResult> validateContractsRemoval(
      AgreementRepository agreementRepository,
      TranchesRepository tranchesRepository,
      UpdateAgreementRequest request) {
    Agreement agreement =
        agreementRepository.findById(request.getId()).orElseThrow(EntityNotFoundException::new);
    Set<Contract> toCheck =
        Agreement.getContractDifference(
            new HashSet<>(agreement.getContracts()), new HashSet<>(request.getContracts()));
    for (Contract contract : toCheck) {
      long count = tranchesRepository.countTranches(contract.getId());
      if (count > 0) {
        String errorMessage = I18n.translate("validation.agreement.contract.removal");
        return Result.ofErr(
            InvalidResult.of(List.of(WalletValidationMessage.of("contractId", errorMessage))));
      }
    }
    return Result.ofOK(ValidResult.of());
  }
}
