/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.create.service;

import com.codemonkeys.wallet.domain.calendar.Holiday;
import com.codemonkeys.wallet.domain.calendar.HolidayRepository;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Predicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service responsible for determining whether a given date is a workday or not. It checks whether a
 * date falls on a weekend or a holiday.
 */
@Service
public class PredicateService {

  /** Predicate to check if a given date is a weekend (Saturday or Sunday). */
  private final Predicate<LocalDate> IS_WEEKEND =
      d -> d.getDayOfWeek().equals(DayOfWeek.SATURDAY) || d.getDayOfWeek().equals(DayOfWeek.SUNDAY);

  /** List of holidays, fetched from the {@link HolidayRepository}. */
  private List<Holiday> holidays;

  /** Predicate to check if a given date is a holiday. */
  private final Predicate<LocalDate> IS_HOLIDAY =
      d -> holidays.stream().anyMatch(h -> h.getDate().equals(d));

  /**
   * Constructs a new instance of {@link PredicateService}.
   *
   * @param holidayRepository the repository used to fetch the list of holidays
   */
  @Autowired
  public PredicateService(HolidayRepository holidayRepository) {
    this.holidays = holidayRepository.findAll();
  }

  /**
   * Checks whether the given date is a workday. A workday is defined as a day that is not a weekend
   * (Saturday or Sunday) and not a holiday.
   *
   * @param date the date to check
   * @return true if the date is a workday, false otherwise
   */
  public boolean isWorkDay(LocalDate date) {
    return IS_HOLIDAY.or(IS_WEEKEND).negate().test(date);
  }
}
