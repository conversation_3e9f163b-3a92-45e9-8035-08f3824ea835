/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.duplicate;

import com.codemonkeys.wallet.agreements.application.duplicate.DuplicateResponse.DuplicateMultipleResponse;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;

/** Mapper for converting Agreement to DuplicateResponse */
@Service
public class DuplicateMapper {

  /**
   * Converts an Agreement to DuplicateResponse for successful operation
   *
   * @param agreement the Agreement to be converted
   * @return duplicate response of the Agreement
   */
  public DuplicateResponse toDto(
      Agreement agreement, List<DuplicateMultipleResponse> multipleResponseList) {

    Set<String> keys = agreement.getVolumes().getMonthly().keySet();

    List<Integer> months =
        keys.stream().map(key -> key.replaceAll("\\D", "")).map(Integer::parseInt).toList();

    return new DuplicateResponse(
        ValidResult.of(),
        agreement.getMediaType(),
        agreement.getAuthorizedBuyers(),
        agreement.getPurchaseModel(),
        agreement.getSupplier(),
        agreement.getMedia(),
        agreement.getPropertyRights(),
        months,
        multipleResponseList,
        agreement.isRequiresCustomerAcceptance(),
        agreement.isSendRecommendation());
  }
}
