/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.create.service;

import com.codemonkeys.wallet.agreements.application.create.factory.EnergyContractFactory;
import com.codemonkeys.wallet.agreements.application.create.factory.GasContractFactory;
import com.codemonkeys.wallet.agreements.application.create.factory.PropertyContractFactory;
import com.codemonkeys.wallet.common.framework.domain.enums.PurchaseModel;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.contract.PropertyRightContract;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Default implementation of the {@link CreateContractService}. Handles the creation of different
 * types of contracts, including energy, gas, and property rights.
 */
@Slf4j
@Service
@AllArgsConstructor(onConstructor = @__(@Autowired))
public class DefaultCreateContractService implements CreateContractService {
  EnergyContractFactory energyContractFactory;
  GasContractFactory gasContractFactory;
  PropertyContractFactory propertyContractFactory;
  TimeUnitLocalDateRangeFactory factory;

  /**
   * Filters a list of contracts to include only those that are tradeable within the specified date
   * range.
   *
   * @param contracts The list of contracts to filter.
   * @param startAt The starting date for the tradeable period.
   * @param endAt The ending date for the tradeable period.
   * @return A list containing only the contracts that are tradeable between the given dates.
   */
  private List<? extends Contract> includeOnlyTradeableContractsAt(
      List<? extends Contract> contracts, LocalDate startAt, LocalDate endAt) {
    return contracts.stream().filter(filterInappropriateContracts(startAt, endAt)).toList();
  }

  /**
   * Creates a predicate to filter out contracts that are not tradeable within the specified date
   * range.
   *
   * @param startAt The starting date for the tradeable period.
   * @param endAt The ending date for the tradeable period.
   * @return A predicate that evaluates whether a contract is tradeable between the given dates.
   */
  private @NotNull Predicate<Contract> filterInappropriateContracts(
      LocalDate startAt, LocalDate endAt) {
    return c -> {
      if (c.isSpotContract()) return true;
      else {
        LocalDateRange range =
            factory.getTimeUnitRange(c.getName().getTimeUnit(), c.getYear().getYearAsInt());
        return !startAt.isAfter(range.getStart()) && !endAt.isBefore(range.getEnd());
      }
    };
  }

  /**
   * Creates contracts for a specific year based on the request.
   *
   * @param request the contract creation request
   * @param year the year for which the contracts are being created
   * @return a list of created contracts
   */
  private List<? extends Contract> createForYear(CreateContractRequest request, Integer year) {
    List<Contract> result = new ArrayList<>();
    List<? extends Contract> mediaContracts =
        switch (request.getMediaType()) {
          case ENERGY ->
              createEnergyContracts(request, year, request.getMedia().getAvailableProducts());
          case GAS -> createGasContracts(request, year, request.getMedia().getAvailableProducts());
        };
    result.addAll(createPropertyRightContracts(request, year));
    result.addAll(mediaContracts);
    return result;
  }

  /**
   * Creates property right contracts based on the request.
   *
   * @param request the contract creation request
   * @param year the year for which the contracts are being created
   * @return a list of created property right contracts
   */
  public List<Contract> createPropertyRightContracts(CreateContractRequest request, Integer year) {
    List<Contract> result = new ArrayList<>();
    PurchaseModel purchaseModel = request.getPropertyRights().getPurchaseModel();

    if (!handlePurchaseModel(purchaseModel, request)) return result;

    List<ContractType> availableProducts = request.getPropertyRights().getAvailableProducts();
    List<PropertyRightContract> contracts =
        createPropertyContracts(request, year, availableProducts);
    result.addAll(contracts);

    return result;
  }

  /**
   * Determines whether a contract can proceed based on the purchase model.
   *
   * @param purchaseModel the purchase model for the contract
   * @param request the contract creation request
   * @return true if the contract can proceed, false otherwise
   */
  private boolean handlePurchaseModel(PurchaseModel purchaseModel, CreateContractRequest request) {
    return switch (purchaseModel) {
      case FIXED_PRICE -> false; // fixed price - end, just an info that we are not buying them
      case PASSIVE_PURCHASE -> {
        String comment = request.getPropertyRights().getComment();
        if (comment == null || comment.isEmpty()) {
          throw new IllegalArgumentException("Comment is required for passive purchase");
        }
        // komentarz
        yield false;
      }
      case TRANCHE -> true;
      default -> false;
    };
  }

  /**
   * Creates property contracts for a given year based on the available products.
   *
   * @param request the contract creation request
   * @param year the year for which the contracts are being created
   * @param availableContracts the available contract types for property rights
   * @return a list of created property right contracts
   */
  private List<PropertyRightContract> createPropertyContracts(
      CreateContractRequest request, Integer year, List<ContractType> availableContracts) {
    List<PropertyRightContract> result = new ArrayList<>();
    for (ContractType contractType : availableContracts) {
      result.addAll(
          switch (contractType) {
            case PMOZE_A, PMEF_F, PMOZE_BIO ->
                propertyContractFactory.create(
                    request.getMediaType(),
                    request.getAverageCalculationStartDate(),
                    request.getPropertyRights(),
                    contractType,
                    Year.of(year));
            default -> throw new IllegalArgumentException("Wrong product type for property rights");
          });
    }

    return (List<PropertyRightContract>)
        includeOnlyTradeableContractsAt(result, request.getStartDate(), request.getEndDate());
  }

  /**
   * Creates a list of gas contracts for a given year.
   *
   * @param request the contract creation request
   * @param year the year for which the contracts are being created
   * @param availableContracts the available contract types for the year
   * @return a list of created gas contracts
   */
  private List<GasContract> createGasContracts(
      CreateContractRequest request, Integer year, List<ContractType> availableContracts) {
    List<GasContract> result = Lists.newArrayList();
    for (ContractType contractType : availableContracts) {
      result.addAll(
          gasContractFactory.create(
              request.getMediaType(),
              request.getAverageCalculationStartDate(),
              request.getMedia(),
              contractType,
              Year.of(year)));
    }
    return (List<GasContract>)
        includeOnlyTradeableContractsAt(result, request.getStartDate(), request.getEndDate());
  }

  /**
   * Creates a list of energy contracts for a given year.
   *
   * @param request the contract creation request
   * @param year the year for which the contracts are being created
   * @param availableContracts the available contract types for the year
   * @return a list of created energy contracts
   */
  private List<EnergyContract> createEnergyContracts(
      CreateContractRequest request, Integer year, List<ContractType> availableContracts) {

    List<EnergyContract> result = Lists.newArrayList();
    for (ContractType contractType : availableContracts) {
      result.addAll(
          energyContractFactory.create(
              request.getMediaType(),
              request.getAverageCalculationStartDate(),
              request.getMedia(),
              contractType,
              Year.of(year)));
    }
    return (List<EnergyContract>)
        includeOnlyTradeableContractsAt(result, request.getStartDate(), request.getEndDate());
  }

  /**
   * Creates contracts for the specified years based on the provided {@link CreateContractRequest}.
   *
   * <p>The method iterates over the set of years provided in the request and generates contracts
   * for each year. The {@link CreateContractRequest#assignPurchaseModel()} is called to set the
   * appropriate purchase model before the contracts are created.
   *
   * @param request the contract creation request that contains the details necessary for generating
   *     contracts.
   * @return a list of created contracts for all specified years.
   */
  @Override
  public List<Contract> createContracts(CreateContractRequest request) {
    Set<Integer> years = request.getYears();
    List<Contract> result = new ArrayList<>();
    request.assignPurchaseModel();
    for (Integer year : years) {
      result.addAll(createForYear(request, year));
    }
    // TODO: remove after finish of https://energysolution.atlassian.net/browse/POR-388
    Set<String> contractNames = new HashSet<>();
    List<Contract> uniqueContracts =
        result.stream().filter(e -> contractNames.add(e.getName().getValue())).toList();
    return uniqueContracts;
  }
}
