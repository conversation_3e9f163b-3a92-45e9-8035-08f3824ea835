/* (C)2024-2025 */
package com.codemonkeys.wallet.agreements.application.duplicate;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.shared.dto.Response;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationResult;
import com.codemonkeys.wallet.domain.agreement.vo.Volumes;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Response of an agreement data for duplicate Duplicate response consists of Agreement data without
 * dates, contracts and description Includes validation results
 */
@Data
@AllArgsConstructor
public class DuplicateResponse implements Response {

  private ValidationResult validation;
  private MediaType mediaType;
  private Set<String> authorizedBuyers;
  private Supplier supplier;
  private CreateContractRequest.ContractParameters media;
  private CreateContractRequest.ContractParameters propertyRights;
  private List<Integer> months;
  private List<DuplicateMultipleResponse> multipleResponseList;
  private boolean requiresCustomerAcceptance;
  private boolean sendRecommendation;

  @Data
  @AllArgsConstructor
  public static class DuplicateMultipleResponse {

    private CustomerId customerId;
    private Volumes volumes;
  }

  /**
   * Constructs a response indicating a failed duplicate operation with error messages.
   *
   * @param invalidResult the invalid result containing details about the failure.
   */
  public DuplicateResponse(InvalidResult invalidResult) {
    this.validation = new ValidationResult(false, invalidResult.getMessages());
  }

  /**
   * Determines if the find operation was valid based on the included validation results.
   *
   * @return true if the validation results are positive, otherwise false.
   */
  @Override
  public boolean isValid() {
    return validation.isValid();
  }
}
