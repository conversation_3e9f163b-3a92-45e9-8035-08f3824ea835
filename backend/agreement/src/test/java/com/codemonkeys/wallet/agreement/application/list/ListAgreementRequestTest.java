/* (C)2024-2025 */
package com.codemonkeys.wallet.agreement.application.list;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.codemonkeys.wallet.agreements.application.list.ListAgreementRequest;
import java.util.Map;
import org.junit.jupiter.api.Test;

class ListAgreementRequestTest {

  @Test
  void constructorShouldSetFieldsFromParams() {
    Map<String, String> params = Map.of("page", "2", "limit", "5");
    ListAgreementRequest request = new ListAgreementRequest(params);

    assertEquals(2, request.getPage());
    assertEquals(5, request.getLimit());
  }

  @Test
  void constructorShouldDefaultForInvalidLimit() {
    Map<String, String> params = Map.of("page", "2", "limit", "invalid");
    ListAgreementRequest request = new ListAgreementRequest(params);

    assertEquals(0, request.getPage());
    assertEquals(10, request.getLimit());
  }
}
