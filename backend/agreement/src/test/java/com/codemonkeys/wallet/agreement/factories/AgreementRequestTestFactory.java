/* (C)2024-2025 */
package com.codemonkeys.wallet.agreement.factories;

import com.codemonkeys.wallet.agreements.application.create.CreateAgreementRequest;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementRequest;
import com.codemonkeys.wallet.common.framework.domain.enums.FormType;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.enums.OrderType;
import com.codemonkeys.wallet.common.framework.domain.enums.PurchaseModel;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.agreement.vo.Volumes;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.customer.vo.CustomerName;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierName;
import com.google.common.collect.Lists;
import java.time.LocalDate;
import java.util.*;

public class AgreementRequestTestFactory {

  public static class Valid {

    public static CreateAgreementRequest createExampleAgreementRequest() {
      CreateContractRequest.ContractParameters propertyRights =
          new CreateContractRequest.ContractParameters();
      propertyRights.setPurchaseModel(PurchaseModel.TRANCHE);
      CreateAgreementRequest.AgreementGroup agreementGroup =
          new CreateAgreementRequest.AgreementGroup(UUID.randomUUID(), "Test Agreement Group");

      return new CreateAgreementRequest(
          FormType.CUSTOMER,
          List.of(UUID.fromString("00000000-0000-0000-0000-000000000005")),
          UUID.fromString("00000000-0000-0000-0000-000000000005"),
          agreementGroup,
          MediaType.GAS,
          UUID.fromString("00000000-0000-0000-0000-000000000005"),
          PurchaseModel.TRANCHE,
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 1, 1),
          new CreateContractRequest.ContractParameters(),
          List.of(),
          OrderType.ATTACHMENT_EMAIL,
          propertyRights,
          Lists.newArrayList(),
          Lists.newArrayList(),
          "description");
    }

    public static UpdateAgreementRequest createExampleUpdateAgreementRequest() {
      UpdateAgreementRequest.AgreementGroup agreementGroup =
          new UpdateAgreementRequest.AgreementGroup(UUID.randomUUID(), "Test Agreement Group");
      return new UpdateAgreementRequest(
          Volumes.of(new HashMap<>()),
          AgreementId.of(UUID.randomUUID()),
          MediaType.ENERGY,
          new Customer(CustomerName.of("test"), CustomerId.randomId()),
          new Supplier(SupplierId.randomId(), SupplierName.of("test")),
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 1, 1),
          PurchaseModel.PASSIVE_PURCHASE,
          new HashSet<>(),
          List.of(),
          "description",
          agreementGroup);
    }
  }

  public static class Invalid {

    public static CreateAgreementRequest createInvalidAgreementRequest() {
      CreateContractRequest.ContractParameters propertyRights =
          new CreateContractRequest.ContractParameters();
      propertyRights.setPurchaseModel(PurchaseModel.TRANCHE);

      return new CreateAgreementRequest(
          FormType.CUSTOMER,
          List.of(UUID.fromString("00000000-0000-0000-0000-000000000005")),
          UUID.fromString("00000000-0000-0000-0000-000000000005"),
          null,
          MediaType.GAS,
          UUID.fromString("00000000-0000-0000-0000-000000000005"),
          PurchaseModel.TRANCHE,
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 1, 1),
          new CreateContractRequest.ContractParameters(),
          List.of(),
          OrderType.ATTACHMENT_EMAIL,
          propertyRights,
          Lists.newArrayList(),
          Lists.newArrayList(),
          "description");
    }
  }
}
