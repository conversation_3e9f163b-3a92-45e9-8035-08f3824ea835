/* (C)2024-2025 */
package com.codemonkeys.wallet.agreement.factories;

import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementRequest;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.enums.PurchaseModel;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.agreement.vo.Volumes;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

public class AgreementUpdateRequestTestFactory {

  public static class Valid {

    public static UpdateAgreementRequest createExampleUpdateAgreementRequest() {
      Customer customer =
          CustomerSupplierFactory.createCustomer(
              UUID.fromString("00000000-0000-0000-0000-000000000005"), "Test Customer");
      Supplier supplier =
          CustomerSupplierFactory.createSupplier(
              UUID.fromString("00000000-0000-0000-0000-000000000005"), "Test Supplier");

      UpdateAgreementRequest.AgreementGroup agreementGroup =
          new UpdateAgreementRequest.AgreementGroup(UUID.randomUUID(), "Test Agreement Group");

      return new UpdateAgreementRequest(
          Volumes.of(new HashMap<>()),
          AgreementId.of(UUID.randomUUID()),
          MediaType.GAS,
          customer,
          supplier,
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 12, 31),
          LocalDate.of(2024, 1, 1),
          PurchaseModel.TRANCHE,
          new HashSet<>(), // contracts
          List.of(),
          "Example description",
          agreementGroup);
    }
  }

  public static class Invalid {

    public static UpdateAgreementRequest createInvalidUpdateAgreementRequest() {
      Customer customer =
          CustomerSupplierFactory.createCustomer(
              UUID.fromString("00000000-0000-0000-0000-000000000005"), "Invalid Customer");
      Supplier supplier =
          CustomerSupplierFactory.createSupplier(
              UUID.fromString("00000000-0000-0000-0000-000000000005"), "Invalid Supplier");

      return new UpdateAgreementRequest(
          null, // Invalid: null volumes
          AgreementId.of(UUID.randomUUID()),
          null, // Invalid: null mediaType
          customer,
          supplier,
          LocalDate.of(2024, 1, 1),
          LocalDate.of(2024, 12, 31),
          LocalDate.of(2024, 1, 1),
          null, // Invalid: null purchaseModel
          new HashSet<>(), // contracts
          List.of(),
          "Invalid example description",
          null);
    }
  }
}
