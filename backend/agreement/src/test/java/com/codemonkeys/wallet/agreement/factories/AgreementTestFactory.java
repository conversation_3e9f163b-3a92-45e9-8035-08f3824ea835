/* (C)2024-2025 */
package com.codemonkeys.wallet.agreement.factories;

import com.codemonkeys.wallet.agreements.application.create.CreateAgreementRequest;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementGroup;
import com.codemonkeys.wallet.domain.agreement.vo.*;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.customer.vo.CustomerName;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierName;
import java.util.HashMap;
import java.util.HashSet;
import java.util.UUID;

public class AgreementTestFactory {

  public static class Valid {
    public static Agreement createExampleAgreement() {
      return fromCreateAgreementRequest(
          AgreementRequestTestFactory.Valid.createExampleAgreementRequest());
    }

    public static Agreement fromCreateAgreementRequest(CreateAgreementRequest request) {
      Supplier supplier =
          new Supplier(SupplierId.of(request.getSupplier()), SupplierName.of("Supplier Name"));

      Customer customer =
          new Customer(CustomerName.of("Supplier Name"), CustomerId.of(request.getSupplier()));

      AgreementGroup agreementGroup =
          new AgreementGroup(
              AgreementGroupId.of(UUID.randomUUID()),
              AgreementGroupName.of("Test Agreement Group"));

      return new Agreement(
          request.getMediaType(),
          request.getStartDate(),
          request.getEndDate(),
          request.getAverageCalculationStartDate(),
          request.getDescription(),
          Volumes.of(new HashMap<>()),
          new HashSet<>(),
          new HashSet<>(),
          supplier,
          customer,
          agreementGroup,
          new CreateContractRequest.ContractParameters(),
          new CreateContractRequest.ContractParameters(),
          false,
          false);
    }
  }
}
