/* (C)2024-2025 */
package com.codemonkeys.wallet.agreement.application.update;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.codemonkeys.wallet.agreement.factories.AgreementTestFactory;
import com.codemonkeys.wallet.agreement.factories.AgreementUpdateRequestTestFactory;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementControllerImpl;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementRequest;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementResponse;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementServiceImpl;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class UpdateAgreementControllerImplTest {

  @Mock private UpdateAgreementServiceImpl updateAgreementService;

  @InjectMocks private UpdateAgreementControllerImpl updateAgreementController;

  private UpdateAgreementRequest updateAgreementRequest;
  private UpdateAgreementResponse updateAgreementResponse;
  private AgreementId agreementId;

  @BeforeEach
  void setUp() {
    agreementId = AgreementId.randomId();
    updateAgreementRequest =
        AgreementUpdateRequestTestFactory.Valid.createExampleUpdateAgreementRequest();
    updateAgreementRequest.setId(agreementId);
    updateAgreementResponse =
        new UpdateAgreementResponse(
            ValidResult.of(), AgreementTestFactory.Valid.createExampleAgreement());
  }

  @Test
  void updateShouldReturnSuccessResponse() {
    when(updateAgreementService.updateById(agreementId, updateAgreementRequest))
        .thenReturn(updateAgreementResponse);

    ResponseEntity<UpdateAgreementResponse> response =
        updateAgreementController.update(agreementId, updateAgreementRequest);

    assertNotNull(response);
    assertTrue(response.getStatusCode().is2xxSuccessful());
    assertEquals(response.getBody(), updateAgreementResponse);
    verify(updateAgreementService, times(2)).updateById(agreementId, updateAgreementRequest);
  }
}
