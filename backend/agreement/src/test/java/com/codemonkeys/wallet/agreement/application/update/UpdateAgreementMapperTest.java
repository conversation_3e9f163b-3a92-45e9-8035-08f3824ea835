/* (C)2024-2025 */
package com.codemonkeys.wallet.agreement.application.update;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.codemonkeys.wallet.agreement.factories.AgreementTestFactory;
import com.codemonkeys.wallet.agreement.factories.AgreementUpdateRequestTestFactory;
import com.codemonkeys.wallet.agreement.factories.CustomerSupplierFactory;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementMapper;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementRequest;
import com.codemonkeys.wallet.agreements.application.update.UpdateAgreementResponse;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementGroup;
import com.codemonkeys.wallet.domain.agreement.AgreementGroupRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementGroupId;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.SupplierRepository;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateAgreementMapperTest {

  @Mock private SupplierRepository supplierRepository;
  @Mock private CustomerRepository customerRepository;
  @Mock private AgreementGroupRepository agreementGroupRepository;

  @InjectMocks private UpdateAgreementMapper updateAgreementMapper;

  private Agreement agreement;
  private UpdateAgreementRequest updateAgreementRequest;

  @BeforeEach
  void setUp() {
    agreement = AgreementTestFactory.Valid.createExampleAgreement();
    updateAgreementRequest =
        AgreementUpdateRequestTestFactory.Valid.createExampleUpdateAgreementRequest();
  }

  @Test
  void toDomainShouldMapCorrectly() {
    Supplier supplier =
        CustomerSupplierFactory.createSupplier(
            UUID.fromString(updateAgreementRequest.getSupplier().getId().toString()),
            updateAgreementRequest.getSupplier().getName().getName());
    Customer customer =
        CustomerSupplierFactory.createCustomer(
            UUID.fromString(updateAgreementRequest.getCustomer().getId().toString()),
            updateAgreementRequest.getCustomer().getName().getName());
    AgreementGroup agreementGroup =
        CustomerSupplierFactory.createAgreementGroup(
            UUID.fromString(updateAgreementRequest.getAgreementGroup().getId().toString()),
            updateAgreementRequest.getAgreementGroup().getName());

    when(supplierRepository.findById(
            SupplierId.of(
                UUID.fromString(updateAgreementRequest.getSupplier().getId().toString()))))
        .thenReturn(Optional.of(supplier));
    when(customerRepository.findById(
            CustomerId.of(
                UUID.fromString(updateAgreementRequest.getCustomer().getId().toString()))))
        .thenReturn(Optional.of(customer));
    when(agreementGroupRepository.findById(
            AgreementGroupId.of(
                UUID.fromString(updateAgreementRequest.getAgreementGroup().getId().toString()))))
        .thenReturn(Optional.of(agreementGroup));

    Agreement result = updateAgreementMapper.toDomain(agreement, updateAgreementRequest);

    assertNotNull(result);
    assertEquals(agreement.getId(), result.getId());
    assertEquals(supplier, result.getSupplier());
    assertEquals(customer, result.getCustomer());
    assertEquals(agreementGroup, result.getAgreementGroup());
  }

  @Test
  void toResponseShouldReturnValidResponse() {
    UpdateAgreementResponse response = updateAgreementMapper.toResponse(agreement);

    assertNotNull(response);
    assertTrue(response.getValidation().isValid());
    assertEquals(agreement, response.getAgreement());
  }
}
