<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.codemonkeys</groupId>
    <artifactId>wallet</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <groupId>com.codemonkeys.wallet</groupId>
  <artifactId>agreement</artifactId>
  <version>0.0.1-SNAPSHOT</version>

  <name>agreement</name>
  <description>agreement</description>
  <properties>
    <java.version>21</java.version>
    <maven-failsafe-plugin.version>3.2.5</maven-failsafe-plugin.version>
    <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.codemonkeys.wallet</groupId>
      <artifactId>domain</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.codemonkeys.wallet</groupId>
      <artifactId>common</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>3.2.5</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>21</source>
          <target>21</target>
          <compilerArgs>--enable-preview</compilerArgs>
          <compilerArgument>-parameters</compilerArgument>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
        <configuration>
          <argLine>--enable-preview</argLine>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>${maven-failsafe-plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <includes>
            <include>**/*IntegrationTest.java</include>
          </includes>
          <argLine>--enable-preview</argLine>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
