--liquibase formatted sql
--changeset doma:003_create_table_groups
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'groups'

CREATE TABLE IF NOT EXISTS groups (
                                      id UUID PRIMARY KEY,
                                      group_name VA<PERSON>HAR(100) NOT NULL UNIQUE,
                                      media_type VARCHAR(255) NOT NULL CHECK (media_type IN ('ENERGY', 'GAS')),
                                      group_year VARCHAR(4) NOT NULL,
                                      created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
                                      updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL);
ALTER TABLE groups owner to eswalletadmin;