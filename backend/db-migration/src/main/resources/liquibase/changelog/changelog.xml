<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
  <include file="001_create_table_agreement_groups.sql" relativeToChangelogFile="true"/>
  <include file="002_create_table_event_logs.sql" relativeToChangelogFile="true"/>
  <include file="003_create_table_groups.sql" relativeToChangelogFile="true"/>
  <include file="004_create_table_customers.sql" relativeToChangelogFile="true"/>
  <include file="005_create_table_attachments.sql" relativeToChangelogFile="true"/>
  <include file="006_create_table_holidays.sql" relativeToChangelogFile="true"/>
  <include file="007_create_table_newsletters.sql" relativeToChangelogFile="true"/>
  <include file="008_create_table_notes.sql" relativeToChangelogFile="true"/>
  <include file="009_create_table_prices.sql" relativeToChangelogFile="true"/>
  <include file="010_create_table_product_calendar.sql" relativeToChangelogFile="true"/>
  <include file="011_create_table_recommendations.sql" relativeToChangelogFile="true"/>
  <include file="012_create_table_suppliers.sql" relativeToChangelogFile="true"/>
  <include file="013_create_table_agreements.sql" relativeToChangelogFile="true"/>
  <include file="014_create_table_authorized_buyers.sql" relativeToChangelogFile="true"/>
  <include file="015_create_table_contacts.sql" relativeToChangelogFile="true"/>
  <include file="016_create_table_contracts.sql" relativeToChangelogFile="true"/>
  <include file="017_create_table_contract_price_reference.sql" relativeToChangelogFile="true"/>
  <include file="018_create_table_order_times.sql" relativeToChangelogFile="true"/>
  <include file="019_create_table_supplier_contact_customers.sql" relativeToChangelogFile="true"/>
  <include file="020_create_table_wallets.sql" relativeToChangelogFile="true"/>
  <include file="021_create_table_elements.sql" relativeToChangelogFile="true"/>
  <include file="022_create_table_wallet_prices.sql" relativeToChangelogFile="true"/>
  <include file="023_create_table_wallet_products.sql" relativeToChangelogFile="true"/>
  <include file="024_create_table_wallet_tranches.sql" relativeToChangelogFile="true"/>
  <include file="025_alter_table_agreements.sql" relativeToChangelogFile="true"/>
  <include file="026_alter_table_agreements.sql" relativeToChangelogFile="true"/>
  <include file="027_alter_table_elements_add_contract_type.sql" relativeToChangelogFile="true"/>
  <include file="028_alter_table_tranche.sql" relativeToChangelogFile="true"/>
  <include file="029_alter_table_wallet_add_green_property_right_calculation_type.sql"
      relativeToChangelogFile="true"/>
  <include file="030_alter_table_contracts_update_constraint.sql" relativeToChangelogFile="true"/>
  <include file="031_rename_column_in_recommendations.sql" relativeToChangelogFile="true"/>
  <include file="032_alter_table_attachments_add_notification_type.sql"
      relativeToChangelogFile="true"/>
  <include file="033_alter_table_products_elements_add_media.sql" relativeToChangelogFile="true"/>
  <include file="034_alter_table_products_update_product_type.sql" relativeToChangelogFile="true"/>
  <include file="035_alter_table_agreements_add_authorized_buyers.sql"
      relativeToChangelogFile="true"/>
  <include file="036_alter_table_contracts_update_volume_type_check.sql"
      relativeToChangelogFile="true"/>
  <include file="037_update_final_purchase_date_type.sql"
      relativeToChangelogFile="true"/>
  <include file="038_recommendations_update_email_template.sql"
    relativeToChangelogFile="true"/>
  <include file="039_add_requires_customer_acceptance_and_send_recommendation.sql"
    relativeToChangelogFile="true"/>
  <include file="040_recommendations_add_authorized_order_type.sql"
    relativeToChangelogFile="true"/>
  <include file="041_agreement_add_human_readable_agreement_id.sql"
    relativeToChangelogFile="true"/>
  <include file="042_update_existing_human_readable_agreement_id.sql"
    relativeToChangelogFile="true"/>
  <include file="043_add_human_readable_agreement_id_for_recommendation.sql"
    relativeToChangelogFile="true"/>
  <include file="044_add-tenant-id-to-tables.sql"
    relativeToChangelogFile="true"/>
  <include file="045_add-confirmed-flag-to-wallet-prices.sql"
      relativeToChangelogFile="true"/>
  <include file="046_add_analytical_settings_to_wallets.sql"
    relativeToChangelogFile="true"/>
  <include file="047_create_remunerations_table.sql"
    relativeToChangelogFile="true"/>
  <include file="048_add_price_metadata_to_wallet_prices.sql"
      relativeToChangelogFile="true"/>
  <include file="049_create_results_table.sql"
      relativeToChangelogFile="true"/>
  <include file="050_drop_purchase_model_from_agreement.sql"
      relativeToChangelogFile="true"/>
  <include file="051_add_confirmed_to_wallet_products.sql"
    relativeToChangelogFile="true"/>
  <include file="052_remove_check.sql"
    relativeToChangelogFile="true"/>
  <include file="053_alter_table_elements_update_value_precision.sql"
    relativeToChangelogFile="true"/>
  <include file="054_extend_order_type_length.sql"
    relativeToChangelogFile="true"/>
  <include file="055_add_contacts_indexes.sql"
    relativeToChangelogFile="true"/>
</databaseChangeLog>