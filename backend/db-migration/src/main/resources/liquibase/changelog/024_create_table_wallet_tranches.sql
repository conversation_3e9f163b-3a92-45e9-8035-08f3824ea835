--liquibase formatted sql
--changeset doma:024_create_table_wallet_tranches
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'wallet_tranches'
CREATE TABLE IF NOT EXISTS wallet_tranches (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                               created_at TIMESTAMP(6) NOT NULL,
                                               updated_at TIMESTAMP(6) NOT NULL,
                                               execution_date DATE,
                                               price NUMERIC(38, 2),
                                               price_reference VARCHAR(255) NOT NULL CHECK (price_reference IN ('DKR', 'TARGET_PRICE', 'TARGET_PRICE_DKR', 'TRADER_REQUEST', 'YESTERDAY')),
                                               size NUMERIC(38, 2),
                                               time_unit SMALLINT CHECK (time_unit BETWEEN 0 AND 16),
                                               email_sent BOOLEAN NOT NULL,
                                               contract_id UUID REFERENCES contracts,
                                               wallet_id UUID NOT NULL REFERENCES wallets);
ALTER TABLE wallet_tranches owner to wallet;