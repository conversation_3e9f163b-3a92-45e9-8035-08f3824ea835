--liquibase formatted sql
--changeset doma:020_create_table_wallets
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'wallets'

CREATE TABLE IF NOT EXISTS wallets (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                       created_at TIMESTAMP(6) NOT NULL,
                                       updated_at TIMESTAMP(6) NOT NULL,
                                       description VARCHAR(150),
                                       media_type VARCHAR(255) NOT NULL CHECK (media_type IN ('ENERGY', 'GAS')),
                                       start_date DATE NOT NULL,
                                       year VARCHAR(255),
                                       agreement_id UUID NOT NULL REFERENCES agreements(id) ON DELETE CASCADE
);

ALTER TABLE wallets owner to eswalletadmin;