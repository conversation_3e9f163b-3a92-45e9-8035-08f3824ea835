--liquibase formatted sql
--changeset doma:009_create_table_prices
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'prices'

CREATE TABLE IF NOT EXISTS prices (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                      created_at TIMESTAMP(6) NOT NULL,
                                      updated_at TIMESTAMP(6) NOT NULL,
                                      name VARCHAR(100) NOT NULL,
                                      date DATE NOT NULL,
                                      price NUMERIC(38, 2) NOT NULL,
                                      volume NUMERIC(38, 2) NOT NULL);
ALTER TABLE prices owner to eswalletadmin;