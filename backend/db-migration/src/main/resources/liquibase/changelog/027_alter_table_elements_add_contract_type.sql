--liquibase formatted sql
--changeset dodz:027_alter_table_elements_add_contract_type.sql
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM information_schema.columns WHERE table_name='elements' AND column_name='contract_type';

ALTER TABLE elements
ADD COLUMN IF NOT EXISTS contract_type VARCHAR(255) NOT NULL CHECK (contract_type IN
                                                                    ('ENERGY', 'GAS',
                                                                     'GREEN_PROPERTY_RIGHTS',
                                                                     'WHITE_PROPERTY_RIGHTS',
                                                                     'BLUE_PROPERTY_RIGHTS')) default 'ENERGY';

ALTER TABLE elements
OWNER TO postgres;
