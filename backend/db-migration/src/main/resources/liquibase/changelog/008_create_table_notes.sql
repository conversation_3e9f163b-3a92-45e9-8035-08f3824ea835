--liquibase formatted sql
--changeset doma:008_create_table_notes
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'notes'

CREATE TABLE IF NOT EXISTS notes (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                     created_at TIMESTAMP(6) NOT NULL,
                                     updated_at TIMESTAMP(6) NOT NULL,
                                     first_name VARCHA<PERSON>(255),
                                     last_name VARCHAR(255),
                                     content TEXT,
                                     type VARCHAR(255) CHECK (type IN ('EMPTY', 'CUSTOMER', 'AGREEMENT', 'SUPPLIER')),
                                     customer_id UUID REFERENCES customers);
ALTER TABLE notes owner to eswalletadmin;