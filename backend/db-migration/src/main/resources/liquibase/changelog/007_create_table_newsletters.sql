--liquibase formatted sql
--changeset doma:007_create_table_newsletters
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'newsletters'

CREATE TABLE IF NOT EXISTS newsletters (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                           created_at TIMESTAMP(6) NOT NULL,
                                           updated_at TIMESTAMP(6) NOT NULL,
                                           attachment_id UUID,
                                           author VARCHAR(255),
                                           message VARCHAR(10000),
                                           period VARCHAR(255) NOT NULL,
                                           newsletter_type VARCHAR(255) NOT NULL CHECK (newsletter_type IN ('WEEKLY', 'MONTHLY')));
ALTER TABLE newsletters owner to eswalletad<PERSON>;