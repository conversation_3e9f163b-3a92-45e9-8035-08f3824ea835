--liquibase formatted sql
--changeset doma:014_create_table_authorized_buyers
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'authorized_buyers'

CREATE TABLE IF NOT EXISTS authorized_buyers (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                                 created_at TIMESTAMP(6) NOT NULL,
                                                 updated_at TIMESTAMP(6) NOT NULL,
                                                 agreement_id UUID REFERENCES agreements);
ALTER TABLE authorized_buyers owner to wallet;