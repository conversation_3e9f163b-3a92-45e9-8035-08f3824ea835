--liquibase formatted sql
--changeset doma:005_create_table_attachments
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'attachments'

CREATE TABLE IF NOT EXISTS attachments (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                           customer_id UUID,
                                           created_at TIMESTAMP(6) NOT NULL,
                                           updated_at TIMESTAMP(6) NOT NULL,
                                           bucket_name VARCHAR(255),
                                           name VARCHAR(255),
                                           path VARCHAR(255),
                                           storage_name VARCHAR(255),
                                           type VARCHAR(255) CHECK (type IN ('EMPTY', 'CUSTOMER', 'AGREEMENT', 'SUPPLIER')),
                                           CONSTRAINT fk_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE);
ALTER TABLE attachments OWNER TO postgres;