--liquibase formatted sql
--changeset doma:013_create_table_agreements
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'agreements'

CREATE TABLE IF NOT EXISTS agreements (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                          created_at TIMESTAMP(6) NOT NULL,
                                          updated_at TIMESTAMP(6) NOT NULL,
                                          description VARCHAR(255),
                                          end_date DATE NOT NULL,
                                          media JSONB,
                                          media_type VARCHAR(255) NOT NULL CHECK (media_type IN ('ENERGY', 'GAS')),
                                          property_rights JSONB,
                                          purchase_model VARCHAR(255) NOT NULL CHECK (purchase_model IN ('VOLUME', 'PERCENTAGE', 'FIXED_PRICE', 'TRANCHE', 'PASSIVE_PURCHASE')),
                                          start_date DATE NOT NULL,
                                          status VARCHAR(255) CHECK (status IN ('ACTIVE', 'ARCHIVE')),
                                          volumes JSONB,
                                          agreement_group_id UUID REFERENCES agreement_groups,
                                          customer_id UUID NOT NULL REFERENCES customers,
                                          supplier_id UUID NOT NULL REFERENCES suppliers);
ALTER TABLE agreements OWNER TO postgres;