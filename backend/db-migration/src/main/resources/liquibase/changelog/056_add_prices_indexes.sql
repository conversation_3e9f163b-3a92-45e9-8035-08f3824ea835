--liquibase formatted sql
--changeset doma:055_add_contacts_indexes
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'contacts';

-- INDEX pod zapytanie newsletterowe (newsletter + configuration_email + email)
CREATE INDEX IF NOT EXISTS idx_contacts_newsletter_email
    ON contacts (newsletter, configuration_email, email);

-- INDEX pod zapytanie price (prices + configuration_email + email)
CREATE INDEX IF NOT EXISTS idx_contacts_prices_email
    ON contacts (prices, configuration_email, email);

-- INDEX pod DISTINCT ON (email, updated_at DESC)
CREATE INDEX IF NOT EXISTS idx_contacts_email_updated_at
    ON contacts (email, updated_at DESC);