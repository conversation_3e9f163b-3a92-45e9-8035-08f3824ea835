--liquibase formatted sql
--changeset doma:006_create_table_holidays
--preconditions onFail:MARK_RAN onError:HALT
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_tables WHERE tablename = 'holidays'

CREATE TABLE IF NOT EXISTS holidays (
    id UUID NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
                                        created_at TIMESTAMP(6) NOT NULL,
                                        updated_at TIMESTAMP(6) NOT NULL,
                                        date DATE NOT NULL UNIQUE,
                                        is_holiday BOOLEAN NOT NULL);
ALTER TABLE holidays owner to eswalletadmin;