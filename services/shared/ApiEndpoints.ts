const BASE_URL = '/api';

export const AuthEndpoints = {
  BASE: `${BASE_URL}/auth`,
  TOKEN: `${BASE_URL}/auth/token`,
  LOGOUT: `${BASE_URL}/auth/logout`,
  RESET_PASSWORD: `${BASE_URL}/auth/reset-password`,
  LOGIN: `/login`,
  FORGOT_PASSWORD: `/forgot-password`,
};

export const UserEndpoints = {
  BASE: `${BASE_URL}/auth/users`,
  AUTH: `${BASE_URL}/auth`,
  CHANGE_PASSWORD: `${BASE_URL}/auth/change-password`,
  USER_BY_ID: (userId: string) => `${UserEndpoints.BASE}/${userId}`,
};

export const AgreementEndpoints = {
  BASE: `${BASE_URL}/agreements`,
  GROUPS: `${BASE_URL}/agreements/groups`,
  BY_ID: (agreementId: string) => `${AgreementEndpoints.BASE}/${agreementId}`,
  DELETE: (agreementId: string) => `${AgreementEndpoints.BASE}/${agreementId}`,
  UPDATE: (agreementId: string) => `${AgreementEndpoints.BASE}/${agreementId}`,
  DUPLICATE: `${BASE_URL}/agreements/duplicate`,
};

export const ContractEndpoints = {
  BASE: `${BASE_URL}/contracts`,
  OPTIONS: `${BASE_URL}/contracts/option`,
  OPTIONS_FROM_PRICES: `${BASE_URL}/contracts/prices`,
  GENERATE_FINANCIAL_INSTRUMENTS: `${BASE_URL}/contracts/generate_financial_instruments`,
  BY_AGREEMENT: `${BASE_URL}/contracts/by-agreement`,
  BY_ID: (contractId: string) => `${ContractEndpoints.BASE}/contracts/${contractId}`,
  TIME_UNIT_BY_NAME: (name: string) => `${BASE_URL}/contracts/time-unit?name=${name}`,
  AGREEMENT_PRICE_REFERENCE: (agreementId: string, contractName: string) =>
    `${BASE_URL}/contracts/agreement-price-reference?agreementId=${agreementId}&contractName=${contractName}`,
  WALLET_PRICE_REFERENCE: (walletId: string, contractName: string) =>
    `${BASE_URL}/contracts/wallet-price-reference?walletId=${walletId}&contractName=${contractName}`,
};

export const CustomerEndpoints = {
  BASE: `${BASE_URL}/customers`,
  GROUP: `${BASE_URL}/customers/groups`,
  BY_ID: (customerId: any) => `${CustomerEndpoints.BASE}/${customerId}`,
  BY_SEGMENT: `${BASE_URL}/customers/by-segment`,
};

export const CustomerContactEndpoints = {
  BASE: `${BASE_URL}/customers/contacts`,
  BY_ID: (contactId: string) => `${CustomerContactEndpoints.BASE}/${contactId}`,
  DELETE: (contactId: string) => `${CustomerContactEndpoints.BASE}/${contactId}`,
  UPDATE: (contactId: string) => `${CustomerContactEndpoints.BASE}/${contactId}`,
};

export const AttachmentEndpoints = {
  BASE: `${BASE_URL}/attachments`,
  DELETE: (fileId: string) => `${AttachmentEndpoints.BASE}/${fileId}`,
  DOWNLOAD: (id: string) => `${AttachmentEndpoints.BASE}/${id}/download`,
};

export const GroupEndpoints = {
  BASE: `${BASE_URL}/groups`,
  BY_ID: (groupId: any) => `${GroupEndpoints.BASE}/${groupId}`,
};

export const NewsletterEndpoints = {
  BASE: `${BASE_URL}/newsletters`,
};

export const RecommendationEndpoints = {
  BASE: `${BASE_URL}/recommendations`,
  UPLOAD: `${BASE_URL}/recommendations/upload`,
  DELETE: (recommendationId: string) => `${RecommendationEndpoints.BASE}/${recommendationId}`,
  UPDATE: (recommendationId: string) => `${RecommendationEndpoints.BASE}/${recommendationId}`,
  ACTION: `${BASE_URL}/recommendations/action`,
};

export const SimulationEndpoints = {
  BASE: `${BASE_URL}/simulate`,
  LAST_PRICE: (contractId: string) => `${SimulationEndpoints.BASE}/last-price/${contractId}`,
  BY_ID: (walletId: string) => `${SimulationEndpoints.BASE}/${walletId}`,
  FULL_PURCHASE: `${BASE_URL}/simulate/full-purchase`,
  WALLET_EXPORT: `${BASE_URL}/export`,
  BASE_BROKER_DASHBOARD: `${BASE_URL}/broker/simulate`,
  FULL_PURCHASE_DASHBOARD: `${BASE_URL}/broker/simulate/full-purchase`,
};

export const SupplierEndpoints = {
  BASE: `${BASE_URL}/suppliers`,
  CONTACTS: `${BASE_URL}/suppliers/contacts`,
  CONTACT_BY_ID: (contactId: string) => `${SupplierEndpoints.CONTACTS}/${contactId}`,
  CONTACT_UPDATE: (contactId: string) => `${SupplierEndpoints.CONTACTS}/${contactId}/update`,
  SUPPLIER_BY_ID: (supplierId: string) => `${SupplierEndpoints.BASE}/${supplierId}`,
};

export const TrancheEndpoints = {
  CREATE: `${BASE_URL}/tranches`,
  UPDATE: (trancheId: string) => `${BASE_URL}/tranches/${trancheId}`,
  DELETE: (trancheId: string) => `${BASE_URL}/tranches/${trancheId}`,
};

export const WalletEndpoints = {
  BASE: `${BASE_URL}/wallets`,
  SIMULATE: `${BASE_URL}/wallets/simulate`,
  RECALCULATE_BY_ID: (walletId: string) => `${BASE_URL}/wallets/recalculate?id=${walletId}`,
  OVERVIEW: `${BASE_URL}/wallets/annual_purchase_overview`,
  PRICE_CONFIRMATION_LIST: `${BASE_URL}/wallets/price_confirmation`,
  UPDATE_PRICE_CONFIRMATIONS: `${BASE_URL}/wallets/price_confirmation`,
  SHOPPING_REMINDER: `${BASE_URL}/wallets/shopping_reminder`,
  SHOPPING_OVERVIEW: `${BASE_URL}/wallets/shopping_overview`,
  VALIDATE: `${BASE_URL}/wallets/validate`,
  WALLET_BY_ID: (walletId: string) => `${WalletEndpoints.BASE}/${walletId}`,
};

export const ElementEndpoints = {
  BASE: `${BASE_URL}/elements`,
  UPDATE: (elementId: string) => `${ElementEndpoints.BASE}/${elementId}`,
  DELETE: (elementId: string) => `${ElementEndpoints.BASE}/${elementId}`,
};

export const RealisationEndpoints = {
  BASE: `${BASE_URL}/realisations`,
  REALISATION: (walletId: string) => `${RealisationEndpoints.BASE}/v2/${walletId}`,
  CONFIRM_MONTH: (walletId: string, media: string, month: string) => `${RealisationEndpoints.BASE}/${walletId}/${media}/${month}/confirm`,
};

export const ResultEndpoints = {
  BASE: `${BASE_URL}/results`,
  UPDATE: (id: string) => `${BASE_URL}/results/${id}`,
  DELETE: (id: string) => `${BASE_URL}/results/${id}`,
};

export const PricesAndChartsEndpoints = {
  BASE: `${BASE_URL}/prices-and-charts`,
  GET_BY_DATE_RANGE: (contract) => `${BASE_URL}/prices/${contract}/range`,
  GET_AVERAGE_SPOT: `${BASE_URL}/prices/average`,
  STRUCTURE: `${BASE_URL}/prices/prices-structure`,
  GET_MARKET: `${BASE_URL}/prices/market`,
  GENERATE_XLS: (contract: string) => `${BASE_URL}/prices/${contract}/export`,
  GET_AVAILABLE_RANGES: (contract: string) => `${BASE_URL}/prices/${contract}/available-ranges`,
};

export const BrokerDashboardEndpoints = {
  BASE: `${BASE_URL}/broker`,
  DASHBOARD_BY_ID: (dashboardId: string) => `${BrokerDashboardEndpoints.BASE}/${dashboardId}`,
};

export const RouterPaths = {
  AGREEMENT_CREATE: '/agreements/create',
  AGREEMENTS_CONTRACT_DETAILS: (agreementId: string, contractId: string) =>
    `/agreements/details/${agreementId}/contracts/${contractId}`,
  AGREEMENTS_CONTRACTS: (id: string) => `/agreements/details/${id}/contracts`,
  AGREEMENTS_DETAILS: (id: string) => `/agreements/details/${id}`,
  AGREEMENTS_EDIT: (id: string) => `/agreements/edit/${id}`,
  AGREEMENT_DUPLICATE: (agreementIdList: string[]) =>
    '/agreements/create?agreementIdList=' + agreementIdList.join(', '),
  AGREEMENTS_LIST: '/agreements/list',
  CREATE: '/create',
  CUSTOMERS_CREATE: '/customers/create',
  CUSTOMERS_EDIT: (id: string) => `/customers/edit/${id}`,
  CUSTOMERS_LIST: '/customers/list',
  GROUPS_EDIT: (id: string) => `/groups/edit/${id}`,
  GROUPS_LIST: '/groups/list',
  LIST: './list',
  LOGIN: '/login',
  NEWSLETTER_LIST: '/newsletter/list',
  NEWSLETTER_SEND: '/newsletter/send',
  SUPPLIER_EDIT: (id: string) => `/suppliers/edit/${id}`,
  SUPPLIERS_CONTACTS_LIST: '/suppliers/contacts/list',
  SUPPLIERS_LIST: '/suppliers/list',
  SIMULATION: '/wallets/simulation',
  SIMULATION_ID: (id: string) => `/wallets/simulation?id=${id}`,
  USERS_CHANGE_PASSWORD: '/users/change-password',
  USERS_CREATE: '/users/create-account',
  USERS_EDIT: (userId: string) => `/users/edit/${userId}`,
  USERS_LIST: '/users/list',
  WALLET_CREATE: '/wallets/create',
  WALLET_DETAILS: (id: string) => `/wallets/details/${id}`,
  WALLET_EDIT: (id: string) => `/wallets/edit/${id}`,
  WALLET_LIST: '/wallets/list',
  WALLET_REALISATION: (id: string) => `/wallets/realisations/${id}`,
  WALLET_REALISATIONS: `/wallets/realisations/list`,
  WALLET_RECOMMENDATION_VALIDATION_RESULTS: '/wallets/recommendations/validation-results',
};
