import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { SessionStorageService } from '@/services/auth/SessionStorage';
import { AuthService } from '@/services/auth/RoleService';
import { AuthEndpoints } from '@/services/shared/ApiEndpoints';

interface AuthContextType {
  realmRoles: string[];
  resourceRoles: { [key: string]: string[] };
  accountRoles: string[];
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const router = useRouter();

  const initialToken =
    typeof window !== 'undefined' ? SessionStorageService.getAccessToken() : null;

  const [isAuthenticated, setIsAuthenticated] = useState(!!initialToken);
  const [realmRoles, setRealmRoles] = useState<string[]>(
    initialToken ? AuthService.getRealmRoles(initialToken) : []
  );
  const [resourceRoles, setResourceRoles] = useState<{ [k: string]: string[] }>(
    initialToken ? AuthService.getAllResourceRoles(initialToken) : {}
  );
  const [accountRoles, setAccountRoles] = useState<string[]>(
    initialToken ? AuthService.getAccountRoles(initialToken) : []
  );

  useEffect(() => {
    const token = SessionStorageService.getAccessToken();
    const unprotected = [AuthEndpoints.LOGIN, AuthEndpoints.FORGOT_PASSWORD, '/auth/callback'];

    if (token) {
      setRealmRoles(AuthService.getRealmRoles(token));
      setResourceRoles(AuthService.getAllResourceRoles(token));
      setAccountRoles(AuthService.getAccountRoles(token));
      setIsAuthenticated(true);
    } else if (!unprotected.includes(router.pathname)) {
      router.push(AuthEndpoints.LOGIN);
    }
  }, [router.pathname]);

  return (
    <AuthContext.Provider value={{ realmRoles, resourceRoles, accountRoles, isAuthenticated }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used inside <AuthProvider>');
  }
  return context;
};
