import React, {createContext, ReactNode, useContext, useEffect, useState} from 'react';
import {useRouter} from 'next/router';
import {SessionStorageService} from '@/services/auth/SessionStorage';
import {AuthService} from '@/services/auth/RoleService';
import {AuthEndpoints} from "@/services/shared/ApiEndpoints";

interface AuthContextType {
    realmRoles: string[];
    resourceRoles: { [key: string]: string[] };
    accountRoles: string[];
    isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({children}: { children: ReactNode }) => {
    const [realmRoles, setRealmRoles] = useState<string[]>([]);
    const [resourceRoles, setResourceRoles] = useState<{ [key: string]: string[] }>({});
    const [accountRoles, setAccountRoles] = useState<string[]>([]);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const router = useRouter();

    useEffect(() => {
        const token = SessionStorageService.getAccessToken();
        const unprotectedRoutes = [AuthEndpoints.LOGIN, AuthEndpoints.FORGOT_PASSWORD];

        if (token) {
            setRealmRoles(AuthService.getRealmRoles(token));
            setResourceRoles(AuthService.getAllResourceRoles(token));
            setAccountRoles(AuthService.getAccountRoles(token));
            setIsAuthenticated(true);
        } else if (!unprotectedRoutes.includes(router.pathname)) {
            router.push(AuthEndpoints.LOGIN);
        }
    }, [router.pathname]);

    return (
        <AuthContext.Provider value={{realmRoles, resourceRoles, accountRoles, isAuthenticated}}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};