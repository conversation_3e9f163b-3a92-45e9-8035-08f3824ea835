import axios, { AxiosResponse } from 'axios';
import { API } from '@/utils/api';
import qs from 'qs';
import { SessionStorageService } from '@/services/auth/SessionStorage';
import jwt from 'jsonwebtoken';
import { AuthEndpoints } from '@/services/shared/ApiEndpoints';

class AuthService {
  private static baseURL = process.env.NEXT_PUBLIC_API_URL;
  private refreshUrl: string = `${AuthService.baseURL}/api/auth/refresh-token`;

  async login(
    username: string,
    password: string
  ): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    try {
      const data = qs.stringify({ username, password });
      const response: AxiosResponse<{
        access_token: string;
        refresh_token: string;
      }> = await API.post(AuthEndpoints.TOKEN, data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      SessionStorageService.setAccessToken(response.data.access_token);
      SessionStorageService.setRefreshToken(response.data.refresh_token);
      return {
        accessToken: response.data.access_token,
        refreshToken: response.data.refresh_token,
      };
    } catch (error) {
      console.error('Wystąpił problem z logowaniem:', error);
      throw error;
    }
  }

  async refreshToken(): Promise<{ accessToken: string; refreshToken: string }> {
    const refreshToken = SessionStorageService.getRefreshToken();

    if (!refreshToken) {
      throw new Error('No refresh token found');
    }

    try {
      const data = { refresh_token: refreshToken };
      const response: AxiosResponse<{
        access_token: string;
        refresh_token: string;
      }> = await axios.post(this.refreshUrl, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (response.status !== 200) {
        throw new Error('Failed to refresh token');
      }

      SessionStorageService.setAccessToken(response.data.access_token);
      SessionStorageService.setRefreshToken(response.data.refresh_token);
      return {
        accessToken: response.data.access_token,
        refreshToken: response.data.refresh_token,
      };
    } catch (error) {
      console.error('Wystąpił problem z odświeżeniem tokenu:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    const refreshToken = SessionStorageService.getRefreshToken();
    if (!refreshToken) {
      throw new Error('Refresh token not found');
    }

    try {
      const data = {
        refresh_token: refreshToken,
      };

      const response: AxiosResponse<any> = await API.post(AuthEndpoints.LOGOUT, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (response.status === 204) {
        SessionStorageService.clearTokens();
      } else {
        console.error('Błąd podczas wylogowania', response.data);
      }
    } catch (error) {
      console.error('Wystąpił problem podczas wylogowania:', error);
      throw error;
    }
  }

  private getUserFromToken(token) {
    if (!token) return null;
    try {
      const decoded = jwt.decode(token);
      return {
        name: decoded.name || '',
        email: decoded.email || '',
      };
    } catch (error) {
      console.error('Invalid token:', error);
      return null;
    }
  }

  public getUser() {
    const token = SessionStorageService.getAccessToken();
    return this.getUserFromToken(token);
  }

  async resetPassword(email: string): Promise<{
    success: boolean;
    message?: string;
    error?: string;
  }> {
    try {
      const data = qs.stringify({ email });
      const response: AxiosResponse<any> = await API.post(AuthEndpoints.RESET_PASSWORD, data, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      return { success: true, message: response.data.message };
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message;
      return { success: false, error: errorMessage };
    }
  }

  async exchangeCode(code: string): Promise<{ accessToken: string; refreshToken: string }> {
    const response: AxiosResponse<{ access_token: string; refresh_token: string }> = await API.post(
      AuthEndpoints.CODE_EXCHANGE,
      { code }
    );

    const { access_token, refresh_token } = response.data;

    SessionStorageService.setTokens(access_token, refresh_token);
    return { accessToken: access_token, refreshToken: refresh_token };
  }
}

const instance = new AuthService();
export { instance as LoginService };
