services:
  wallet-frontend:
    image: git.es-t.pl:5050/wallet/wallet-v2-frontend:${IMAGE_TAG:-development}
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_KEYCLOAK_BASE_URL=${NEXT_PUBLIC_KEYCLOAK_BASE_URL}
      - NEXT_PUBLIC_KEYCLOAK_REALM=${NEXT_PUBLIC_KEYCLOAK_REALM}
      - NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=${NEXT_PUBLIC_KEYCLOAK_CLIENT_ID}
      - NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI=${NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI}
    ports:
      - 3030:3000
    labels:
      - traefik.enable=true
      - traefik.http.routers.wallet-v2-frontend.rule=Host(`walletv2.dev.es-t.pl`)
      - traefik.http.routers.wallet-v2-frontend.entrypoints=websecure
      - traefik.http.routers.wallet-v2-frontend.tls.certresolver=myresolver
      - traefik.http.services.wallet-v2-frontend.loadbalancer.server.port=3000
    networks:
      - traefik
networks:
  traefik:
    external: true
    name: traefik