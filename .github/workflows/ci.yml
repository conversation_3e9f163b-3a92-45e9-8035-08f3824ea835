name: Wallet application CI/CD pipeline

on:
  push:
    branches:
      - dev
      - main
  pull_request:
  workflow_dispatch:

permissions:
  contents: read

env:
  BACKEND_IMAGE_NAME: wallet-backend
  FRONTEND_IMAGE_NAME: wallet-frontend
  ACR_SERVER: esreg.azurecr.io

jobs:
  backend-build:
    runs-on: ubuntu-latest
    container:
      image: maven:3.9.6-eclipse-temurin-21
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Copy .env file to backend and frontend
        run: cp docker/.env backend/.env
      - name: Create JAR package
        working-directory: ./backend
        run: mvn package -Dmaven.test.skip=true
      - name: Upload JAR for docker image build
        uses: actions/upload-artifact@v4
        with:
          name: application-jar
          path: backend/application/target/*.jar
  frontend-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Copy .env file to backend and frontend
        run: cp docker/.env frontend/.env
      - name: Install deps
        working-directory: ./frontend
        run: npm ci --legacy-peer-deps
      - name: Build frontend application
        working-directory: ./frontend
        env:
          NEXT_PUBLIC_API_URL: https://wallet-backend.lemoncliff-6b8b9072.polandcentral.azurecontainerapps.io/api
        run: npm run build

  build-and-push-images:
    runs-on: ubuntu-latest
    needs: [ backend-build, frontend-build ]
    if: github.event_name != 'pull_request'
    permissions:
      contents: read
      packages: read
    steps:
      - uses: actions/checkout@v3
      - name: Download backend JAR
        uses: actions/download-artifact@v4
        with:
          name: application-jar
          path: backend/application/target/

      - name: Set up docker build
        uses: docker/setup-buildx-action@v3

      - name: Log in to Azure Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.ACR_LOGIN_SERVER }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: ${{ secrets.ACR_LOGIN_SERVER }}/${{ env.BACKEND_IMAGE_NAME }}:${{ github.sha }}
          cache-from: type=registry,ref=${{ secrets.ACR_LOGIN_SERVER }}/${{ env.BACKEND_IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ secrets.ACR_LOGIN_SERVER }}/${{ env.BACKEND_IMAGE_NAME }}:buildcache,mode=max

      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: true
          tags: ${{ secrets.ACR_LOGIN_SERVER }}/${{ env.FRONTEND_IMAGE_NAME }}:${{ github.sha }}
          cache-from: type=registry,ref=${{ secrets.ACR_LOGIN_SERVER }}/${{ env.FRONTEND_IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ secrets.ACR_LOGIN_SERVER }}/${{ env.FRONTEND_IMAGE_NAME }}:buildcache,mode=max
          build-args: |
            NEXT_PUBLIC_API_URL_ARG=https://wallet-backend.lemoncliff-6b8b9072.polandcentral.azurecontainerapps.io

  deploy-backend:
    runs-on: ubuntu-latest
    needs: [ build-and-push-images ]
    if: github.event_name != 'pull_request'
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.WALLETBACKEND_AZURE_CREDENTIALS }}
#        with:
#          client-id: ${{ secrets.WALLETBACKEND_AZURE_CLIENT_ID }}
#          tenant-id: ${{ secrets.WALLETBACKEND_AZURE_TENANT_ID }}
#          subscription-id: ${{ secrets.WALLETBACKEND_AZURE_SUBSCRIPTION_ID }}

      - name: Deploy backend to Container Apps
        uses: azure/container-apps-deploy-action@v2
        with:
          containerAppName: wallet-backend
          resourceGroup: ES-PORTFEL-PROD
          imageToDeploy: ${{ secrets.ACR_LOGIN_SERVER }}/${{ env.BACKEND_IMAGE_NAME }}:${{ github.sha }}
          registryUrl: ${{ secrets.ACR_LOGIN_SERVER }}
          registryUsername: ${{ secrets.WALLETBACKEND_REGISTRY_USERNAME }}
          registryPassword: ${{ secrets.WALLETBACKEND_REGISTRY_PASSWORD }}

  deploy-frontend:
    runs-on: ubuntu-latest
    needs: [ build-and-push-images ]
    if: github.event_name != 'pull_request'
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.WALLETFRONTEND_AZURE_CREDENTIALS }}
#        with:
#          client-id: ${{ secrets.WALLETFRONTEND_AZURE_CLIENT_ID }}
#          tenant-id: ${{ secrets.WALLETFRONTEND_AZURE_TENANT_ID }}
#          subscription-id: ${{ secrets.WALLETFRONTEND_AZURE_SUBSCRIPTION_ID }}

      - name: Deploy frontend to Container Apps
        uses: azure/container-apps-deploy-action@v2
        with:
          containerAppName: wallet-frontend
          resourceGroup: ES-PORTFEL-PROD
          imageToDeploy: ${{ secrets.ACR_LOGIN_SERVER }}/${{ env.FRONTEND_IMAGE_NAME }}:${{ github.sha }}
          registryUrl: ${{ secrets.ACR_LOGIN_SERVER }}
          registryUsername: ${{ secrets.WALLETFRONTEND_REGISTRY_USERNAME }}
          registryPassword: ${{ secrets.WALLETFRONTEND_REGISTRY_PASSWORD }}