# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

# This workflow checks out code, performs a Codacy security scan
# and integrates the results with the
# GitHub Advanced Security code scanning feature.  For more information on
# the Codacy security scan action usage and parameters, see
# https://github.com/codacy/codacy-analysis-cli-action.
# For more information on Codacy Analysis CLI in general, see
# https://github.com/codacy/codacy-analysis-cli.

name: Codacy Security Scan

on:
  push:
    branches: [ "dev" ]
  pull_request:
    branches: [ "dev" ]
  schedule:
    - cron: '20 12 * * 2'
    #- cron: '35 3 * * 2'

permissions:
  contents: read

jobs:
  codacy-security-scan:
    permissions:
      contents: read # for actions/checkout to fetch code
      security-events: write # for github/codeql-action/upload-sarif to upload SARIF results
      actions: read # only required for a private repository by github/codeql-action/upload-sarif to get the Action run status
    name: Codacy Security Scan
    runs-on: ubuntu-latest
    steps:
      # Checkout the repository to the GitHub Actions runner
      - name: Checkout code
        uses: actions/checkout@v4

      # Execute Codacy Analysis CLI and generate a SARIF output with the security issues identified during the analysis
      - name: Run Codacy Analysis CLI
        uses: codacy/codacy-analysis-cli-action@d840f886c4bd4edc059706d09c6a1586111c540b
        with:
          # Check https://github.com/codacy/codacy-analysis-cli#project-token to get your project token from your Codacy repository
          # You can also omit the token and run the tools that support default configurations
          project-token: ${{ secrets.CODACY_PROJECT_TOKEN }}
          verbose: true
          output: results.sarif
          format: sarif
          # Adjust severity of non-security issues
          gh-code-scanning-compat: true
          # Force 0 exit code to allow SARIF file generation
          # This will handover control about PR rejection to the GitHub side
          max-allowed-issues: 2147483647

      # Upload the SARIF file generated in the previous step
      - name: Upload SARIF results file
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: results.sarif
