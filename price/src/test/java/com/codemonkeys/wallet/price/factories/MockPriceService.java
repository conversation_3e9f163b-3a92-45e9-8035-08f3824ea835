/* (C)2024-2025 */
package com.codemonkeys.wallet.price.factories;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.service.AbstractPriceService;
import com.codemonkeys.wallet.price.validation.RecordNameValidator;
import org.jsoup.nodes.Element;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
public class MockPriceService extends AbstractPriceService {

  public MockPriceService(
      PriceRepository priceRepository,
      ApplicationEventPublisher eventPublisher,
      PriceProperties priceProperties,
      HolidayChecker holidayChecker,
      RecordNameValidator recordNameValidator) {
    super(priceRepository, eventPublisher, priceProperties, holidayChecker, recordNameValidator);
  }

  @Override
  protected String getRowSelector() {
    return "mockSelector";
  }

  @Override
  protected Price parseRowToScrapedContract(Element row) {
    return PriceFactory.createMockContract();
  }
}
