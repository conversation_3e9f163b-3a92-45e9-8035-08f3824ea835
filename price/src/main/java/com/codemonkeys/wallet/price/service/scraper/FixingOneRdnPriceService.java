/* (C)2025 */
package com.codemonkeys.wallet.price.service.scraper;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceDate;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.price.vo.PriceValue;
import com.codemonkeys.wallet.domain.price.vo.PriceVolume;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.service.AbstractPriceService;
import com.codemonkeys.wallet.price.service.TextNormalizer;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.jsoup.nodes.Element;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/** Scraper service for FIXING I values from TGE RDN hourly contracts. */
@Service
public class FixingOneRdnPriceService extends AbstractPriceService {

  public FixingOneRdnPriceService(
      PriceRepository priceRepository,
      ApplicationEventPublisher eventPublisher,
      PriceProperties priceProperties,
      HolidayChecker holidayChecker) {
    super(priceRepository, eventPublisher, priceProperties, holidayChecker);
  }

  @Override
  protected String getRowSelector() {
    return "#footable_kontrakty_godzinowe tbody tr";
  }

  @Override
  protected Price parseRowToScrapedContract(Element row) {
    String contract = TextNormalizer.normalize(row.select("td:nth-child(1)").text().trim());
    String price = TextNormalizer.normalize(row.select("td:nth-child(2)").text().trim());
    String volume = TextNormalizer.normalize(row.select("td:nth-child(3)").text().trim());

    if (!price.equals("-") && !price.isEmpty() && !volume.isEmpty()) {
      String mappedContract = mapContractHourToTgeFormat(contract);

      return Price.builder()
          .date(PriceDate.of(LocalDate.now().plusDays(1)))
          .contract(PriceName.of(mappedContract))
          .value(PriceValue.of(new BigDecimal(price.replace(",", "."))))
          .volume(PriceVolume.of(new BigDecimal(volume.replace(",", "."))))
          .build();
    }

    return null;
  }

  private String mapContractHourToTgeFormat(String hourRange) {
    try {
      String[] parts = hourRange.split("-");
      int hour = Integer.parseInt(parts[1]);
      return STR."TGe24-H\{hour}";
    } catch (Exception e) {
      return hourRange;
    }
  }
}
