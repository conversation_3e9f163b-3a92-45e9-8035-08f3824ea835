/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service.scraper;

import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceDate;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.price.vo.PriceValue;
import com.codemonkeys.wallet.domain.price.vo.PriceVolume;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.service.PriceService;
import com.codemonkeys.wallet.price.validation.RecordNameValidator;
import com.codemonkeys.wallet.price.validation.SkipNameValidation;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.ParseException;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** Service for downloading and processing WIBOR1R data from CSV file. */
@Slf4j
@Service
public class WiborCsvPriceService implements PriceService, SkipNameValidation {

  private static final String CONTRACT_NAME = "WIBOR1R";
  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
  private final PriceRepository priceRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final PriceProperties priceProperties;
  private final HolidayChecker holidayChecker;
  private final RecordNameValidator recordNameValidator;

  public WiborCsvPriceService(
      PriceRepository priceRepository,
      ApplicationEventPublisher eventPublisher,
      PriceProperties priceProperties,
      HolidayChecker holidayChecker,
      RecordNameValidator recordNameValidator) {
    this.priceRepository = priceRepository;
    this.eventPublisher = eventPublisher;
    this.priceProperties = priceProperties;
    this.holidayChecker = holidayChecker;
    this.recordNameValidator = recordNameValidator;
  }

  @Transactional
  @Override
  public void scrape(PriceProperties.PriceConfig config) throws IOException, ParseException {
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      String csvContent = fetchContent(httpClient, config.getUrl());
      if (csvContent != null) {
        long startTime = System.currentTimeMillis();
        List<Price> scrapedPrices = parseCSVContent(csvContent);
        long endTime = System.currentTimeMillis();
        log.info(
            "CSV parsing completed for {} in {} ms, total records: {}",
            config.getUrl(),
            (endTime - startTime),
            scrapedPrices.size());
        saveScrapedContracts(scrapedPrices);
      }
    } catch (IOException | ParseException e) {
      String errorMessage = "Error downloading data from URL: " + config.getUrl();
      log.error(errorMessage, e);
      throw e;
    }
  }

  /**
   * Fetches content from the specified URL.
   *
   * @param httpClient the HTTP client to use
   * @param url the URL to fetch content from
   * @return the content as a string, or null if an error occurs
   * @throws IOException if an I/O error occurs
   * @throws ParseException if a parse error occurs
   */
  private String fetchContent(CloseableHttpClient httpClient, String url)
      throws IOException, ParseException {
    HttpGet httpGet = new HttpGet(url);
    try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
      int statusCode = response.getCode();
      if (statusCode == 200) {
        HttpEntity entity = response.getEntity();
        if (entity != null) {
          // Use InputStreamReader with UTF-8 encoding to handle potential encoding issues
          StringBuilder content = new StringBuilder();
          try (BufferedReader reader =
              new BufferedReader(
                  new InputStreamReader(entity.getContent(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
              content.append(line).append("\n");
            }
          }
          return content.toString();
        }
      } else {
        log.error("Failed to fetch content from URL: {}, status code: {}", url, statusCode);
      }
    }
    return null;
  }

  /**
   * Parses CSV content into a list of Price entities.
   *
   * @param csvContent the CSV content as a string
   * @return a list of Price entities
   */
  private List<Price> parseCSVContent(String csvContent) {
    List<Price> prices = new ArrayList<>();
    try (BufferedReader reader = new BufferedReader(new StringReader(csvContent))) {
      String line;
      boolean isHeader = true;

      while ((line = reader.readLine()) != null) {
        if (isHeader) {
          isHeader = false;
          continue; // Skip header line
        }

        Price price = parseCSVLine(line);
        if (price != null) {
          prices.add(price);
        }
      }
    } catch (IOException e) {
      log.error("Error parsing CSV content", e);
    }
    return prices;
  }

  /**
   * Parses a single CSV line into a Price entity.
   *
   * @param line the CSV line
   * @return a Price entity or null if parsing fails
   */
  private Price parseCSVLine(String line) {
    try {
      String[] parts = line.split(",");
      if (parts.length < 5) {
        log.warn("Invalid CSV line format: {}", line);
        return null;
      }

      String dateStr = parts[0].trim();
      String closePrice = parts[4].trim(); // "Zamkniecie" column

      LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
      BigDecimal price = new BigDecimal(closePrice);
      BigDecimal volume = BigDecimal.ONE; // Default volume

      return Price.builder()
          .date(PriceDate.of(date))
          .contract(PriceName.of(CONTRACT_NAME))
          .volume(PriceVolume.of(volume))
          .value(PriceValue.of(price))
          .build();
    } catch (Exception e) {
      log.warn("Error parsing CSV line: {}", line, e);
      return null;
    }
  }

  /**
   * Saves the scraped contracts to the repository.
   *
   * @param scrapedPrices the list of scraped contracts
   */
  private void saveScrapedContracts(List<Price> scrapedPrices) {
    for (Price price : scrapedPrices) {
      if (!priceRepository.existsByDateAndContract(price.getDate(), price.getContract())) {
        priceRepository.save(price);
      } else {
        log.info(
            "Record already exists for date: {} and contract: {}",
            price.getDate(),
            price.getContract());
      }
    }
  }
}
