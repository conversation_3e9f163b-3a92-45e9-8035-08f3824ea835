/* (C)2024-2025 */
package com.codemonkeys.wallet.price.service;

import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.price.config.HolidayChecker;
import com.codemonkeys.wallet.price.config.PriceProperties;
import com.codemonkeys.wallet.price.config.PriceProperties.PriceConfig;
import java.lang.reflect.Constructor;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/** Service responsible for managing scraping tasks based on configurations. */
@Slf4j
@RequiredArgsConstructor
@Service
public class PriceManagerService {

  private final PriceProperties priceProperties;
  private final PriceRepository priceRepository;
  private final ApplicationEventPublisher eventPublisher;
  private final HolidayChecker holidayChecker;

  /** Scheduled method to trigger scraping based on the cron configuration. */
  @Scheduled(cron = "${price.cron}")
  public void scheduledScrape() {
    Map<String, PriceConfig> prices = priceProperties.getPrices();
    if (prices == null || prices.isEmpty()) {
      log.error("Scrapers configuration is null or empty");
      return;
    }
    for (Map.Entry<String, PriceConfig> entry : prices.entrySet()) {
      PriceConfig config = entry.getValue();
      if (config.isEnabled()) {
        try {
          PriceService priceService = getPriceService(config.getType());
          priceService.scrape(config);
        } catch (Exception e) {
          log.error("Failed to instantiate scraper for class: {}", config.getType(), e);
        }
      }
    }
  }

  /**
   * Method to instantiate a scraper service based on the provided class name.
   *
   * @param className the fully qualified name of the scraper service class
   * @return an instance of ScraperService
   * @throws Exception if instantiation fails
   */
  private PriceService getPriceService(String className) throws Exception {
    Class<?> clazz = Class.forName(className);
    Constructor<?> constructor =
        clazz.getConstructor(
            PriceRepository.class,
            ApplicationEventPublisher.class,
            PriceProperties.class,
            HolidayChecker.class);
    return (PriceService)
        constructor.newInstance(priceRepository, eventPublisher, priceProperties, holidayChecker);
  }
}
