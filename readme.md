# Monorepo Template (Spring Boot + Maven multi‑module + Next.js + Shared Types)

Ten szablon pokazuje, jak u<PERSON><PERSON> **wspólne typy** międ<PERSON> backendem Spring Boot (Maven multi‑module) a frontendem Next.js w jednym monorepo zarządzanym przez **pnpm workspaces**.

---

## Wymagania

| Narzędzie   | Minimalna wersja    | Sprawdź poleceniem |
| ----------- | ------------------- | ------------------ |
| **Node.js** | 20 LTS              | `node -v`          |
| **pnpm**    | 8 lub 10            | `pnpm -v`          |
| **JDK**     | 17                  | `java -version`    |
| **Maven**   | 3.9.x               | `mvn -v`           |
| **Git**     | dowolna współczesna | `git --version`    |

---

## Struktura repo (skrót)

```
/monorepo-template
├── pnpm-workspace.yaml
├── package.json              # skrypty pnpm (root)
└── packages
    ├── backend/              # projekt Maven (parent)
    │   ├── pom.xml           # packaging=pom
    │   ├── api/              # DTO + plugin TS‑generator
    │   ├── core/             # logika biznesowa
    │   └── web-adapter/      # Spring Boot (REST)
    ├── shared-types/         # (opc.) tylko index.d.ts + pom
    └── frontend/             # Next.js 18 + TypeScript
```

---

## Getting Started

```bash
# 1 – klonuj repo i przejdź do katalogu projektu
git clone https://github.com/your-org/monorepo-maven-template.git
cd monorepo-maven-template

# 2 – zainstaluj zależności frontu
pnpm install --no-strict-peer-dependencies

# 3 – wygeneruj deklaracje TypeScript
mvn -q -pl packages/backend/api generate-sources

# 4 – uruchom backend (Spring Boot) i frontend (Next.js) równolegle
mvn -pl packages/backend/web-adapter spring-boot:run & \
  pnpm --filter frontend dev
```

> Po kroku 3 plik `frontend/src/generated/index.d.ts` *(lub **`shared-types/index.d.ts`**, jeśli używasz paczki)* jest już dostępny dla TypeScripta.

---

## Jak to działa?

1. W module **api/** działa `typescript-generator-maven-plugin` (`3.2.1263`).
2. Plugin skanuje klasy `com.yourcompany.api.dto.**` i generuje plik *d.ts*.
3. **pnpm workspaces** od razu udostępnia typy frontendowi.
4. Komponent React może importować DTO:
   ```tsx
   import type { UserDto } from '../generated/index';
   ```

---

## Kluczowe fragmenty konfiguracji

### parent `pom.xml`

```xml
<modules>
  <module>api</module>
  <module>core</module>
  <module>web-adapter</module>
</modules>
<properties>
  <typescript-generator.version>3.2.1263</typescript-generator.version>
</properties>
```

### `api/pom.xml` – wtyczka generująca typy

```xml
<plugin>
  <groupId>cz.habarta.typescript-generator</groupId>
  <artifactId>typescript-generator-maven-plugin</artifactId>
  <version>${typescript-generator.version}</version>
  <executions>
    <execution>
      <id>generate-dts</id>
      <phase>generate-sources</phase>
      <goals><goal>generate</goal></goals>
      <configuration>
        <outputFile>${project.basedir}/../../frontend/src/generated/index.d.ts</outputFile>
        <classPatterns>
          <classPattern>com.yourcompany.api.dto.**</classPattern>
        </classPatterns>
        <outputKind>module</outputKind>
        <outputFileType>declarationFile</outputFileType>
        <jsonLibrary>jackson2</jsonLibrary>
      </configuration>
    </execution>
  </executions>
</plugin>
```

---

## Najczęstsze problemy

| Objaw                                | Rozwiązanie                                                                                      |
| ------------------------------------ | ------------------------------------------------------------------------------------------------ |
| `Failed to execute goal cz.habarta…` | Upewnij się, że katalog docelowy istnieje lub zmień ścieżkę `outputFile`.                        |
| Frontend nie widzi nowych pól        | Uruchom ponownie `mvn -pl packages/backend/api generate-sources`.                                |
| Port 3000 lub 8080 zajęty            | `set PORT=4001` (Windows) lub `export PORT=4001` przed `pnpm dev`; zmień `server.port` w Spring. |
| pnpm nie linkuje `shared-types`      | Dopisz `"shared-types": "workspace:*"` w `frontend/package.json` i `pnpm install`.               |

---

Happy coding 🚀

