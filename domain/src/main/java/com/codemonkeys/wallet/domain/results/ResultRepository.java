/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.results;

import com.codemonkeys.wallet.common.framework.shared.persistence.CommonRepository;
import com.codemonkeys.wallet.domain.results.vo.ResultId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import org.springframework.data.jpa.repository.Query;

public interface ResultRepository extends CommonRepository<Result, ResultId> {
  @Query("SELECT r.wallet from Result r where r.id = :resultId")
  Wallet findWalletByResultId(ResultId resultId);
}
