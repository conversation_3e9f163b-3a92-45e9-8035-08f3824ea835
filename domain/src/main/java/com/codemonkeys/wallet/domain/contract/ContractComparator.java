/* (C)2025 */
package com.codemonkeys.wallet.domain.contract;

import com.codemonkeys.wallet.domain.agreement.ContractType;
import java.io.Serializable;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.Map;

public class ContractBusinessComparator implements Comparator<Contract>, Serializable {

  private static final Map<ContractType, Integer> ORDER = new EnumMap<>(ContractType.class);

  static {
    ORDER.put(ContractType.Y, 1);
    ORDER.put(ContractType.Q, 2);
    ORDER.put(ContractType.M, 3);
    ORDER.put(ContractType.TGe24, 4);
    ORDER.put(ContractType.TGEgasDA, 4);
    ORDER.put(ContractType.TGEgasID, 4);
    ORDER.put(ContractType.PMOZE_A, 5);
    ORDER.put(ContractType.PMOZE_BIO, 5);
    ORDER.put(ContractType.PMEF_F, 5);
  }

  @Override
  public int compare(Contract a, Contract b) {
    int byType =
        Integer.compare(
            ORDER.getOrDefault(a.getContractType(), Integer.MAX_VALUE),
            ORDER.getOrDefault(b.getContractType(), Integer.MAX_VALUE));
    return byType != 0 ? byType : a.getName().toString().compareTo(b.getName().toString());
  }
}
