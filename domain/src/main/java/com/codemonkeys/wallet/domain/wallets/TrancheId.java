/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.fasterxml.jackson.annotation.JsonValue;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Embeddable;
import java.io.Serializable;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.Value;
import org.jmolecules.ddd.types.Identifier;
import org.jmolecules.ddd.types.ValueObject;

@Value(staticConstructor = "of")
@NoArgsConstructor(access = AccessLevel.PROTECTED, force = true)
@Embeddable
public class TrancheId implements Identifier, Serializable, ValueObject {
  @Access(AccessType.FIELD)
  UUID id;

  private TrancheId(UUID id) {
    this.id = id;
  }

  public static TrancheId randomId() {
    return new TrancheId(UUID.randomUUID());
  }

  public static TrancheId of(UUID uuid) {
    return new TrancheId(uuid);
  }

  public static TrancheId valueOf(String uuid) {
    return new TrancheId(UUID.fromString(uuid));
  }

  @JsonValue
  public String getId() {
    return id.toString();
  }

  @Override
  public String toString() {
    return id.toString();
  }
}
