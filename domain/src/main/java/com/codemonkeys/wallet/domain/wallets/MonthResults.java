/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.domain.results.Result;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * A class for storing and managing result objects ({@link Result}) grouped by time units ({@link
 * TimeUnit}). It provides functionality for grouping, retrieving, and manipulating results assigned
 * to specific time periods.
 *
 * <p>This class is useful for storing and looking up results related to Wallets across different
 * time periods (months, quarters, year).
 */
@Data
@AllArgsConstructor
public class MonthResults {

  /**
   * A map storing results (Result) assigned to specific time units (TimeUnit). Each time unit can
   * have at most one result assigned to it.
   */
  @JsonValue private Map<TimeUnit, Result> data;

  /**
   * Creates a MonthResults instance from a list of results.
   *
   * @param results The list of results to process and group by time units
   */
  public MonthResults(List<Result> results) {
    data = prepare(results);
  }

  /**
   * Processes a list of results and groups them by time units. For each month in the year
   * (TimeUnit.Y), it finds the first matching result which is then added to the resulting map.
   *
   * @param results The list of results to process
   * @return A map of results grouped by time units
   */
  private Map<TimeUnit, Result> prepare(List<Result> results) {
    Map<TimeUnit, Result> result = new HashMap<>();

    for (Result r : results) {
      if (r.getTimeUnit() != null) {
        for (TimeUnit t : r.getTimeUnit().getMonths()) {
          if (!result.containsKey(t)) {
            result.put(t, r);
          }
        }
      }
    }
    // for (TimeUnit month : TimeUnit.Y.getMonths()) {
    //  results.stream()
    //      .filter(t -> month.equals(t.getTimeUnit()))
    //      .findFirst()
    //      .ifPresent(value -> result.put(month, value));
    // }
    //
    // for (TimeUnit quarter : List.of(TimeUnit.Q1, TimeUnit.Q2, TimeUnit.Q3, TimeUnit.Q4)) {
    //  results.stream()
    //      .filter(t -> quarter.equals(t.getTimeUnit()))
    //      .findFirst()
    //      .ifPresent(value -> result.put(quarter, value));
    // }
    // results.stream()
    //    .filter(t -> TimeUnit.Y.equals(t.getTimeUnit()))
    //    .findFirst()
    //    .ifPresent(value -> result.put(TimeUnit.Y, value));
    return result;
  }

  /**
   * Adds a new result to the specified time unit. If a result already exists for the given time
   * unit, it will be replaced with the new one.
   *
   * @param timeUnit The time unit to which the result should be assigned
   * @param resultToAdd The result to add
   */
  public void add(TimeUnit timeUnit, Result resultToAdd) {
    data.put(timeUnit, resultToAdd);
  }

  /**
   * Retrieves a result assigned to the specified time unit.
   *
   * @param unit The time unit for which to retrieve the result
   * @return An Optional containing the result if it exists for the given unit, or an empty Optional
   */
  public Optional<Result> get(TimeUnit unit) {
    Optional<Result> monthResult = Optional.ofNullable(data.get(unit));
    Optional<Result> quarterResult = Optional.ofNullable(data.get(unit.getQuarter()));
    Optional<Result> yearResult = Optional.ofNullable(data.get(TimeUnit.Y));
    return monthResult.or(() -> quarterResult).or(() -> yearResult);
  }

  /**
   * Retrieves a result assigned to the specified time unit and media type. This method checks if a
   * result exists for the given time unit and if its media type matches the requested one.
   *
   * @param unit The time unit for which to retrieve the result
   * @param media The media type that the result must belong to
   * @return An Optional containing the result if it exists for the given unit and media type, or an
   *     empty Optional
   */
  public Optional<Result> get(TimeUnit unit, Media media) {
    return Optional.ofNullable(data.get(unit))
        .filter(result -> Objects.equals(result.getMediaType(), media));
  }
}
