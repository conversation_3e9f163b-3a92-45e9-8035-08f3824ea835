/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.price;

import com.codemonkeys.wallet.common.framework.shared.persistence.CommonRepository;
import com.codemonkeys.wallet.common.framework.shared.persistence.ExcludedFromTenantFilter;
import com.codemonkeys.wallet.domain.price.vo.PriceDate;
import com.codemonkeys.wallet.domain.price.vo.PriceId;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.jmolecules.ddd.annotation.Repository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

@Repository
public interface PriceRepository
    extends CommonRepository<Price, PriceId>, ExcludedFromTenantFilter {

  @Query("SELECT p FROM Price p WHERE p.date.date = :date")
  List<Price> findByDate(@Param("date") LocalDate date);

  @Query("SELECT p FROM Price p WHERE p.date.date BETWEEN :startDate AND :endDate")
  List<Price> findByDateBetween(
      @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

  @Query(
      "SELECT p FROM Price p WHERE p.date.date BETWEEN :startDate AND :endDate and p.contract"
          + " = :contract")
  List<Price> findByContractAndDateBetween(
      @Param("contract") PriceName contract,
      @Param("startDate") LocalDate startDate,
      @Param("endDate") LocalDate endDate);

  @Query(
      value = "SELECT * FROM PRICES p WHERE p.name = :contract ORDER BY p.date DESC LIMIT 1",
      nativeQuery = true)
  Optional<Price> findLatestPriceByContract(@Param("contract") String contract);

  @Query("SELECT p FROM Price p WHERE p.date.date = :date and  p.contract = :contract")
  Price findByContractAndDate(@Param("contract") PriceName contract, @Param("date") LocalDate date);

  boolean existsByDateAndContract(PriceDate date, PriceName contract);

  @Query(value = "SELECT DISTINCT name FROM PRICES WHERE name LIKE :type%", nativeQuery = true)
  List<String> findDistinctContractsByTypeNative(@Param("type") String type);

  @Query("SELECT MAX(p.value.value) FROM Price p WHERE p.contract.name = :contractName")
  Optional<Double> findMaxPriceByContract(@Param("contractName") String contractName);
}
