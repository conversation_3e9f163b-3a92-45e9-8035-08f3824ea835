/* (C)2025 */
package com.codemonkeys.wallet.domain.customer;

import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.domain.customer.vo.CapUnit;
import com.codemonkeys.wallet.domain.customer.vo.DatePeriod;
import com.codemonkeys.wallet.domain.customer.vo.FixedRemunerationValue;
import com.codemonkeys.wallet.domain.customer.vo.RemunerationId;
import com.codemonkeys.wallet.domain.customer.vo.RemunerationType;
import com.codemonkeys.wallet.domain.customer.vo.RemunerationValue;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.jmolecules.ddd.types.AggregateRoot;

@Getter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "REMUNERATIONS")
public class Remuneration extends IdentifiableAggregateRoot<RemunerationId>
    implements AggregateRoot<Remuneration, RemunerationId> {

  @EmbeddedId private RemunerationId id;

  @Column(name = "carrier", nullable = false)
  @Enumerated(EnumType.STRING)
  private Media carrier;

  @Embedded
  @AttributeOverrides({
    @AttributeOverride(name = "from", column = @Column(name = "date_from")),
    @AttributeOverride(name = "to", column = @Column(name = "date_to"))
  })
  private DatePeriod period;

  @Column(name = "cap")
  private BigDecimal cap;

  @Embedded
  @AttributeOverride(name = "value", column = @Column(name = "cap_unit"))
  private CapUnit capUnit;

  @Embedded
  @AttributeOverride(name = "value", column = @Column(name = "fixed_remuneration_type"))
  private RemunerationType fixedRemunerationType;

  @Embedded
  @AttributeOverride(name = "amount", column = @Column(name = "fixed_remuneration_value"))
  private FixedRemunerationValue fixedRemunerationValue;

  @Embedded
  @AttributeOverride(name = "value", column = @Column(name = "remuneration_type"))
  private RemunerationType remunerationType;

  @Embedded
  @AttributeOverride(name = "amount", column = @Column(name = "remuneration_value"))
  private RemunerationValue remunerationValue;

  @Column(name = "tender_cost")
  private BigDecimal tenderCost;

  @Column(name = "substitution_fee")
  private BigDecimal substitutionFee;

  @Column(name = "market_pmozea")
  private BigDecimal marketPmozea;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customer_id", nullable = false)
  @JsonBackReference
  private Customer customer;

  public Remuneration(
      RemunerationId id,
      Media carrier,
      DatePeriod period,
      BigDecimal cap,
      CapUnit capUnit,
      RemunerationType fixedRemunerationType,
      FixedRemunerationValue fixedRemunerationValue,
      RemunerationType remunerationType,
      RemunerationValue remunerationValue,
      BigDecimal tenderCost,
      BigDecimal substitutionFee,
      BigDecimal marketPmozea) {
    this.id = id;
    this.carrier = carrier;
    this.period = period;
    this.cap = cap;
    this.capUnit = capUnit;
    this.fixedRemunerationType = fixedRemunerationType;
    this.fixedRemunerationValue = fixedRemunerationValue;
    this.remunerationType = remunerationType;
    this.remunerationValue = remunerationValue;
    this.tenderCost = tenderCost;
    this.substitutionFee = substitutionFee;
    this.marketPmozea = marketPmozea;
  }

  public void setCustomer(Customer customer) {
    this.customer = customer;
  }
}
