/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.PriceId;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.*;
import org.hibernate.annotations.Type;
import org.jmolecules.ddd.types.AggregateRoot;

@Getter
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "WALLET_PRICES")
@ToString
public class WalletPrice extends IdentifiableAggregateRoot<PriceId>
    implements AggregateRoot<WalletPrice, PriceId> {

  @EmbeddedId private PriceId id;

  @JsonIgnoreProperties(
      value = {
        "tranches",
        "elements",
        "products",
        "prices",
        "agreement",
        "hibernateLazyInitializer",
        "handler",
        "results",
        "realisationDetails",
        "analyticalSettings"
      })
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "wallet_id", nullable = false)
  private Wallet wallet;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private ElementType type;

  @Enumerated(EnumType.STRING)
  @Column(name = "time_unit", nullable = false)
  private TimeUnit timeUnit;

  @Column(name = "value", nullable = false)
  private BigDecimal value;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  @Column(name = "confirmed")
  private Boolean confirmed;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private PriceMetadata priceMetadata;

  public WalletPrice(@NonNull PriceId id) {
    this.id = id;
  }

  public WalletPrice(
      PriceId id, Wallet wallet, ElementType type, TimeUnit timeUnit, BigDecimal value) {
    this.id = id;
    this.wallet = wallet;
    // this.type = type;
    this.timeUnit = timeUnit;
    this.value = value;
    this.type = type;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
    this.confirmed = false;
  }

  public WalletPrice(WalletPrice other) {
    this.id = PriceId.randomId();
    this.wallet = other.getWallet();
    this.type = other.getType();
    this.timeUnit = other.getTimeUnit();
    this.value = other.getValue();
    this.confirmed = false;
  }

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    this.tenantId =
        Optional.ofNullable(TenantContext.getTenantId())
            .filter(id -> !id.isEmpty())
            .orElseGet(
                () -> {
                  if (wallet == null
                      || wallet.getAgreement() == null
                      || wallet.getAgreement().getTenantId() == null) {
                    throw new IllegalStateException(
                        "Tenant ID must be set. Ensure Wallet and Agreement are properly configured.");
                  }
                  return wallet.getAgreement().getTenantId();
                });
  }

  @Override
  public PriceId getId() {
    return id;
  }

  public Wallet getWallet() {
    return wallet;
  }

  public void setWallet(Wallet wallet) {
    this.wallet = wallet;
  }

  public void add(BigDecimal addend) {
    value = value.add(addend);
  }

  public void multiply(BigDecimal multiplier) {
    value = value.multiply(multiplier);
  }

  public void setConfirmed(boolean state) {
    this.confirmed = state;
  }

  public void setPriceMetadata(PriceMetadata priceMetadata) {
    this.priceMetadata = priceMetadata;
  }
}
