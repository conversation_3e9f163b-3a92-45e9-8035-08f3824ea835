/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.contract.vo;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Value
@NoArgsConstructor(access = AccessLevel.PROTECTED, force = true)
public class ContractName {
  private static final Set<String> PROPERTY_RIGHTS_PREFIXES =
      Set.of("PMOZE_A", "PMOZE_BIO", "PMEF_F");
  @NonNull String value;

  private ContractName(
      @NonNull Year year,
      @NonNull MediaType mediaType,
      @NonNull ContractType product,
      Integer timeUnit) {
    String yearDigits = year.toString().substring(2);
    String base =
        switch (mediaType) {
          case ENERGY -> "BASE";
          case GAS -> "GAS_BASE";
        };
    String name =
        switch (product) {
          case Y -> String.format("%s_Y-%s", base, yearDigits);
          case Q -> String.format("%s_Q-%01d-%s", base, timeUnit, yearDigits);
          case M -> String.format("%s_M-%02d-%s", base, timeUnit, yearDigits);
          case PMOZE_A -> {
            if (timeUnit != null) {
              yield String.format("%s-%02d-%s", ContractType.PMOZE_A, timeUnit, yearDigits);
            } else {
              yield String.format("%s", ContractType.PMOZE_A);
            }
          }
          case PMOZE_BIO -> {
            if (timeUnit != null) {
              yield String.format("%s-%02d-%s", ContractType.PMOZE_BIO, timeUnit, yearDigits);
            } else {
              yield String.format("%s", ContractType.PMOZE_BIO);
            }
          }
          case PMEF_F -> {
            if (timeUnit != null) {
              yield String.format("%s-%02d-%s", ContractType.PMEF_F, timeUnit, yearDigits);
            } else {
              yield String.format("%s", ContractType.PMEF_F);
            }
          }
          case SS -> String.format("%s_S-S-%s", base, yearDigits);
          case SW -> String.format("%s_S-W-%s", base, yearDigits);
          case TGe24 -> ContractType.TGe24.toString();
          case TGEgasDA -> ContractType.TGEgasDA.toString();
          case TGEgasID -> ContractType.TGEgasID.toString();
        };

    // this.year = Integer.valueOf(year.getYear());
    this.value = name;
  }

  public ContractName(@NonNull String value) {
    this.value = value;
  }

  public static ContractName of(@NonNull String value) {
    return new ContractName(value);
  }

  public static ContractName of(
      @NonNull Year year,
      @NonNull MediaType mediaType,
      @NonNull ContractType product,
      Integer timeUnit) {
    return new ContractName(year, mediaType, product, timeUnit);
  }

  public static ContractName of(
      @NonNull Year year, @NonNull MediaType mediaType, @NonNull ContractType product) {
    return new ContractName(year, mediaType, product, null);
  }

  @JsonValue
  @Override
  public String toString() {
    return value;
  }

  public TimeUnit getTimeUnit() {
    if (value == null || value.isEmpty()) {
      return null;
    }
    Pattern[] patterns =
        new Pattern[] {
          Pattern.compile(".*_M-(\\d{1,2})-.*"),
          Pattern.compile(".*_Q-(\\d{1})-.*"),
          Pattern.compile(".*_Y-.*"),
        };

    String[] prefixes = new String[] {"M", "Q", "Y"};
    for (int i = 0; i < patterns.length; i++) {
      Matcher matcher = patterns[i].matcher(value);
      if (matcher.matches()) {
        String prefix = prefixes[i];
        if ("Y".equals(prefix)) {
          return TimeUnit.Y;
        }
        int unit = Integer.parseInt(matcher.group(1));
        return TimeUnit.valueOf(prefix + unit);
      }
    }
    List<String> spot =
        List.of(
            ContractType.TGe24.name(), ContractType.TGEgasDA.name(), ContractType.TGEgasID.name());
    if (spot.contains(value)) {
      return TimeUnit.Y;
    }

    // handle property rights
    return parseTimeUnit(value);
  }

  /**
   * Parse a time unit string into a TimeUnit enum value. Examples: - "PMOZE_A-25" -> TimeUnit.Y -
   * "PMOZE_A-M1-25" -> TimeUnit.M1
   *
   * @param value The string to parse
   * @return The corresponding TimeUnit
   */
  public TimeUnit parseTimeUnit(String value) {
    if (value == null || value.isEmpty()) {
      throw new IllegalArgumentException("Value cannot be null or empty");
    }

    if (!hasValidPrefix(value)) {
      return TimeUnit.Y;
    }

    String[] parts = value.split("-");
    return switch (parts.length) {
      case 1, 2 -> TimeUnit.Y;
      case 3 -> parseMonthTimeUnit(parts[1]);
      default -> TimeUnit.Y;
    };
  }

  private boolean hasValidPrefix(String value) {
    return PROPERTY_RIGHTS_PREFIXES.stream().anyMatch(value::startsWith);
  }

  private TimeUnit parseMonthTimeUnit(String monthPart) {
    if (!monthPart.startsWith("M") && monthPart.startsWith("0")) {
      monthPart = "M%s".formatted(monthPart.substring(1, 2));
    }
    if (!monthPart.startsWith("M") && !monthPart.startsWith("0")) {
      monthPart = "M%s".formatted(monthPart);
    }
    return TimeUnit.valueOf(monthPart);
  }

  public String getStockName() {
    if (!value.contains("PMOZE_A-")
        && !value.contains("PMOZE_BIO-")
        && !value.contains("PMEF_F-")) {
      return value;
    } else {
      int idx = value.indexOf("-");
      return value.substring(0, idx);
    }
  }
}
