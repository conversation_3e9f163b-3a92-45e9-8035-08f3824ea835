/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.contract;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.enums.PurchaseModel;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.contract.vo.ActionIfNot100;
import com.codemonkeys.wallet.domain.contract.vo.AverageCalculation;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.contract.vo.ContractName;
import com.codemonkeys.wallet.domain.contract.vo.OrderTimes;
import com.codemonkeys.wallet.domain.contract.vo.OrderTypeParameters;
import com.codemonkeys.wallet.domain.contract.vo.Quantity;
import com.codemonkeys.wallet.domain.contract.vo.RejectionReason;
import com.codemonkeys.wallet.domain.contract.vo.Volume;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.persistence.*;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.*;
import org.jmolecules.ddd.types.AggregateRoot;

@Getter
@EqualsAndHashCode(
    callSuper = true,
    exclude = {"agreement"})
@Entity
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@Table(name = "contracts")
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY)
@JsonSubTypes({
  @JsonSubTypes.Type(value = EnergyContract.class, name = "Energy"),
  @JsonSubTypes.Type(value = GasContract.class, name = "Gas"),
  @JsonSubTypes.Type(value = PropertyRightContract.class, name = "PropertyRight")
})
public abstract class Contract extends IdentifiableAggregateRoot<ContractId>
    implements AggregateRoot<Contract, ContractId> {

  @Setter @EmbeddedId protected ContractId id;

  @Embedded
  @AttributeOverride(name = "value", column = @Column(name = "name"))
  protected ContractName name;

  @Embedded
  @AttributeOverride(name = "startDate", column = @Column(name = "average_calculation_start_date"))
  @AttributeOverride(name = "endDate", column = @Column(name = "average_calculation_end_date"))
  protected AverageCalculation averageCalculation;

  @Embedded
  @AttributeOverride(name = "type", column = @Column(name = "quantity_type"))
  @AttributeOverride(name = "value", column = @Column(name = "quantity_value"))
  @AttributeOverride(name = "year", column = @Column(name = "quantity_year"))
  @AttributeOverride(name = "quarter", column = @Column(name = "quantity_quarter"))
  @AttributeOverride(name = "month", column = @Column(name = "quantity_month"))
  protected Quantity quantity;

  @AttributeOverride(name = "type", column = @Column(name = "volume_type"))
  @AttributeOverride(name = "x", column = @Column(name = "volume_x"))
  @AttributeOverride(name = "y", column = @Column(name = "volume_y"))
  @AttributeOverride(name = "value", column = @Column(name = "volume_value"))
  @Embedded
  protected Volume volume;

  @Embedded
  @AttributeOverride(name = "type", column = @Column(name = "actionifnot100_type"))
  @AttributeOverride(name = "value", column = @Column(name = "actionifnot100_value"))
  protected ActionIfNot100 actionIfNot100;

  @AttributeOverride(name = "type", column = @Column(name = "rejection_reason_type"))
  @AttributeOverride(name = "value", column = @Column(name = "rejection_reason_value"))
  @Embedded
  protected RejectionReason rejectionReason;

  @ElementCollection(fetch = FetchType.LAZY)
  @CollectionTable(
      name = "contract_price_reference",
      joinColumns = @JoinColumn(name = "contract_id"))
  @Enumerated(EnumType.STRING)
  @Column(name = "price_reference")
  protected List<PriceReference> priceReference;

  @Column(name = "year")
  protected Year year;

  @JsonIgnoreProperties(
      value = {"contracts", "authorizedBuyers", "customer", "supplier", "wallets"})
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "agreement_id", updatable = false)
  @Setter
  protected Agreement agreement;

  @Enumerated(EnumType.STRING)
  @Column(name = "purchase_model")
  protected PurchaseModel purchaseModel;

  @Embedded
  @AttributeOverride(name = "type", column = @Column(name = "order_type"))
  @AttributeOverride(name = "value", column = @Column(name = "order_value"))
  protected OrderTypeParameters orderTypeParameters;

  @Enumerated(EnumType.STRING)
  @Column(name = "contract_type")
  protected ContractType contractType;

  @ElementCollection(fetch = FetchType.LAZY)
  @CollectionTable(name = "order_times", joinColumns = @JoinColumn(name = "contract_id"))
  @MapKeyEnumerated(EnumType.STRING)
  @MapKeyColumn(name = "price_reference")
  @AttributeOverrides({
    @AttributeOverride(name = "start", column = @Column(name = "order_time_start")),
    @AttributeOverride(name = "end", column = @Column(name = "order_time_end"))
  })
  private Map<PriceReference, OrderTimes.OrderTime> orderTimes;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  public Contract(
      ContractId id,
      ContractName name,
      AverageCalculation averageCalculation,
      Quantity quantity,
      Volume volume,
      ActionIfNot100 actionIfNot100,
      Map<PriceReference, OrderTimes.OrderTime> orderTimes,
      RejectionReason rejectionReason,
      List<PriceReference> priceReference,
      Year year,
      Agreement agreement,
      PurchaseModel purchaseModel,
      OrderTypeParameters orderTypeParameters,
      ContractType contractType) {
    this.id = id;
    this.name = name;
    this.averageCalculation = averageCalculation;
    this.quantity = quantity;
    this.volume = volume;
    this.actionIfNot100 = actionIfNot100;
    this.orderTimes = orderTimes;
    this.rejectionReason = rejectionReason;
    this.priceReference = priceReference;
    this.year = year;
    this.agreement = agreement;
    this.purchaseModel = purchaseModel;
    this.orderTypeParameters = orderTypeParameters;
    this.contractType = contractType;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  public Contract(@NonNull ContractId id) {
    this.id = id;
  }

  /**
   * Returns a contract matching the given type, media, year, and optional time unit.
   *
   * <p>Matches by generated contract name using {@link ContractName#of}.
   *
   * @param wallet the wallet containing the agreement
   * @param contractType the contract type (Y, Q, M, etc.)
   * @param mediaType the media type (e.g., ENERGY, GAS)
   * @param yearInt full year (e.g., 2025)
   * @param timeUnit optional time unit (e.g., 1 for Q1), null for yearly
   * @return optional matching contract
   */
  public static Optional<Contract> findContract(
      Wallet wallet,
      ContractType contractType,
      MediaType mediaType,
      int yearInt,
      Integer timeUnit) {
    Year year = Year.of(yearInt);
    ContractName name = ContractName.of(year, mediaType, contractType, timeUnit);

    return wallet.getAgreement().getContracts().stream()
        .filter(c -> c.getName().toString().equalsIgnoreCase(name.toString()))
        .findFirst();
  }

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    String contextTenantId = TenantContext.getTenantId();
    if (contextTenantId != null && !contextTenantId.isEmpty()) {
      this.tenantId = contextTenantId;
    } else if (agreement != null
        && agreement.getTenantId() != null
        && !agreement.getTenantId().isEmpty()) {
      this.tenantId = agreement.getTenantId();
    } else {
      throw new IllegalStateException("Tenant ID must be set for Contract.");
    }
  }

  /**
   * Calculates the number of working days (excluding Saturdays and Sundays) from the current date
   * until the end date specified in the averageCalculation.
   *
   * @return the number of working days between today and the end date, or 0 if the
   *     averageCalculation or its end date is null, or if today is after the end date.
   */
  @JsonProperty("daysToPurchase")
  public long getDaysToPurchase() {
    if (averageCalculation == null || averageCalculation.getEndDate() == null) {
      return 0;
    }
    LocalDate today = LocalDate.now();
    LocalDate endDate = averageCalculation.getEndDate();
    if (today.isAfter(endDate)) {
      return 0;
    }
    long daysToPurchase = 0;
    for (LocalDate date = today; !date.isAfter(endDate); date = date.plusDays(1)) {
      DayOfWeek day = date.getDayOfWeek();
      if (day == DayOfWeek.SATURDAY || day == DayOfWeek.SUNDAY) {
        continue;
      }
      daysToPurchase++;
    }
    return daysToPurchase;
  }

  @Override
  public ContractId getId() {
    return id;
  }

  @JsonIgnore
  public boolean isMonthOrSpotContract() {
    return isSpotContract() || isMonthContract();
  }

  @JsonIgnore
  public boolean isSpotContract() {
    return List.of(ContractType.TGe24, ContractType.TGEgasDA, ContractType.TGEgasID)
        .contains(this.contractType);
  }

  @JsonIgnore
  public boolean isMonthContract() {
    return Objects.equals(ContractType.M, this.contractType);
  }

  @JsonIgnore
  public boolean isQuarterContract() {
    return Objects.equals(ContractType.Q, this.contractType);
  }

  @JsonIgnore
  public boolean isYearContract() {
    return Objects.equals(ContractType.Y, this.contractType);
  }

  @JsonIgnore
  public boolean isPropertyRightsContract() {
    return List.of(ContractType.PMOZE_A, ContractType.PMOZE_BIO, ContractType.PMEF_F)
        .contains(this.contractType);
  }

  @JsonIgnore
  public boolean isPropertyRightsYearContract() {
    return List.of(ContractType.PMOZE_A, ContractType.PMOZE_BIO, ContractType.PMEF_F)
        .contains(this.contractType);
  }

  @JsonIgnore
  public boolean isPropertyRightsMonthContract() {
    return List.of(ContractType.PMOZE_A, ContractType.PMOZE_BIO, ContractType.PMEF_F)
        .contains(this.contractType);
  }

  /**
   * Determines if a given contract is eligible based on its time unit and year.
   *
   * @param contract the contract to evaluate.
   * @return true if the contract is eligible, false otherwise.
   */
  public boolean purchasableAt(LocalDate at) {
    // if the contract is a PropertyRightContract, we allow it unconditionally.
    if (this instanceof PropertyRightContract) {
      return true;
    }
    return !averageCalculation.getEndDate().isBefore(at);
  }

  /**
   * Checks if a monthly contract is purchasable based on its year and month.
   *
   * @param contractYear the contract's year.
   * @param currentYear the current year.
   * @param timeUnit the contract's month as a {@link TimeUnit}.
   * @param currentMonth the current month (1-12).
   * @return {@code true} if the contract is purchasable; {@code false} otherwise.
   */
  public boolean isMonthPurchasable(
      int contractYear, int currentYear, TimeUnit timeUnit, int currentMonth) {
    return contractYear > currentYear
        || (contractYear == currentYear && timeUnit.asNumber() > currentMonth);
  }

  /**
   * Checks if a quarterly contract is purchasable based on its year and quarter.
   *
   * @param contractYear the contract's year.
   * @param currentYear the current year.
   * @param timeUnit the contract's quarter as a {@link TimeUnit}.
   * @param currentQuarter the current quarter (1-4).
   * @return {@code true} if the contract is purchasable; {@code false} otherwise.
   */
  public boolean isQuarterPurchasable(
      int contractYear, int currentYear, TimeUnit timeUnit, int currentQuarter) {
    return contractYear > currentYear
        || (contractYear == currentYear && timeUnit.asNumber() > currentQuarter);
  }

  /**
   * Checks if a yearly contract is purchasable based on its year.
   *
   * @param contractYear the contract's year.
   * @param currentYear the current year.
   * @return {@code true} if the contract is purchasable; {@code false} otherwise.
   */
  public boolean isYearPurchasable(int contractYear, int currentYear) {
    return contractYear >= currentYear + 1;
  }

  public void update(Contract newContract) {
    if (newContract == null) {
      return;
    }

    this.name = newContract.getName();
    this.averageCalculation = newContract.getAverageCalculation();
    this.quantity = newContract.getQuantity();
    this.volume = newContract.getVolume();
    this.actionIfNot100 = newContract.getActionIfNot100();
    this.orderTimes = newContract.getOrderTimes();
    this.rejectionReason = newContract.getRejectionReason();
    this.priceReference = newContract.getPriceReference();
    this.year = newContract.getYear();
    this.purchaseModel = newContract.getPurchaseModel();
    this.orderTypeParameters = newContract.getOrderTypeParameters();
    this.contractType = newContract.getContractType();
    this.updatedAt = LocalDateTime.now();
  }

  public Media getMedia() {
    return switch (this) {
      case EnergyContract ec -> Media.ENERGY;
      case GasContract gc -> Media.GAS;
      case PropertyRightContract prc -> Media.GREEN_PROPERTY_RIGHTS;
      default -> Media.ENERGY;
    };
  }
}
