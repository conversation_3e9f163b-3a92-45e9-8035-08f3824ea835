/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.customer;

import com.codemonkeys.wallet.common.framework.domain.Contact;
import com.codemonkeys.wallet.common.framework.domain.vo.CommunicationLanguage;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactConfiguration;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactId;
import com.codemonkeys.wallet.common.framework.domain.vo.Email;
import com.codemonkeys.wallet.common.framework.domain.vo.ModuleType;
import com.codemonkeys.wallet.common.framework.domain.vo.PersonName;
import com.codemonkeys.wallet.common.framework.domain.vo.PhoneNumber;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Where;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Table(name = "CONTACTS")
@EntityListeners(AuditingEntityListener.class)
@Data
@Where(clause = "type = 'CUSTOMER'")
public class CustomerContact extends Contact {

  @Transient private final boolean isApplicationUser = false;

  @NonNull
  @Setter
  @Enumerated(EnumType.STRING)
  @ColumnDefault("'PL'")
  protected CommunicationLanguage language = CommunicationLanguage.PL;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customer_id")
  @JsonIgnore
  private Customer customer;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    String contextTenantId = TenantContext.getTenantId();

    if (this.customer != null && this.customer.getTenantId() != null) {
      this.tenantId = this.customer.getTenantId();
    } else if (contextTenantId != null && !contextTenantId.isEmpty()) {
      this.tenantId = contextTenantId;
    } else {
      throw new IllegalStateException(
          "Tenant ID must be set based on the associated customer or the current context.");
    }
  }

  public CustomerContact(
      @NonNull ContactId id,
      @NonNull ModuleType type,
      @NonNull PersonName name,
      @NonNull Customer customer,
      PhoneNumber number,
      Email email,
      ContactConfiguration configuration,
      CommunicationLanguage language) {
    super(id, type, name, number, email, configuration);
    this.customer = customer;
    this.language = language;
  }

  public CustomerContact(
      @NonNull ContactId id,
      @NonNull ModuleType type,
      @NonNull PersonName name,
      @NonNull Customer customer,
      PhoneNumber number,
      Email email,
      ContactConfiguration configuration,
      LocalDateTime createdAt,
      LocalDateTime updatedAt,
      CommunicationLanguage language) {
    super(id, type, name, number, email, configuration);
    this.customer = customer;
    this.language = language;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  public void setCustomer(Customer customer) {
    this.customer = customer;
  }

  public void update(CustomerContact updated) {
    this.customer = updated.getCustomer();
    this.name = updated.getName();
    this.email = updated.getEmail();
    this.type = updated.getType();
    this.number = updated.getNumber();
    this.configuration = updated.getConfiguration();
    this.language = updated.getLanguage();
  }
}
