/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.agreement;

import com.codemonkeys.wallet.common.framework.shared.persistence.CommonRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementGroupName;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface AgreementRepository extends CommonRepository<Agreement, AgreementId> {
  @Query(
      value =
          "select count(*) from agreements where (start_date, end_date) overlaps"
              + " (:startDate, :endDate) and customer_id = :customerId and supplier_id ="
              + " :supplierId and media_type = :mediaType",
      nativeQuery = true)
  int countAgreementBy(
      @Param("customerId") UUID customerId,
      @Param("supplierId") UUID supplierId,
      @Param("mediaType") String mediaType,
      @Param("startDate") LocalDate startDate,
      @Param("endDate") LocalDate endDate);

  /**
   * Finds all agreements that belong to a specific agreement group by its name.
   *
   * @param groupName the name of the agreement group
   * @return a list of agreements that belong to the specified group
   */
  List<Agreement> findByAgreementGroupName(AgreementGroupName groupName);

  /**
   * Archives agrements whom end date is before today and their status is active.
   *
   * @param today LocalDate
   */
  @Query(
      value =
          "update Agreement set status ="
              + " com.codemonkeys.wallet.domain.agreement.vo.AgreementStatus.ARCHIVE"
              + " where endDate < :today and status ="
              + " com.codemonkeys.wallet.domain.agreement.vo.AgreementStatus.ACTIVE")
  @Modifying
  void archive(@Param("today") LocalDate today);

  /**
   * Finds an Agreement by its human-readable agreement ID.
   *
   * @param humanReadableAgreementId the human-readable agreement ID.
   * @return an Optional containing the Agreement if found.
   */
  @Query("SELECT a FROM Agreement a WHERE a.humanReadableAgreementId = :humanReadableAgreementId")
  Optional<Agreement> findByHumanReadableAgreementId(
      @Param("humanReadableAgreementId") Long humanReadableAgreementId);

  boolean existsByCustomerId(CustomerId customerId);
}
