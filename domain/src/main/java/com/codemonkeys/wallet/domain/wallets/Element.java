/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.ElementId;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.jmolecules.ddd.types.AggregateRoot;

@Getter
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "ELEMENTS")
public class Element extends IdentifiableAggregateRoot<ElementId>
    implements AggregateRoot<Element, ElementId> {

  @EmbeddedId private ElementId id;

  @JsonIgnoreProperties(
      value = {
        "tranches",
        "elements",
        "products",
        "prices",
        "agreement",
        "hibernateLazyInitializer",
        "handler",
        "results",
        "realisationDetails",
        "analyticalSettings"
      })
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "wallet_id", nullable = false)
  private Wallet wallet;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private ElementType type;

  @ColumnDefault("'ENERGY'")
  @Enumerated(EnumType.STRING)
  @Column(name = "media", nullable = false)
  private Media media;

  @Enumerated(EnumType.STRING)
  @Column(name = "time_unit", nullable = false)
  private TimeUnit timeUnit;

  @Column(name = "value", nullable = false)
  private BigDecimal value;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    this.tenantId =
        Optional.ofNullable(TenantContext.getTenantId())
            .filter(id -> !id.isEmpty())
            .orElseGet(
                () -> {
                  if (wallet == null
                      || wallet.getAgreement() == null
                      || wallet.getAgreement().getTenantId() == null) {
                    throw new IllegalStateException(
                        "Tenant ID must be set. Ensure Wallet and Agreement are properly configured.");
                  }
                  return wallet.getAgreement().getTenantId();
                });
  }

  public Element(@NonNull ElementId id) {
    this.id = id;
  }

  // public Element(Wallet wallet, ElementType type, TimeUnit timeUnit, BigDecimal value) {
  //  this.id = ElementId.randomId();
  //  this.wallet = wallet;
  //  this.type = type;
  //  this.timeUnit = timeUnit;
  //  this.value = value;
  //  this.createdAt = LocalDateTime.now();
  //  this.updatedAt = LocalDateTime.now();
  // }

  public Element(
      Wallet wallet, ElementType type, TimeUnit timeUnit, BigDecimal value, Media media) {
    this.id = ElementId.randomId();
    this.wallet = wallet;
    this.type = type;
    this.timeUnit = timeUnit;
    this.value = value;
    this.media = media;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
  }

  public Element(Element other) {
    this.id = ElementId.randomId();
    this.wallet = other.getWallet();
    this.type = other.getType();
    this.timeUnit = other.getTimeUnit();
    this.value = other.getValue();
    this.media = other.getMedia();
    this.createdAt = other.getCreatedAt();
    this.updatedAt = other.getUpdatedAt();
  }

  public Element(
      ElementId id,
      Wallet wallet,
      ElementType type,
      Media media,
      TimeUnit timeUnit,
      BigDecimal value) {
    this.id = id;
    this.wallet = wallet;
    this.type = type;
    this.media = media;
    this.timeUnit = timeUnit;
    this.value = value;
  }

  @Override
  public ElementId getId() {
    return id;
  }

  public Wallet getWallet() {
    return wallet;
  }

  public void setWallet(Wallet wallet) {
    this.wallet = wallet;
  }

  public void update(Element updated) {
    this.wallet = updated.getWallet();
    this.type = updated.getType();
    this.media = updated.getMedia();
    this.timeUnit = updated.getTimeUnit();
    this.value = updated.getValue();
  }
}
