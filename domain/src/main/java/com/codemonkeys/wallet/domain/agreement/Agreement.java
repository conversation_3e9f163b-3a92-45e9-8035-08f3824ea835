/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.agreement;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementStatus;
import com.codemonkeys.wallet.domain.agreement.vo.Volumes;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractComparator;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.contract.PropertyRightContract;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.GenerationTime;
import org.hibernate.annotations.SortComparator;
import org.hibernate.annotations.Type;
import org.jetbrains.annotations.NotNull;
import org.jmolecules.ddd.types.AggregateRoot;
import org.springframework.format.annotation.DateTimeFormat;

@Getter
@EqualsAndHashCode(
    callSuper = true,
    exclude = {"contracts"})
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "AGREEMENTS")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Slf4j
public class Agreement extends IdentifiableAggregateRoot<AgreementId>
    implements AggregateRoot<Agreement, AgreementId> {

  @EmbeddedId private AgreementId id;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "media_type", nullable = false)
  private MediaType mediaType;

  @Type(JsonType.class)
  @Column(name = "volumes", columnDefinition = "jsonb")
  private Volumes volumes;

  @SortComparator(ContractComparator.class)
  @OneToMany(
      cascade = {CascadeType.ALL, CascadeType.REMOVE},
      orphanRemoval = true,
      fetch = FetchType.LAZY,
      mappedBy = "agreement")
  @JsonIgnoreProperties("agreement")
  private SortedSet<Contract> contracts = new TreeSet<>(new ContractComparator());

  @Type(JsonType.class)
  @Column(name = "authorized_buyers", columnDefinition = "jsonb")
  private Set<String> authorizedBuyers = new HashSet<>();

  @ManyToOne(fetch = FetchType.EAGER, cascade = CascadeType.PERSIST)
  @JoinColumn(name = "customer_id", referencedColumnName = "id", nullable = false)
  private Customer customer;

  @ManyToOne(fetch = FetchType.EAGER, cascade = CascadeType.PERSIST)
  @JoinColumn(name = "supplier_id", referencedColumnName = "id", nullable = false)
  private Supplier supplier;

  @OneToMany(
      mappedBy = "agreement",
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY)
  @JsonIgnoreProperties("agreement")
  private Set<Wallet> wallets = new HashSet<>();

  @ManyToOne(fetch = FetchType.EAGER, cascade = CascadeType.PERSIST)
  @JoinColumn(name = "agreement_group_id", referencedColumnName = "id")
  private AgreementGroup agreementGroup;

  @Column(name = "start_date", nullable = false)
  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private LocalDate startDate;

  @Column(name = "end_date", nullable = false)
  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private LocalDate endDate;

  @Column(
      name = "average_calculation_start_date",
      nullable = false,
      columnDefinition = "DATE DEFAULT NOW()")
  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private LocalDate averageCalculationStartDate;

  @Enumerated(EnumType.STRING)
  private AgreementStatus status;

  @Type(JsonType.class)
  @Column(name = "media", columnDefinition = "jsonb")
  private CreateContractRequest.ContractParameters media;

  @Type(JsonType.class)
  @Column(name = "property_rights", columnDefinition = "jsonb")
  private CreateContractRequest.ContractParameters propertyRights;

  @Column(name = "description", nullable = true)
  private String description;

  @Column(name = "requires_customer_acceptance", nullable = false)
  private boolean requiresCustomerAcceptance;

  @Column(name = "send_recommendation", nullable = false)
  private boolean sendRecommendation;

  @Column(name = "human_readable_agreement_id", nullable = false, unique = true, updatable = false)
  @org.hibernate.annotations.Generated(GenerationTime.INSERT)
  private Long humanReadableAgreementId;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  public Agreement(
      @NonNull MediaType mediaType,
      LocalDate startDate,
      LocalDate endDate,
      LocalDate averageCalculationStartDate,
      String description,
      Volumes volumes,
      Set<? extends Contract> contracts,
      Set<String> authorizedBuyers,
      Supplier supplier,
      Customer customer,
      AgreementGroup agreementGroup,
      CreateContractRequest.ContractParameters media,
      CreateContractRequest.ContractParameters propertyRights,
      boolean requiresCustomerAcceptance,
      boolean sendRecommendation) {
    this.id = AgreementId.randomId();
    this.mediaType = mediaType;
    this.volumes = volumes;
    this.description = description;
    // todo: do sprawdzenia czy nadal potrzebne, wydaje mi sie, ze nie
    this.contracts =
        contracts.stream()
            .map(
                c ->
                    switch (c) {
                      case EnergyContract e ->
                          new EnergyContract(
                              e.getId() != null ? e.getId() : ContractId.randomId(),
                              e.getName(),
                              e.getAverageCalculation(),
                              e.getQuantity(),
                              e.getVolume(),
                              e.getActionIfNot100(),
                              e.getOrderTimes(),
                              e.getRejectionReason(),
                              e.getPriceReference(),
                              e.getYear(),
                              this,
                              e.getPurchaseModel(),
                              e.getOrderTypeParameters(),
                              e.getContractType());
                      case GasContract g ->
                          new GasContract(
                              g.getId() != null ? g.getId() : ContractId.randomId(),
                              g.getName(),
                              g.getAverageCalculation(),
                              g.getQuantity(),
                              g.getVolume(),
                              g.getActionIfNot100(),
                              g.getOrderTimes(),
                              g.getRejectionReason(),
                              g.getPriceReference(),
                              g.getYear(),
                              this,
                              g.getPurchaseModel(),
                              g.getOrderTypeParameters(),
                              g.getContractType());
                      case PropertyRightContract pr ->
                          new PropertyRightContract(
                              pr.getId() != null ? pr.getId() : ContractId.randomId(),
                              pr.getName(),
                              pr.getAverageCalculation(),
                              pr.getQuantity(),
                              pr.getVolume(),
                              pr.getActionIfNot100(),
                              pr.getOrderTimes(),
                              pr.getRejectionReason(),
                              pr.getPriceReference(),
                              pr.getYear(),
                              this,
                              pr.getPurchaseModel(),
                              pr.getOrderTypeParameters(),
                              pr.getContractType());
                      default -> throw new IllegalStateException("Unexpected value: " + c);
                    })
            .collect(Collectors.toCollection(() -> new TreeSet<>(new ContractComparator())));
    this.authorizedBuyers = authorizedBuyers;
    this.customer = customer;
    this.agreementGroup = agreementGroup;
    this.supplier = supplier;
    this.startDate = startDate;
    this.endDate = endDate;
    this.averageCalculationStartDate = averageCalculationStartDate;
    this.media = media;
    this.propertyRights = propertyRights;
    this.requiresCustomerAcceptance = requiresCustomerAcceptance;
    this.sendRecommendation = sendRecommendation;
    LocalDate now = LocalDate.now();
    this.status =
        getEndDate().getYear() < now.getYear() ? AgreementStatus.ARCHIVE : AgreementStatus.ACTIVE;
  }

  public Agreement(
      AgreementId agreementId,
      @NonNull MediaType mediaType,
      LocalDate startDate,
      LocalDate endDate,
      LocalDate averageCalculationStartDate,
      String description,
      Volumes volumes,
      Set<? extends Contract> contracts,
      Set<String> authorizedBuyers,
      Supplier supplier,
      Customer customer,
      AgreementGroup agreementGroup,
      CreateContractRequest.ContractParameters media,
      CreateContractRequest.ContractParameters propertyRights,
      boolean requiresCustomerAcceptance,
      boolean sendRecommendation) {
    this.id = agreementId;
    this.mediaType = mediaType;
    this.volumes = volumes;
    this.description = description;
    // todo: do sprawdzenia czy nadal potrzebne, wydaje mi sie, ze nie
    this.contracts =
        contracts.stream()
            .map(
                c ->
                    switch (c) {
                      case EnergyContract e ->
                          new EnergyContract(
                              e.getId() != null ? e.getId() : ContractId.randomId(),
                              e.getName(),
                              e.getAverageCalculation(),
                              e.getQuantity(),
                              e.getVolume(),
                              e.getActionIfNot100(),
                              e.getOrderTimes(),
                              e.getRejectionReason(),
                              e.getPriceReference(),
                              e.getYear(),
                              this,
                              e.getPurchaseModel(),
                              e.getOrderTypeParameters(),
                              e.getContractType());
                      case GasContract g ->
                          new GasContract(
                              g.getId() != null ? g.getId() : ContractId.randomId(),
                              g.getName(),
                              g.getAverageCalculation(),
                              g.getQuantity(),
                              g.getVolume(),
                              g.getActionIfNot100(),
                              g.getOrderTimes(),
                              g.getRejectionReason(),
                              g.getPriceReference(),
                              g.getYear(),
                              this,
                              g.getPurchaseModel(),
                              g.getOrderTypeParameters(),
                              g.getContractType());
                      case PropertyRightContract pr ->
                          new PropertyRightContract(
                              pr.getId() != null ? pr.getId() : ContractId.randomId(),
                              pr.getName(),
                              pr.getAverageCalculation(),
                              pr.getQuantity(),
                              pr.getVolume(),
                              pr.getActionIfNot100(),
                              pr.getOrderTimes(),
                              pr.getRejectionReason(),
                              pr.getPriceReference(),
                              pr.getYear(),
                              this,
                              pr.getPurchaseModel(),
                              pr.getOrderTypeParameters(),
                              pr.getContractType());
                      default -> throw new IllegalStateException("Unexpected value: " + c);
                    })
            .collect(Collectors.toCollection(() -> new TreeSet<>(new ContractComparator())));
    this.authorizedBuyers = authorizedBuyers;
    this.customer = customer;
    this.agreementGroup = agreementGroup;
    this.supplier = supplier;
    this.startDate = startDate;
    this.endDate = endDate;
    this.averageCalculationStartDate = averageCalculationStartDate;
    this.media = media;
    this.propertyRights = propertyRights;
    this.requiresCustomerAcceptance = requiresCustomerAcceptance;
    this.sendRecommendation = sendRecommendation;
    LocalDate now = LocalDate.now();
    this.status =
        getEndDate().getYear() < now.getYear() ? AgreementStatus.ARCHIVE : AgreementStatus.ACTIVE;
  }

  public Agreement(Agreement toCopy) {
    this(
        toCopy.getId(),
        toCopy.getMediaType(),
        toCopy.getStartDate(),
        toCopy.getEndDate(),
        toCopy.getAverageCalculationStartDate(),
        toCopy.getDescription(),
        toCopy.getVolumes(),
        toCopy.getContracts(),
        toCopy.getAuthorizedBuyers(),
        toCopy.getSupplier(),
        toCopy.getCustomer(),
        toCopy.getAgreementGroup(),
        toCopy.getMedia(),
        toCopy.getPropertyRights(),
        toCopy.isRequiresCustomerAcceptance(),
        toCopy.isSendRecommendation());
  }

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    String contextTenantId = TenantContext.getTenantId();
    if (contextTenantId != null && !contextTenantId.isEmpty()) {
      this.tenantId = contextTenantId;
    } else if (customer != null && customer.getId() != null) {
      this.tenantId = customer.getId().getId();
    } else {
      throw new IllegalStateException("Tenant ID must be set.");
    }
  }

  @Override
  public AgreementId getId() {
    return id;
  }

  public void setContracts(Set<Contract> contracts) {
    SortedSet<Contract> sorted = new TreeSet<>(new ContractComparator());
    sorted.addAll(contracts);
    sorted.forEach(c -> c.setAgreement(this));
    this.contracts = sorted;
  }

  public void setWallets(Set<Wallet> wallets) {
    this.wallets = wallets;
    for (Wallet wallet : wallets) {
      wallet.setAgreement(this);
    }
  }

  public void update(Agreement data) {
    if (data == null) {
      return;
    }
    this.description = data.getDescription();
    this.startDate = data.getStartDate();
    this.endDate = data.getEndDate();
    this.averageCalculationStartDate = data.getAverageCalculationStartDate();
    this.agreementGroup = data.getAgreementGroup();
    this.volumes = data.getVolumes();
    this.supplier = data.getSupplier();
    this.authorizedBuyers = data.getAuthorizedBuyers();
    this.requiresCustomerAcceptance = data.isRequiresCustomerAcceptance();
    this.sendRecommendation = data.isSendRecommendation();
    Map<ContractId, Contract> contractMap =
        contracts.stream().collect(Collectors.toMap(Contract::getId, Function.identity()));
    data.getContracts()
        .forEach(
            newContract -> {
              Contract existingContract = contractMap.get(newContract.getId());
              if (existingContract != null) {
                existingContract.update(newContract);
              } else {
                newContract.setAgreement(this);
                contracts.add(newContract);
              }
            });
    Set<Contract> toDelete =
        Agreement.getContractDifference(
            new HashSet<>(contracts), new HashSet<>(data.getContracts()));
    toDelete.forEach(contracts::remove);
    log.info("{}", contracts);
  }

  public static Set<Contract> getContractDifference(
      Set<Contract> oldContractIds, Set<Contract> newContractIds) {
    Set<String> firstSet =
        newContractIds.stream().map((Contract c) -> c.getId().getId()).collect(Collectors.toSet());
    Set<Contract> difference =
        oldContractIds.stream()
            .filter(contract -> !firstSet.contains(contract.getId().getId()))
            .collect(Collectors.toSet());
    return difference;
  }

  public boolean hasContractOfType(Class<?> contractType) {
    return contracts.stream().anyMatch(contractType::isInstance);
  }

  @JsonIgnore
  public String getLabel() {
    return String.join(" - ",
        getCustomer().getName().getName(),
        getStartDate().toString(),
        getMediaType().name(),
        getSupplier().getName().getName());
  }
}
