/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets.enums;

/**
 * Enumeration of different types of Green Property Right calculations. Each type corresponds to a
 * specific calculation strategy implementation.
 */
public enum GreenPropertyRightCalculationType {
  /** Calculation type for ENEA energy provider */
  ENEA,

  /** Calculation type for EON energy provider */
  EON,

  /** Complex calculation type for ENERGA energy provider */
  ENERGA_COMPLEX,

  /** Simple calculation type for ENERGA energy provider */
  ENERGA_SIMPLE,

  /** Calculation type for PGE energy provider */
  PGE,

  /** Calculation type for TIEW energy provider */
  TIEW,

  /** Default calculation type for other providers not specifically handled */
  OTHER
}
