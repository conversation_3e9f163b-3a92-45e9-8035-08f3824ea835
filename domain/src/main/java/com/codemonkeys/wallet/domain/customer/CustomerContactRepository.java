/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.customer;

import com.codemonkeys.wallet.common.framework.domain.vo.ContactId;
import com.codemonkeys.wallet.common.framework.domain.vo.Email;
import com.codemonkeys.wallet.common.framework.domain.vo.PhoneNumber;
import com.codemonkeys.wallet.common.framework.shared.persistence.CommonRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerContactRepository extends CommonRepository<CustomerContact, ContactId> {
  boolean existsByNumber(PhoneNumber phoneNumber);

  boolean existsByEmail(Email email);

  boolean existsByNumberAndIdIsNot(PhoneNumber phoneNumber, ContactId id);

  boolean existsByCustomerIdAndEmailAndIdIsNot(CustomerId customerId, Email email, ContactId id);

  boolean existsByCustomerIdAndEmail(CustomerId customerId, Email email);

  @Query(
      "SELECT c FROM CustomerContact c WHERE c.customer.id = :customerId "
          + "AND c.configuration.tranches = true AND c.configuration.email = true")
  List<CustomerContact> findByCustomerIdAndTranchesAndEmailEnabled(
      @Param("customerId") CustomerId customerId);

  @Query(
      value =
          """
  SELECT DISTINCT ON (email) *
  FROM contacts
  WHERE type = 'CUSTOMER'
    AND newsletter = true
    AND configuration_email = true
    AND email IS NOT NULL
    AND email <> ''
  ORDER BY email, updated_at DESC
  """,
      nativeQuery = true)
  List<CustomerContact> findAllDistinctNewsletterContacts();

  @Query(
      value =
          """
  SELECT DISTINCT ON (email) *
  FROM contacts
  WHERE type = 'CUSTOMER'
    AND prices = true
    AND configuration_email = true
    AND email IS NOT NULL
    AND email <> ''
  ORDER BY email, updated_at DESC
  """,
      nativeQuery = true)
  List<CustomerContact> findAllDistinctPriceContacts();
}
