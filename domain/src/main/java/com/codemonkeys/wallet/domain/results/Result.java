/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.results;

import com.codemonkeys.wallet.common.framework.shared.persistence.IdentifiableAggregateRoot;
import com.codemonkeys.wallet.common.framework.shared.persistence.TenantContext;
import com.codemonkeys.wallet.domain.results.vo.ResultId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.jmolecules.ddd.types.AggregateRoot;

@Getter
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "wallet_results")
public class Result extends IdentifiableAggregateRoot<ResultId>
    implements AggregateRoot<Result, ResultId> {

  @EmbeddedId private ResultId id;

  @ColumnDefault("'ENERGY'")
  @Enumerated(EnumType.STRING)
  @Column(name = "media", nullable = false)
  private Media mediaType;

  @Column(name = "value", nullable = false)
  private BigDecimal value;

  @Enumerated(EnumType.STRING)
  @Column(name = "time_unit", nullable = false)
  private TimeUnit timeUnit;

  @Column(name = "tenant_id", updatable = false)
  private String tenantId;

  // @JsonIgnoreProperties(value = {"tranches", "elements", "products", "prices", "agreement"})
  @JsonIgnore
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "wallet_id", nullable = false)
  private Wallet wallet;

  public Result(@NonNull ResultId id) {
    this.id = id;
  }

  public Result(Media mediaType, BigDecimal value, TimeUnit timeUnit, Wallet wallet) {
    this.id = ResultId.randomId();
    this.mediaType = mediaType;
    this.value = value;
    this.timeUnit = timeUnit;
    this.createdAt = LocalDateTime.now();
    this.updatedAt = LocalDateTime.now();
    this.wallet = wallet;
  }

  public Result(Result other) {
    this.id = ResultId.randomId();
    this.mediaType = other.getMediaType();
    this.value = other.getValue();
    this.timeUnit = other.getTimeUnit();
    this.createdAt = other.getCreatedAt();
    this.updatedAt = other.getUpdatedAt();
  }

  public Result(ResultId id, Media mediaType, BigDecimal value, TimeUnit timeUnit) {
    this.id = id;
    this.mediaType = mediaType;
    this.value = value;
    this.timeUnit = timeUnit;
  }

  @Override
  public ResultId getId() {
    return id;
  }

  public void update(Result updated) {
    this.mediaType = updated.getMediaType();
    this.value = updated.getValue();
    this.timeUnit = updated.getTimeUnit();
    this.updatedAt = LocalDateTime.now();
  }

  @PrePersist
  @PreUpdate
  public void setTenantIdAutomatically() {
    if (wallet != null) {
      this.tenantId =
          Optional.ofNullable(TenantContext.getTenantId())
              .filter(id -> !id.isEmpty())
              .orElseGet(
                  () ->
                      Optional.ofNullable(wallet.getTenantId())
                          .orElseThrow(() -> new IllegalStateException("Tenant ID must be set.")));
    } else {
      throw new IllegalStateException("Wallet must be set in Result.");
    }
  }
}
