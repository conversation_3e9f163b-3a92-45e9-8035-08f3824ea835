/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.common.framework.shared.persistence.CommonRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface WalletRepository extends CommonRepository<Wallet, WalletId> {

  List<Wallet> findByAgreement_Id(AgreementId agreementId);

  @Query("SELECT w.id FROM Wallet w WHERE w.agreement.id = :agreementId")
  List<WalletId> findWalletIds(AgreementId agreementId);

  boolean existsByAgreementCustomerId(CustomerId customerId);

  @Query(
      """
         select  w
         from    Wallet w
         join fetch w.agreement a
         join fetch a.contracts
         where   w.id = :id
         """)
  Optional<Wallet> findWithContracts(@Param("id") WalletId id);

  @Query(
      """
    SELECT DISTINCT w FROM Wallet w
    LEFT JOIN FETCH w.prices
    LEFT JOIN FETCH w.products
    LEFT JOIN FETCH w.elements
    LEFT JOIN FETCH w.tranches
    LEFT JOIN FETCH w.results
    LEFT JOIN FETCH w.agreement a
    LEFT JOIN FETCH a.customer c
    LEFT JOIN FETCH a.supplier s
""")
  List<Wallet> findAllWithEverythingLoaded();
}
