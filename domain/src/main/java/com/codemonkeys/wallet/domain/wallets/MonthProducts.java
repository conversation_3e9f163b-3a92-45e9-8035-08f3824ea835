/* (C)2024-2025 */
package com.codemonkeys.wallet.domain.wallets;

import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.ProductType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

@Data
@AllArgsConstructor
public class MonthProducts {
  @JsonValue Map<TimeUnit, List<Product>> data;

  public MonthProducts(List<Product> products) {
    data = prepare(products);
  }

  private Map<TimeUnit, List<Product>> prepare(List<Product> products) {
    Map<TimeUnit, List<Product>> result = new HashMap<>();
    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      List<Product> productsToAdd =
          products.stream().filter(t -> Objects.equals(t.getTimeUnit(), month)).toList();
      result.put(month, productsToAdd);
    }
    return result;
  }

  public void add(TimeUnit timeUnit, List<Product> productsToAdd) {
    data.computeIfPresent(timeUnit, (key, value) -> productsToAdd);
  }

  public List<Product> get(TimeUnit unit) {
    return data.getOrDefault(unit, Lists.newArrayList());
  }

  public List<Product> get(TimeUnit unit, Media media) {
    return data.getOrDefault(unit, Lists.newArrayList()).stream()
        .filter(p -> Objects.equals(p.getMedia(), media))
        .toList();
  }

  public Optional<Product> get(TimeUnit unit, Media media, ProductType productType) {
    return data.getOrDefault(unit, Lists.newArrayList()).stream()
        .filter(
            p -> Objects.equals(p.getMedia(), media) && Objects.equals(p.getType(), productType))
        .findFirst();
  }

  /**
   * Checks if all products for the given time unit are confirmed.
   *
   * @param unit The time unit to check.
   * @return true if all products for the given time unit are confirmed, false otherwise.
   */
  public boolean isConfirmed(TimeUnit unit, Media media) {
    List<Product> list =
        data.getOrDefault(unit, Lists.newArrayList()).stream()
            .filter(p -> p.getMedia().equals(media))
            .toList();
    return !list.isEmpty() && list.stream().allMatch(Product::isConfirmed);
  }
}
