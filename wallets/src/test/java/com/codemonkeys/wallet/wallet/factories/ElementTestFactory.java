/* (C)2025 */
package com.codemonkeys.wallet.wallet.factories;

import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.util.List;

public class ElementTestFactory {
  public static class Valid {

    public static Element createEnergyBluePropertyRightsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(
          null, ElementType.BLUE_PROPERTY_RIGHTS, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createBluePropertyRightsBluePropertyRightsElement() {
      return new Element(
          null,
          ElementType.BLUE_PROPERTY_RIGHTS,
          TimeUnit.Y,
          BigDecimal.TEN,
          Media.BLUE_PROPERTY_RIGHTS);
    }

    public static Element createEnergyCorrectionFactorPriceElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(
          null, ElementType.CORRECTION_FACTOR, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createGasCorrectionFactorPriceElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.CORRECTION_FACTOR, timeUnit, BigDecimal.TEN, Media.GAS);
    }

    public static Element createEnergyHiddenCorrectionFactorPriceElementWithTimeUnit(
        TimeUnit timeUnit) {
      return new Element(
          null, ElementType.HIDDEN_CORRECTION_FACTOR, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createGasHiddenCorrectionFactorPriceElementWithTimeUnit(
        TimeUnit timeUnit) {
      return new Element(
          null, ElementType.HIDDEN_CORRECTION_FACTOR, timeUnit, BigDecimal.TEN, Media.GAS);
    }

    public static Element createEnergyCostsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.COSTS, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createGasCostsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.COSTS, timeUnit, BigDecimal.TEN, Media.GAS);
    }

    public static Element createGreenPropertyRightsCostsElement() {
      return new Element(
          null, ElementType.COSTS, TimeUnit.Y, BigDecimal.TEN, Media.GREEN_PROPERTY_RIGHTS);
    }

    public static Element createGreenPropertyRightsGreenPropertyRightsCostsElement() {
      return new Element(
          null,
          ElementType.GREEN_PROPERTY_RIGHTS_COSTS,
          TimeUnit.Y,
          BigDecimal.ONE,
          Media.GREEN_PROPERTY_RIGHTS);
    }

    public static Element createEnergyExciseElement() {
      return new Element(null, ElementType.EXCISE, TimeUnit.Y, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createEnergyExciseElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.EXCISE, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createGasExciseElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.EXCISE, timeUnit, BigDecimal.TEN, Media.GAS);
    }

    public static Element createGreenPropertyRightsExciseElement() {
      return new Element(
          null, ElementType.EXCISE, TimeUnit.Y, BigDecimal.TEN, Media.GREEN_PROPERTY_RIGHTS);
    }

    public static Element createEnergyGreenPropertyRightsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(
          null, ElementType.GREEN_PROPERTY_RIGHTS, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createEnergyIndustrialStatusDiscountElement() {
      return new Element(
          null, ElementType.INDUSTRIAL_STATUS_DISCOUNT, TimeUnit.Y, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createEnergyProfileElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.PROFILE, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createGasProfileElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.PROFILE, timeUnit, BigDecimal.TEN, Media.GAS);
    }

    public static Element createEnergyProfilePercentElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.PROFILE_PERCENT, timeUnit, BigDecimal.ONE, Media.ENERGY);
    }

    public static Element createGasProfilePercentElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(null, ElementType.PROFILE_PERCENT, timeUnit, BigDecimal.ONE, Media.GAS);
    }

    public static List<Element> createEnergyTotalElementPrepareListWithTimeUnit(TimeUnit timeUnit) {
      Element bluePropertyRightsElement =
          ElementTestFactory.Valid.createEnergyBluePropertyRightsElementWithTimeUnit(timeUnit);
      Element correctionFactorPriceElement =
          ElementTestFactory.Valid.createEnergyCorrectionFactorPriceElementWithTimeUnit(timeUnit);
      Element costsElement =
          ElementTestFactory.Valid.createEnergyCostsElementWithTimeUnit(timeUnit);
      Element exciseElement =
          ElementTestFactory.Valid.createEnergyExciseElementWithTimeUnit(timeUnit);
      Element profileElement =
          ElementTestFactory.Valid.createEnergyProfileElementWithTimeUnit(timeUnit);
      Element totalPropertyRightsElement =
          ElementTestFactory.Valid.createEnergyTotalPropertyRightsElementWithTimeUnit(timeUnit);
      Element whitePropertyRightsElement =
          ElementTestFactory.Valid.createEnergyWhitePropertyRightsElementWithTimeUnit(timeUnit);

      return List.of(
          bluePropertyRightsElement,
          correctionFactorPriceElement,
          costsElement,
          exciseElement,
          profileElement,
          totalPropertyRightsElement,
          whitePropertyRightsElement);
    }

    public static List<Element> createGasTotalElementPrepareListWithTimeUnit(TimeUnit timeUnit) {
      Element bluePropertyRightsElement =
          ElementTestFactory.Valid.createEnergyBluePropertyRightsElementWithTimeUnit(timeUnit);
      Element gasCorrectionFactorPriceElement =
          ElementTestFactory.Valid.createGasCorrectionFactorPriceElementWithTimeUnit(timeUnit);
      Element gasCostsElement =
          ElementTestFactory.Valid.createGasCostsElementWithTimeUnit(timeUnit);
      Element gasExciseElement =
          ElementTestFactory.Valid.createGasExciseElementWithTimeUnit(timeUnit);
      Element gasProfileElement =
          ElementTestFactory.Valid.createGasProfileElementWithTimeUnit(timeUnit);
      Element gasTotalPropertyRightsElement =
          ElementTestFactory.Valid.createGasTotalPropertyRightsElementWithTimeUnit(timeUnit);
      Element gasWhitePropertyRightsElement =
          ElementTestFactory.Valid.createGasWhitePropertyRightsElementWithTimeUnit(timeUnit);

      return List.of(
          bluePropertyRightsElement,
          gasCorrectionFactorPriceElement,
          gasCostsElement,
          gasExciseElement,
          gasProfileElement,
          gasTotalPropertyRightsElement,
          gasWhitePropertyRightsElement);
    }

    public static Element createEnergyTotalPropertyRightsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(
          null, ElementType.TOTAL_PROPERTY_RIGHTS, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createGasTotalPropertyRightsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(
          null, ElementType.TOTAL_PROPERTY_RIGHTS, timeUnit, BigDecimal.TEN, Media.GAS);
    }

    public static Element createEnergyWhitePropertyRightsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(
          null, ElementType.WHITE_PROPERTY_RIGHTS, timeUnit, BigDecimal.TEN, Media.ENERGY);
    }

    public static Element createGasWhitePropertyRightsElementWithTimeUnit(TimeUnit timeUnit) {
      return new Element(
          null, ElementType.WHITE_PROPERTY_RIGHTS, timeUnit, BigDecimal.TEN, Media.GAS);
    }

    public static Element createEnergySupplierDiscountPriceElement() {
      return new Element(
          null, ElementType.SUPPLIER_DISCOUNT_PRICE, TimeUnit.Q1, BigDecimal.ONE, Media.ENERGY);
    }

    public static Element createEnergySupplierServicePriceElement() {
      return new Element(
          null, ElementType.SUPPLIER_SERVICE_PRICE, TimeUnit.Q1, BigDecimal.ONE, Media.ENERGY);
    }

    public static Element createEnergyCJElement() {
      return new Element(null, ElementType.CJ, TimeUnit.Y, BigDecimal.ONE, Media.ENERGY);
    }

    public static Element createEnergyLastCJElement() {
      return new Element(null, ElementType.LAST_CJ, TimeUnit.Y, BigDecimal.ONE, Media.ENERGY);
    }

    public static Element createEnergyWFElement() {
      return new Element(null, ElementType.WF, TimeUnit.Y, BigDecimal.ONE, Media.ENERGY);
    }
  }
}
