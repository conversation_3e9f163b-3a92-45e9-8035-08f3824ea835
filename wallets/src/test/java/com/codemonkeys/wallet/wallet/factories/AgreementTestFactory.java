/* (C)2025 */
package com.codemonkeys.wallet.wallet.factories;

import static com.codemonkeys.wallet.wallet.factories.ContractTestFactory.createContractParameters;
import static java.util.Map.entry;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.enums.PurchaseModel;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.agreement.vo.Volumes;
import com.codemonkeys.wallet.domain.contract.create.CreateContractRequest;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class AgreementTestFactory {

  static final LocalDate START_DATE = LocalDate.of(2025, 1, 22);
  static final LocalDate END_DATE = LocalDate.of(2025, 12, 31);

  public static class Valid {
    public static Agreement createEnergyAgreement() {
      Map<String, Double> monthlyVolumes = createMonthlyVolumes();

      Supplier supplier = SupplierTestFactory.Valid.createSupplier();
      Customer customer = CustomerTestFactory.Valid.createCustomer();

      CreateContractRequest.ContractParameters contractParametersMedia = createContractParameters();

      CreateContractRequest.ContractParameters contractParametersPropertyRights =
          createContractParameters();

      return new Agreement(
          AgreementId.randomId(),
          MediaType.ENERGY,
          START_DATE,
          END_DATE,
          START_DATE,
          null,
          PurchaseModel.PERCENTAGE,
          Volumes.of(monthlyVolumes),
          new HashSet<>(),
          Set.of("Klient"),
          supplier,
          customer,
          null,
          contractParametersMedia,
          contractParametersPropertyRights,
          false,
          false);
    }

    public static Agreement createGasAgreement() {
      Map<String, Double> monthlyVolumes = createMonthlyVolumes();

      Supplier supplier = SupplierTestFactory.Valid.createSupplier();
      Customer customer = CustomerTestFactory.Valid.createCustomer();

      CreateContractRequest.ContractParameters contractParametersMedia = createContractParameters();

      CreateContractRequest.ContractParameters contractParametersPropertyRights =
          createContractParameters();

      return new Agreement(
          AgreementId.randomId(),
          MediaType.GAS,
          START_DATE,
          END_DATE,
          START_DATE,
          null,
          PurchaseModel.PERCENTAGE,
          Volumes.of(monthlyVolumes),
          new HashSet<>(),
          Stream.of("Klient").collect(Collectors.toSet()),
          supplier,
          customer,
          null,
          contractParametersMedia,
          contractParametersPropertyRights,
          false,
          false);
    }
  }

  private static Map<String, Double> createMonthlyVolumes() {
    return Map.ofEntries(
        entry("M1", 1.0),
        entry("M2", 1.0),
        entry("M3", 1.0),
        entry("M4", 1.0),
        entry("M5", 1.0),
        entry("M6", 1.0),
        entry("M7", 1.0),
        entry("M8", 1.0),
        entry("M9", 1.0),
        entry("M10", 1.0),
        entry("M11", 1.0),
        entry("M12", 1.0));
  }
}
