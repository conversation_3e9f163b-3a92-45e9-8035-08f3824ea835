/* (C)2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights;

import static com.codemonkeys.wallet.computation.application.pipeline.handlers.helper.PriceTestHelper.assertPrice;
import static com.codemonkeys.wallet.computation.application.pipeline.handlers.helper.PriceTestHelper.assertPriceMonths;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;

import an.awesome.pipelinr.Pipeline;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.CalculationStrategyFactory;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.enea.EneaCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa.EnergaSimpleCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.eon.EONCalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.pge.PGECalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.tiew.TIEWCalculationStrategy;
import com.codemonkeys.wallet.domain.contract.PropertyRightContract;
import com.codemonkeys.wallet.domain.wallets.*;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.wallet.factories.*;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.Test;

class GreenPropertyRightsTrancheHandlerTest {

  Pipeline pipeline = PipelineTestFactory.createGreenPropertyRightsTranche();

  /** Test handles a price calculation for enea calculation strategy in a year condition */
  @Test
  void shouldCalculateEneaYearPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEneaWallet();

    EneaCalculationStrategy strategy = new EneaCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Y);
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement, greenPropertyRightsCostsElement, industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for enea calculation strategy in a quarter condition */
  @Test
  void shouldCalculateEneaQuarterPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEneaWallet();

    EneaCalculationStrategy strategy = new EneaCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Q1);
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement, greenPropertyRightsCostsElement, industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for enea calculation strategy in a month condition */
  @Test
  void shouldCalculateEneaMonthPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEneaWallet();

    EneaCalculationStrategy strategy = new EneaCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.M1);
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement, greenPropertyRightsCostsElement, industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for energa simple calculation strategy in a year condition */
  @Test
  void shouldCalculateEnergaSimpleYearPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEnergaSimpleWallet();

    EnergaSimpleCalculationStrategy strategy = new EnergaSimpleCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Y);
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement, greenPropertyRightsCostsElement, industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /**
   * Test handles a price calculation for energa simple calculation strategy in a quarter condition
   */
  @Test
  void shouldCalculateEnergaSimpleQuarterPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEnergaSimpleWallet();

    EnergaSimpleCalculationStrategy strategy = new EnergaSimpleCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Q1);
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement, greenPropertyRightsCostsElement, industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /**
   * Test handles a price calculation for energa simple calculation strategy in a month condition
   */
  @Test
  void shouldCalculateEnergaSimpleMonthPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEnergaSimpleWallet();

    EnergaSimpleCalculationStrategy strategy = new EnergaSimpleCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.M1);
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement, greenPropertyRightsCostsElement, industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for EON calculation strategy in a year condition */
  @Test
  void shouldCalculateEONYearPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEONWallet();

    EONCalculationStrategy strategy = new EONCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Y);
    Element supplierDiscountPriceElement =
        ElementTestFactory.Valid.createEnergySupplierDiscountPriceElement();
    Element supplierServicePriceElement =
        ElementTestFactory.Valid.createEnergySupplierServicePriceElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            supplierDiscountPriceElement,
            supplierServicePriceElement,
            industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for EON calculation strategy in a quarter condition */
  @Test
  void shouldCalculateEONQuarterPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEONWallet();

    EONCalculationStrategy strategy = new EONCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Q1);
    Element supplierDiscountPriceElement =
        ElementTestFactory.Valid.createEnergySupplierDiscountPriceElement();
    Element supplierServicePriceElement =
        ElementTestFactory.Valid.createEnergySupplierServicePriceElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            supplierDiscountPriceElement,
            supplierServicePriceElement,
            industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for EON calculation strategy in a month condition */
  @Test
  void shouldCalculateEONMonthPrice() {
    Wallet wallet = WalletTestFactory.Valid.createEONWallet();

    EONCalculationStrategy strategy = new EONCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.M1);
    Element supplierDiscountPriceElement =
        ElementTestFactory.Valid.createEnergySupplierDiscountPriceElement();
    Element supplierServicePriceElement =
        ElementTestFactory.Valid.createEnergySupplierServicePriceElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            supplierDiscountPriceElement,
            supplierServicePriceElement,
            industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for PGE calculation strategy in a year condition */
  @Test
  void shouldCalculatePGEYearPrice() {
    Wallet wallet = WalletTestFactory.Valid.createPGEWallet();

    PGECalculationStrategy strategy = new PGECalculationStrategy();

    Element cjElement = ElementTestFactory.Valid.createEnergyCJElement();
    Element lastCJElement = ElementTestFactory.Valid.createEnergyLastCJElement();
    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Y);
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addElements(List.of(cjElement, lastCJElement, industrialStatusDiscountElement));
    wallet.addTranche(tranche);

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for PGE calculation strategy in a quarter condition */
  @Test
  void shouldCalculatePGEQuarterPrice() {
    Wallet wallet = WalletTestFactory.Valid.createPGEWallet();

    PGECalculationStrategy strategy = new PGECalculationStrategy();

    Element cjElement = ElementTestFactory.Valid.createEnergyCJElement();
    Element lastCJElement = ElementTestFactory.Valid.createEnergyLastCJElement();
    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Q1);
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addElements(List.of(cjElement, lastCJElement, industrialStatusDiscountElement));
    wallet.addTranche(tranche);

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for PGE calculation strategy in a month condition */
  @Test
  void shouldCalculatePGEMonthPrice() {
    Wallet wallet = WalletTestFactory.Valid.createPGEWallet();

    PGECalculationStrategy strategy = new PGECalculationStrategy();

    Element cjElement = ElementTestFactory.Valid.createEnergyCJElement();
    Element lastCJElement = ElementTestFactory.Valid.createEnergyLastCJElement();
    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.M1);
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addElements(List.of(cjElement, lastCJElement, industrialStatusDiscountElement));
    wallet.addTranche(tranche);

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for TIEW calculation strategy in a year condition */
  @Test
  void shouldCalculateTIEWYearPrice() {
    Wallet wallet = WalletTestFactory.Valid.createTIEWWallet();

    TIEWCalculationStrategy strategy = new TIEWCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Y);
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element energyWFElement = ElementTestFactory.Valid.createEnergyWFElement();
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement,
            energyWFElement,
            greenPropertyRightsCostsElement,
            industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for TIEW calculation strategy in a quarter condition */
  @Test
  void shouldCalculateTIEWQuarterPrice() {
    Wallet wallet = WalletTestFactory.Valid.createTIEWWallet();

    TIEWCalculationStrategy strategy = new TIEWCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.Q1);
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element energyWFElement = ElementTestFactory.Valid.createEnergyWFElement();
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement,
            energyWFElement,
            greenPropertyRightsCostsElement,
            industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles a price calculation for TIEW calculation strategy in a month condition */
  @Test
  void shouldCalculateTIEWMonthPrice() {
    Wallet wallet = WalletTestFactory.Valid.createTIEWWallet();

    TIEWCalculationStrategy strategy = new TIEWCalculationStrategy();

    Tranche tranche = TrancheTestFactory.Valid.createPropertyRightTrancheWithTimeUnit(TimeUnit.M1);
    Element energyExciseElement = ElementTestFactory.Valid.createEnergyExciseElement();
    Element energyWFElement = ElementTestFactory.Valid.createEnergyWFElement();
    Element greenPropertyRightsCostsElement =
        ElementTestFactory.Valid.createGreenPropertyRightsGreenPropertyRightsCostsElement();
    Element industrialStatusDiscountElement =
        ElementTestFactory.Valid.createEnergyIndustrialStatusDiscountElement();

    wallet.addTranche(tranche);
    wallet.addElements(
        List.of(
            energyExciseElement,
            energyWFElement,
            greenPropertyRightsCostsElement,
            industrialStatusDiscountElement));

    CommandTestFactory.createGreenPropertyRightsTrancheCommandList(wallet)
        .forEach(command -> command.execute(pipeline));

    MonthTranches monthlyTranches = wallet.getMonthTranches(PropertyRightContract.class);

    assertAll(
        wallet.getPrices().stream()
            .flatMap(
                price ->
                    assertPrice(
                        wallet,
                        price,
                        ElementType.GREEN_PROPERTY_RIGHTS,
                        strategy
                            .price(monthlyTranches.get(price.getTimeUnit()))
                            .orElse(BigDecimal.ZERO))));

    assertPriceMonths(wallet.getPrices(), tranche.getTimeUnit());
  }

  /** Test handles green property rights tranche handler associated elements return list */
  @Test
  void shouldReturnAssociatedElements() {
    GreenPropertyRightsTrancheHandler handler =
        new GreenPropertyRightsTrancheHandler(new CalculationStrategyFactory());
    assertThat(handler.getAssociatedElements()).isEmpty();
  }
}
