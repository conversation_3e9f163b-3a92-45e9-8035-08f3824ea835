/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.common;

import com.codemonkeys.wallet.application.LogEvent;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class WalletRecalcOnPrice {

  private final WalletRepository walletRepository;
  private final RecalculationService recalculationService;

  @EventListener(condition = "#event.eventType == T(EventType).PRICES_RECALCULATED")
  public void handle(LogEvent event) {
    log.info("Triggering recalculation of all wallets after prices scraped.");

    walletRepository
        .findAll()
        .forEach(
            wallet -> {
              try {
                recalculationService.recalculate(wallet);
              } catch (Exception e) {
                log.error("Failed to recalculate wallet with ID: {}", wallet.getId(), e);
              }
            });

    log.info("Recalculation completed.");
  }
}
