/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.common;

import com.codemonkeys.wallet.application.LogEvent;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class WalletRecalcOnPrice {

  private final WalletRepository walletRepository;
  private final RecalculationService recalculationService;

  /**
   * Listens for {@code PRICES_RECALCULATED} events and triggers recalculation for all wallets with
   * fully loaded data.
   *
   * @param event the event indicating that price data has been updated
   */
  @EventListener(
      condition =
          "#event.eventType == T(com.codemonkeys.wallet.domain.wallets.vo.EventType).PRICES_RECALCULATED")
  public void handle(LogEvent event) {
    log.info("Triggering recalculation of all wallets after prices scraped.");

    walletRepository.findAllWithEverythingLoaded().forEach(this::safelyRecalculate);

    log.info("Recalculation completed.");
  }

  /**
   * Safely performs recalculation for a given wallet. Skips wallets with invalid or null price
   * entries to avoid processing errors.
   *
   * @param wallet the wallet to recalculate
   */
  private void safelyRecalculate(Wallet wallet) {
    try {
      if (wallet.getPrices() == null
          || wallet.getPrices().stream().anyMatch(p -> p == null || p.getId() == null)) {
        log.warn("Skipping wallet {} due to invalid WalletPrice entries.", wallet.getId());
        return;
      }

      recalculationService.recalculate(wallet);

    } catch (Exception e) {
      log.error("Failed to recalculate wallet with ID: {}", wallet.getId(), e);
    }
  }
}
