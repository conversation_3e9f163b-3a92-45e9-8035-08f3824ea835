/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.annualpurchaseoverview;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListMapper;
import com.codemonkeys.wallet.domain.wallets.AnnualPurchaseOverview;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * Service mapper that handles the conversion between Wallet entities and their data transfer
 * objects, with specific functionality for creating annual purchase overviews. This mapper
 * implements {@link ProjectionListMapper} with the same entity type for both source and target, as
 * it primarily serves as a pass-through for wallet entities while providing additional mapping
 * capabilities for annual purchase overviews.
 *
 * @see ProjectionListMapper
 * @see Wallet
 * @see WalletId
 * @see AnnualPurchaseOverview
 */
@Service
public class AnnualPurchaseOverviewMapper
    implements ProjectionListMapper<Wallet, WalletId, Wallet> {
  /**
   * Converts a Wallet entity to its DTO representation. In this implementation, the entity is
   * returned as-is since the source and target types are the same.
   *
   * @param entity the wallet entity to convert
   * @return the same wallet entity
   */
  @Override
  public Wallet toDto(Wallet entity) {
    return entity;
  }

  /**
   * Converts a page of Wallet entities to a page of DTOs. In this implementation, the page is
   * returned as-is since the source and target types are the same.
   *
   * @param entities the page of wallet entities to convert
   * @return the same page of wallet entities
   */
  @Override
  public Page<Wallet> toDto(Page<Wallet> entities) {
    return entities;
  }

  /**
   * Converts a page of Wallet entities to a page of annual purchase overviews. This method maps
   * each wallet to its annual purchase overview representation based on the specified media type in
   * the request.
   *
   * @param entities the page of wallet entities to convert
   * @param request the request containing parameters for the conversion, including media type
   * @return a new page containing the annual purchase overviews for each wallet
   */
  public Page<AnnualPurchaseOverview> toPagedAnnualPurchaseOverview(
      Page<Wallet> wallets, AnnualPurchaseOverviewRequest request) {
    return wallets.map(
        wallet -> {
          AnnualPurchaseOverview apo = wallet.annualPurchaseOverview(request.getMedia());
          apo.put("walletId", wallet.getId().getId());

          return apo;
        });
  }
}
