/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.list;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListService;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListServiceImpl;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.SpecHelper;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementGroupName;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TranchesRepository;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ListWalletServiceImpl
    extends ProjectionListServiceImpl<
        ListWalletRequest, ListWalletResponse, Wallet, WalletId, WalletRepository, ListWalletMapper>
    implements ProjectionListService<ListWalletRequest, ListWalletResponse> {

  private final TranchesRepository tranchesRepository;

  public ListWalletServiceImpl(
      WalletRepository repository, ListWalletMapper mapper, TranchesRepository tranchesRepository) {
    super(repository, mapper, ListWalletResponse.class);
    this.tranchesRepository = tranchesRepository;
  }

  /**
   * Customizes the list of wallets by enriching each wallet response with additional data,
   * specifically the calculated average price.
   *
   * @param request the request object containing pagination and filtering options.
   * @return a {@link Page} of {@link ListWalletResponseDto} objects, enriched with additional data.
   */
  public Page<ListWalletResponseDto> customList(ListWalletRequest request) {
    Page<ListWalletResponse> baseResponses = super.list(request);
    return baseResponses.map(this::enrichWithAveragePrice);
  }

  /**
   * Enriches a wallet response with additional data such as the average price.
   *
   * @param walletResponse the wallet response to enrich.
   * @return a {@link ListWalletResponseDto} object containing the enriched data.
   */
  private ListWalletResponseDto enrichWithAveragePrice(ListWalletResponse walletResponse) {
    BigDecimal averagePrice = calculateAveragePrice(walletResponse.getId());
    BigDecimal annualPurchaseRealization =
        calculateAnnualPurchaseRealization(walletResponse.getId());
    return new ListWalletResponseDto(
        walletResponse.getId(),
        walletResponse.getMediaType(),
        walletResponse.getStartDate(),
        walletResponse.getYear(),
        mapAgreementToResponse(walletResponse.getAgreement()),
        walletResponse.getDescription(),
        walletResponse.getName(),
        averagePrice,
        annualPurchaseRealization);
  }

  /**
   * Maps an agreement response to a {@link ListWalletResponseDto.AgreementResponse}.
   *
   * @param agreement the agreement response to map.
   * @return a mapped {@link ListWalletResponseDto.AgreementResponse} object.
   */
  private ListWalletResponseDto.AgreementResponse mapAgreementToResponse(
      ListWalletResponse.AgreementResponse agreement) {
    if (agreement == null) {
      return null;
    }

    return new ListWalletResponseDto.AgreementResponse(
        agreement.getId(),
        mapCustomerToResponse(agreement.getCustomer()),
        mapSupplierToResponse(agreement.getSupplier()),
        Optional.ofNullable(agreement.getAgreementGroup())
            .map(ListWalletResponse.AgreementResponse.AgreementGroupResponse::getName)
            .map(AgreementGroupName::of)
            .map(ListWalletResponseDto.AgreementResponse.AgreementGroupResponse::new)
            .orElse(null));
  }

  /**
   * Maps a customer response to a {@link ListWalletResponseDto.AgreementResponse.CustomerResponse}.
   *
   * @param customer the customer response to map.
   * @return a mapped {@link ListWalletResponseDto.AgreementResponse.CustomerResponse} object.
   */
  private ListWalletResponseDto.AgreementResponse.CustomerResponse mapCustomerToResponse(
      ListWalletResponse.AgreementResponse.CustomerResponse customer) {
    return new ListWalletResponseDto.AgreementResponse.CustomerResponse(customer.getName());
  }

  /**
   * Maps a supplier response to a {@link ListWalletResponseDto.AgreementResponse.SupplierResponse}.
   *
   * @param supplier the supplier response to map.
   * @return a mapped {@link ListWalletResponseDto.AgreementResponse.SupplierResponse} object.
   */
  private ListWalletResponseDto.AgreementResponse.SupplierResponse mapSupplierToResponse(
      ListWalletResponse.AgreementResponse.SupplierResponse supplier) {
    return new ListWalletResponseDto.AgreementResponse.SupplierResponse(supplier.getName());
  }

  /**
   * Calculates the average price of a wallet based on its tranches.
   *
   * @param walletId the ID of the wallet for which the average price is to be calculated.
   * @return the calculated average price, or {@code BigDecimal.ZERO} if there are no tranches.
   */
  private BigDecimal calculateAveragePrice(WalletId walletId) {
    List<Tranche> tranches = tranchesRepository.findByWalletId(walletId);

    if (tranches.isEmpty()) {
      return BigDecimal.ZERO;
    }

    BigDecimal weightedTotalPrice = BigDecimal.ZERO;

    for (Tranche tranche : tranches) {
      if (tranche.getPrice() != null && tranche.getTimeUnit() != null) {
        int monthsCovered =
            tranche.getTimeUnit().isMonth()
                ? 1
                : tranche.getTimeUnit().isQuarter() ? 3 : tranche.getTimeUnit().isYear() ? 12 : 0;
        weightedTotalPrice =
            weightedTotalPrice.add(tranche.getPrice().multiply(BigDecimal.valueOf(monthsCovered)));
      }
    }
    return weightedTotalPrice.divide(BigDecimal.valueOf(12), RoundingMode.HALF_UP);
  }

  /**
   * Calculates the annual purchase realization percentage for a wallet.
   *
   * @param walletId the ID of the wallet to calculate realization for
   * @return the annual realization percentage as a {@link BigDecimal}, or {@code BigDecimal.ZERO}
   *     if no tranches exist
   */
  private BigDecimal calculateAnnualPurchaseRealization(WalletId walletId) {
    List<Tranche> tranches = tranchesRepository.findByWalletId(walletId);

    if (tranches.isEmpty()) {
      return BigDecimal.ZERO;
    }

    BigDecimal totalRealization =
        tranches.stream()
            .filter(tranche -> tranche.getSize() != null && tranche.getTimeUnit() != null)
            .filter(tranche -> tranche.getContract() instanceof EnergyContract)
            .map(
                tranche -> {
                  int monthsCovered =
                      tranche.getTimeUnit().isMonth()
                          ? 1
                          : tranche.getTimeUnit().getMonths().size();

                  return tranche
                      .getSize()
                      .divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP)
                      .multiply(BigDecimal.valueOf(monthsCovered));
                })
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    return totalRealization.divide(BigDecimal.valueOf(12), 5, RoundingMode.HALF_UP);
  }

  /**
   * Prepares a specification for filtering wallets based on the provided request.
   *
   * @param request the request object containing filtering parameters.
   * @return a {@link Specification} for filtering wallets.
   */
  @Override
  protected Specification<Wallet> prepareSpecification(ListWalletRequest request) {
    Specification<Wallet> specification = SpecHelper.filterBy(request.getFilters());

    if (request.getContractYear() != null) {
      Integer year = request.getContractYear();
      specification =
          specification.and(
              (root, query, builder) ->
                  builder.equal(
                      root.get("year")
                          .get("year"), // Access the 'year' field of the Year value object
                      year.toString() // Convert to String since Year stores it as String
                      ));
    }

    cleanFilters(request);
    return specification;
  }

  /**
   * Removes filters that are not valid database fields.
   *
   * @param request the request object containing filters.
   */
  private void cleanFilters(ListWalletRequest request) {
    if (request.getFilters() != null)
      request.getFilters().removeIf(filter -> "contractYear".equals(filter.getId()));
  }
}
