/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.tranches.update;

import com.codemonkeys.wallet.common.framework.crud.update.UpdateByIdServiceImpl;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.TranchesRepository;
import org.springframework.stereotype.Service;

@Service
public class UpdateTrancheServiceImpl
    extends UpdateByIdServiceImpl<
        UpdateTrancheRequest,
        UpdateTrancheResponse,
        Tranche,
        TrancheId,
        TranchesRepository,
        UpdateTrancheMapper,
        UpdateTrancheValidator> {
  private final RecalculationService recalculationService;

  public UpdateTrancheServiceImpl(
      TranchesRepository repository,
      UpdateTrancheMapper mapper,
      UpdateTrancheValidator validator,
      RecalculationService recalculationService) {
    super(repository, mapper, validator);
    this.recalculationService = recalculationService;
  }

  @Override
  public UpdateTrancheResponse updateById(TrancheId trancheId, UpdateTrancheRequest request) {
    UpdateTrancheResponse response = super.updateById(trancheId, request);
    recalculationService.recalculate(request.getWalletId());
    return response;
  }
}
