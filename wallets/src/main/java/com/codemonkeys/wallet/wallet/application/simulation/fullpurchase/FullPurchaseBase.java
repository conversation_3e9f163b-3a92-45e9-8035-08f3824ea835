/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.simulation.fullpurchase;

import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationHelper;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FullPurchaseBase {

  private final PriceRepository priceRepository;
  private final RecommendationHelper recommendationHelper;

  /**
   * Adds missing tranches for the longest available contract based on annual, quarterly, and
   * monthly units.
   *
   * @param request the WalletSimulationRequest containing initial wallet data
   */
  void addBaseTranche(WalletSimulationRequest request, List<String> missingContracts) {
    int fullYear = Integer.parseInt(request.getYear().getYear());
    String yearSuffix = String.valueOf(fullYear).substring(2);
    LocalDate simulationDate = request.getStartDate();

    LocalDate currentDate = LocalDate.now();
    if (currentDate.isBefore(LocalDate.of(fullYear, 1, 1))) {
      addAnnualTranche(request, yearSuffix, missingContracts);
    } else {
      addQuarterlyAndMonthlyTranches(request, simulationDate, yearSuffix, missingContracts);
    }
  }

  private void addAnnualTranche(
      WalletSimulationRequest request, String yearSuffix, List<String> missingContracts) {
    log.info("Attempting to add annual tranche for BASE_Y-{}", yearSuffix);
    tryAddTrancheForContract(
        request, TimeUnit.Y, String.format("BASE_Y-%s", yearSuffix), missingContracts);
  }

  private void addQuarterlyAndMonthlyTranches(
      WalletSimulationRequest request,
      LocalDate simulationDate,
      String yearSuffix,
      List<String> missingContracts) {
    List<TimeUnit> quarters = List.of(TimeUnit.Q2, TimeUnit.Q3, TimeUnit.Q4);

    for (TimeUnit quarter : quarters) {
      if (isQuarterAvailable(simulationDate, quarter)) {
        tryAddTrancheForContract(
            request,
            quarter,
            String.format("BASE_Q-%d-%s", quarter.getOrder(), yearSuffix),
            missingContracts);
      }
    }

    for (TimeUnit month : TimeUnit.values()) {
      if (month.isMonth() && isMonthAvailable(simulationDate, month)) {
        tryAddTrancheForContract(
            request,
            month,
            String.format("BASE_M-%02d-%s", month.getOrder(), yearSuffix),
            missingContracts);
      }
    }
  }

  /**
   * Determines if the specified quarter is available for simulation based on the current simulation
   * date.
   *
   * @param simulationDate the date the simulation starts
   * @param quarter the quarter being checked for availability
   * @return true if the quarter is available for simulation; false otherwise
   */
  private boolean isQuarterAvailable(LocalDate simulationDate, TimeUnit quarter) {
    return switch (quarter) {
      case Q1 -> simulationDate.isBefore(LocalDate.of(simulationDate.getYear(), 1, 1));
      case Q2 -> simulationDate.isBefore(LocalDate.of(simulationDate.getYear(), 4, 1));
      case Q3 -> simulationDate.isBefore(LocalDate.of(simulationDate.getYear(), 7, 1));
      case Q4 -> simulationDate.isBefore(LocalDate.of(simulationDate.getYear(), 10, 1));
      default -> false;
    };
  }

  /**
   * Determines if the specified month is available for simulation based on the current simulation
   * date.
   *
   * @param simulationDate the date the simulation starts
   * @param month the month being checked for availability
   * @return true if the month is available for simulation; false otherwise
   */
  private boolean isMonthAvailable(LocalDate simulationDate, TimeUnit month) {
    if (!month.isMonth()) {
      log.error("Invalid TimeUnit in isMonthAvailable: {} is not a month.", month);
      return false;
    }
    int monthNumber = month.getOrder();
    return simulationDate.isBefore(LocalDate.of(simulationDate.getYear(), monthNumber, 1));
  }

  /**
   * Attempts to add a tranche for a specific contract and time unit if there is a missing coverage
   * percentage. Retrieves the latest price for the contract and calculates the missing coverage
   * percentage. If a gap exists, it creates and adds a new tranche to the request, updating
   * quarterly and yearly tranches as needed.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param timeUnit the time unit (annual, quarterly, or monthly) for the tranche
   * @param contractName the contract name used to fetch the latest price
   */
  private void tryAddTrancheForContract(
      WalletSimulationRequest request,
      TimeUnit timeUnit,
      String contractName,
      List<String> missingContracts) {

    priceRepository
        .findLatestPriceByContract(contractName)
        .ifPresentOrElse(
            price -> {
              BigDecimal priceValue = price.getValue().getValue();
              BigDecimal missingPercentage = calculateMissingPercentage(request, timeUnit);

              BigDecimal adjustedMissingPercentage =
                  adjustToMonthlyLimit(request, timeUnit, missingPercentage);

              if (adjustedMissingPercentage.compareTo(BigDecimal.ZERO) > 0) {
                WalletSimulationRequest.CreateTrancheRequest tranche =
                    createTranche(
                        request, timeUnit, contractName, priceValue, adjustedMissingPercentage);
                request.getTranches().add(tranche);

                if (timeUnit.isQuarter()) {
                  addQuarterlyTranchesToMonths(
                      request, timeUnit, tranche, priceValue, adjustedMissingPercentage);
                } else if (timeUnit.isYear()) {
                  //                  addYearlyTranchesToMonths(
                  //                      request, tranche, priceValue, adjustedMissingPercentage);
                  expandYearlyContract(request, contractName, missingContracts);
                } else {
                  log.info(
                      "Added tranche for {} with adjusted size {}: {}",
                      timeUnit,
                      tranche.getSize(),
                      tranche);
                }
              } else {
                log.info("Skipping {} as it already has full coverage", timeUnit);
              }
            },
            () -> {
              log.warn("Brak notowań dla kontraktu: {}", contractName);
              missingContracts.add(contractName);
            });
  }

  /**
   * Expands a "BASE_Y-" contract into its quarterly ("BASE_Q-") and monthly ("BASE_M-") variants by
   * extracting the suffix and calling {@link #tryAddTrancheForContract} for each quarter and month.
   *
   * @param request the request to which new contracts are added
   * @param contractName the yearly contract name (e.g. "BASE_Y-2025")
   */
  private void expandYearlyContract(
      WalletSimulationRequest request, String contractName, List<String> missingContracts) {
    String suffix = contractName.substring("BASE_Y-".length());

    for (TimeUnit q : List.of(TimeUnit.Q1, TimeUnit.Q2, TimeUnit.Q3, TimeUnit.Q4)) {
      String qContract = STR."BASE_Q-\{q.getOrder()}-\{suffix}";
      tryAddTrancheForContract(request, q, qContract, missingContracts);
    }

    for (TimeUnit m : TimeUnit.values()) {
      if (m.isMonth()) {
        String mContract = String.format("BASE_M-%02d-%s", m.getOrder(), suffix);
        tryAddTrancheForContract(request, m, mContract, missingContracts);
      }
    }
  }

  /**
   * Adjusts the missing percentage to ensure total coverage does not exceed 100% for any month
   * within the specified {@code TimeUnit}.
   *
   * @param request the simulation request with current tranches
   * @param timeUnit the time unit (month, quarter, or year) being evaluated
   * @param missingPercentage the percentage to be added
   * @return the adjusted percentage that can be safely added without exceeding 100% coverage
   */
  private BigDecimal adjustToMonthlyLimit(
      WalletSimulationRequest request, TimeUnit timeUnit, BigDecimal missingPercentage) {
    List<TimeUnit> months = timeUnit.isMonth() ? List.of(timeUnit) : timeUnit.getMonths();

    return months.stream()
        .map(
            month -> {
              BigDecimal totalCoverageForMonth =
                  calculateTotalPercentageWithHierarchy(request, month);
              return BigDecimal.valueOf(100).subtract(totalCoverageForMonth).max(BigDecimal.ZERO);
            })
        .reduce(missingPercentage, BigDecimal::min);
  }

  /**
   * Adds monthly tranches based on an existing quarterly tranche, duplicating the value for each
   * month in the quarter.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param timeUnit the quarterly time unit for the tranche
   * @param tranche the existing quarterly tranche data
   * @param priceValue the price associated with the contract
   * @param missingPercentage the missing percentage for full coverage
   */
  private void addQuarterlyTranchesToMonths(
      WalletSimulationRequest request,
      TimeUnit timeUnit,
      WalletSimulationRequest.CreateTrancheRequest tranche,
      BigDecimal priceValue,
      BigDecimal missingPercentage) {
    for (TimeUnit month : timeUnit.getMonths()) {
      if (!month.isMonth()) {
        log.error("Non-month TimeUnit passed in addQuarterlyTranchesToMonths: {}", month);
        continue;
      }
      BigDecimal adjustedMissingPercentage = calculateMissingPercentage(request, month);

      if (adjustedMissingPercentage.compareTo(BigDecimal.ZERO) > 0) {
        WalletSimulationRequest.CreateTrancheRequest monthlyTranche =
            new WalletSimulationRequest.CreateTrancheRequest(
                LocalDate.now(),
                tranche.getContract(),
                month,
                adjustedMissingPercentage.min(missingPercentage),
                priceValue,
                PriceReference.DKR,
                true);
        request.getTranches().add(monthlyTranche);
        log.info(
            "Added monthly tranche for {} based on quarter {} with size {}: {}",
            month,
            timeUnit,
            monthlyTranche.getSize(),
            monthlyTranche);
      }
    }
  }

  /**
   * Adds monthly tranches based on an existing annual tranche, duplicating the annual contract
   * details to cover each month in the year. For each month, it checks for missing coverage and, if
   * necessary, creates and adds a new monthly tranche to the request.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param tranche the existing annual tranche data to be distributed across months
   * @param priceValue the price associated with the contract
   * @param missingPercentage the missing percentage for full coverage
   */
  private void addYearlyTranchesToMonths(
      WalletSimulationRequest request,
      WalletSimulationRequest.CreateTrancheRequest tranche,
      BigDecimal priceValue,
      BigDecimal missingPercentage) {
    for (TimeUnit month : TimeUnit.values()) {
      if (!month.isMonth()) {
        continue;
      }
      BigDecimal adjustedMissingPercentage = calculateMissingPercentage(request, month);

      if (adjustedMissingPercentage.compareTo(BigDecimal.ZERO) > 0) {
        WalletSimulationRequest.CreateTrancheRequest monthlyTranche =
            new WalletSimulationRequest.CreateTrancheRequest(
                LocalDate.now(),
                tranche.getContract(),
                month,
                adjustedMissingPercentage.min(missingPercentage),
                priceValue,
                PriceReference.DKR,
                true);
        request.getTranches().add(monthlyTranche);
        log.info(
            "Added monthly tranche for {} based on annual contract with size {}: {}",
            month,
            monthlyTranche.getSize(),
            monthlyTranche);
      }
    }
  }

  /**
   * Creates a new tranche request for a specific contract, time unit, and price value. This method
   * fetches the agreement and contract details, calculates the missing percentage, and builds a
   * tranche request with these values.
   *
   * @param request the WalletSimulationRequest containing initial tranche data
   * @param timeUnit the time unit (e.g., annual, quarterly, or monthly) for the new tranche
   * @param contractName the name of the contract associated with this tranche
   * @param priceValue the price value associated with the contract
   * @param missingPercentage the missing percentage needed to achieve full coverage
   * @return WalletSimulationRequest.CreateTrancheRequest a new tranche request object populated
   *     with contract and pricing details
   */
  private WalletSimulationRequest.CreateTrancheRequest createTranche(
      WalletSimulationRequest request,
      TimeUnit timeUnit,
      String contractName,
      BigDecimal priceValue,
      BigDecimal missingPercentage) {
    Agreement agreement = findAgreementOrThrow(request.getAgreement());
    Contract contract = findContractOrThrow(agreement, contractName);
    UUID contractUuid = UUID.fromString(contract.getId().getId());

    return new WalletSimulationRequest.CreateTrancheRequest(
        LocalDate.now(),
        contractUuid,
        timeUnit,
        missingPercentage,
        priceValue,
        PriceReference.DKR,
        true);
  }

  /**
   * Calculates the missing percentage for a specified time unit to achieve full coverage.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param timeUnit the time unit for which the missing percentage is calculated
   * @return BigDecimal the missing percentage needed for full coverage
   */
  private BigDecimal calculateMissingPercentage(
      WalletSimulationRequest request, TimeUnit timeUnit) {
    BigDecimal totalPercentage = calculateTotalPercentageWithHierarchy(request, timeUnit);
    return BigDecimal.valueOf(100).subtract(totalPercentage);
  }

  /**
   * Calculates the total percentage coverage for a specified time unit, considering hierarchical
   * relationships between annual, quarterly, and monthly units.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param timeUnit the time unit for which total coverage is calculated
   * @return BigDecimal the total percentage coverage for the specified time unit
   */
  private BigDecimal calculateTotalPercentageWithHierarchy(
      WalletSimulationRequest request, TimeUnit timeUnit) {
    return request.getTranches().stream()
        .filter(
            tranche ->
                tranche.getTimeUnit() == timeUnit
                    || (tranche.getTimeUnit().isQuarter()
                        && timeUnit.isMonth()
                        && tranche.getTimeUnit().getMonths().contains(timeUnit))
                    || (tranche.getTimeUnit().isYear()
                        && (timeUnit.isQuarter() || timeUnit.isMonth())))
        .map(
            tranche -> {
              if (tranche.getTimeUnit().isQuarter() && timeUnit.isMonth()) {
                return tranche.getSize();
              }
              if (tranche.getTimeUnit().isYear() && (timeUnit.isQuarter() || timeUnit.isMonth())) {
                return tranche.getSize();
              }
              return tranche.getSize();
            })
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  /**
   * Retrieves an Agreement entity by ID or throws an exception if not found.
   *
   * @param agreementId the ID of the agreement
   * @return Agreement the found agreement entity
   */
  private Agreement findAgreementOrThrow(UUID agreementId) {
    return recommendationHelper.findAgreementOrThrow(agreementId);
  }

  /**
   * Retrieves a Contract entity within a specified Agreement by contract name or throws an
   * exception if not found.
   *
   * @param agreement the agreement containing the contract
   * @param contractName the name of the contract
   * @return Contract the found contract entity
   */
  private Contract findContractOrThrow(Agreement agreement, String contractName) {
    return recommendationHelper.findContractOrThrow(agreement, contractName);
  }
}
