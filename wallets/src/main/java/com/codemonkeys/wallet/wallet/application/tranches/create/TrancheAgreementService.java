/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.tranches.create;

import com.codemonkeys.wallet.common.Validation;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationHelper;
import com.codemonkeys.wallet.wallet.application.common.validation.PriceConfirmationValidationService;
import com.codemonkeys.wallet.wallet.application.common.validation.RealisationConfirmationValidationService;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Helper class responsible for handling operations related to tranches, wallets, agreements, and
 * contracts. Provides utility methods for creating and validating tranches based on specific
 * criteria.
 */
@Component
public class TrancheAgreementService {
  private final Validation validation;
  private final WalletRepository walletRepository;
  private final RecommendationHelper recommendationHelper;
  private final PriceConfirmationValidationService priceConfirmationValidationService;
  private final RealisationConfirmationValidationService realisationConfirmationValidationService;

  /**
   * Constructor for CreateTrancheHelper.
   *
   * @param validation the helper used to validate agreements and contracts.
   * @param walletRepository the repository used to access wallets.
   */
  @Autowired
  public TrancheAgreementService(
      Validation validation,
      WalletRepository walletRepository,
      RecommendationHelper recommendationHelper,
      PriceConfirmationValidationService priceConfirmationValidationService,
      RealisationConfirmationValidationService realisationConfirmationValidationService) {
    this.validation = validation;
    this.walletRepository = walletRepository;
    this.recommendationHelper = recommendationHelper;
    this.priceConfirmationValidationService = priceConfirmationValidationService;
    this.realisationConfirmationValidationService = realisationConfirmationValidationService;
  }

  /**
   * Creates a tranche for a specific wallet based on the provided request.
   *
   * @param request the request containing tranche details.
   * @param walletId the ID of the wallet to which the tranche will be assigned.
   * @return the created Tranche entity.
   */
  public Tranche createTrancheForWallet(CreateTrancheRequest request, WalletId walletId) {
    Wallet wallet = findWalletOrThrow(walletId);
    Contract contract = findContractForWallet(wallet, request.getContract());
    priceConfirmationValidationService.verifyTrancheCreation(walletId, request.getTimeUnit());
    realisationConfirmationValidationService.verifyTrancheCreation(
        walletId, request.getTimeUnit(), contract.getMedia());
    return createTranche(request, wallet, contract);
  }

  /**
   * Creates tranches for all wallets associated with a specific agreement.
   *
   * @param request the request containing tranche details.
   * @param agreementId the ID of the agreement.
   * @return a list of created Tranche entities.
   */
  public List<Tranche> createTranchesForAgreement(CreateTrancheRequest request, UUID agreementId) {
    Agreement agreement = recommendationHelper.findAgreementOrThrow(agreementId);
    List<Wallet> wallets = findWalletsForAgreement(agreement);

    Contract contract = recommendationHelper.findContractOrThrow(agreement, request.getContract());
    wallets.forEach(
        wallet -> {
          priceConfirmationValidationService.verifyTrancheCreation(
              wallet.getId(), request.getTimeUnit());
          realisationConfirmationValidationService.verifyTrancheCreation(
              wallet.getId(), request.getTimeUnit(), contract.getMedia());
        });
    validation.validatePriceReferenceOrThrow(contract, request.getPriceReference().toString());
    return wallets.stream().map(wallet -> createTranche(request, wallet, contract)).toList();
  }

  /**
   * Creates a single tranche based on the given wallet, contract, and request details.
   *
   * @param request the request containing tranche details.
   * @param wallet the wallet to which the tranche will be assigned.
   * @param contract the contract associated with the tranche.
   * @return the created Tranche entity.
   */
  private Tranche createTranche(CreateTrancheRequest request, Wallet wallet, Contract contract) {
    Tranche t =
        new Tranche(
            TrancheId.randomId(),
            wallet,
            request.getExecutionDate(),
            request.getTimeUnit(),
            request.getVolume(),
            request.getPrice(),
            request.getPriceReference(),
            contract,
            false);
    wallet.addTranche(t);
    walletRepository.save(wallet);
    return t;
  }

  /**
   * Finds a wallet by its ID or throws an exception if not found.
   *
   * @param walletId the ID of the wallet.
   * @return the found Wallet entity.
   * @throws IllegalStateException if the wallet is not found.
   */
  public Wallet findWalletOrThrow(WalletId walletId) {
    return walletRepository
        .findById(walletId)
        .orElseThrow(
            () ->
                createException(
                    I18n.translate("error.wallet.not.found2", walletId),
                    "walletId",
                    "WALLET",
                    walletId.toString()));
  }

  /**
   * Finds all wallets associated with a specific agreement.
   *
   * @param agreement the agreement for which wallets are retrieved.
   * @return a list of wallets associated with the agreement.
   * @throws IllegalStateException if no wallets are found.
   */
  public List<Wallet> findWalletsForAgreement(Agreement agreement) {
    List<Wallet> wallets = walletRepository.findByAgreement_Id(agreement.getId());
    if (wallets.isEmpty()) {
      throw createException(
          I18n.translate("error.wallet.not.found.for.agreement2", agreement.getId()),
          "agreementId",
          "AGREEMENT",
          agreement.getId().toString());
    }
    return wallets;
  }

  /**
   * Finds a contract associated with a specific wallet and contract.
   *
   * @param wallet the wallet associated with the contract.
   * @param contract the name of the contract.
   * @return the found Contract entity.
   * @throws IllegalStateException if the contract is not found.
   */
  private Contract findContractForWallet(Wallet wallet, String contract) {
    return wallet.getAgreement().getContracts().stream()
        .filter(
            agreementContract -> agreementContract.getName().getValue().equalsIgnoreCase(contract))
        .findFirst()
        .orElseThrow(
            () ->
                createException(
                    I18n.translate(
                        "error.contract.not.found.for.wallet2",
                        new Object[] {contract, wallet.getId()}),
                    "contractId",
                    "CONTRACT",
                    wallet.getId().toString()));
  }

  private ValidationFailedException createException(
      String message, String source, String subtype, String uuid) {
    return new ValidationFailedException(
        message, List.of(WalletValidationMessage.of(source, message, uuid, subtype)));
  }
}
