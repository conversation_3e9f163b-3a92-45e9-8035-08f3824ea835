/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.simulation.fullpurchase;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationHelper;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationDto;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationMapper;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationRequest;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Orchestrates the full purchase simulation by coordinating the base tranche service, property
 * rights tranche service, and the extraction/restoration of property rights tranches.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FullPurchaseSimulationService {

  private final WalletSimulationMapper walletMapper;
  private final RecalculationService recalculationService;
  private final FullPurchaseBase fullPurchaseBase;
  private final FullPurchaseGas fullPurchaseGas;
  private final FullPurchasePropertyRights fullPurchasePropertyRights;
  private final PropertyRightsTrancheManager propertyRightsTrancheManager;
  private final RecommendationHelper recommendationHelper;

  /**
   * Simulates a full purchase for the given simulation request.
   *
   * <p>The process is as follows: 1. Extract and remove any property rights tranches from the
   * request (if present). 2. Add missing base tranches (these calculations ignore property rights
   * tranches). 3. Restore the extracted property rights tranches. 4. Invoke the property rights
   * service to adjust or add the property rights tranche. 5. Recalculate the wallet state.
   *
   * @param request the simulation request.
   * @return a WalletSimulationDto representing the recalculated wallet state.
   */
  public WalletSimulationDto simulateFullPurchase(WalletSimulationRequest request) {
    List<String> missingContracts = new ArrayList<>();

    Agreement agreement = recommendationHelper.findAgreementOrThrow(request.getAgreement());
    LocalDate agreementEnd = agreement.getEndDate();

    try {
      if (request.getMediaType() == MediaType.GAS) {
        fullPurchaseGas.addGasTranche(request, agreementEnd, missingContracts);
      } else {

        // extract property rights tranches, if any.
        List<WalletSimulationRequest.CreateTrancheRequest> originalPRTranches =
            propertyRightsTrancheManager.extractPropertyRightsTranches(request);

        // process base tranches – these will not include property rights data.
        fullPurchaseBase.addBaseTranche(request, agreementEnd, missingContracts);

        // restore the extracted property rights tranches.
        propertyRightsTrancheManager.restorePropertyRightsTranches(request, originalPRTranches);

        // process property rights tranche separately.
        fullPurchasePropertyRights.addPropertyRightsTranche(request /*, missingContracts*/);
      }
      // TODO pewnie tutaj dociągnąć prices też
      var wallet = walletMapper.toDomain(request);
      var recalculatedWallet = recalculationService.simulateRecalculation(wallet);
      log.info("Final wallet state after recalculation: {}", recalculatedWallet);

      WalletSimulationDto dto = new WalletSimulationDto(recalculatedWallet);

      if (!missingContracts.isEmpty()) {
        dto.setMissingContracts(missingContracts);
      }

      return dto;

    } catch (Exception e) {
      log.error("Simulation error: {}", e.getMessage());
      throw new ValidationFailedException(
          "Simulation failed due to invalid data.",
          List.of(WalletValidationMessage.of("simulation", e.getMessage())));
    }
  }
}
