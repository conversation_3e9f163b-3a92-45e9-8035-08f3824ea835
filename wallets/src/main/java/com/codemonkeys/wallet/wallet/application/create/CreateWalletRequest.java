/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.create;

import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.common.framework.shared.dto.Request;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.GreenPropertyRightCalculationType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class CreateWalletRequest implements Request {
  private MediaType mediaType;
  private LocalDate startDate;
  private Year year;
  private GreenPropertyRightCalculationType greenPropertyRightCalculationType;
  private UUID agreement;
  private List<CreateTrancheRequest> tranches;
  private List<CreateElementRequest> elements;
  private List<CreatePriceRequest> prices;
  private String description;

  // public AgreementId getAgreement(){
  //  return AgreementId.of(agreement);
  // }

  @Data
  @AllArgsConstructor
  public static class CreateTrancheRequest {
    private LocalDate executionDate;
    private UUID contract;
    private TimeUnit timeUnit;
    private BigDecimal size;
    private BigDecimal price;
    private PriceReference priceReference;
  }

  @Data
  @AllArgsConstructor
  public static class CreateElementRequest {
    private ElementType type;
    private Media media;
    private TimeUnit timeUnit;
    private BigDecimal value;
  }

  @Data
  @AllArgsConstructor
  public static class CreatePriceRequest {
    private ElementType type;
    private TimeUnit timeUnit;
    private BigDecimal value;
  }
}
