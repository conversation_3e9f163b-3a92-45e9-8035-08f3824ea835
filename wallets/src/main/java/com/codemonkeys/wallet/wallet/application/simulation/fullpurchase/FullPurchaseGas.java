/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.simulation.fullpurchase;

import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.common.RecommendationHelper;
import com.codemonkeys.wallet.wallet.application.simulation.WalletSimulationRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FullPurchaseGas {

  private final PriceRepository priceRepository;
  private final RecommendationHelper recommendationHelper;

  /**
   * Adds missing tranches for the longest available contract based on annual, quarterly, and
   * monthly units.
   *
   * @param request the WalletSimulationRequest containing initial wallet data
   */
  void addGasTranche(
      WalletSimulationRequest request, LocalDate agreementEnd, List<String> missingContracts) {
    int fullYear = Integer.parseInt(request.getYear().getYear());
    String yearSuffix = String.valueOf(fullYear).substring(2);
    LocalDate simulationDate = request.getStartDate();

    LocalDate currentDate = LocalDate.now();
    if (currentDate.isBefore(LocalDate.of(fullYear, 1, 1))
        && !agreementEnd.isBefore(LocalDate.of(fullYear, 12, 31))) {
      addAnnualTranche(request, yearSuffix, missingContracts);
    } else {
      addQuarterlyAndMonthlyTranches(
          request, simulationDate, yearSuffix, agreementEnd, missingContracts);
    }
  }

  /**
   * Adds an annual tranche (GASE_BASE_Y) contract for the specified year suffix, if pricing data is
   * available. Tracks missing contracts if price is not found.
   *
   * @param request the simulation request containing tranche data
   * @param yearSuffix the two-digit suffix of the contract year (e.g. "25" for 2025)
   * @param missingContracts a list for collecting contract names with missing price data
   */
  private void addAnnualTranche(
      WalletSimulationRequest request, String yearSuffix, List<String> missingContracts) {
    tryAddTrancheForContract(
        request, TimeUnit.Y, String.format("GAS_BASE_Y-%s", yearSuffix), missingContracts);
  }

  /**
   * Adds missing quarterly (GASE_BASE_Q) and monthly (BASE_M) tranches for the given year, based on
   * the simulation start date and agreement end date. Only future periods within the agreement
   * duration are considered.
   *
   * @param request the simulation request containing tranche data
   * @param simulationDate the start date of the simulation
   * @param yearSuffix the two-digit year suffix used to construct contract names
   * @param agreementEnd the end date of the agreement
   * @param missingContracts a list for collecting contract names with missing price data
   */
  private void addQuarterlyAndMonthlyTranches(
      WalletSimulationRequest request,
      LocalDate simulationDate,
      String yearSuffix,
      LocalDate agreementEnd,
      List<String> missingContracts) {
    List<TimeUnit> quarters =
        Arrays.stream(TimeUnit.values())
            .filter(TimeUnit::isQuarter)
            .filter(q -> q != TimeUnit.Q1)
            .toList();

    for (TimeUnit quarter : quarters) {
      if (canAddQuarterlyTranche(simulationDate, agreementEnd, quarter)) {
        tryAddTrancheForContract(
            request,
            quarter,
            String.format("GAS_BASE_Q-%d-%s", quarter.getOrder(), yearSuffix),
            missingContracts);
      }
    }

    List<TimeUnit> months = Arrays.stream(TimeUnit.values()).filter(TimeUnit::isMonth).toList();

    for (TimeUnit month : months) {
      if (!canAddMonthlyTranche(simulationDate, agreementEnd, month)) {
        continue;
      }
      tryAddTrancheForContract(
          request,
          month,
          String.format("GAS_BASE_M-%02d-%s", month.getOrder(), yearSuffix),
          missingContracts);
    }
  }

  /**
   * Determines whether a quarterly tranche should be added based on simulation start date and
   * agreement end date.
   *
   * @param simulationDate the start date of the simulation
   * @param agreementEnd the end date of the agreement
   * @param quarter the quarter time unit being evaluated
   * @return true if the quarterly tranche can be added; false otherwise
   */
  private boolean canAddQuarterlyTranche(
      LocalDate simulationDate, LocalDate agreementEnd, TimeUnit quarter) {

    if (!quarter.isQuarter() || !isQuarterAvailable(simulationDate, quarter)) {
      return false;
    }
    LocalDate qStart = getTimeUnitStart(simulationDate, quarter);
    return startsBeforeOrOn(qStart, agreementEnd);
  }

  /**
   * Determines whether a monthly tranche should be added based on simulation start date and
   * agreement end date.
   *
   * @param simulationDate the start date of the simulation
   * @param agreementEnd the end date of the agreement
   * @param month the month time unit being evaluated
   * @return true if the monthly tranche can be added; false otherwise
   */
  private boolean canAddMonthlyTranche(
      LocalDate simulationDate, LocalDate agreementEnd, TimeUnit month) {
    if (!month.isMonth() || !isMonthAvailable(simulationDate, month)) {
      return false;
    }
    LocalDate mStart = getTimeUnitStart(simulationDate, month);
    return startsBeforeOrOn(mStart, agreementEnd);
  }

  /**
   * Determines if the specified quarter is available for simulation based on the current simulation
   * date.
   *
   * @param simulationDate the date the simulation starts
   * @param quarter the quarter being checked for availability
   * @return true if the quarter is available for simulation; false otherwise
   */
  private boolean isQuarterAvailable(LocalDate simulationDate, TimeUnit quarter) {
    return quarter.isQuarter()
        && simulationDate.isBefore(getTimeUnitStart(simulationDate, quarter));
  }

  /**
   * Determines if the specified month is available for simulation based on the current simulation
   * date.
   *
   * @param simulationDate the date the simulation starts
   * @param month the month being checked for availability
   * @return true if the month is available for simulation; false otherwise
   */
  private boolean isMonthAvailable(LocalDate simulationDate, TimeUnit month) {
    if (!month.isMonth()) {
      log.error("Invalid TimeUnit in isMonthAvailable: {} is not a month.", month);
      return false;
    }
    int monthNumber = month.getOrder();
    return simulationDate.isBefore(LocalDate.of(simulationDate.getYear(), monthNumber, 1));
  }

  /**
   * Attempts to add a tranche for a specific contract and time unit if there is a missing coverage
   * percentage. Retrieves the latest price for the contract and calculates the missing coverage
   * percentage. If a gap exists, it creates and adds a new tranche to the request, updating
   * quarterly and yearly tranches as needed.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param timeUnit the time unit (annual, quarterly, or monthly) for the tranche
   * @param contractName the contract name used to fetch the latest price
   */
  private void tryAddTrancheForContract(
      WalletSimulationRequest request,
      TimeUnit timeUnit,
      String contractName,
      List<String> missingContracts) {

    priceRepository
        .findLatestPriceByContract(contractName)
        .ifPresentOrElse(
            price -> {
              BigDecimal priceValue = price.getValue().getValue();
              BigDecimal missingPercentage = calculateMissingPercentage(request, timeUnit);

              BigDecimal adjustedMissingPercentage =
                  adjustToMonthlyLimit(request, timeUnit, missingPercentage);

              if (adjustedMissingPercentage.compareTo(BigDecimal.ZERO) > 0) {
                WalletSimulationRequest.CreateTrancheRequest tranche =
                    createTranche(
                        request, timeUnit, contractName, priceValue, adjustedMissingPercentage);
                request.getTranches().add(tranche);

                if (timeUnit.isQuarter()) {
                  addQuarterlyTranchesToMonths(
                      request,
                      timeUnit,
                      tranche,
                      priceValue,
                      adjustedMissingPercentage,
                      missingContracts);
                } else if (timeUnit.isYear()) {
                  //                  addYearlyTranchesToMonths(
                  //                      request, tranche, priceValue, adjustedMissingPercentage);
                  expandYearlyContract(request, contractName, missingContracts);
                } else {
                  log.info(
                      "Added tranche for {} with adjusted size {}: {}",
                      timeUnit,
                      tranche.getSize(),
                      tranche);
                }
              } else {
                log.info("Skipping {} as it already has full coverage", timeUnit);
              }
            },
            () -> {
              log.warn("Brak notowań dla kontraktu: {}", contractName);
              missingContracts.add(contractName);
            });
  }

  /**
   * Expands a "BASE_Y-" contract into its quarterly ("BASE_Q-") and monthly ("BASE_M-") variants by
   * extracting the suffix and calling {@link #tryAddTrancheForContract} for each quarter and month.
   *
   * @param request the request to which new contracts are added
   * @param contractName the yearly contract name (e.g. "BASE_Y-2025")
   */
  private void expandYearlyContract(
      WalletSimulationRequest request, String contractName, List<String> missingContracts) {
    String suffix = contractName.substring("GAS_BASE_Y-".length());

    for (TimeUnit q : List.of(TimeUnit.Q1, TimeUnit.Q2, TimeUnit.Q3, TimeUnit.Q4)) {
      String qContract = STR."GAS_BASE_Q-\{q.getOrder()}-\{suffix}";
      tryAddTrancheForContract(request, q, qContract, missingContracts);
    }

    for (TimeUnit m : TimeUnit.values()) {
      if (m.isMonth()) {
        String mContract = String.format("GAS_BASE_M-%02d-%s", m.getOrder(), suffix);
        tryAddTrancheForContract(request, m, mContract, missingContracts);
      }
    }
  }

  /**
   * Adjusts the missing percentage to ensure total coverage does not exceed 100% for any month
   * within the specified {@code TimeUnit}.
   *
   * @param request the simulation request with current tranches
   * @param timeUnit the time unit (month, quarter, or year) being evaluated
   * @param missingPercentage the percentage to be added
   * @return the adjusted percentage that can be safely added without exceeding 100% coverage
   */
  private BigDecimal adjustToMonthlyLimit(
      WalletSimulationRequest request, TimeUnit timeUnit, BigDecimal missingPercentage) {
    List<TimeUnit> months = timeUnit.isMonth() ? List.of(timeUnit) : timeUnit.getMonths();

    return months.stream()
        .map(
            month -> {
              BigDecimal totalCoverageForMonth =
                  calculateTotalPercentageWithHierarchy(request, month);
              return BigDecimal.valueOf(100).subtract(totalCoverageForMonth).max(BigDecimal.ZERO);
            })
        .reduce(missingPercentage, BigDecimal::min);
  }

  /**
   * Distributes a quarterly tranche into its component monthly tranches.
   *
   * <p>For each month in the given quarter:
   *
   * <ul>
   *   <li>Calculates the remaining uncovered percentage (up to 100%).
   *   <li>Attempts to fetch the monthly contract and price; falls back to the quarterly price if
   *       missing.
   *   <li>Creates and adds a monthly tranche if coverage is still needed.
   * </ul>
   *
   * @param request the wallet simulation request to which monthly tranches are added
   * @param quarter the quarterly time unit being split (e.g., Q2)
   * @param quarterTranche the original tranche representing the quarter (not used directly)
   * @param quarterPrice the fallback price if monthly price data is unavailable
   * @param quarterSize the size (percentage) of the original quarterly tranche
   * @param missingContracts a list to record missing contract names for diagnostics
   */
  private void addQuarterlyTranchesToMonths(
      WalletSimulationRequest request,
      TimeUnit quarter,
      WalletSimulationRequest.CreateTrancheRequest quarterTranche,
      BigDecimal quarterPrice,
      BigDecimal quarterSize,
      List<String> missingContracts) {
    String yearSuffix = request.getYear().getYear().substring(2);

    for (TimeUnit month : quarter.getMonths()) {
      if (!month.isMonth()) continue;
      String monthContractName = String.format("GAS_BASE_M-%02d-%s", month.getOrder(), yearSuffix);

      BigDecimal alreadyCovered = calculateTotalPercentageWithHierarchy(request, month);
      BigDecimal freeSpace = BigDecimal.valueOf(100).subtract(alreadyCovered);
      BigDecimal sizeToAdd = quarterSize.min(freeSpace);
      if (sizeToAdd.compareTo(BigDecimal.ZERO) <= 0) continue;

      BigDecimal monthPrice =
          priceRepository
              .findLatestPriceByContract(monthContractName)
              .map(p -> p.getValue().getValue())
              .orElseGet(
                  () -> {
                    log.warn("Brak notowań dla {}", monthContractName);
                    missingContracts.add(monthContractName);
                    return quarterPrice;
                  });

      Agreement ag = findAgreementOrThrow(request.getAgreement());
      Contract monthContract = findContractOrThrow(ag, monthContractName);

      WalletSimulationRequest.CreateTrancheRequest mTranche =
          new WalletSimulationRequest.CreateTrancheRequest(
              LocalDate.now(),
              UUID.fromString(monthContract.getId().getId()),
              month,
              sizeToAdd,
              monthPrice,
              PriceReference.DKR,
              true);

      request.getTranches().add(mTranche);
      log.info(
          "Mirrored {} into {} using contract {} ({} %)",
          quarter, month, monthContractName, sizeToAdd);
    }
  }

  /**
   * Adds monthly tranches based on an existing annual tranche, duplicating the annual contract
   * details to cover each month in the year. For each month, it checks for missing coverage and, if
   * necessary, creates and adds a new monthly tranche to the request.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param tranche the existing annual tranche data to be distributed across months
   * @param priceValue the price associated with the contract
   * @param missingPercentage the missing percentage for full coverage
   */
  private void addYearlyTranchesToMonths(
      WalletSimulationRequest request,
      WalletSimulationRequest.CreateTrancheRequest tranche,
      BigDecimal priceValue,
      BigDecimal missingPercentage) {
    for (TimeUnit month : TimeUnit.values()) {
      if (!month.isMonth()) {
        continue;
      }
      BigDecimal adjustedMissingPercentage = calculateMissingPercentage(request, month);

      if (adjustedMissingPercentage.compareTo(BigDecimal.ZERO) > 0) {
        WalletSimulationRequest.CreateTrancheRequest monthlyTranche =
            new WalletSimulationRequest.CreateTrancheRequest(
                LocalDate.now(),
                tranche.getContract(),
                month,
                adjustedMissingPercentage.min(missingPercentage),
                priceValue,
                PriceReference.DKR,
                true);
        request.getTranches().add(monthlyTranche);
        log.info(
            "Added monthly tranche for {} based on annual contract with size {}: {}",
            month,
            monthlyTranche.getSize(),
            monthlyTranche);
      }
    }
  }

  /**
   * Creates a new tranche request for a specific contract, time unit, and price value. This method
   * fetches the agreement and contract details, calculates the missing percentage, and builds a
   * tranche request with these values.
   *
   * @param request the WalletSimulationRequest containing initial tranche data
   * @param timeUnit the time unit (e.g., annual, quarterly, or monthly) for the new tranche
   * @param contractName the name of the contract associated with this tranche
   * @param priceValue the price value associated with the contract
   * @param missingPercentage the missing percentage needed to achieve full coverage
   * @return WalletSimulationRequest.CreateTrancheRequest a new tranche request object populated
   *     with contract and pricing details
   */
  private WalletSimulationRequest.CreateTrancheRequest createTranche(
      WalletSimulationRequest request,
      TimeUnit timeUnit,
      String contractName,
      BigDecimal priceValue,
      BigDecimal missingPercentage) {
    Agreement agreement = findAgreementOrThrow(request.getAgreement());
    Contract contract = findContractOrThrow(agreement, contractName);
    UUID contractUuid = UUID.fromString(contract.getId().getId());

    return new WalletSimulationRequest.CreateTrancheRequest(
        LocalDate.now(),
        contractUuid,
        timeUnit,
        missingPercentage,
        priceValue,
        PriceReference.DKR,
        true);
  }

  /**
   * Calculates the missing percentage for a specified time unit to achieve full coverage.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param timeUnit the time unit for which the missing percentage is calculated
   * @return BigDecimal the missing percentage needed for full coverage
   */
  private BigDecimal calculateMissingPercentage(
      WalletSimulationRequest request, TimeUnit timeUnit) {
    BigDecimal totalPercentage = calculateTotalPercentageWithHierarchy(request, timeUnit);
    return BigDecimal.valueOf(100).subtract(totalPercentage);
  }

  /**
   * Calculates the total percentage coverage for a specified time unit, considering hierarchical
   * relationships between annual, quarterly, and monthly units.
   *
   * @param request the WalletSimulationRequest containing tranche data
   * @param timeUnit the time unit for which total coverage is calculated
   * @return BigDecimal the total percentage coverage for the specified time unit
   */
  private BigDecimal calculateTotalPercentageWithHierarchy(
      WalletSimulationRequest request, TimeUnit timeUnit) {
    return request.getTranches().stream()
        .filter(
            tranche ->
                tranche.getTimeUnit() == timeUnit
                    || (tranche.getTimeUnit().isQuarter()
                        && timeUnit.isMonth()
                        && tranche.getTimeUnit().getMonths().contains(timeUnit))
                    || (tranche.getTimeUnit().isYear()
                        && (timeUnit.isQuarter() || timeUnit.isMonth())))
        .map(
            tranche -> {
              if (tranche.getTimeUnit().isQuarter() && timeUnit.isMonth()) {
                return tranche.getSize();
              }
              if (tranche.getTimeUnit().isYear() && (timeUnit.isQuarter() || timeUnit.isMonth())) {
                return tranche.getSize();
              }
              return tranche.getSize();
            })
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  /**
   * Retrieves an Agreement entity by ID or throws an exception if not found.
   *
   * @param agreementId the ID of the agreement
   * @return Agreement the found agreement entity
   */
  private Agreement findAgreementOrThrow(UUID agreementId) {
    return recommendationHelper.findAgreementOrThrow(agreementId);
  }

  /**
   * Retrieves a Contract entity within a specified Agreement by contract name or throws an
   * exception if not found.
   *
   * @param agreement the agreement containing the contract
   * @param contractName the name of the contract
   * @return Contract the found contract entity
   */
  private Contract findContractOrThrow(Agreement agreement, String contractName) {
    return recommendationHelper.findContractOrThrow(agreement, contractName);
  }

  /**
   * Returns the start date (first day) of the given time unit in the simulation year.
   *
   * @param simulationDate the reference date used to extract the simulation year
   * @param unit the time unit (month, quarter, or year)
   * @return LocalDate representing the first day of the specified time unit in the simulation year
   */
  private LocalDate getTimeUnitStart(LocalDate simulationDate, TimeUnit unit) {
    return LocalDate.of(simulationDate.getYear(), unit.toMonth().getValue(), 1);
  }

  /**
   * Checks if the start date is before or equal to the agreement end date.
   *
   * @param start the date representing the start of a time unit
   * @param agreementEnd the end date of the agreement
   * @return true if the start date is before or on the agreement end date, false otherwise
   */
  private static boolean startsBeforeOrOn(LocalDate start, LocalDate agreementEnd) {
    return !start.isAfter(agreementEnd);
  }
}
