/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.simulation;

import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import jakarta.persistence.EntityNotFoundException;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class WalletSimulationMapper {

  @NonNull private final ContractRepository contractRepository;
  @NonNull private final AgreementRepository agreementRepository;

  public Wallet toDomain(WalletSimulationRequest request) {
    Set<Tranche> tranches =
        request.getTranches().stream()
            .filter(t -> t.getContract() != null)
            .map(
                t -> {
                  Contract contract =
                      contractRepository.findById(ContractId.of(t.getContract())).orElse(null);
                  if (contract == null) {
                    return null;
                  }
                  return new Tranche(
                      TrancheId.randomId(),
                      null,
                      t.getExecutionDate(),
                      t.getTimeUnit(),
                      t.getSize(),
                      t.getPrice(),
                      t.getPriceReference(),
                      contract,
                      t.isVirtual());
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    Set<Element> elements =
        request.getElements().stream()
            .map(e -> new Element(null, e.getType(), e.getTimeUnit(), e.getValue(), Media.ENERGY))
            .collect(Collectors.toSet());

    Agreement agreement =
        agreementRepository
            .findById(AgreementId.of(request.getAgreement()))
            .orElseThrow(EntityNotFoundException::new);

    return new Wallet(
        agreement.getMediaType(),
        agreement,
        request.getStartDate(),
        Year.of(request.getYear().getYear()),
        tranches,
        elements,
        null,
        null);
  }
}
