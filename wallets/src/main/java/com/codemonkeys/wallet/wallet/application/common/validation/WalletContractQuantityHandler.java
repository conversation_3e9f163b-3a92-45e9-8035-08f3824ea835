/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.common.validation;

import com.codemonkeys.wallet.common.ValidationHandler;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.contract.PropertyRightContract;
import com.codemonkeys.wallet.domain.wallets.MonthTranches;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.wallet.application.common.QuantityVerificationService;
import com.codemonkeys.wallet.wallet.application.common.WalletMapper;
import com.codemonkeys.wallet.wallet.application.create.CreateWalletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WalletContractQuantityHandler<T> implements ValidationHandler<T> {

  private final WalletMapper walletMapper;
  private final QuantityVerificationService quantityVerificationService;
  private ValidationHandler<T> nextHandler;

  @Autowired
  public WalletContractQuantityHandler(
      WalletMapper walletMapper, QuantityVerificationService quantityVerificationService) {
    this.walletMapper = walletMapper;
    this.quantityVerificationService = quantityVerificationService;
  }

  @Override
  public ValidationHandler<T> setNext(ValidationHandler<T> nextHandler) {
    this.nextHandler = nextHandler;
    return nextHandler;
  }

  @Override
  public void validate(T request) throws ValidationFailedException {
    if (!(request instanceof CreateWalletRequest createRequest)) {
      throw new IllegalArgumentException("Unsupported request type");
    }
    Wallet wallet = walletMapper.toDomain(createRequest);
    AgreementId agreementId = wallet.getAgreement().getId();
    MonthTranches energy = wallet.getMonthTranches(EnergyContract.class);
    quantityVerificationService.validateYearWithRecommendations(agreementId, energy);
    MonthTranches gas = wallet.getMonthTranches(GasContract.class);
    quantityVerificationService.validateYearWithRecommendations(agreementId, gas);
    MonthTranches propertyRights = wallet.getMonthTranches(PropertyRightContract.class);
    quantityVerificationService.validateYearWithRecommendations(agreementId, propertyRights);
    if (nextHandler != null) {
      nextHandler.validate(request);
    }
  }
}
