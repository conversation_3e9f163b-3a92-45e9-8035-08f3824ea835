/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.create;

import com.codemonkeys.wallet.common.Validation;
import com.codemonkeys.wallet.common.framework.crud.create.CreateControllerImpl;
import com.codemonkeys.wallet.common.framework.security.Permission;
import com.codemonkeys.wallet.common.framework.security.Permissions;
import com.codemonkeys.wallet.wallet.application.common.CreateUpdateWalletResponse;
import com.codemonkeys.wallet.wallet.application.common.WalletConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for creating wallets. This controller handles HTTP POST requests for creating new
 * wallet entities.
 */
@RestController
@RequestMapping(WalletConstants.PATH)
public class CreateWalletControllerImpl
    extends CreateControllerImpl<
        CreateWalletRequest, CreateUpdateWalletResponse, CreateWalletServiceImpl> {
  private final Validation validation;

  /**
   * Constructs a new {@code CreateWalletControllerImpl} with the given {@link
   * CreateWalletServiceImpl}.
   *
   * @param service The service used for creating wallet entities. This service provides the
   *     business logic necessary to process create operations for wallets.
   */
  @Autowired
  public CreateWalletControllerImpl(CreateWalletServiceImpl service, Validation validation) {
    super(service);
    this.validation = validation;
  }

  /**
   * Handles the HTTP POST request to create a new wallet.
   *
   * @param request The {@link CreateWalletRequest} containing the details for the new wallet.
   * @return A {@link ResponseEntity} containing a {@link CreateUpdateWalletResponse}. If the
   *     creation is successful, it typically returns a {@link ResponseEntity} with the created
   *     wallet details. If the creation fails, it returns a {@link ResponseEntity} with an
   *     appropriate error status.
   */
  @PostMapping
  @Permission(Permissions.CREATE_WALLET)
  @Override
  public ResponseEntity<CreateUpdateWalletResponse> create(
      @RequestBody CreateWalletRequest request) {
    return super.create(request);
  }

  @PostMapping("/validate")
  @Permission(Permissions.CREATE_WALLET)
  public ResponseEntity<?> validate(@RequestBody CreateWalletRequest request) {
    validation.validateNecessaryElementsExistenceForGreenPropertyRightsCalculationType(request);
    return ResponseEntity.ok().build();
  }
}
