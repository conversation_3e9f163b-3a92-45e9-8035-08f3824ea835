/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.results.update;

import com.codemonkeys.wallet.common.framework.crud.update.UpdateByIdControllerImpl;
import com.codemonkeys.wallet.common.framework.shared.dto.Request;
import com.codemonkeys.wallet.domain.results.Result;
import com.codemonkeys.wallet.domain.results.vo.ResultId;
import com.codemonkeys.wallet.wallet.application.results.common.ResultConstants;
import org.jmolecules.ddd.types.Identifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for handling Result creation requests. Provides an endpoint for creating new
 * Result entities.
 */
@RestController
@RequestMapping(ResultConstants.PATH)
public class UpdateResultControllerImpl
    extends UpdateByIdControllerImpl<
        ResultId, UpdateResultRequest, UpdateResultResponse, UpdateResultServiceImpl> {

  /**
   * Constructs a new CreateResultControllerImpl with the required service.
   *
   * @param service the service for handling Result creation operations
   */
  @Autowired
  public UpdateResultControllerImpl(UpdateResultServiceImpl service) {
    super(service);
  }

  @PutMapping("/{id}")
  @Override
  public ResponseEntity<UpdateResultResponse> update(
      ResultId resultId, UpdateResultRequest toBeUpdated) {
    return super.update(resultId, toBeUpdated);
  }
}
