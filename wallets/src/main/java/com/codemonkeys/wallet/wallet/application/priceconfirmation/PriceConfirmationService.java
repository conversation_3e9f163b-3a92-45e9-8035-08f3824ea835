/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.priceconfirmation;

import com.codemonkeys.wallet.common.framework.crud.projectionlist.AggregateRootListService;
import com.codemonkeys.wallet.common.framework.crud.projectionlist.ProjectionListService;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.domain.wallets.MonthPrices;
import com.codemonkeys.wallet.domain.wallets.PriceConfirmation;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletPrice;
import com.codemonkeys.wallet.domain.wallets.WalletPriceRepository;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service class for handling price confirmation operations. This class extends {@link
 * AggregateRootListService} and implements {@link ProjectionListService} to provide functionalities
 * for listing and updating price confirmations for wallets.
 */
@Service
public class PriceConfirmationService
    extends AggregateRootListService<
        PriceConfirmationRequest,
        Wallet,
        Wallet,
        WalletId,
        WalletRepository,
        PriceConfirmationMapper>
    implements ProjectionListService<PriceConfirmationRequest, Wallet> {
  WalletPriceRepository walletPriceRepository;

  /**
   * Constructor for the PriceConfirmationService.
   *
   * @param repository The repository for {@link Wallet} entities.
   * @param mapper The mapper for converting between {@link Wallet} and {@link PriceConfirmation}
   *     objects.
   * @param walletPriceRepository The repository for {@link WalletPrice} entities.
   */
  @Autowired
  public PriceConfirmationService(
      WalletRepository repository,
      PriceConfirmationMapper mapper,
      WalletPriceRepository walletPriceRepository) {
    super(repository, mapper);
    this.walletPriceRepository = walletPriceRepository;
  }

  /**
   * Prepares a specification for filtering {@link Wallet} entities based on the provided {@link
   * PriceConfirmationRequest}.
   *
   * @param criteria The criteria to filter wallets.
   * @return A {@link Specification} for the {@link Wallet} entity.
   */
  @Override
  protected Specification<Wallet> prepareSpecification(PriceConfirmationRequest criteria) {
    return (root, query, criteriaBuilder) -> {
      List<Predicate> predicates = new ArrayList<>();
      if (criteria.getMedia() != null && criteria.getMedia().equals(Media.GREEN_PROPERTY_RIGHTS)) {
        predicates.add(criteriaBuilder.equal(root.get("mediaType"), MediaType.ENERGY));
        predicates.add(
            criteriaBuilder.equal(
                criteriaBuilder.function(
                    "jsonb_extract_path_text",
                    String.class,
                    root.get("agreement").get("propertyRights"),
                    criteriaBuilder.literal("purchaseModel")),
                "TRANCHE"));
      }

      if (criteria.getMedia() != null && criteria.getMedia().equals(Media.ENERGY)) {
        predicates.add(criteriaBuilder.equal(root.get("mediaType"), MediaType.ENERGY));
      }

      if (criteria.getMedia() != null && criteria.getMedia().equals(Media.GAS)) {
        predicates.add(criteriaBuilder.equal(root.get("mediaType"), MediaType.GAS));
      }
      if (criteria.getYear() != null) {
        predicates.add(criteriaBuilder.equal(root.get("year").get("year"), criteria.getYear()));
      }
      if (criteria.getWallet() != null) {
        predicates.add(
            criteriaBuilder.like(
                root.get("agreement").get("customer").get("name").get("name"),
                "%s%s%s".formatted("%", criteria.getWallet(), "%")));
      }
      if (criteria.getSupplier() != null) {
        predicates.add(
            criteriaBuilder.like(
                root.get("agreement").get("supplier").get("name").get("name"),
                "%s%s%s".formatted("%", criteria.getSupplier(), "%")));
      }
      return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
    };
  }

  /**
   * Updates confirmation states for a list of PriceConfirmation objects. For each entry in the
   * list, corresponding monthly prices are updated.
   *
   * @param updatedEntries list of PriceConfirmation objects containing updated confirmation states
   */
  @Transactional
  public void updatePriceConfirmation(List<PriceConfirmation> updatedEntries) {
    for (PriceConfirmation entry : updatedEntries) {
      updateEntry(entry);
    }
  }

  /**
   * Updates a single price confirmation entry. Retrieves wallet by ID and updates its monthly
   * prices.
   *
   * @param entry PriceConfirmation object containing update data
   * @throws EntityNotFoundException when wallet with given ID is not found
   */
  private void updateEntry(PriceConfirmation entry) {
    WalletId id = WalletId.of((String) entry.get("walletId"));
    Wallet wallet = repository.findById(id).orElseThrow(EntityNotFoundException::new);
    updateMonthlyPrices(wallet, entry);
    repository.saveAndFlush(wallet);
  }

  /**
   * Updates monthly prices for a given wallet. Iterates through all months and updates confirmation
   * state for each found price.
   *
   * @param wallet wallet to be updated
   * @param entry price confirmation data containing new states
   */
  private void updateMonthlyPrices(Wallet wallet, PriceConfirmation entry) {
    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      MonthPrices prices = wallet.getMonthPrices();
      Optional<WalletPrice> priceOptional = prices.get(month, ElementType.TOTAL);
      priceOptional.ifPresent(walletPrice -> updatePriceConfirmation(walletPrice, entry, month));
    }
  }

  /**
   * Updates confirmation state for a single price.
   *
   * @param price price to be updated
   * @param entry confirmation data containing new state
   * @param month month for which the price is being updated
   */
  private void updatePriceConfirmation(WalletPrice price, PriceConfirmation entry, TimeUnit month) {
    LinkedHashMap<String, Object> pEntry =
        (LinkedHashMap<String, Object>) entry.get(month.toString());
    boolean confirmed = (boolean) pEntry.get("checked");
    price.setConfirmed(confirmed);
  }
}
