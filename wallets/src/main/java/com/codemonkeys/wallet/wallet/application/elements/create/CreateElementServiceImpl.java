/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.elements.create;

import com.codemonkeys.wallet.common.framework.crud.create.CreateServiceImpl;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.ElementRepository;
import com.codemonkeys.wallet.domain.wallets.vo.ElementId;
import com.codemonkeys.wallet.wallet.application.common.validation.PriceConfirmationValidationService;
import com.codemonkeys.wallet.wallet.application.elements.common.CreateElementResponse;
import com.codemonkeys.wallet.wallet.application.elements.common.ElementMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CreateElementServiceImpl
    extends CreateServiceImpl<
        CreateElementRequest,
        CreateElementResponse,
        <PERSON><PERSON>,
        <PERSON>ementId,
        <PERSON><PERSON><PERSON><PERSON>ository,
        ElementMap<PERSON>,
        CreateElementValidator> {

  private final RecalculationService recalculationService;
  private final PriceConfirmationValidationService priceConfirmationValidationService;

  @Autowired
  public CreateElementServiceImpl(
      ElementRepository repository,
      ElementMapper mapper,
      CreateElementValidator validator,
      RecalculationService recalculationService,
      PriceConfirmationValidationService priceConfirmationValidationService) {
    super(repository, mapper, validator);
    this.recalculationService = recalculationService;
    this.priceConfirmationValidationService = priceConfirmationValidationService;
  }

  @Transactional
  @Override
  public CreateElementResponse create(CreateElementRequest request) {
    priceConfirmationValidationService.verifyElementAddition(
        request.getWalletId(), request.getTimeUnit());
    CreateElementResponse response = super.create(request);
    recalculationService.recalculate(request.getWalletId());
    return response;
  }
}
