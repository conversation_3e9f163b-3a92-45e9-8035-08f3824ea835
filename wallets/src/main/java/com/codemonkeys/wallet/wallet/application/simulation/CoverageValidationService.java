/* (C)2025 */
package com.codemonkeys.wallet.wallet.application.simulation;

import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CoverageValidationService {

  /**
   * Validates that the combined size of all tranches does not exceed 100% coverage for any month.
   * Throws a {@link ValidationFailedException} if the limit is exceeded.
   *
   * @param request the simulation request containing tranches with sizes and time units
   */
  public void validateNotExceeding100Percent(WalletSimulationRequest request) {
    for (TimeUnit month : TimeUnit.values()) {
      if (!month.isMonth()) continue;

      BigDecimal total =
          request.getTranches().stream()
              .filter(tranche -> covers(month, tranche.getTimeUnit()))
              .map(WalletSimulationRequest.CreateTrancheRequest::getSize)
              .reduce(BigDecimal.ZERO, BigDecimal::add);

      if (total.compareTo(BigDecimal.valueOf(100)) > 0) {
        throw new ValidationFailedException(
            STR."W trakcie przeliczeń wykryto przekroczenie 100% pokrycia \{month.name()}",
            List.of(
                WalletValidationMessage.of(
                    "coverage", STR."Przerwano próbę przekroczenia pokrycia: \{total}%")));
      }
    }
  }

  /**
   * Checks whether a given time unit (e.g., year or quarter) covers the specified month.
   *
   * @param month the month to check coverage for
   * @param unit the time unit to check against
   * @return true if the time unit covers the month, false otherwise
   */
  private boolean covers(TimeUnit month, TimeUnit unit) {
    if (unit == null) return false;
    if (unit == month) return true;
    if (unit.isQuarter() && unit.getMonths().contains(month)) return true;
    if (unit.isYear()) return true;
    return false;
  }
}
