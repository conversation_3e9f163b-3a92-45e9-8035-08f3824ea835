/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.common;

import com.codemonkeys.wallet.common.framework.crud.create.CreateMapper;
import com.codemonkeys.wallet.common.framework.domain.vo.Year;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.contract.vo.ContractId;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.wallet.application.create.CreateWalletRequest;
import jakarta.persistence.EntityNotFoundException;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class WalletMapper
    implements CreateMapper<Wallet, WalletId, CreateWalletRequest, CreateUpdateWalletResponse> {

  @NonNull ContractRepository contractRepository;
  @NonNull AgreementRepository agreementRepository;

  @Override
  public Wallet toDomain(CreateWalletRequest request) {
    Set<Tranche> tranches =
        request.getTranches().stream()
            .map(
                t -> {
                  Contract contract =
                      contractRepository
                          .findById(ContractId.of(t.getContract()))
                          .orElseThrow(EntityNotFoundException::new);
                  return new Tranche(
                      TrancheId.randomId(),
                      null,
                      t.getExecutionDate(),
                      t.getTimeUnit(),
                      t.getSize(),
                      t.getPrice(),
                      t.getPriceReference(),
                      contract,
                      false);
                })
            .collect(Collectors.toSet());
    Set<Element> elements =
        request.getElements().stream()
            .map(t -> new Element(null, t.getType(), t.getTimeUnit(), t.getValue(), t.getMedia()))
            .collect(Collectors.toSet());
    Agreement agreement =
        agreementRepository
            .findById(AgreementId.of(request.getAgreement()))
            .orElseThrow(EntityNotFoundException::new);
    return new Wallet(
        agreement.getMediaType(),
        agreement,
        request.getStartDate(),
        Year.of(request.getStartDate().getYear()),
        tranches,
        elements,
        request.getDescription(),
        request.getGreenPropertyRightCalculationType());
  }

  @Override
  public CreateUpdateWalletResponse toResponse(Wallet entity) {
    return CreateUpdateWalletResponse.of(entity.getId(), ValidResult.of(), entity);
  }

  @Override
  public CreateUpdateWalletResponse toResponse(InvalidResult errors) {
    return CreateUpdateWalletResponse.of(null, errors, null);
  }
}
