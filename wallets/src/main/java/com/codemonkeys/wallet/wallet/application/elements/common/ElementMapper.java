/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.elements.common;

import com.codemonkeys.wallet.common.framework.crud.create.CreateMapper;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.vo.ElementId;
import com.codemonkeys.wallet.wallet.application.elements.create.CreateElementRequest;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class ElementMapper
    implements CreateMapper<Element, ElementId, CreateElementRequest, CreateElementResponse> {
  private final WalletRepository walletRepository;

  @Override
  public Element toDomain(CreateElementRequest request) {
    Wallet wallet =
        walletRepository.findById(request.getWalletId()).orElseThrow(EntityNotFoundException::new);
    Element e =
        new Element(
            wallet,
            request.getType(),
            request.getTimeUnit(),
            request.getValue(),
            request.getMedia(wallet.getAgreement().getMediaType()));
    wallet.addElement(e);
    return e;
  }

  @Override
  public CreateElementResponse toResponse(Element entity) {
    return new CreateElementResponse(entity.getId(), ValidResult.of(), entity);
  }

  @Override
  public CreateElementResponse toResponse(InvalidResult errors) {
    return new CreateElementResponse(null, errors, null);
  }
}
