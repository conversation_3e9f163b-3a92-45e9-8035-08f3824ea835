/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.common.validation;

import com.codemonkeys.wallet.common.SpotContractSkipper;
import com.codemonkeys.wallet.common.ValidationHandler;
import com.codemonkeys.wallet.common.VolumeVerificationService;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.wallet.application.tranches.create.CreateTrancheRequest;
import jakarta.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Handles volume validation for tranche creation requests using a chain of validation
 * responsibilities.
 *
 * <p>This handler is responsible for validating volume constraints for both wallet-based and
 * agreement-based tranche creation requests. It leverages a {@link VolumeVerificationService} to
 * perform specific volume validation checks.
 *
 * <p>Implements the Chain of Responsibility design pattern for flexible validation processing.
 *
 * @param <T> The type of request being validated, typically {@link CreateTrancheRequest}
 * @see CreateTrancheRequest
 * @see VolumeVerificationService
 * @see WalletRepository
 */
@Component
@Transactional
public class TrancheVolumeHandler<T> implements ValidationHandler<T> {

  private final WalletRepository walletRepository;
  private final VolumeVerificationService volumeVerificationService;
  private final ContractRepository contractRepository;
  private ValidationHandler<T> nextHandler;

  /**
   * Constructs a TrancheVolumeHandler with required dependencies for volume validation.
   *
   * @param walletRepository Repository for retrieving wallet information
   * @param volumeVerificationService Utility for performing volume validation checks
   */
  @Autowired
  public TrancheVolumeHandler(
      WalletRepository walletRepository,
      VolumeVerificationService volumeVerificationService,
      ContractRepository contractRepository) {
    this.walletRepository = walletRepository;
    this.volumeVerificationService = volumeVerificationService;
    this.contractRepository = contractRepository;
  }

  /**
   * Sets the next validation handler in the chain of responsibility.
   *
   * @param nextHandler The next validation handler to be executed
   * @return The next validation handler
   */
  @Override
  public ValidationHandler<T> setNext(ValidationHandler<T> nextHandler) {
    this.nextHandler = nextHandler;
    return nextHandler;
  }

  /**
   * Validates the volume constraints for a tranche creation request.
   *
   * <p>Supports two types of validation based on the request type:
   *
   * <ul>
   *   <li>Wallet-based validation for non-form type requests
   *   <li>Agreement-based validation for form type requests
   * </ul>
   *
   * @param request The tranche creation request to validate
   * @throws ValidationFailedException If volume validation fails or request type is invalid
   */
  @Override
  public void validate(T request) throws ValidationFailedException {

    if (SpotContractSkipper.isSpotContract(request)) {
      if (nextHandler != null) nextHandler.validate(request);
      return;
    }

    switch (request) {
      case CreateTrancheRequest createRequest -> {
        if (!createRequest.getWalletIds().isEmpty()) {
          validateWallets(createRequest, createRequest.getVolume());
        }
        if (!createRequest.getAgreementsIds().isEmpty()) {
          validateAgreements(
              createRequest, createRequest.getAgreementsIds(), createRequest.getVolume());
        }
      }
      default -> throw ValidationFailedException.of("request", "invalid.request.type");
    }
    if (nextHandler != null) {
      nextHandler.validate(request);
    }
  }

  /**
   * Validates volume constraints for multiple agreements.
   *
   * <p>Performs comprehensive volume checks including:
   *
   * <ul>
   *   <li>Wallet volume validation
   *   <li>Recommendation volume validation
   *   <li>Media volume validation
   * </ul>
   *
   * @param createRequest The tranche creation request
   * @param agreementsToVerify List of agreement IDs to validate
   * @param additionalVolume The additional volume to be validated
   */
  private void validateAgreements(
      CreateTrancheRequest createRequest,
      List<AgreementId> agreementsToVerify,
      BigDecimal additionalVolume) {
    for (AgreementId agreementId : agreementsToVerify) {
      Contract contract =
          contractRepository
              .findByAgreementIdAndName(agreementId, createRequest.getContract())
              .orElseThrow(EntityNotFoundException::new);
      volumeVerificationService.validateMediaVolume(
          agreementId, additionalVolume, contract.getClass());
      volumeVerificationService.validateWalletsVolume(
          agreementId, createRequest.getVolume(), createRequest.getTimeUnit(), contract.getClass());
      volumeVerificationService.validateRecommendationVolume(
          agreementId, createRequest.getVolume(), createRequest.getTimeUnit(), contract.getClass());
    }
  }

  /**
   * Validates volume constraints for multiple wallets.
   *
   * <p>Performs comprehensive volume checks including:
   *
   * <ul>
   *   <li>Wallet volume validation
   *   <li>Recommendation volume validation
   *   <li>Media volume validation
   * </ul>
   *
   * @param createRequest The tranche creation request
   * @param additionalVolume The additional volume to be validated
   */
  private void validateWallets(CreateTrancheRequest createRequest, BigDecimal additionalVolume) {
    for (WalletId walletId : createRequest.getWalletIds()) {
      Wallet wallet =
          walletRepository
              .findById(walletId)
              .orElseThrow(() -> ValidationFailedException.of("walletId", "wallet.not.found"));
      AgreementId agreementId = wallet.getAgreement().getId();
      Contract contract =
          contractRepository
              .findByAgreementIdAndName(agreementId, createRequest.getContract())
              .orElseThrow(EntityNotFoundException::new);
      volumeVerificationService.validateMediaVolume(
          agreementId, additionalVolume, contract.getClass());
      volumeVerificationService.validateWalletVolume(
          wallet, createRequest.getVolume(), createRequest.getTimeUnit(), contract.getClass());
      volumeVerificationService.validateRecommendationVolume(
          agreementId, createRequest.getVolume(), createRequest.getTimeUnit(), contract.getClass());
    }
  }
}
