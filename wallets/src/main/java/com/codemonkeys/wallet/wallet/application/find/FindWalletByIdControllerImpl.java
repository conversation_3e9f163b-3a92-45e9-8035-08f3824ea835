/* (C)2024-2025 */
package com.codemonkeys.wallet.wallet.application.find;

import com.codemonkeys.wallet.common.framework.crud.findbyid.FindByIdControllerImpl;
import com.codemonkeys.wallet.common.framework.security.Permission;
import com.codemonkeys.wallet.common.framework.security.Permissions;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.wallet.application.common.WalletConstants;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for finding wallets by ID. It extends the generic FindByIdControllerImpl to
 * handle wallet-specific find requests.
 */
@RequestMapping(WalletConstants.PATH)
@RestController
public class FindWalletByIdControllerImpl
    extends FindByIdControllerImpl<WalletId, FindWalletByIdResponse, FindWalletByIdServiceImpl> {

  /**
   * Constructs a new FindWalletByIdController with the specified service for finding wallets.
   *
   * @param service the service used for handling the find logic of wallets.
   */
  public FindWalletByIdControllerImpl(FindWalletByIdServiceImpl service) {
    super(service);
  }

  /**
   * Handles the HTTP GET request to find a wallet by ID.
   *
   * @param id the unique identifier of the wallet to be found.
   * @return a response entity containing the find operation response.
   */
  @GetMapping("/{id}")
  @Permission(Permissions.VIEW_WALLET)
  @Override
  public ResponseEntity<FindWalletByIdResponse> findById(@PathVariable("id") WalletId id) {
    return super.findById(id);
  }
}
