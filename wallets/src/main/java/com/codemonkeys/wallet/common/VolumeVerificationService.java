/* (C)2024-2025 */
package com.codemonkeys.wallet.common;

import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.common.framework.shared.validation.WalletValidationMessage;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.contract.enums.VolumeType;
import com.codemonkeys.wallet.domain.contract.vo.Volume;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.RecommendationRepository;
import com.codemonkeys.wallet.domain.recommendation.vo.RecommendationId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service class responsible for verifying volume-related constraints across different domain
 * contexts.
 *
 * <p>This verifier handles volume validation for various entities including:
 *
 * <ul>
 *   <li>Wallets
 *   <li>Recommendations
 *   <li>Agreements
 * </ul>
 *
 * The class provides methods to parse, validate, and check volume limits across different time
 * units and contexts.
 */
@AllArgsConstructor(onConstructor = @__(@Autowired))
@Service
@Slf4j
public class VolumeVerificationService {
  private static final String RECOMMENDATION_VOLUME_EXCEEDS_LIMIT =
      "validations.contract.recommendationVolumeExceedsLimit";
  private static final String VOLUME = "volume";
  private static final String INVALID_VOLUME_FORMAT = "validations.contract.invalidVolumeFormat";
  private static final String VOLUME_FIELD = "volume";
  private static final String WALLET_VOLUME_EXCEEDS_LIMIT =
      "validations.wallet.volumeTimeUnitLimit";
  private static final String MEDIA_VOLUME_MULTIPLE_MISMATCH =
      "validations.media.volumeMultipleMismatch";
  private static final String MEDIA_VOLUME_LESS_THAN_MIN = "validations.media.volumeLessThanMin";
  private static final String MEDIA_VOLUME_EXCEEDS_LIMIT = "validation.media.volumeAboveMax";
  private final WalletRepository walletRepository;
  private final RecommendationRepository recommendationRepository;
  private final AgreementRepository agreementRepository;

  /**
   * Validates total wallet volumes against time unit volume limits for a specific agreement.
   *
   * <p>This method checks if the proposed transaction volume does not exceed the predefined limits
   * for wallets associated with the given agreement.
   *
   * @param agreementId Unique identifier of the agreement being validated
   * @param toBeBoughtVolume Proposed volume to be purchased
   * @param timeUnit Time unit (e.g., daily, monthly) for volume calculation
   * @throws ValidationFailedException If the proposed volume exceeds wallet volume limits
   */
  public void validateWalletsVolume(
      AgreementId agreementId,
      BigDecimal toBeBoughtVolume,
      TimeUnit timeUnit,
      Class<? extends Contract> type) {
    List<Wallet> wallets = walletRepository.findByAgreement_Id(agreementId);
    for (Wallet wallet : wallets) {
      if (wallet.doesTransactionViolateTimeUnitVolumeLimit(toBeBoughtVolume, timeUnit, type)) {
        throw new ValidationFailedException(
            I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, timeUnit),
            Lists.newArrayList(
                WalletValidationMessage.of(
                    VOLUME_FIELD, I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, timeUnit))));
      }
    }
  }

  /**
   * Validates volume for a given wallet instance.
   *
   * <p>Performs direct volume limit validation on an existing wallet object.
   *
   * @param wallet Wallet instance to be validated
   * @param toBeBoughtVolume Volume proposed for purchase
   * @param timeUnit Time unit for volume calculation
   * @throws ValidationFailedException If the proposed volume exceeds wallet limits
   */
  public void validateWalletVolume(
      Wallet wallet,
      BigDecimal toBeBoughtVolume,
      TimeUnit timeUnit,
      Class<? extends Contract> type) {
    if (wallet.doesTransactionViolateTimeUnitVolumeLimit(toBeBoughtVolume, timeUnit, type)) {
      throw new ValidationFailedException(
          I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, timeUnit),
          Lists.newArrayList(
              WalletValidationMessage.of(
                  VOLUME_FIELD, I18n.translate(WALLET_VOLUME_EXCEEDS_LIMIT, timeUnit))));
    }
  }

  /**
   * Validates recommendation volumes across associated wallets.
   *
   * <p>Checks if the total recommendation volume (including new additions) complies with wallet
   * volume limits. Compares proposed volume against existing volumes to ensure no limit is
   * exceeded.
   *
   * @param agreementId Identifier of the agreement associated with recommendations
   * @param additionalVolume New volume to be added to recommendations
   * @param beforeUpdateVolume Previous volume before the proposed update
   * @param timeUnit Time unit for volume calculation
   * @throws ValidationFailedException If recommendation volume would exceed limits
   */
  public void validateRecommendationVolume(
      AgreementId agreementId,
      BigDecimal additionalVolume,
      BigDecimal beforeUpdateVolume,
      TimeUnit timeUnit,
      Class<? extends Contract> type) {
    BigDecimal recommendationVolume = currentRecommendationVolume(agreementId, timeUnit);
    BigDecimal toBeBoughtVolume = recommendationVolume.add(additionalVolume);
    List<Wallet> wallets = walletRepository.findByAgreement_Id(agreementId);
    for (Wallet wallet : wallets) {
      if (wallet.doesTransactionViolateTimeUnitVolumeLimit(toBeBoughtVolume, timeUnit, type)) {
        throw ValidationFailedException.of(VOLUME_FIELD, RECOMMENDATION_VOLUME_EXCEEDS_LIMIT);
      }
    }
  }

  /**
   * Overloaded method to validate recommendation volume without a previous volume.
   *
   * <p>Defaults the previous volume to zero and calls the full validation method.
   *
   * @param agreementId Identifier of the agreement associated with recommendations
   * @param additionalVolume New volume to be added to recommendations
   * @param timeUnit Time unit for volume calculation
   * @throws ValidationFailedException If recommendation volume would exceed limits
   */
  public void validateRecommendationVolume(
      AgreementId agreementId,
      BigDecimal additionalVolume,
      TimeUnit timeUnit,
      Class<? extends Contract> type) {
    validateRecommendationVolume(agreementId, additionalVolume, BigDecimal.ZERO, timeUnit, type);
  }

  /**
   * Calculates the current total recommendation volume for a specific agreement and time unit.
   *
   * <p>Retrieves all recommendations for the given agreement and time unit, then sums their
   * volumes.
   *
   * @param agreementId Identifier of the agreement
   * @param timeUnit Time unit for volume calculation
   * @return Total volume of recommendations in the specified time unit
   */
  private BigDecimal currentRecommendationVolume(AgreementId agreementId, TimeUnit timeUnit) {
    List<Recommendation> allRecommendations =
        recommendationRepository.findByAgreementIdAndTimeUnit(
            UUID.fromString(agreementId.getId()), timeUnit.name());

    List<Recommendation> allowed = RecommendationFilter.filterAllowed(allRecommendations);

    return allowed.stream()
        .map(this::convertVolumeToBigDecimal)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  /**
   * Parses a volume string into a BigDecimal representation.
   *
   * <p>Converts a string input to a precise decimal volume, handling potential parsing errors.
   *
   * @param volume Volume represented as a string
   * @return Parsed volume as a BigDecimal
   * @throws ValidationFailedException If the volume string cannot be parsed correctly
   */
  public BigDecimal parseVolume(String volume) {
    try {
      return new BigDecimal(volume);
    } catch (NumberFormatException e) {
      throw ValidationFailedException.of(VOLUME, INVALID_VOLUME_FORMAT);
    }
  }

  /**
   * Converts a recommendation's volume to BigDecimal.
   *
   * <p>Uses the {@link #parseVolume(String)} method to safely convert the recommendation's volume
   * string to a BigDecimal.
   *
   * @param recommendation Recommendation to convert
   * @return Volume as a BigDecimal
   */
  public BigDecimal convertVolumeToBigDecimal(Recommendation recommendation) {
    return parseVolume(recommendation.getVolume());
  }

  /**
   * Retrieves the volume of a specific recommendation before an update.
   *
   * <p>Fetches the recommendation by ID and returns its current volume. Returns zero if the
   * recommendation is not found.
   *
   * @param recommendationId Unique identifier of the recommendation
   * @return Current volume of the recommendation, or zero if not found
   */
  public BigDecimal getVolumeBeforeUpdate(UUID recommendationId) {
    return recommendationRepository
        .findById(RecommendationId.of(recommendationId))
        .map(this::convertVolumeToBigDecimal)
        .orElse(BigDecimal.ZERO);
  }

  /**
   * Validates media volume against specific agreement constraints.
   *
   * <p>Performs comprehensive volume validation including:
   *
   * <ul>
   *   <li>Multiplicity constraints
   *   <li>Minimum volume requirements
   *   <li>Maximum volume limits
   * </ul>
   *
   * @param agreementId Identifier of the agreement governing media volume
   * @param additionalVolume Volume to be validated against agreement constraints
   * @throws ValidationFailedException If the volume violates any media-specific constraints
   */
  public void validateMediaVolume(
      AgreementId agreementId, BigDecimal additionalVolume, Class<? extends Contract> type) {
    if (EnergyContract.class.isAssignableFrom(type) || GasContract.class.isAssignableFrom(type)) {
      Agreement agreement = agreementRepository.getReferenceById(agreementId);

      //      TODO check if get volume from contract either agreement
      Volume volume = agreement.getMedia().getVolume();
      validateValueX(additionalVolume, volume);
      validateValueY(additionalVolume, volume);
    }
  }

  /**
   * Validates media volume against specific agreement constraints.
   *
   * <p>Performs comprehensive volume validation including:
   *
   * <ul>
   *   <li>Multiplicity constraints
   *   <li>Minimum volume requirements
   *   <li>Maximum volume limits
   * </ul>
   *
   * @param agreementId Identifier of the agreement governing media volume
   * @param additionalVolume Volume to be validated against agreement constraints
   * @throws ValidationFailedException If the volume violates any media-specific constraints
   */
  public void validateMaxMediaVolume(
      AgreementId agreementId, BigDecimal additionalVolume, Class<? extends Contract> type) {
    if (EnergyContract.class.isAssignableFrom(type) || GasContract.class.isAssignableFrom(type)) {
      Agreement agreement = agreementRepository.getReferenceById(agreementId);
      Volume volume = agreement.getMedia().getVolume();
      validateValueY(additionalVolume, volume);
    }
  }

  /**
   * Validates volume type with respect to a specific x value.
   *
   * <p>Performs volume validation based on the volume type:
   *
   * <ul>
   *   <li>For MULTIPLE type: Checks if volume is a multiple of x
   *   <li>For MIN_MAX type: Checks if volume meets minimum requirement
   * </ul>
   *
   * @param additionalVolume Volume to validate
   * @param volume Volume configuration object
   */
  private void validateValueX(BigDecimal additionalVolume, Volume volume) {
    VolumeType volumeType = volume.getType();
    if (volume.getX() != null) {
      BigDecimal x = new BigDecimal(volume.getX());
      switch (volumeType) {
        case MULTIPLE -> validateValueMultipleX(additionalVolume, x);
        case MIN_MAX -> validateValueMinX(additionalVolume, x);
      }
    }
  }

  /**
   * Validates volume type with respect to a specific y value.
   *
   * <p>Performs volume validation based on the volume type:
   *
   * <ul>
   *   <li>For MULTIPLE and MIN_MAX types: Checks maximum volume limit
   * </ul>
   *
   * @param additionalVolume Volume to validate
   * @param volume Volume configuration object
   */
  private void validateValueY(BigDecimal additionalVolume, Volume volume) {
    VolumeType volumeType = volume.getType();
    if (volume.getY() != null) {
      BigDecimal y = new BigDecimal(volume.getY());
      switch (volumeType) {
        case MULTIPLE, MIN_MAX -> validateValueMaxY(additionalVolume, y);
      }
    }
  }

  /**
   * Validates volume against a multiple value constraint.
   *
   * <p>Checks if the additional volume is perfectly divisible by the specified x value. Throws a
   * validation exception if the volume is not a multiple of x.
   *
   * @param additionalVolume Volume to be validated
   * @param x The multiple value to check against
   * @throws ValidationFailedException If the volume is not a multiple of x
   */
  private void validateValueMultipleX(BigDecimal additionalVolume, BigDecimal x) {
    if (additionalVolume.remainder(x).compareTo(BigDecimal.ZERO) != 0) {
      throw ValidationFailedException.of(VOLUME_FIELD, MEDIA_VOLUME_MULTIPLE_MISMATCH);
    }
  }

  /**
   * Validates volume against a minimum value constraint.
   *
   * <p>Checks if the additional volume meets or exceeds the minimum x value. Throws a validation
   * exception if the volume is less than the minimum.
   *
   * @param additionalVolume Volume to be validated
   * @param x The minimum value to compare against
   * @throws ValidationFailedException If the volume is less than the minimum x value
   */
  private void validateValueMinX(BigDecimal additionalVolume, BigDecimal x) {
    if (additionalVolume.compareTo(x) < 0) {
      throw new ValidationFailedException(
          I18n.translate(MEDIA_VOLUME_LESS_THAN_MIN, x),
          Lists.newArrayList(
              WalletValidationMessage.of(
                  VOLUME_FIELD, I18n.translate(MEDIA_VOLUME_LESS_THAN_MIN, x))));
    }
  }

  /**
   * Validates volume against a maximum value constraint.
   *
   * <p>Checks if the additional volume does not exceed the specified y value. Throws a validation
   * exception if the volume is greater than the maximum y value.
   *
   * @param additionalVolume Volume to be validated
   * @param y The maximum value to compare against
   * @throws ValidationFailedException If the volume exceeds the maximum y value
   */
  private void validateValueMaxY(BigDecimal additionalVolume, BigDecimal y) {
    if (additionalVolume.compareTo(y) > 0) {
      String message = I18n.translate(MEDIA_VOLUME_EXCEEDS_LIMIT, y);
      throw new ValidationFailedException(
          message, Lists.newArrayList(WalletValidationMessage.of(VOLUME_FIELD, message)));
    }
  }
}
