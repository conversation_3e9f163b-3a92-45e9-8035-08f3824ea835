/* (C)2024-2025 */
package com.codemonkeys.wallet.common;

import com.codemonkeys.wallet.recommendation.application.common.validation.*;
import com.codemonkeys.wallet.recommendation.application.create.CreateRecommendationRequest;
import com.codemonkeys.wallet.recommendation.application.update.UpdateRecommendationRequest;
import com.codemonkeys.wallet.wallet.application.common.validation.*;
import com.codemonkeys.wallet.wallet.application.create.CreateWalletRequest;
import com.codemonkeys.wallet.wallet.application.tranches.create.CreateTrancheRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class ValidationHandlerFactory {

  private final AgreementExistsHandler agreementExistsHandler;
  private final ContractExistsHandler contractExistsHandler;
  private final PriceReferenceHandler priceReferenceHandler;
  private final VolumeHandler volumeHandler;
  private final DeadlineHandler deadlineHandler;
  private final ContractQuantityHandler contractQuantityHandler;
  private final TrancheVolumeHandler trancheVolumeHandler;
  private final TrancheContractQuantityHandler trancheContractQuantityHandler;
  private final TrancheDeadlineHandler trancheDeadlineHandler;
  private final TranchePriceReferenceHandler tranchePriceReferenceHandler;
  private final WalletTrancheDeadlineHandler walletTrancheDeadlineHandler;
  private final WalletTrancheVolumeHandler walletTrancheVolumeHandler;
  private final WalletContractQuantityHandler walletContractQuantityHandler;
  private final WalletPriceReferenceHandler walletPriceReferenceHandler;
  private final ContractHandler contractHandler;

  /**
   * Creates a validation chain for creating recommendations.
   *
   * @return The starting handler in the validation chain for creating recommendations.
   */
  public ValidationHandler<CreateRecommendationRequest> createRecommendationValidationChain() {
    agreementExistsHandler
        .setNext(contractExistsHandler)
        .setNext(priceReferenceHandler)
        .setNext(volumeHandler)
        .setNext(contractQuantityHandler)
        .setNext(null);
    return agreementExistsHandler;
  }

  /**
   * Creates a validation chain for updating recommendations.
   *
   * @return The starting handler in the validation chain for updating recommendations.
   */
  public ValidationHandler<UpdateRecommendationRequest> updateRecommendationValidationChain() {
    agreementExistsHandler
        .setNext(contractExistsHandler)
        .setNext(priceReferenceHandler)
        .setNext(volumeHandler)
        .setNext(contractQuantityHandler)
        .setNext(null);
    return agreementExistsHandler;
  }

  /**
   * Creates a validation chain for handling the addition of tranches during a recommendation
   * action.
   *
   * @return The starting handler in the validation chain for adding tranches to a wallet during
   *     recommendation actions.
   */
  public ValidationHandler<CreateRecommendationRequest>
      addToWalletRecommendationActionValidation() {
    agreementExistsHandler
        .setNext(contractExistsHandler)
        .setNext(priceReferenceHandler)
        .setNext(null);
    return agreementExistsHandler;
  }

  /**
   * Creates a validation chain for validating the addition of tranches in a group context.
   *
   * @return The starting handler in the validation chain for group tranche addition.
   */
  public ValidationHandler<CreateTrancheRequest> groupTrancheAdditionValidationChain() {
    trancheContractQuantityHandler
        .setNext(contractHandler)
        .setNext(trancheVolumeHandler)
        .setNext(tranchePriceReferenceHandler)
        .setNext(trancheDeadlineHandler)
        .setNext(null);
    return trancheContractQuantityHandler;
  }

  /**
   * Creates a validation chain for validating basic tranche operations in a wallet.
   *
   * @return The starting handler in the validation chain for wallet-based tranche validation.
   */
  public ValidationHandler<CreateWalletRequest> walletBaseTrancheValidationChain() {
    walletTrancheVolumeHandler
        .setNext(walletContractQuantityHandler)
        .setNext(walletPriceReferenceHandler)
        .setNext(null);
    return walletTrancheVolumeHandler;
  }
}
