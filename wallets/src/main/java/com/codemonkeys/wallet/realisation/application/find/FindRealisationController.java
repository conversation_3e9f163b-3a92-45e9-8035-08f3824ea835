/* (C)2024-2025 */
package com.codemonkeys.wallet.realisation.application.find;

import com.codemonkeys.wallet.common.framework.security.Permission;
import com.codemonkeys.wallet.common.framework.security.Permissions;
import com.codemonkeys.wallet.computation.application.pipeline.service.RecalculationService;
import com.codemonkeys.wallet.domain.agreement.Agreement;
import com.codemonkeys.wallet.domain.contract.EnergyContract;
import com.codemonkeys.wallet.domain.contract.GasContract;
import com.codemonkeys.wallet.domain.contract.PropertyRightContract;
import com.codemonkeys.wallet.domain.wallets.MonthProducts;
import com.codemonkeys.wallet.domain.wallets.MonthTranches;
import com.codemonkeys.wallet.domain.wallets.Product;
import com.codemonkeys.wallet.domain.wallets.RealisationDetails;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.WalletRepository;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.RealisationType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.realisation.application.common.RealisationConstants;
import com.google.common.collect.Maps;
import jakarta.persistence.EntityExistsException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for retrieving realisation details for wallets.
 *
 * <p>This controller provides endpoints to fetch information about contract realisations, including
 * volumes, prices, and other metrics organized by realisation types.
 */
@Slf4j
@RestController
@RequestMapping(RealisationConstants.PATH)
@AllArgsConstructor(onConstructor = @__(@Autowired))
public class FindRealisationController {

  private final WalletRepository walletRepository;
  private final RecalculationService recalculationService;

  /**
   * Populates realisation details for a specific realisation type.
   *
   * <p>This method extracts tranches and products for each month in the year for the given
   * realisation type and adds them to the result map.
   *
   * @param realisationType The type of realisation to process
   * @param wallet The wallet containing the realisation data
   * @param result The map to populate with realisation details
   */
  private void provideRealisationForType(
      RealisationType realisationType,
      Wallet wallet,
      Map<RealisationType, MonthRealisationDetail> result) {
    boolean isPresent = false;
    MonthTranches tranches = wallet.getMonthTranches(classForRealisationType(realisationType));
    MonthProducts products = wallet.getMonthProducts(mediaForRealisationType(realisationType));
    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      List<Tranche> monthTranches = tranches.get(month, classForRealisationType(realisationType));
      List<Product> monthProducts = products.get(month, mediaForRealisationType(realisationType));
      if (!monthProducts.isEmpty()) {
        isPresent = true;
      }
      tranches.add(month, monthTranches);
      products.add(month, monthProducts);
    }
    result.put(realisationType, new MonthRealisationDetail(tranches, products, isPresent));
  }

  /**
   * Maps a realisation type to its corresponding media type.
   *
   * <p>This method provides the same functionality as {@link RealisationType#toMedia()} but is
   * implemented locally for historical reasons.
   *
   * @param type The realisation type to convert
   * @return The corresponding media type
   */
  private Media mediaForRealisationType(RealisationType type) {
    return switch (type) {
      case ENERGY -> Media.ENERGY;
      case GAS -> Media.GAS;
        // TODO: Tutaj celowo uproszczenie, bo nawet nie mielismy zadnych przykladow z
        // bialych/niebieskich certyfiaktow.
        // Ten enum wystepuje tylko po to zeby na froncie podzielic wyswietlanie na taby.
        // Może warto do tego podejść w inny sposób?
      case PROPERTY_RIGHT -> Media.GREEN_PROPERTY_RIGHTS;
    };
  }

  /**
   * Maps a realisation type to its corresponding contract class.
   *
   * <p>This method provides the same functionality as {@link RealisationType#getContractType()} but
   * is implemented locally for historical reasons.
   *
   * @param type The realisation type to convert
   * @return The corresponding contract class
   */
  private Class<?> classForRealisationType(RealisationType type) {
    return switch (type) {
      case ENERGY -> EnergyContract.class;
      case GAS -> GasContract.class;
      case PROPERTY_RIGHT -> PropertyRightContract.class;
    };
  }

  /**
   * Retrieves realisation details for a wallet.
   *
   * <p>This endpoint returns detailed information about contract realisations for all realisation
   * types in the specified wallet.
   *
   * @param id The ID of the wallet to retrieve realisation details for
   * @return A response containing realisation details organized by realisation type
   * @throws EntityExistsException if the wallet with the specified ID does not exist
   */
  @GetMapping("/{id}")
  @Permission(Permissions.VIEW_WALLET_REALISATION)
  public ResponseEntity<Map<RealisationType, MonthRealisationDetail>> realisation(
      @PathVariable @NonNull WalletId id) {
    Wallet wallet = walletRepository.findById(id).orElseThrow(EntityExistsException::new);
    Map<RealisationType, MonthRealisationDetail> result = Maps.newHashMap();
    for (RealisationType realisationType : RealisationType.values()) {
      provideRealisationForType(realisationType, wallet, result);
    }
    return ResponseEntity.ok(result);
  }

  /**
   * Retrieves version 2 of realisation details for a wallet.
   *
   * <p>This endpoint returns a more structured representation of realisation details, organized by
   * media type rather than realisation type, and includes presence information.
   *
   * @param id The ID of the wallet to retrieve realisation details for
   * @return A response containing realisation details organized by media type
   * @throws EntityExistsException if the wallet with the specified ID does not exist
   */
  @GetMapping("/v2/{id}")
  @Permission(Permissions.VIEW_WALLET_REALISATION)
  public ResponseEntity<Map<String, Object>> V2Realisation(@PathVariable @NonNull WalletId id) {
    Wallet wallet = recalculationService.recalculate(id);
    // Wallet wallet = walletRepository.findById(id).orElseThrow(EntityExistsException::new);
    Agreement agreement = wallet.getAgreement();
    RealisationDetails details = wallet.getRealisationDetails();
    Map<String, Object> response = new HashMap<>();
    for (RealisationType realisationType : RealisationType.values()) {
      Class<?> contractClass = realisationType.getContractType();
      Media media = realisationType.toMedia();
      response.put(
          media.name(),
          Map.of(
              "isPresent",
              agreement.hasContractOfType(contractClass),
              "details",
              details.get(media)));
    }
    return ResponseEntity.ok(response);
  }
}
