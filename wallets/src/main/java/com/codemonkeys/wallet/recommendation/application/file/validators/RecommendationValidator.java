/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.file.validators;

import com.codemonkeys.wallet.common.ValidationContext;
import com.codemonkeys.wallet.common.ValidationService;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.domain.contract.enums.PriceReference;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.recommendation.application.create.CreateRecommendationRequest;
import com.codemonkeys.wallet.recommendation.application.file.RecommendationImportRequest;
import com.codemonkeys.wallet.recommendation.application.file.utils.FileUploadError;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RecommendationValidator {

  private final AgreementValidator agreementValidator;
  private final ValidationService validationService;

  public boolean validateBusinessAndAddErrors(
      RecommendationImportRequest dto, List<FileUploadError> errors, int rowIndex) {
    CreateRecommendationRequest createRequest;

    try {
      createRequest = mapToCreateRecommendationRequest(dto);
    } catch (IllegalArgumentException | NullPointerException ex) {
      errors.add(new FileUploadError(rowIndex, ex.getMessage()));
      return true;
    }

    if (createRequest.getTimeUnit() == null || createRequest.getTimeUnit().isEmpty()) {
      agreementValidator
          .findContractByAgreementIdAndContract(
              createRequest.getContractId().get(0), createRequest.getContract())
          .ifPresent(
              contract -> {
                if (contract.getName().getTimeUnit() != null) {
                  createRequest.setTimeUnit(contract.getName().getTimeUnit().name());
                }
              });
    }

    try {
      validationService.validate(ValidationContext.RECOMMENDATION, createRequest);
      return false;
    } catch (ValidationFailedException ex) {
      if (ex.getMessages() != null && !ex.getMessages().isEmpty()) {
        ex.getMessages().forEach(msg -> errors.add(new FileUploadError(rowIndex, msg.getReason())));
      } else {
        errors.add(new FileUploadError(rowIndex, ex.getMessage()));
      }
      return true;
    }
  }

  public CreateRecommendationRequest mapToCreateRecommendationRequest(
      RecommendationImportRequest dto) {

    String purchaseMethod = dto.getPurchaseMethod();
    PriceReference priceReference;
    try {
      priceReference = PriceReference.valueOf(purchaseMethod.toUpperCase());
    } catch (IllegalArgumentException e) {
      throw new IllegalArgumentException(STR."Nieprawidłowy sposób zakupu: \{purchaseMethod}");
    }

    return new CreateRecommendationRequest(
        List.of(dto.getContractId()),
        dto.getContract(),
        null,
        dto.getDeadline(),
        dto.getPrice(),
        dto.getVolume(),
        priceReference,
        null,
        false,
        false,
        dto.getEmailComment(),
        "",
        RecommendationStatus.NEW,
        dto.getAgreementGroup(),
        dto.getTimeUnit());
  }
}
