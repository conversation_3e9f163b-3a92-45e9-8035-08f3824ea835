/* (C)2024-2025 */
package com.codemonkeys.wallet.recommendation.application.common.validation;

import com.codemonkeys.wallet.common.SpotContractSkipper;
import com.codemonkeys.wallet.common.ValidationHandler;
import com.codemonkeys.wallet.common.VolumeVerificationService;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import com.codemonkeys.wallet.recommendation.application.create.CreateRecommendationRequest;
import com.codemonkeys.wallet.recommendation.application.update.UpdateRecommendationRequest;
import jakarta.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.springframework.stereotype.Component;

/**
 * Handles volume validation for different types of recommendation requests using a chain of
 * responsibility pattern.
 *
 * <p>This handler validates volumes across multiple dimensions including:
 *
 * <ul>
 *   <li><Wallet volumes /li>
 *   <li>Recommendation volumes
 *   <li>Media volumes
 * </ul>
 *
 * @param <T> The type of request to be validated
 */
@Component
public class VolumeHandler<T> implements ValidationHandler<T> {
  private final VolumeVerificationService volumeVerificationService;
  private final ContractRepository contractRepository;
  private ValidationHandler<T> nextHandler;

  /**
   * Constructs a VolumeHandler with a specific volume verifier.
   *
   * @param volumeVerificationService Service responsible for performing volume validations
   */
  public VolumeHandler(
      VolumeVerificationService volumeVerificationService, ContractRepository contractRepository) {
    this.volumeVerificationService = volumeVerificationService;
    this.contractRepository = contractRepository;
  }

  /**
   * Sets the next handler in the validation chain.
   *
   * @param nextHandler Subsequent validation handler to be executed
   * @return The next handler in the chain
   */
  @Override
  public ValidationHandler<T> setNext(ValidationHandler<T> nextHandler) {
    this.nextHandler = nextHandler;
    return nextHandler;
  }

  /**
   * Validates request volumes across different agreement contexts.
   *
   * <p>Performs comprehensive volume checks including:
   *
   * <ul>
   *   <li>Wallet volume validation
   *   <li>Recommendation volume validation
   *   <li>Media volume validation
   * </ul>
   *
   * @param request The request to validate
   * @throws ValidationFailedException If any volume validation fails
   */
  @Override
  public void validate(T request) throws ValidationFailedException {

    if (SpotContractSkipper.isSpotContract(request)) {
      if (nextHandler != null) nextHandler.validate(request);
      return;
    }

    RequestData requestData = extractRequestData(request);
    for (UUID id : requestData.agreementIds) {
      AgreementId agreementId = AgreementId.of(id);
      Contract contract =
          contractRepository
              .findByAgreementIdAndName(agreementId, requestData.contractName())
              .orElseThrow(EntityNotFoundException::new);
      volumeVerificationService.validateMediaVolume(
          agreementId, requestData.toBeBoughtVolume, contract.getClass());
      volumeVerificationService.validateWalletsVolume(
          agreementId, requestData.toBeBoughtVolume, requestData.timeUnit, contract.getClass());
      volumeVerificationService.validateRecommendationVolume(
          agreementId,
          requestData.toBeBoughtVolume,
          requestData.beforeUpdateVolume,
          requestData.timeUnit,
          contract.getClass());
    }
    if (nextHandler != null) {
      nextHandler.validate(request);
    }
  }

  /**
   * Extracts standardized request data from different request types.
   *
   * <p>Supports:
   *
   * <ul>
   *   <li>CreateRecommendationRequest
   *   <li>UpdateRecommendationRequest
   * </ul>
   *
   * @param request The input request to extract data from
   * @return Standardized RequestData containing validation details
   * @throws IllegalArgumentException For unsupported request types
   */
  private RequestData extractRequestData(T request) {
    return switch (request) {
      case CreateRecommendationRequest createRequest ->
          new RequestData(
              createRequest.getContractId(),
              createRequest.getContract(),
              volumeVerificationService.parseVolume(createRequest.getVolume()),
              BigDecimal.ZERO,
              TimeUnit.valueOf(createRequest.getTimeUnit().toUpperCase()));
      case UpdateRecommendationRequest updateRequest -> {
        BigDecimal beforeUpdateVolume =
            volumeVerificationService.getVolumeBeforeUpdate(
                UUID.fromString(updateRequest.getId().getId()));
        yield new RequestData(
            updateRequest.getContractId(),
            updateRequest.getContract(),
            volumeVerificationService.parseVolume(updateRequest.getVolume()),
            beforeUpdateVolume,
            TimeUnit.valueOf(updateRequest.getTimeUnit().toUpperCase()));
      }
      default -> throw new IllegalArgumentException("Unsupported request type");
    };
  }

  /**
   * Represents standardized request data for volume validation.
   *
   * <p>Encapsulates key information needed for volume verification across different request types.
   *
   * @param agreementIds List of agreement identifiers
   * @param contractName Name of the contract
   * @param toBeBoughtVolume Volume to be purchased
   * @param beforeUpdateVolume Volume before potential update
   * @param timeUnit Time unit for volume calculation
   */
  private record RequestData(
      List<UUID> agreementIds,
      String contractName,
      BigDecimal toBeBoughtVolume,
      BigDecimal beforeUpdateVolume,
      TimeUnit timeUnit) {}
}
