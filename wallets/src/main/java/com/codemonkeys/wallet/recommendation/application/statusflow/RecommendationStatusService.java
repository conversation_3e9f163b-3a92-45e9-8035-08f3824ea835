/* (C)2025 */
package com.codemonkeys.wallet.recommendation.application.statusflow;

import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.RecommendationRepository;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationAction;
import com.codemonkeys.wallet.domain.recommendation.vo.RecommendationId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service class responsible for handling status-related actions on recommendations. This service
 * coordinates the execution of actions and updates the status of recommendations accordingly.
 */
@Service
public class RecommendationStatusService {

  private final RecommendationRepository recommendationRepository;
  private final List<RecommendationActionHandler> actionHandlers;

  @Autowired
  public RecommendationStatusService(
      RecommendationRepository recommendationRepository,
      List<RecommendationActionHandler> actionHandlers) {
    this.recommendationRepository = recommendationRepository;
    this.actionHandlers = actionHandlers;
  }

  /**
   * Handles the specified action on a list of recommendations. The method finds the appropriate
   * handler for the action and applies it to each recommendation.
   *
   * @param recommendationIds the list of recommendation IDs to which the action should be applied.
   * @param action the action to be performed on the recommendations.
   * @throws IllegalArgumentException if any recommendation is not found or if the action is
   *     unsupported.
   * @throws IllegalStateException if the action fails to be performed on any recommendation.
   */
  // @Transactional
  public List<RecommendationActionResponse> handleActions(
      List<RecommendationId> recommendationIds, RecommendationAction action) {
    List<RecommendationActionResponse> responses = new ArrayList<>();

    if (recommendationIds.size() > 1 && action == RecommendationAction.SEND) {
      return handleBatchActions(recommendationIds, action); // batch send recommendation
    }

    for (RecommendationId recommendationId : recommendationIds) {
      Optional<Recommendation> recommendationOpt =
          recommendationRepository.findById(recommendationId);
      if (recommendationOpt.isEmpty()) {
        responses.add(
            new RecommendationActionResponse(recommendationId, false, "Recommendation not found"));
        continue;
      }
      Recommendation recommendation = recommendationOpt.get();

      try {
        RecommendationActionHandler handler =
            actionHandlers.stream()
                .filter(h -> h.getAction().equals(action))
                .filter(h -> !(h instanceof RecommendationBatchActionHandler))
                .findFirst()
                .orElseThrow(
                    () ->
                        new IllegalArgumentException(
                            String.format("Unsupported action: %s", action)));
        boolean success = handler.handle(recommendation);
        recommendationRepository.save(recommendation);
        String message =
            success
                ? "Akcja zakończyła się pomyślnie"
                : switch (recommendation.getErrorCode()) {
                  case INVALID_PRICE_FORMAT -> "Pole cena powinna być liczbowa";
                  default -> "Akcja nie powiodła się";
                };
        responses.add(new RecommendationActionResponse(recommendationId, success, message));
      } catch (Exception e) {
        responses.add(new RecommendationActionResponse(recommendationId, false, e.getMessage()));
      }
    }

    return responses;
  }

  /**
   * Handles batch processing of multiple recommendations. This method finds and applies the
   * appropriate batch action handler for the specified recommendations, ensuring that all updates
   * are executed consistently.
   *
   * @param recommendationIds the list of recommendation IDs to be processed in batch
   * @param action the action to be performed on the recommendations
   * @return a list of {@link RecommendationActionResponse} containing the results of the batch
   *     processing
   * @throws IllegalArgumentException if no suitable batch handler is found for the specified action
   */
  @Transactional
  public List<RecommendationActionResponse> handleBatchActions(
      List<RecommendationId> recommendationIds, RecommendationAction action) {
    List<RecommendationActionResponse> responses = new ArrayList<>();
    List<Recommendation> recommendations = recommendationRepository.findAllById(recommendationIds);

    if (recommendations.isEmpty()) {
      return List.of(
          new RecommendationActionResponse(
              null, false, "No recommendations found for batch processing"));
    }

    try {
      RecommendationBatchActionHandler batchHandler =
          actionHandlers.stream()
              .filter(h -> h instanceof RecommendationBatchActionHandler)
              .map(h -> (RecommendationBatchActionHandler) h)
              .findFirst()
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          String.format("Unsupported batch action: %s", action)));

      boolean success = batchHandler.handleBatch(recommendations);
      recommendations.forEach(recommendationRepository::save);

      String message =
          success ? "Grupowa akcja zakończyła się pomyślnie" : "Grupowa akcja nie powiodła się";
      recommendations.forEach(
          rec -> responses.add(new RecommendationActionResponse(rec.getId(), success, message)));

    } catch (Exception e) {
      recommendations.forEach(
          rec ->
              responses.add(new RecommendationActionResponse(rec.getId(), false, e.getMessage())));
    }

    return responses;
  }
}
