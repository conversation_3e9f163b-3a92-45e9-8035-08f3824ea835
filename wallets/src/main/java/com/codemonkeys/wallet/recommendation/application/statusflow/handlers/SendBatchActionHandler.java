/* (C)2025 */
package com.codemonkeys.wallet.recommendation.application.statusflow.handlers;

import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerContact;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationAction;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.email.event.EmailEvent;
import com.codemonkeys.wallet.recommendation.application.statusflow.EmailTemplateDataPreparer;
import com.codemonkeys.wallet.recommendation.application.statusflow.RecommendationBatchActionHandler;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SendBatchActionHandler extends AbstractRecommendationHandler
    implements RecommendationBatchActionHandler {

  public SendBatchActionHandler(
      CustomerRepository customerRepository,
      EmailTemplateDataPreparer emailTemplateDataPreparer,
      ApplicationEventPublisher eventPublisher) {
    super(customerRepository, emailTemplateDataPreparer, eventPublisher);
  }

  @Override
  public RecommendationAction getAction() {
    return RecommendationAction.SEND;
  }

  @Override
  public boolean handle(Recommendation recommendation) {
    throw new UnsupportedOperationException(
        "Single recommendation handling is not supported in batch handler");
  }

  @Override
  public boolean handleBatch(List<Recommendation> recommendations) {
    // grupowanie według id umowy i nazwy kontraktu
    Map<UUID, Map<String, List<Recommendation>>> groupedRecommendations =
        recommendations.stream()
            .collect(
                Collectors.groupingBy(
                    Recommendation::getContractId,
                    Collectors.groupingBy(Recommendation::getContract)));

    boolean allSuccess = true;
    int totalEmailsSent = 0;

    for (var contractRecommendations : groupedRecommendations.values()) {
      for (var recommendationsForContract : contractRecommendations.values()) {
        boolean success = processContractRecommendations(recommendationsForContract);
        if (!success) {
          allSuccess = false;
        } else {
          totalEmailsSent++;
        }
      }
    }

    log.info(
        "Batch processing completed. Total emails sent: {}. Success: {}",
        totalEmailsSent,
        allSuccess);
    return allSuccess;
  }

  /**
   * Processes a list of recommendations for a single contract.
   *
   * @param recommendationsForContract list of recommendations for a specific contract
   * @return {@code true} if emails were sent successfully, {@code false} otherwise.
   */
  private boolean processContractRecommendations(List<Recommendation> recommendationsForContract) {
    List<String> uniqueComments = mergeUniqueComments(recommendationsForContract);
    Customer customer =
        fetchCustomer(
            recommendationsForContract.get(0),
            CustomerId.of(recommendationsForContract.get(0).getCustomerId()));
    List<CustomerContact> contacts =
        validateCustomerConfiguration(getContacts(recommendationsForContract.get(0), customer));

    if (contacts.isEmpty()) {
      log.warn(
          "No valid contacts found for contract '{}'",
          recommendationsForContract.get(0).getContractId());
      return false;
    }

    contacts.forEach(
        contact -> sendEmailForBatch(contact, recommendationsForContract.get(0), uniqueComments));
    updateRecommendationStatuses(recommendationsForContract);
    return true;
  }

  /** Merges unique comments from multiple recommendations into a single string. */
  private List<String> mergeUniqueComments(List<Recommendation> recommendations) {
    return recommendations.stream()
        .map(Recommendation::getEmailTemplateComment)
        .filter(Objects::nonNull)
        .distinct()
        .sorted()
        .collect(Collectors.toList());
  }

  /** Sends an email for a batch of recommendations. */
  private void sendEmailForBatch(
      CustomerContact contact, Recommendation recommendation, List<String> uniqueComments) {
    Map<String, Object> variables =
        emailTemplateDataPreparer.prepareTemplateData(recommendation, contact);
    variables.put("recommendationComment", String.join("\n", uniqueComments));

    String subject = emailTemplateDataPreparer.determineSubject(contact, recommendation);
    String templateName = emailTemplateDataPreparer.determineTemplateName(contact);

    eventPublisher.publishEvent(
        new EmailEvent(this, contact.getEmail().getValue(), subject, templateName, variables));

    log.info(
        "Sent batch email to '{}' for contract '{}'",
        contact.getEmail().getValue(),
        recommendation.getContractId());
  }

  /** Updates the status of recommendations after processing. */
  private void updateRecommendationStatuses(List<Recommendation> recommendations) {
    recommendations.forEach(
        rec ->
            rec.changeStatus(
                rec.isRequiresCustomerAcceptance()
                    ? RecommendationStatus.SEND
                    : RecommendationStatus.ACCEPTED_ORDER_PENDING));

    log.info("Updated status for {} recommendations", recommendations.size());
  }
}
