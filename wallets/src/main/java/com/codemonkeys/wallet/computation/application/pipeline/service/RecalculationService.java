/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.service;

import an.awesome.pipelinr.Pipeline;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.PriceCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.bluepropertyrights.BluePropertyRightsCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.correction_factor.CorrectionFactorCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.correction_factor.GreenPropertyRightsHiddenCorrectionFactorCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.correction_factor.HiddenCorrectionFactorCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.costs.CostsCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.excise.ExciseCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.GreenPropertyRightsElementCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.GreenPropertyRightsTrancheCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.multiplier.MultiplierCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.net.NetEnergyCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.net.NetGasCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.profile.ProfileCostCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.profile.ProfilePercentCostCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.total.TotalCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.totalpropertyrights.TotalPropertyRightsCommand;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.whitepropertyrights.WhitePropertyRightsCommand;
import com.codemonkeys.wallet.computation.application.pipeline.provider.PipelineFactory;
import com.codemonkeys.wallet.domain.agreement.AgreementRepository;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.wallets.*;
import com.codemonkeys.wallet.domain.wallets.vo.PriceId;
import com.codemonkeys.wallet.domain.wallets.vo.ProductId;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import com.codemonkeys.wallet.product.application.pipeline.handlers.ProductCommand;
import com.codemonkeys.wallet.product.application.pipeline.handlers.marketmean.EnergyMarketMeanCommand;
import com.codemonkeys.wallet.product.application.pipeline.handlers.marketmean.GasMarketMeanCommand;
import com.codemonkeys.wallet.product.application.pipeline.handlers.marketmean.PropertyRightsMarketMeanCommand;
import com.codemonkeys.wallet.product.application.pipeline.handlers.volume.EnergyVolumeCommand;
import com.codemonkeys.wallet.product.application.pipeline.handlers.volume.GasVolumeCommand;
import com.codemonkeys.wallet.product.application.pipeline.handlers.volume.PropertyRightsVolumeCommand;
import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class RecalculationService {
  private final PipelineFactory pipelineFactory;
  private final WalletRepository walletRepository;
  private final AgreementRepository agreementRepository;
  private final Pipeline productPipeline;
  private final Pipeline pricePipeline;
  private final WalletPriceRepository priceRepository;
  private final ProductRepository productRepository;

  @Autowired
  public RecalculationService(
      PipelineFactory pipelineFactory,
      WalletRepository walletRepository,
      WalletPriceRepository priceRepository,
      ProductRepository productRepository,
      AgreementRepository agreementRepository,
      @Qualifier("productPipeline") Pipeline productPipeline,
      @Qualifier("pricePipeline") Pipeline pricePipeline) {
    this.pipelineFactory = pipelineFactory;
    this.walletRepository = walletRepository;
    this.priceRepository = priceRepository;
    this.productRepository = productRepository;
    this.productPipeline = productPipeline;
    this.pricePipeline = pricePipeline;
    this.agreementRepository = agreementRepository;
  }

  /**
   * Method that returns current price calculation command list
   *
   * @param wallet
   * @return
   */
  List<? extends PriceCommand> priceCommands(Wallet wallet) {
    return List.of(
        new GreenPropertyRightsElementCommand(wallet),
        new GreenPropertyRightsTrancheCommand(wallet),
        new BluePropertyRightsCommand(wallet),
        new WhitePropertyRightsCommand(wallet),
        new TotalPropertyRightsCommand(wallet),
        new CostsCommand(wallet),
        new ExciseCommand(wallet),
        new NetEnergyCommand(wallet),
        new NetGasCommand(wallet),
        new ProfileCostCommand(wallet),
        new MultiplierCommand(wallet),
        new ProfilePercentCostCommand(wallet),
        new CorrectionFactorCommand(wallet),
        new TotalCommand(wallet),
        new HiddenCorrectionFactorCommand(wallet),
        new GreenPropertyRightsHiddenCorrectionFactorCommand(wallet));
  }

  /**
   * Method that returns current product calculation command list
   *
   * @param wallet
   * @return
   */
  List<? extends ProductCommand> productCommands(Wallet wallet) {
    return List.of(
        new EnergyVolumeCommand(wallet),
        new EnergyMarketMeanCommand(wallet),
        new GasVolumeCommand(wallet),
        new GasMarketMeanCommand(wallet),
        new PropertyRightsVolumeCommand(wallet),
        new PropertyRightsMarketMeanCommand(wallet));
  }

  public void recalculateAll(List<UUID> walletUUIDids) {
    List<WalletId> walletIds = walletUUIDids.stream().map(WalletId::of).toList();
    List<Wallet> wallets = walletRepository.findAllById(walletIds);
    wallets.forEach(this::recalculate);
  }

  public void recalculateAllByWalletIds(List<WalletId> walletIds) {
    List<Wallet> wallets = walletRepository.findAllById(walletIds);
    wallets.forEach(this::recalculate);
  }

  /**
   * Methods recalculates wallet prices and products based on current state of the wallet. Method
   * uses two helper methods for each type of pipeline.
   *
   * @param id WalletId
   * @return wallet with modified prices and products
   */
  public Wallet recalculate(WalletId id) {
    Wallet wallet = walletRepository.findById(id).orElseThrow(EntityNotFoundException::new);
    return recalculate(wallet);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  protected void clearCurrentData(Wallet wallet) {
    List<PriceId> priceIds =
        (wallet.getPrices().stream()
            .filter(p -> !p.getConfirmed())
            .map(WalletPrice::getId)
            .toList());
    priceRepository.deleteAllById(priceIds);
    List<ProductId> productIds =
        wallet.getProducts().stream()
            .filter(product -> !product.isConfirmed())
            .map(Product::getId)
            .toList();
    productRepository.deleteAllById(productIds);
    wallet.getPrices().removeIf(p -> !p.getConfirmed());
    wallet.getProducts().removeIf(product -> !product.isConfirmed());
    walletRepository.save(wallet);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  protected void recalculateData(Wallet wallet) {
    prices(wallet);
    products(wallet);
    walletRepository.save(wallet);
  }

  /**
   * Methods recalculates wallet prices and products based on current state of the wallet. Method
   * uses two helper methods for each type of pipeline.
   *
   * @param wallet Wallet
   * @return wallet with modified prices and products
   */
  public Wallet recalculate(Wallet wallet) {
    clearCurrentData(wallet);
    recalculateData(wallet);
    return wallet;
  }

  /**
   * Simulates recalculation of the given wallet by creating a copy, clearing its prices and
   * products, and applying price and product simulations.
   *
   * @param wallet the wallet to simulate and recalculate
   * @return a new Wallet instance with simulated values
   */
  public Wallet simulateRecalculation(Wallet wallet) {
    Wallet simulatedWallet = new Wallet(wallet);
    simulatedWallet.getPrices().clear();
    simulatedWallet.getProducts().clear();
    prices(simulatedWallet);
    products(simulatedWallet);
    return simulatedWallet;
  }

  /**
   * Helper method to create and execute price pipeline
   *
   * @param wallet
   */
  private void prices(Wallet wallet) {
    List<? extends PriceCommand> priceCommands = priceCommands(wallet);
    priceCommands.forEach(c -> c.execute(pricePipeline));
  }

  /**
   * Helper method to create and execute products pipeline
   *
   * @param wallet
   */
  private void products(Wallet wallet) {
    List<? extends ProductCommand> productCommands = productCommands(wallet);
    productCommands.forEach(c -> c.execute(productPipeline));
  }

  public void recalculateAllByAgreementIds(List<AgreementId> agreementsIds) {
    for (AgreementId id : agreementsIds) {
      List<WalletId> wallets = walletRepository.findWalletIds(id);
      for (WalletId wId : wallets) {
        recalculate(wId);
      }
    }
  }
}
