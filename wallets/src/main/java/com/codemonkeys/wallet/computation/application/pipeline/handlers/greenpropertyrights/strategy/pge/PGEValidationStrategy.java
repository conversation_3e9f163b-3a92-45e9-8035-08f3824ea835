/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.pge;

import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.BaseValidationStrategy;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.util.Optional;

public class PGEValidationStrategy extends BaseValidationStrategy {

  protected void validateCJ(Wallet wallet) {
    Optional<Element> Q1 = wallet.getElement(ElementType.LAST_CJ, TimeUnit.Q1);
    Optional<Element> Q2 = wallet.getElement(ElementType.LAST_CJ, TimeUnit.Q2);
    Optional<Element> Q3 = wallet.getElement(ElementType.LAST_CJ, TimeUnit.Q3);
    Optional<Element> Q4 = wallet.getElement(ElementType.LAST_CJ, TimeUnit.Q4);
    // If they are empty then we do not have any LAST_CJ element which is wrong.
    if (Q1.isEmpty() && Q2.isEmpty() && Q3.isEmpty() && Q4.isEmpty()) {
      throw ValidationFailedException.of("elements", "validations.elements.cj");
    }
  }

  @Override
  public void validateOrThrow(Wallet wallet) {
    validateCJ(wallet);
  }
}
