/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.GreenPropertyRightsConstants;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.PriceMetadata;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class CalculationStrategy implements GreenPropertyRightsCalculationStrategy {

  protected static final int PRICE_DECIMAL_SCALE = 2;
  protected static final String DUTY_METADATA_KEY = "DUTY";
  protected static final String WIBOR_RATE_METADATA_KEY = "WIBOR_RATE";
  protected static final String MONTH_METADATA_KEY = "MONTH_FRACTION";
  protected static final String WIBOR_PERCENT_METADATA_KEY = "WIBOR_PERCENT";
  protected final PriceMetadata metadata = new PriceMetadata();

  protected BigDecimal applyCosts(BigDecimal price, Wallet wallet) {
    if (price == null || wallet == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    Optional<Element> elementOptional = wallet.getElement(ElementType.GREEN_PROPERTY_RIGHTS_COSTS);
    if (elementOptional.isPresent()) {
      Element costs = elementOptional.get();
      price = price.add(costs.getValue());
    }
    return price;
  }

  protected BigDecimal applyExcise(BigDecimal price, Wallet wallet) {
    if (price == null || wallet == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    Optional<Element> elementOptional = wallet.getElement(ElementType.EXCISE);
    if (elementOptional.isPresent()) {
      Element excise = elementOptional.get();
      price = price.subtract(excise.getValue());
    }
    return price;
  }

  protected BigDecimal applyDuty(BigDecimal price, Integer year) {
    if (price == null || year == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    return price.multiply(GreenPropertyRightsConstants.getDuty(year));
  }

  protected BigDecimal getDuty(Integer year) {
    return GreenPropertyRightsConstants.getDuty(year);
  }

  protected void handleDutyMetadata(Integer year) {
    metadata.put(DUTY_METADATA_KEY, getDuty(year));
  }

  protected BigDecimal applyIndustrialStatusDiscount(BigDecimal price, Wallet wallet) {
    if (wallet == null || price == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    Optional<Element> elementOptional = wallet.getElement(ElementType.INDUSTRIAL_STATUS_DISCOUNT);

    if (elementOptional.isPresent()) {
      Element element = elementOptional.get();
      // TODO: może warto napisać metodę getValue switchujaca po ElementType
      // żeby nie wrzucać podobnych kawałków w całej aplikacji. Do przegadania na CR albo teams
      BigDecimal elementValue = element.getValue();
      BigDecimal multiplier = elementValue.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

      log.debug("CalculationStrategy->applyIndustrialStatusDiscount->Multiplier: {}", multiplier);
      log.debug(
          "CalculationStrategy->applyIndustrialStatusDiscount->PriceBeforeDiscount: {}", price);
      price = price.multiply(multiplier);
      log.debug(
          "CalculationStrategy->applyIndustrialStatusDiscount->PriceAfterDiscount: {}", price);
    }

    return price.setScale(PRICE_DECIMAL_SCALE, RoundingMode.HALF_UP);
  }

  protected BigDecimal getSupplierElement(Tranche tranche, ElementType elementType) {
    if (tranche == null || tranche.getExecutionDate() == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    TimeUnit tm = TimeUnit.monthNumberToQuarter(tranche.getExecutionDate().getMonthValue());
    Wallet wallet = tranche.getWallet();
    Optional<Element> elementOptional = wallet.getElement(elementType, tm);
    if (elementOptional.isPresent()) {
      return elementOptional.get().getValue();
    }
    return BigDecimal.ONE;
  }

  protected BigDecimal applySupplierDiscount(BigDecimal price, Tranche tranche) {
    if (tranche == null || price == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    BigDecimal supplierDiscount;
    if (tranche.getExecutionDate().getYear() == tranche.getYear()) {
      supplierDiscount = getSupplierElement(tranche, ElementType.SUPPLIER_DISCOUNT_PRICE);
    } else {
      supplierDiscount = getSupplierElement(tranche, ElementType.LAST_SUPPLIER_DISCOUNT_PRICE);
    }
    return price.subtract(supplierDiscount);
  }

  protected BigDecimal applySupplierServicePrice(BigDecimal price, Tranche tranche) {
    if (tranche == null || price == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    BigDecimal servicePrice;
    if (tranche.getExecutionDate().getYear() == tranche.getYear()) {
      servicePrice = getSupplierElement(tranche, ElementType.SUPPLIER_SERVICE_PRICE);
    } else {
      servicePrice = getSupplierElement(tranche, ElementType.LAST_SUPPLIER_SERVICE_PRICE);
    }
    return price.add(servicePrice);
  }

  protected BigDecimal basePrice(
      List<Tranche> tranches, Function<Tranche, BigDecimal> priceCalculator) {
    if (priceCalculator == null) {
      return BigDecimal.valueOf(Integer.MIN_VALUE);
    }
    return tranches.stream()
        .map(priceCalculator)
        .reduce(BigDecimal.ZERO, BigDecimal::add)
        .setScale(PRICE_DECIMAL_SCALE, RoundingMode.HALF_UP);
  }
}
