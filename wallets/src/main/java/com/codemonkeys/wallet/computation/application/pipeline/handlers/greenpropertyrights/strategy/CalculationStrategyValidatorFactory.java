/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.enea.EneaValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa.EnergaComplexValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa.EnergaSimpleValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.eon.EONValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.pge.PGEValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.tiew.TIEWValidationStrategy;
import com.codemonkeys.wallet.domain.wallets.enums.GreenPropertyRightCalculationType;
import org.springframework.stereotype.Service;

@Service
public class CalculationStrategyValidatorFactory {
  private final EneaValidationStrategy eneaValidationStrategy;
  private final EONValidationStrategy eonValidationStrategy;
  private final EnergaComplexValidationStrategy energaComplexValidationStrategy;
  private final EnergaSimpleValidationStrategy energaSimpleValidationStrategy;
  private final PGEValidationStrategy pgeValidationStrategy;
  private final TIEWValidationStrategy tiewValidationStrategy;

  public CalculationStrategyValidatorFactory() {
    this.eneaValidationStrategy = new EneaValidationStrategy();
    this.eonValidationStrategy = new EONValidationStrategy();
    this.energaComplexValidationStrategy = new EnergaComplexValidationStrategy();
    this.energaSimpleValidationStrategy = new EnergaSimpleValidationStrategy();
    this.pgeValidationStrategy = new PGEValidationStrategy();
    this.tiewValidationStrategy = new TIEWValidationStrategy();
  }

  public GreenPropertyRightsValidationStrategy getStrategy(GreenPropertyRightCalculationType type) {
    return switch (type) {
      case ENEA -> eneaValidationStrategy;
      case EON -> eonValidationStrategy;
      case ENERGA_COMPLEX -> energaComplexValidationStrategy;
      case ENERGA_SIMPLE -> energaSimpleValidationStrategy;
      case PGE -> pgeValidationStrategy;
      case TIEW -> tiewValidationStrategy;
    };
  }
}
