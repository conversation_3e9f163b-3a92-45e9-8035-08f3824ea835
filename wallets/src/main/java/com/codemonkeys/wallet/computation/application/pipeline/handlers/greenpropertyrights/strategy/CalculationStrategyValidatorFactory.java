/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.enea.EneaValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa.EnergaComplexValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa.EnergaSimpleValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.eon.EONValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.noop.NoOpValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.pge.PGEValidationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.tiew.TIEWValidationStrategy;
import com.codemonkeys.wallet.domain.wallets.enums.GreenPropertyRightCalculationType;
import org.springframework.stereotype.Service;

/**
 * Factory class responsible for creating and providing appropriate validation strategies for
 * different types of Green Property Rights calculations. This factory manages various validation
 * strategy implementations and returns the correct one based on the specified calculation type.
 */
@Service
public class CalculationStrategyValidatorFactory {
  private final EneaValidationStrategy eneaValidationStrategy;
  private final EONValidationStrategy eonValidationStrategy;
  private final EnergaComplexValidationStrategy energaComplexValidationStrategy;
  private final EnergaSimpleValidationStrategy energaSimpleValidationStrategy;
  private final PGEValidationStrategy pgeValidationStrategy;
  private final TIEWValidationStrategy tiewValidationStrategy;
  private final NoOpValidationStrategy noOpValidationStrategy;

  /**
   * Constructs a new CalculationStrategyValidatorFactory with all the required validation
   * strategies.
   */
  public CalculationStrategyValidatorFactory() {
    this.eneaValidationStrategy = new EneaValidationStrategy();
    this.eonValidationStrategy = new EONValidationStrategy();
    this.energaComplexValidationStrategy = new EnergaComplexValidationStrategy();
    this.energaSimpleValidationStrategy = new EnergaSimpleValidationStrategy();
    this.pgeValidationStrategy = new PGEValidationStrategy();
    this.tiewValidationStrategy = new TIEWValidationStrategy();
    this.noOpValidationStrategy = new NoOpValidationStrategy();
  }

  /**
   * Returns the appropriate validation strategy based on the specified Green Property Right
   * calculation type.
   *
   * @param type The type of Green Property Right calculation to be validated
   * @return The corresponding validation strategy implementation
   */
  public GreenPropertyRightsValidationStrategy getStrategy(GreenPropertyRightCalculationType type) {
    return switch (type) {
      case ENEA -> eneaValidationStrategy;
      case EON -> eonValidationStrategy;
      case ENERGA_COMPLEX -> energaComplexValidationStrategy;
      case ENERGA_SIMPLE -> energaSimpleValidationStrategy;
      case PGE -> pgeValidationStrategy;
      case TIEW -> tiewValidationStrategy;
      case OTHER -> noOpValidationStrategy;
    };
  }
}
