/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.CalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.GreenPropertyRightsCalculationStrategy;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.PriceMetadata;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

// TODO out of tests
public class EnergaComplexCalculationStrategy extends CalculationStrategy
    implements GreenPropertyRightsCalculationStrategy {
  private static final BigDecimal WIBOR_MARGIN = BigDecimal.valueOf(1.5);
  private static final int SCALE = 2;
  private static final int END_MONTH = 6;
  private static final int END_DAY = 30;
  private static final String WIBOR_CONTRACT_NAME = "WIBOR1R";
  private final PriceRepository priceRepository;
  private PriceMetadata priceMetadata;

  public EnergaComplexCalculationStrategy(PriceRepository priceRepository) {
    this.priceRepository = priceRepository;
    this.priceMetadata = new PriceMetadata();
  }

  @Override
  public PriceMetadata getMetadata() {
    return priceMetadata;
  }

  @Override
  public Optional<BigDecimal> price(List<Tranche> tranches) {
    if (tranches.isEmpty()) {
      return Optional.empty();
    }
    Wallet wallet = tranches.getFirst().getWallet();
    BigDecimal basePrice = basePrice(tranches, Tranche::product);
    BigDecimal marginPrice = applyMargin(basePrice, wallet);
    BigDecimal wiborPrice = applyWibor(marginPrice, tranches);
    BigDecimal costPrice = applyCosts(wiborPrice, wallet);
    BigDecimal excisePrice = applyExcise(costPrice, wallet);
    BigDecimal dutyPrice = applyDuty(excisePrice, wallet.getYear().getYearAsInt());
    return Optional.of(applyIndustrialStatusDiscount(dutyPrice, wallet));
  }

  private BigDecimal applyMargin(BigDecimal price, Wallet wallet) {
    Optional<Element> elementOptional = wallet.getElement(ElementType.MARGIN);
    if (elementOptional.isPresent()) {
      Element margin = elementOptional.get();
      price = price.add(margin.getValue());
    }
    return price;
  }

  private BigDecimal applyWibor(BigDecimal price, List<Tranche> tranches) {
    return findEarliestTranche(tranches)
        .map(tranche -> calculateWiborMultiplier(tranche).multiply(price))
        .orElse(price);
  }

  private Optional<Tranche> findEarliestTranche(List<Tranche> tranches) {
    return Optional.ofNullable(tranches).orElse(List.of()).stream()
        .min(Comparator.comparing(Tranche::getExecutionDate));
  }

  private BigDecimal calculateWiborMultiplier(Tranche tranche) {
    BigDecimal wiborRate = getWiborRate(tranche);
    priceMetadata.put("WIBOR_RATE", wiborRate);
    // this was usage before refactor
    BigDecimal monthFraction = calculateMonthFraction(tranche);
    // BigDecimal monthFraction = applyMonthFraction(tranche);
    priceMetadata.put("MONTH_FRACTION", monthFraction);
    return wiborRate.multiply(monthFraction).add(BigDecimal.ONE).setScale(3, RoundingMode.HALF_UP);
  }

  // todo: zostawiam, gdybyśmy jednak wrócili do innej wersji (XD)
  private BigDecimal applyMonthFraction(Tranche tranche) {
    Wallet wallet = tranche.getWallet();
    Optional<Element> mParameterOptional =
        wallet.getElement(ElementType.M_PARAMETER, tranche.getTimeUnit());
    if (mParameterOptional.isPresent()) {
      return mParameterOptional.get().getValue();
    }
    // Neutral to final price if element was not found
    return BigDecimal.ONE;
  }

  private BigDecimal getWiborRate(Tranche tranche) {
    BigDecimal wiborPercent = wibor(tranche.getExecutionDate());
    priceMetadata.put("WIBOR_PERCENT", wiborPercent);
    wiborPercent = wiborPercent.add(WIBOR_MARGIN);
    return wiborPercent.divide(BigDecimal.valueOf(100), SCALE, RoundingMode.HALF_UP);
  }

  // TODO: niech zostanie gdybyśmy jednak mieli to wyliczać automatycznie...
  private BigDecimal calculateMonthFraction(Tranche tranche) {
    LocalDate executionDate = tranche.getExecutionDate();
    int year = Integer.parseInt(tranche.getWallet().getYear().getYear());
    LocalDate endDate = LocalDate.of(year + 1, END_MONTH, END_DAY);
    BigDecimal months =
        BigDecimal.valueOf(ChronoUnit.MONTHS.between(executionDate, endDate)).add(BigDecimal.ONE);
    return months.divide(BigDecimal.valueOf(12), 0, RoundingMode.HALF_UP);
  }

  // private BigDecimal getWiborValue(Wallet wallet, TimeUnit timeUnit) {
  //  return wallet.getElements(ElementType.WIBOR, timeUnit).stream()
  //      .findFirst()
  //      .map(Element::getValue)
  //      .orElse(BigDecimal.ONE);
  // }

  public BigDecimal wibor(LocalDate date) {
    return priceRepository
        .findByContractAndDate(PriceName.of(WIBOR_CONTRACT_NAME), date)
        .getValue()
        .getValue();
  }
}
