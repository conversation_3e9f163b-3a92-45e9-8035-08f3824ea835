/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.energa;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.CalculationStrategy;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.GreenPropertyRightsCalculationStrategy;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.PriceMetadata;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

// TODO out of tests
@Slf4j
public class EnergaComplexCalculationStrategy extends CalculationStrategy
    implements GreenPropertyRightsCalculationStrategy {

  private static final BigDecimal WIBOR_MARGIN = BigDecimal.valueOf(1.5);
  private static final int END_MONTH = 6;
  private static final int END_DAY = 30;
  private static final String WIBOR_CONTRACT_NAME = "WIBOR1R";
  private final PriceRepository priceRepository;

  public EnergaComplexCalculationStrategy(PriceRepository priceRepository) {
    this.priceRepository = priceRepository;
    this.metadata = new PriceMetadata();
  }

  @Override
  public PriceMetadata getMetadata() {
    return metadata;
  }

  @Override
  public Optional<BigDecimal> price(List<Tranche> tranches) {
    if (tranches.isEmpty()) {
      return Optional.empty();
    }
    metadata = new PriceMetadata();
    Wallet wallet = tranches.getFirst().getWallet();
    handleDutyMetadata(wallet.getYear().getYearAsInt());
    BigDecimal price = BigDecimal.ZERO;
    for (Tranche tranche : tranches) {
      BigDecimal basePrice = basePrice(List.of(tranche), Tranche::getPrice);
      BigDecimal marginPrice = applyMargin(basePrice, wallet);
      // DEBUG: BigDecimal marginPricePart = applyMargin(basePrice, wallet);
      BigDecimal wiborPrice = applyWibor(marginPrice, List.of(tranche));
      // DEBUG: BigDecimal wiborPart = getWibor(tranche);
      BigDecimal costPrice = applyCosts(wiborPrice, wallet);
      BigDecimal excisePrice = applyExcise(costPrice, wallet);
      // DEBUG: BigDecimal costPart = costPrice.subtract(excisePrice);
      BigDecimal finalPrice = applyDuty(excisePrice, wallet.getYear().getYearAsInt());
      price = price.add(finalPrice.multiply(tranche.getFractionalSize()));
    }
    return Optional.of(applyIndustrialStatusDiscount(price, wallet));
  }

  private BigDecimal applyMargin(BigDecimal price, Wallet wallet) {
    Optional<Element> elementOptional = wallet.getElement(ElementType.MARGIN);
    if (elementOptional.isPresent()) {
      Element margin = elementOptional.get();
      price = price.add(margin.getValue());
    }
    return price;
  }

  private BigDecimal getWibor(Tranche tranche) {
    return calculateWiborMultiplier(tranche);
  }

  private BigDecimal applyWibor(BigDecimal price, List<Tranche> tranches) {
    return findEarliestTranche(tranches)
        .map(tranche -> calculateWiborMultiplier(tranche).multiply(price))
        .orElse(price);
  }

  private Optional<Tranche> findEarliestTranche(List<Tranche> tranches) {
    return Optional.ofNullable(tranches).orElse(List.of()).stream()
        .min(Comparator.comparing(Tranche::getExecutionDate));
  }

  private BigDecimal calculateWiborMultiplier(Tranche tranche) {
    BigDecimal wiborRate = getWiborRate(tranche);
    String trancheId = tranche.getExecutionDate().toString();
    metadata.putTrancheMetadata(trancheId, WIBOR_RATE_METADATA_KEY, wiborRate);
    // this was usage before refactor
    BigDecimal monthFraction = calculateMonthFraction(tranche);
    // BigDecimal monthFraction = applyMonthFraction(tranche);
    metadata.putTrancheMetadata(trancheId, MONTH_METADATA_KEY, monthFraction);
    return wiborRate.multiply(monthFraction).add(BigDecimal.ONE).setScale(5, RoundingMode.HALF_UP);
  }

  //  // todo: zostawiam, gdybyśmy jednak wrócili do innej wersji (XD)
  //  private BigDecimal applyMonthFraction(Tranche tranche) {
  //    Wallet wallet = tranche.getWallet();
  //    Optional<Element> mParameterOptional =
  //        wallet.getElement(ElementType.M_PARAMETER, tranche.getTimeUnit());
  //    if (mParameterOptional.isPresent()) {
  //      return mParameterOptional.get().getValue();
  //    }
  //    // Neutral to final price if element was not found
  //    return BigDecimal.ONE;
  //  }

  private BigDecimal getWiborRate(Tranche tranche) {
    BigDecimal wiborPercent = wibor(tranche.getExecutionDate());
    String trancheId = tranche.getExecutionDate().toString();
    metadata.putTrancheMetadata(trancheId, WIBOR_PERCENT_METADATA_KEY, wiborPercent);
    wiborPercent = wiborPercent.add(WIBOR_MARGIN);
    return wiborPercent.divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_UP);
  }

  // TODO: niech zostanie gdybyśmy jednak mieli to wyliczać automatycznie...
  private BigDecimal calculateMonthFraction(Tranche tranche) {
    LocalDate executionDate = tranche.getExecutionDate();
    int year = Integer.parseInt(tranche.getWallet().getYear().getYear());
    LocalDate endDate = LocalDate.of(year + 1, END_MONTH, END_DAY);
    BigDecimal months =
        BigDecimal.valueOf(ChronoUnit.MONTHS.between(executionDate, endDate)).add(BigDecimal.ONE);
    // Store months count in tranche-specific metadata for debugging
    String trancheId = tranche.getExecutionDate().toString();
    metadata.putTrancheMetadata(trancheId, "MONTHS_COUNT", months);
    return months.divide(BigDecimal.valueOf(12), 2, RoundingMode.HALF_UP);
  }

  // private BigDecimal getWiborValue(Wallet wallet, TimeUnit timeUnit) {
  //  return wallet.getElements(ElementType.WIBOR, timeUnit).stream()
  //      .findFirst()
  //      .map(Element::getValue)
  //      .orElse(BigDecimal.ONE);
  // }

  public BigDecimal wibor(LocalDate date) {
    return priceRepository
        .findByContractAndDate(PriceName.of(WIBOR_CONTRACT_NAME), date)
        .map(Price::getValue)
        .map(
            pv -> {
              BigDecimal value = pv.getValue();
              if (value == null) {
                throw new IllegalStateException(
                    STR."Znaleziono notowanie WIBOR, ale jego wartość jest pusta dla daty: \{
                        date}");
              }
              return value;
            })
        .orElseGet(() -> getLatestAvailableWiborBefore(date));
  }

  private BigDecimal getLatestAvailableWiborBefore(LocalDate date) {
    return priceRepository
        .findTopByContractAndDate_DateBeforeOrderByDate_DateDesc(
            PriceName.of(WIBOR_CONTRACT_NAME), date)
        .map(Price::getValue)
        .map(
            pv -> {
              if (pv.getValue() == null) {
                throw new IllegalStateException(
                    STR."Znaleziono wcześniejsze notowanie WIBOR, ale bez wartości dla daty < \{
                        date}");
              }
              return pv.getValue();
            })
        .orElseThrow(
            () ->
                new IllegalStateException(
                    STR."Nie znaleziono żadnego wcześniejszego notowania WIBOR przed datą: \{
                        date}"));
  }
}
