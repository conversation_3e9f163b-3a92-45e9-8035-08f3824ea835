/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.tiew;

import com.codemonkeys.wallet.common.framework.shared.validation.ValidationFailedException;
import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.BaseValidationStrategy;
import com.codemonkeys.wallet.domain.wallets.Element;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.ElementType;
import java.util.Optional;
import java.util.Set;

public class TIEWValidationStrategy extends BaseValidationStrategy {
  protected void validateWF(Wallet wallet) {
    Optional<Element> WF =
        Optional.ofNullable(wallet.getElements()).orElse(Set.of()).stream()
            .filter(e -> e.getType().equals(ElementType.WF))
            .findFirst();
    if (WF.isEmpty()) {
      throw ValidationFailedException.of("elements", "validations.elements.wf");
    }
  }

  @Override
  public void validateOrThrow(Wallet wallet) {
    validateWF(wallet);
  }
}
