/* (C)2024-2025 */
package com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.eon;

import com.codemonkeys.wallet.computation.application.pipeline.handlers.greenpropertyrights.strategy.CalculationStrategy;
import com.codemonkeys.wallet.domain.wallets.PriceMetadata;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public class EONCalculationStrategy extends CalculationStrategy {
  @Override
  public PriceMetadata getMetadata() {
    return null;
  }

  @Override
  public Optional<BigDecimal> price(List<Tranche> tranches) {
    if (tranches.isEmpty()) {
      return Optional.empty();
    }
    Wallet wallet = tranches.getFirst().getWallet();
    BigDecimal totalPrice = basePrice(tranches, this::calculateTranchePrice);
    return Optional.of(applyIndustrialStatusDiscount(totalPrice, wallet));
  }

  private BigDecimal calculateTranchePrice(Tranche tranche) {
    BigDecimal partialPrice = partialPrice(tranche.getPrice(), tranche);
    return tranche.getFractionalSize().multiply(partialPrice);
  }

  private BigDecimal partialPrice(BigDecimal price, Tranche tranche) {
    BigDecimal appliedSupplierDiscount = applySupplierDiscount(price, tranche);
    BigDecimal partialPrice = applyDuty(appliedSupplierDiscount, tranche.getYear());
    BigDecimal applySupplierServicePrice = applySupplierServicePrice(partialPrice, tranche);
    return applySupplierServicePrice;
  }
}
