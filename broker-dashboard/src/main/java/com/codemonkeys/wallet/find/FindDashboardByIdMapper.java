/* (C)2025 */
package com.codemonkeys.wallet.find;

import com.codemonkeys.wallet.analytical.AnalyticalCalculationService;
import com.codemonkeys.wallet.analytical.response.AnalyticalResponse;
import com.codemonkeys.wallet.common.DashboardTrancheMapper;
import com.codemonkeys.wallet.common.framework.crud.findbyid.FindByIdMapper;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationResult;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.vo.WalletId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FindDashboardByIdMapper
    implements FindByIdMapper<Wallet, WalletId, FindDashboardByIdResponse> {

  private final DashboardTrancheMapper trancheMapper;
  private final FindDashboardRecommendationMapper findDashboardRecommendationMapper;
  private final AnalyticalCalculationService analyticalCalculationService;

  /**
   * Converts a Wallet domain object into a complete FindDashboardByIdResponse DTO. Includes regular
   * and recommended tranches, analytics, and wallet metadata.
   *
   * @param wallet the Wallet domain object
   * @return response DTO representing the full dashboard state
   */
  @Override
  public FindDashboardByIdResponse toDto(Wallet wallet) {
    // 1) Regular tranches (domain -> DTO)
    List<DashboardTrancheDto> domainDtos =
        wallet.getTranches().stream()
            .map(trancheMapper::mapTranche)
            .collect(Collectors.toCollection(ArrayList::new));

    // 2) Recommendation DTOs
    List<DashboardTrancheDto> recommendationDtos =
        findDashboardRecommendationMapper.findRecommendationDtos(wallet);

    // 3) Merge both lists into one
    List<DashboardTrancheDto> allDtos = new ArrayList<>();
    allDtos.addAll(domainDtos);
    allDtos.addAll(recommendationDtos);

    // 4) Add recommendations to domain (based on DTOs, if isRecommendation=true)
    findDashboardRecommendationMapper.addRecommendationsToDomain(allDtos, wallet);

    // 5) Run pipeline/analytics
    AnalyticalResponse analyticalData =
        analyticalCalculationService.calculateAnalyticalTable(wallet);

    // 6) Build final set of tranches (domain -> DTO) with complete state
    List<DashboardTrancheDto> finalDtos =
        wallet.getTranches().stream()
            .filter(
                tranche ->
                    tranche.getContract() == null
                        || !tranche.getContract().isPropertyRightsContract())
            .map(trancheMapper::mapTranche)
            .collect(Collectors.toList());

    return new FindDashboardByIdResponse(
        wallet.getId().getId().toString(),
        wallet.getAgreement(),
        wallet.getGreenPropertyRightCalculationType(),
        wallet.getDescription(),
        wallet.getName(),
        wallet.getElements().stream().toList(),
        finalDtos,
        wallet.getPrices().stream().toList(),
        wallet.getMediaType().toString(),
        wallet.getStartDate(),
        wallet.getYear(),
        wallet.monthlyTranches(),
        wallet.monthlyProducts(),
        analyticalData,
        new ValidationResult(true, List.of()));
  }

  /**
   * Converts the given InvalidResult into a FindDashboardByIdResponse DTO representing a failed
   * find operation.
   *
   * @param invalidResult the InvalidResult containing error details.
   * @return the corresponding FindDashboardByIdResponse DTO with error information.
   */
  @Override
  public FindDashboardByIdResponse toDto(InvalidResult invalidResult) {
    return new FindDashboardByIdResponse(invalidResult);
  }
}
