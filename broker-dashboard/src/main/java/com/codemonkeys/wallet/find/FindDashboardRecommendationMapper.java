/* (C)2025 */
package com.codemonkeys.wallet.find;

import com.codemonkeys.wallet.common.DashboardTrancheMapper;
import com.codemonkeys.wallet.domain.agreement.vo.AgreementId;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.contract.ContractRepository;
import com.codemonkeys.wallet.domain.contract.vo.ContractName;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.recommendation.Recommendation;
import com.codemonkeys.wallet.domain.recommendation.RecommendationRepository;
import com.codemonkeys.wallet.domain.recommendation.enums.RecommendationStatus;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.TrancheId;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FindDashboardRecommendationMapper {

  private final RecommendationRepository recommendationRepository;
  private final DashboardTrancheMapper trancheMapper;
  private final ContractRepository contractRepository;
  private final PriceRepository priceRepository;

  /**
   * Returns a list of DashboardTrancheDto with isRecommendation=true, based on recommendations in
   * the database for a given agreement (wallet.getAgreement()).
   */
  public List<DashboardTrancheDto> findRecommendationDtos(Wallet wallet) {
    // 'agreementId' is actually the ID of the agreement (from domain)...
    final UUID agreementId = UUID.fromString(wallet.getAgreement().getId().getId());

    // fetch recommendations from the database
    return recommendationRepository.findByContractId(agreementId).stream()
        .filter(
            rec ->
                rec.getStatus() != RecommendationStatus.NOT_COMPLETED
                    && rec.getStatus() != RecommendationStatus.ADDED_TO_WALLET)
        // map to DashboardTrancheDto (with isRecommendation=true)
        .map(rec -> mapRecommendation(rec))
        .collect(Collectors.toCollection(ArrayList::new));
  }

  /**
   * Based on the given list of DashboardTrancheDto, adds ONLY those tranches to the domain which
   * have isRecommendation=true. Converts them to Tranche and adds to the wallet.
   */
  public void addRecommendationsToDomain(List<DashboardTrancheDto> dtos, Wallet wallet) {
    // filter ONLY objects with isRecommendation=true
    List<DashboardTrancheDto> recommendedDtos =
        dtos.stream().filter(DashboardTrancheDto::isRecommendation).collect(Collectors.toList());

    // convert to domain tranches and add to the wallet
    List<Tranche> recommendedTranches =
        recommendedDtos.stream()
            .map(dto -> mapDtoToRecommendationTranche(dto, wallet))
            .collect(Collectors.toList());

    wallet.getTranches().addAll(recommendedTranches);
  }

  /**
   * mapRecommendation(...) creates a DashboardTrancheDto with isRecommendation=true, matching a
   * contract from the database (ContractRepository) by: - rec.getContractId() => agreement ID -
   * rec.getContract() => contract name
   */
  public DashboardTrancheDto mapRecommendation(@NonNull Recommendation rec) {
    LocalDate date =
        (rec.getDeadline() != null) ? rec.getDeadline().toLocalDate() : LocalDate.now();
    BigDecimal price = resolveRecommendationPrice(rec.getPrice(), rec.getContract());

    // preliminarily build the DTO
    DashboardTrancheDto dto = new DashboardTrancheDto();
    dto.setCreatedAt(date);
    dto.setExecutionDate(date);
    dto.setTimeUnit((rec.getTimeUnit() != null) ? TimeUnit.valueOf(rec.getTimeUnit()) : null);
    dto.setSize(new BigDecimal(rec.getVolume()));
    dto.setPrice(price);
    dto.setPriceReference(rec.getPurchaseMethod());
    dto.setEmailSent(false);
    dto.setVirtual(false);
    dto.setRecommendation(true);

    // 1) try to find the contract in ContractRepository (AgreementId + ContractName)
    AgreementId agreementId = AgreementId.of(UUID.fromString(rec.getContractId().toString()));
    ContractName cName = new ContractName(rec.getContract());

    Optional<Contract> contractOpt =
        contractRepository.findByAgreementIdAndName(agreementId, cName);
    if (contractOpt.isPresent()) {
      dto.setContract(createContractDto(contractOpt.get()));
      // set contract DTO
      // => contract.id = real contract ID
      //    contract.name = name
    } else {
      // fallback: at least set the name
      if (rec.getContract() != null) {
        dto.setContract(new DashboardTrancheDto.ContractDto(rec.getContract(), null));
      }
    }

    return dto;
  }

  /**
   * Converts a DashboardTrancheDto (with isRecommendation=true) into a domain Tranche, looking up
   * the contract in ContractRepository if possible.
   */
  public Tranche mapDtoToRecommendationTranche(
      @NonNull DashboardTrancheDto dto, @NonNull Wallet wallet) {

    LocalDate execDate =
        (dto.getExecutionDate() != null) ? dto.getExecutionDate() : LocalDate.now();
    TimeUnit unit = dto.getTimeUnit();

    // 1) if the DTO contains a contract name, try to find it in the database
    Contract contract = null;
    if (dto.getContract() != null && dto.getContract().getName() != null) {
      AgreementId agreementId =
          AgreementId.of(UUID.fromString(wallet.getAgreement().getId().getId()));
      ContractName cName = new ContractName(dto.getContract().getName());

      contract = contractRepository.findByAgreementIdAndName(agreementId, cName).orElse(null);
    }

    // 2) Build domain tranche (with isRecommendation=true)
    Tranche t =
        new Tranche(
            TrancheId.randomId(),
            wallet,
            execDate,
            unit,
            dto.getSize() != null ? dto.getSize() : BigDecimal.ZERO,
            dto.getPrice() != null ? dto.getPrice() : BigDecimal.ZERO,
            dto.getPriceReference(),
            contract,
            dto.isVirtual());
    t.setRecommendation(true);

    return t;
  }

  /**
   * Creates a ContractDto from the given Contract domain object. Extracts the contract name and ID
   * for use in the DashboardTrancheDto.
   *
   * @param contract the Contract domain object
   * @return a ContractDto containing the contract name and ID
   */
  private DashboardTrancheDto.ContractDto createContractDto(@NonNull Contract contract) {
    return new DashboardTrancheDto.ContractDto(
        contract.getName().getValue(), contract.getId().getId().toString());
  }

  private BigDecimal resolveRecommendationPrice(String priceString, String contractName) {
    try {
      return new BigDecimal(priceString);
    } catch (NumberFormatException e) {
      return priceRepository
          .findLatestPriceByContract(contractName)
          .map(p -> p.getValue().getValue())
          .orElse(BigDecimal.ZERO);
    }
  }
}
