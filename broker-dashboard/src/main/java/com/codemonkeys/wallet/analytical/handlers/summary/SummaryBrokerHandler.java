/* (C)2025 */
package com.codemonkeys.wallet.analytical.handlers.summary;

import an.awesome.pipelinr.Command;
import com.codemonkeys.wallet.analytical.response.AnalyticalResponse;
import com.codemonkeys.wallet.analytical.response.AnalyticalSection;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.Remuneration;
import com.codemonkeys.wallet.domain.customer.vo.FixedRemunerationValue;
import com.codemonkeys.wallet.domain.customer.vo.RemunerationType;
import com.codemonkeys.wallet.domain.customer.vo.RemunerationValue;
import com.codemonkeys.wallet.domain.wallets.MonthProducts;
import com.codemonkeys.wallet.domain.wallets.Product;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.Media;
import com.codemonkeys.wallet.domain.wallets.enums.ProductType;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SummaryBrokerHandler
    implements Command.Handler<SummaryBrokerCommand, AnalyticalResponse> {

  @Override
  public AnalyticalResponse handle(SummaryBrokerCommand command) {
    Wallet wallet = command.wallet();
    if (wallet == null) {
      return new AnalyticalResponse();
    }

    MonthProducts monthProducts = wallet.getMonthProducts();
    Media media = (wallet.getMediaType() == MediaType.GAS) ? Media.GAS : Media.ENERGY;
    Customer customer = wallet.getAgreement().getCustomer();

    List<String> totalResultCells = new ArrayList<>();
    List<String> resultESCells = new ArrayList<>();
    List<String> capESCells = new ArrayList<>();

    for (TimeUnit month : TimeUnit.Y.getMonths()) {
      if (!month.isMonth()) continue;

      LocalDate date =
          LocalDate.of(Integer.parseInt(wallet.getYear().getYear()), month.toMonth(), 1);
      Optional<Remuneration> remunerationOpt = findMatchingRemuneration(customer, media, date);

      BigDecimal benchmarkResult =
          getProductValue(monthProducts, month, media, ProductType.BENCHMARK_RESULT);
      totalResultCells.add(toDisplay(benchmarkResult));

      resultESCells.add(
          getResultEsCell(remunerationOpt, benchmarkResult, monthProducts, month, media));
      capESCells.add(getCapEsCell(remunerationOpt));
    }

    AnalyticalResponse response = new AnalyticalResponse();
    response.setSummary(
        List.of(
            new AnalyticalSection("Wynik łączny", totalResultCells),
            new AnalyticalSection("Wynik ES", resultESCells),
            new AnalyticalSection("CAP-ES", capESCells)));

    log.info("SummaryCommand result: {}", response.getSummary());
    return response;
  }

  private Optional<Remuneration> findMatchingRemuneration(
      Customer customer, Media media, LocalDate date) {
    return customer.getRemunerations().stream()
        .filter(r -> r.getCarrier() == media)
        .filter(r -> r.getPeriod() != null)
        .filter(
            r ->
                r.getPeriod().getFrom().isBefore(date.plusMonths(1))
                    && !r.getPeriod().getTo().isBefore(date))
        .findFirst();
  }

  private String getResultEsCell(
      Optional<Remuneration> remunerationOpt,
      BigDecimal benchmarkResult,
      MonthProducts monthProducts,
      TimeUnit month,
      Media media) {

    if (remunerationOpt.isEmpty()) return "-";

    Remuneration remuneration = remunerationOpt.get();
    BigDecimal factor =
        Optional.ofNullable(remuneration.getRemunerationValue())
            .map(RemunerationValue::getAmount)
            .orElse(null);

    RemunerationType remunerationType = remuneration.getRemunerationType();
    RemunerationType.Kind kind = (remunerationType != null) ? remunerationType.getKind() : null;

    if (factor == null || kind == null) return "-";

    BigDecimal esValue = null;

    if (kind == RemunerationType.Kind.MARKET_MIXED_PERCENT && benchmarkResult != null) {
      esValue =
          benchmarkResult.multiply(factor).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
    } else if (kind == RemunerationType.Kind.MARKET_ANNUAL_PERCENT) {
      BigDecimal marketMean = getProductValue(monthProducts, month, media, ProductType.MARKET_MEAN);
      if (marketMean != null) {
        esValue =
            marketMean.multiply(factor).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
      }
    }

    return toDisplay(esValue);
  }

  private String getCapEsCell(Optional<Remuneration> remunerationOpt) {
    if (remunerationOpt.isEmpty()) return "nd.";

    Remuneration remuneration = remunerationOpt.get();
    BigDecimal cap = remuneration.getCap();
    BigDecimal fixed =
        Optional.ofNullable(remuneration.getFixedRemunerationValue())
            .map(FixedRemunerationValue::getAmount)
            .orElse(null);

    Optional<RemunerationType.Kind> variableKindOpt =
        Optional.ofNullable(remuneration.getRemunerationType()).map(RemunerationType::getKind);
    Optional<BigDecimal> variableValueOpt =
        Optional.ofNullable(remuneration.getRemunerationValue()).map(RemunerationValue::getAmount);

    if (cap != null && fixed != null) {
      // TODO: Dominik D. dodałem ten warunek po konsultacji 29.04.2025 z Moniką.
      //      Komentarz JIRA
      // https://energysolution.atlassian.net/************************42dd454de03d#media-blob-url=true&id=526ad4da-da2f-495d-9581-115e44ef3cd6&contextId=13329&collection=
      //      https://energysolution.atlassian.net/browse/POR-752
      if (variableKindOpt.isEmpty() && variableValueOpt.isEmpty()) {
        return "-";
      }
      return toDisplay(cap.subtract(fixed));
    }

    if (cap != null) {
      return toDisplay(cap);
    }

    RemunerationType remunerationType = remuneration.getRemunerationType();
    RemunerationType.Kind kind = remunerationType != null ? remunerationType.getKind() : null;

    boolean hasVariable =
        kind == RemunerationType.Kind.MARKET_MIXED_PERCENT
            || kind == RemunerationType.Kind.MARKET_ANNUAL_PERCENT;

    if (hasVariable) {
      return "no limit";
    }

    // all others
    return "nd.";
  }

  private BigDecimal getProductValue(
      MonthProducts products, TimeUnit month, Media media, ProductType type) {
    return products.get(month, media, type).map(Product::getValue).orElse(null);
  }

  private String toDisplay(BigDecimal value) {
    return (value == null || value.compareTo(BigDecimal.ZERO) == 0) ? "-" : formatDecimal(value);
  }

  private String formatDecimal(BigDecimal value) {
    return value
        .setScale(2, RoundingMode.HALF_UP)
        .stripTrailingZeros()
        .toPlainString()
        .replace(".", ",");
  }
}
