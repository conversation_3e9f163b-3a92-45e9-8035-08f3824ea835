/* (C)2025 */
package com.codemonkeys.wallet.analytical.handlers.dashed;

import an.awesome.pipelinr.Command;
import com.codemonkeys.wallet.analytical.response.AnalyticalResponse;
import com.codemonkeys.wallet.analytical.response.DashedLines;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class Dashed<PERSON>ines<PERSON>rokerHandler
    implements Command.Handler<DashedLinesBrokerCommand, AnalyticalResponse> {

  private final VWAPCalculator vwapCalculator;

  @Override
  public AnalyticalResponse handle(DashedLinesBrokerCommand command) {
    Wallet wallet = command.wallet();
    LocalDate today = LocalDate.now();

    int currentYear = today.getYear();
    int currentMonth = today.getMonthValue();
    int currentQuarter = (currentMonth - 1) / 3 + 1;

    int yValue = 0;
    Map<String, Integer> qMap =
        java.util.Arrays.stream(TimeUnit.values())
            .filter(TimeUnit::isQuarter)
            .collect(java.util.stream.Collectors.toMap(Enum::name, q -> 0));
    List<Integer> mList = new ArrayList<>(Collections.nCopies(12, 0));
    mList.replaceAll(x -> 0);

    for (Contract c : wallet.getAgreement().getContracts()) {
      TimeUnit unit = c.getName().getTimeUnit();
      if (unit == null) continue;
      int year = c.getYear().getYearAsInt();

      if (c.isYearContract() && c.isYearPurchasable(year, currentYear)) {
        double wzm = vwapCalculator.calculate(c, today);
        yValue = scale(wzm, 10);
      } else if (c.isQuarterContract()
          && unit.isQuarter()
          && c.isQuarterPurchasable(year, currentYear, unit, currentQuarter)) {
        qMap.put(unit.name(), quarter(unit));
      } else if (c.isMonthContract()
          && unit.isMonth()
          && c.isMonthPurchasable(year, currentYear, unit, currentMonth)) {
        mList.set(unit.asNumber() - 1, month(unit));
      }
    }

    AnalyticalResponse response = new AnalyticalResponse();
    response.setDashedLines(new DashedLines(yValue, qMap, mList));
    return response;
  }

  private int quarter(TimeUnit q) {
    return switch (q) {
      case Q1 -> 5;
      case Q2 -> 10;
      case Q3 -> 15;
      case Q4 -> 20;
      default -> throw new IllegalArgumentException(STR."Unsupported quarter: \{q}");
    };
  }

  private int month(TimeUnit m) {
    return switch (m) {
      case M1 -> 2;
      case M2 -> 4;
      case M3 -> 6;
      case M4 -> 8;
      case M5 -> 10;
      case M6 -> 12;
      case M7 -> 14;
      case M8 -> 16;
      case M9 -> 18;
      case M10 -> 5;
      case M11 -> 11;
      case M12 -> 6;
      default -> throw new IllegalArgumentException(STR."Unsupported month: \{m}");
    };
  }
}
