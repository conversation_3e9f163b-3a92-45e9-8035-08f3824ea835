/* (C)2025 */
package com.codemonkeys.wallet.analytical.handlers.y;

import an.awesome.pipelinr.Command;
import com.codemonkeys.wallet.analytical.response.AnalyticalResponse;
import com.codemonkeys.wallet.analytical.response.AnalyticalSection;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class YearlyBrokerDataHandler
    implements Command.Handler<YearlyBrokerDataCommand, AnalyticalResponse> {

  private final PriceRepository priceRepository;

  @Override
  public AnalyticalResponse handle(YearlyBrokerDataCommand command) {
    Wallet wallet = command.wallet();
    int currentYear = LocalDate.now().getYear();

    String marketResult = "0";
    String clientResult = "0";
    String finalResult = "0";

    Optional<Contract> optionalContract =
        Contract.findContract(wallet, ContractType.Y, MediaType.GAS, currentYear, null)
            .or(
                () ->
                    Contract.findContract(
                        wallet, ContractType.Y, MediaType.ENERGY, currentYear, null));

    if (optionalContract.isPresent()) {
      Contract contract = optionalContract.get();
      String contractName = contract.getName().toString();

      // Market price calculation
      var avg = contract.getAverageCalculation();
      var prices =
          priceRepository.findByContractAndDateBetween(
              PriceName.of(contractName), avg.getStartDate(), avg.getEndDate());

      BigDecimal marketNumerator = BigDecimal.ZERO;
      BigDecimal marketDenominator = BigDecimal.ZERO;

      for (Price price : prices) {
        BigDecimal volume = price.getVolume().getVolume();
        BigDecimal value = price.getValue().getValue();
        marketNumerator = marketNumerator.add(value.multiply(volume));
        marketDenominator = marketDenominator.add(volume);
      }

      if (marketDenominator.compareTo(BigDecimal.ZERO) > 0) {
        marketResult =
            formatDecimal(marketNumerator.divide(marketDenominator, 2, RoundingMode.HALF_UP));
      }

      // Client price calculation (weighted average of all tranches for this contract)
      List<Tranche> allTranches =
          wallet.getTranches().stream()
              .filter(t -> t.getContract() != null && t.getContract().getName() != null)
              .filter(t -> !t.getContract().isPropertyRightsContract())
              .filter(t -> t.getContract().getName().toString().equals(contractName))
              .toList();

      BigDecimal clientNumerator = BigDecimal.ZERO;
      BigDecimal clientDenominator = BigDecimal.ZERO;

      for (Tranche t : allTranches) {
        if (t.getPrice() != null && t.getSize() != null) {
          clientNumerator = clientNumerator.add(t.getPrice().multiply(t.getSize()));
          clientDenominator = clientDenominator.add(t.getSize());
        }
      }

      if (clientDenominator.compareTo(BigDecimal.ZERO) > 0) {
        clientResult =
            formatDecimal(clientNumerator.divide(clientDenominator, 2, RoundingMode.HALF_UP));
      } else {
        clientResult = "0";
      }

      try {
        BigDecimal marketDecimal = new BigDecimal(marketResult.replace(",", "."));
        BigDecimal clientDecimal = new BigDecimal(clientResult.replace(",", "."));
        finalResult = formatDecimal(marketDecimal.subtract(clientDecimal));
      } catch (NumberFormatException ignored) {
        // fallback remains "0"
      }
    }

    AnalyticalResponse response = new AnalyticalResponse();
    response.setY(
        List.of(
            new AnalyticalSection("Rynek", List.of(marketResult)),
            new AnalyticalSection("Klient", List.of(clientResult)),
            new AnalyticalSection("Wynik", List.of(finalResult), true)));

    return response;
  }

  private String formatDecimal(BigDecimal value) {
    return value.stripTrailingZeros().toPlainString().replace(".", ",");
  }
}
