/* (C)2025 */
package com.codemonkeys.wallet.analytical.handlers.dashed;

import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Predicate;

public record TrimInfo(int top, int bottom) {

  /**
   * Calculates how many working days (excluding weekends and holidays) fall outside the contract's
   * purchase window.
   *
   * <p>The window is defined by {@code AverageCalculation.startDate} and {@code endDate}. Days
   * before the start and after the end are counted for trimming.
   *
   * @param contract the contract with defined average calculation period
   * @param isHoliday predicate determining whether a date is a holiday (non-trading day)
   * @return a {@code TrimInfo} record containing the number of days to trim from top and bottom
   */
  public static TrimInfo forContract(
      Contract contract, Predicate<LocalDate> isHoliday, PriceRepository priceRepository) {

    String contractName = contract.getName().toString();
    List<Price> prices = priceRepository.findByContractName(contractName);
    if (prices.isEmpty()) return new TrimInfo(0, 0);

    LocalDate start = contract.getAverageCalculation().getStartDate();
    LocalDate end = contract.getAverageCalculation().getEndDate();

    // faktyczny pierwszy i ostatni dzień notowań
    LocalDate firstNotedDay =
        prices.stream().map(p -> p.getDate().getDate()).min(LocalDate::compareTo).orElse(start);

    LocalDate lastNotedDay =
        prices.stream().map(p -> p.getDate().getDate()).max(LocalDate::compareTo).orElse(end);

    // liczba dni roboczych przed rozpoczęciem zakupu
    int topTrim = 0;
    for (LocalDate d = firstNotedDay; d.isBefore(start); d = d.plusDays(1)) {
      if (!isHoliday.test(d)) topTrim++;
    }

    // liczba dni roboczych po zakończeniu zakupu
    int bottomTrim = 0;
    for (LocalDate d = end.plusDays(1); d.isBefore(lastNotedDay.plusDays(1)); d = d.plusDays(1)) {
      if (!isHoliday.test(d)) bottomTrim++;
    }

    return new TrimInfo(topTrim, bottomTrim);
  }
}
