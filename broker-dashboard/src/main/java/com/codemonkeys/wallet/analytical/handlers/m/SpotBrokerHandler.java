/* (C)2025 */
package com.codemonkeys.wallet.analytical.handlers.m;

import an.awesome.pipelinr.Command;
import com.codemonkeys.wallet.analytical.response.AnalyticalResponse;
import com.codemonkeys.wallet.analytical.response.AnalyticalSection;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SpotBrokerHandler implements Command.Handler<SpotBrokerCommand, AnalyticalResponse> {

  private final PriceRepository priceRepository;

  @Override
  public AnalyticalResponse handle(SpotBrokerCommand command) {
    Wallet wallet = command.wallet();
    int currentYear = LocalDate.now().getYear();

    List<String> marketResults = new ArrayList<>();
    List<String> clientResults = new ArrayList<>();
    List<String> finalResults = new ArrayList<>();

    for (int month = 1; month <= 12; month++) {
      final int currentMonth = month;

      Optional<Contract> optionalContract =
          Contract.findContract(wallet, ContractType.M, MediaType.GAS, currentYear, currentMonth)
              .or(
                  () ->
                      Contract.findContract(
                          wallet, ContractType.M, MediaType.ENERGY, currentYear, currentMonth));

      String marketResult = "0";
      String clientResult = "0";
      String finalResult = "0";

      if (optionalContract.isPresent()) {
        Contract contract = optionalContract.get();
        String contractName = contract.getName().toString();

        // Market average
        var avg = contract.getAverageCalculation();
        var prices =
            priceRepository.findByContractAndDateBetween(
                PriceName.of(contractName), avg.getStartDate(), avg.getEndDate());

        BigDecimal marketNumerator = BigDecimal.ZERO;
        BigDecimal marketDenominator = BigDecimal.ZERO;

        for (Price price : prices) {
          var volume = price.getVolume().getVolume();
          var value = price.getValue().getValue();
          marketNumerator = marketNumerator.add(value.multiply(volume));
          marketDenominator = marketDenominator.add(volume);
        }

        if (marketDenominator.compareTo(BigDecimal.ZERO) > 0) {
          marketResult =
              formatDecimal(marketNumerator.divide(marketDenominator, 2, RoundingMode.HALF_UP));
        }

        // Client average
        List<Tranche> allTranches =
            wallet.getTranches().stream()
                .filter(t -> !t.getContract().isPropertyRightsContract())
                .filter(t -> t.getContract() != null && t.getContract().getName() != null)
                .filter(t -> t.getContract().getName().toString().equals(contractName))
                .toList();

        BigDecimal clientNumerator = BigDecimal.ZERO;
        BigDecimal clientDenominator = BigDecimal.ZERO;

        for (Tranche t : allTranches) {
          if (t.getPrice() != null && t.getSize() != null) {
            clientNumerator = clientNumerator.add(t.getPrice().multiply(t.getSize()));
            clientDenominator = clientDenominator.add(t.getSize());
          }
        }

        if (clientDenominator.compareTo(BigDecimal.ZERO) > 0) {
          clientResult =
              formatDecimal(clientNumerator.divide(clientDenominator, 2, RoundingMode.HALF_UP));
        } else {
          clientResult = "0";
        }

        try {
          BigDecimal marketDecimal = new BigDecimal(marketResult.replace(",", "."));
          BigDecimal clientDecimal = new BigDecimal(clientResult.replace(",", "."));
          finalResult = formatDecimal(marketDecimal.subtract(clientDecimal));
        } catch (NumberFormatException ignored) {
          // fallback remains "0"
        }
      }

      marketResults.add(marketResult);
      clientResults.add(clientResult);
      finalResults.add(finalResult);
    }

    AnalyticalResponse response = new AnalyticalResponse();
    response.setM_SPOT(
        List.of(
            new AnalyticalSection("Rynek", marketResults),
            new AnalyticalSection("Klient", clientResults),
            new AnalyticalSection("Wynik", finalResults, true)));

    return response;
  }

  private String formatDecimal(BigDecimal value) {
    return value.stripTrailingZeros().toPlainString().replace(".", ",");
  }
}
