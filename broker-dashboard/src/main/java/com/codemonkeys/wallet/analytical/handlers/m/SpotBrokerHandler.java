/* (C)2025 */
package com.codemonkeys.wallet.analytical.handlers.m;

import an.awesome.pipelinr.Command;
import com.codemonkeys.wallet.analytical.response.AnalyticalResponse;
import com.codemonkeys.wallet.analytical.response.AnalyticalSection;
import com.codemonkeys.wallet.common.framework.domain.enums.MediaType;
import com.codemonkeys.wallet.domain.agreement.ContractType;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.price.Price;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import com.codemonkeys.wallet.domain.price.vo.PriceName;
import com.codemonkeys.wallet.domain.wallets.Tranche;
import com.codemonkeys.wallet.domain.wallets.Wallet;
import com.codemonkeys.wallet.domain.wallets.enums.TimeUnit;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SpotBrokerHandler implements Command.Handler<SpotBrokerCommand, AnalyticalResponse> {

  private final PriceRepository priceRepository;

  /**
   * Handles calculation of monthly market, client and result averages
   * for spot contracts, including monthly and spot tranches.
   */
  @Override
  public AnalyticalResponse handle(SpotBrokerCommand command) {

    Wallet wallet = command.wallet();
    int currentYear = wallet.getYear().getYearAsInt();

    List<String> market = new ArrayList<>();
    List<String> client = new ArrayList<>();
    List<String> result = new ArrayList<>();

    for (int month = 1; month <= 12; month++) {
      processMonth(wallet, currentYear, month, market, client, result);
    }

    AnalyticalResponse response = new AnalyticalResponse();
    response.setM_SPOT(
        List.of(
            new AnalyticalSection("Rynek", market),
            new AnalyticalSection("Klient", client),
            new AnalyticalSection("Wynik", result, true)));
    return response;
  }

  /**
   * Processes a single month: calculates market, client and result values.
   */
  private void processMonth(
      Wallet wallet,
      int year,
      int month,
      List<String> marketOut,
      List<String> clientOut,
      List<String> resultOut) {

    /**
     * Finds the monthly contract for given year and month, for GAS or ENERGY.
     */
    findMonthlyContract(wallet, year, month)
        .ifPresentOrElse(
            contract -> {
              Set<String> relevantNames = collectRelevantNames(contract, wallet);

              String marketStr = formatDecimal(calculateMarketAverage(contract));
              String clientStr =
                  formatDecimal(calculateClientAverage(wallet, relevantNames, month));
              String resultStr = subtract(marketStr, clientStr);

              marketOut.add(marketStr);
              clientOut.add(clientStr);
              resultOut.add(resultStr);
            },
            () -> {
              // brak kontraktu w M
              marketOut.add("0");
              clientOut.add("0");
              resultOut.add("0");
            });
  }

  /**
   * Finds the monthly contract for given year and month, for GAS or ENERGY.
   */
  private Optional<Contract> findMonthlyContract(Wallet wallet, int year, int month) {
    return Contract.findContract(wallet, ContractType.M, MediaType.GAS, year, month)
        .or(() -> Contract.findContract(wallet, ContractType.M, MediaType.ENERGY, year, month));
  }

  /**
   * Collects all contract names relevant for the current calculation:
   * monthly contract + all spot contracts of the same media type.
   */
  private Set<String> collectRelevantNames(Contract monthly, Wallet wallet) {
    Set<String> names = new HashSet<>();
    names.add(monthly.getName().toString());

    wallet.getAgreement().getContracts().stream()
        .filter(Contract::isSpotContract)
        .filter(c -> c.getMedia() == monthly.getMedia())
        .map(c -> c.getName().toString())
        .forEach(names::add);

    return names;
  }

  /* ---------------------------------------------------- market average */

  /**
   * Calculates market average price for the contract based on price repository data.
   */
  private BigDecimal calculateMarketAverage(Contract monthly) {

    var avg = monthly.getAverageCalculation();
    var prices =
        priceRepository.findByContractAndDateBetween(
            PriceName.of(monthly.getName().toString()), avg.getStartDate(), avg.getEndDate());

    BigDecimal numerator = BigDecimal.ZERO;
    BigDecimal denominator = BigDecimal.ZERO;

    for (Price p : prices) {
      BigDecimal vol = p.getVolume().getVolume();
      BigDecimal val = p.getValue().getValue();
      numerator = numerator.add(val.multiply(vol));
      denominator = denominator.add(vol);
    }
    return denominator.compareTo(BigDecimal.ZERO) == 0
        ? BigDecimal.ZERO
        : numerator.divide(denominator, 2, RoundingMode.HALF_UP);
  }

  /* ---------------------------------------------------- client average */

  /**
   * Calculates client's weighted average price for the given month.
   * Includes both monthly and spot tranches.
   */
  private BigDecimal calculateClientAverage(Wallet wallet, Set<String> names, int month) {

    List<Tranche> tranches = selectEligibleTranches(wallet, names, month);

    BigDecimal numerator = BigDecimal.ZERO;
    BigDecimal denominator = BigDecimal.ZERO;

    for (Tranche t : tranches) {
      if (t.getPrice() == null || t.getSize() == null) continue;
      numerator = numerator.add(t.product());
      denominator = denominator.add(t.getFractionalSize());
    }
    return denominator.compareTo(BigDecimal.ZERO) == 0
        ? BigDecimal.ZERO
        : numerator.divide(denominator, 2, RoundingMode.HALF_UP);
  }

  private List<Tranche> selectEligibleTranches(Wallet wallet, Set<String> names, int currentMonth) {

    return wallet.getTranches().stream()
        .filter(
            t ->
                t.getContract() != null
                    && !t.getContract().isPropertyRightsContract()
                    && names.contains(t.getContract().getName().toString()))
        .filter(t -> isInScope(t.getTimeUnit(), currentMonth))
        .toList();
  }

  private boolean isInScope(TimeUnit tu, int month) {
    if (tu == TimeUnit.Y) return true;
    if (tu.isQuarter()) return tu.getMonths().stream().anyMatch(m -> m.asNumber() == month);
    return tu.isMonth() && tu.asNumber() == month;
  }

  private String subtract(String a, String b) {
    try {
      BigDecimal x = new BigDecimal(a.replace(",", "."));
      BigDecimal y = new BigDecimal(b.replace(",", "."));
      return formatDecimal(x.subtract(y));
    } catch (NumberFormatException nfe) {
      return "0";
    }
  }

  private String formatDecimal(BigDecimal value) {
    return value.stripTrailingZeros().toPlainString().replace(".", ",");
  }
}
