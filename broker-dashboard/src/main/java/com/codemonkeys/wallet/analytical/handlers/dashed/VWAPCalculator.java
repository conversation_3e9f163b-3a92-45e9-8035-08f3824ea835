/* (C)2025 */
package com.codemonkeys.wallet.analytical.handlers.dashed;

import com.codemonkeys.wallet.domain.calendar.Holiday;
import com.codemonkeys.wallet.domain.calendar.HolidayRepository;
import com.codemonkeys.wallet.domain.contract.Contract;
import com.codemonkeys.wallet.domain.price.PriceRepository;
import java.text.DecimalFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class VWAPCalculator {

  private final PriceRepository priceRepository;
  private final HolidayRepository repo;

  /**
   * Calculates the VWAP (WZM) for a given contract as of a specific date.
   *
   * @param current The contract to calculate VWAP for
   * @param asOf The date to calculate VWAP as of
   * @return The calculated VWAP value between 0 and 1, or 0 if calculation is not possible
   */
  public double calculate(Contract current, LocalDate asOf) {
    log.info("START  ➜ {}", current.getName());

    /* ───────────────────────────────────────────────────────────────────── */
    /* 1. Walidacja okna zakupu                                             */
    /* ───────────────────────────────────────────────────────────────────── */
    if (asOf.isAfter(current.getAverageCalculation().getEndDate())) {
      log.info(
          "END    ➜ {}, okres zakupu zakończony (asOf={}, endDate={})",
          current.getName(),
          asOf,
          current.getAverageCalculation().getEndDate());
      return 0;
    }

    /* ───────────────────────────────────────────────────────────────────── */
    /* 2. Lista kontraktów historycznych (-1,-2,-3 lata)                    */
    /* ───────────────────────────────────────────────────────────────────── */
    List<String> histNames = previous3Years(String.valueOf(current.getName()));
    log.info("kontrakty historyczne: {}", histNames);

    List<Map<Integer, Double>> histVWAP = new ArrayList<>();

    // [5–6] pobieramy notowania, liczymy %VWAP (udział wolumenu),
    // indeksujemy dni względem ostatniego dnia notowań (0 = last trading day)
    for (String name : histNames) {
      List<PriceEntry> prices =
          priceRepository.findByContractName(name).stream()
              .map(
                  p ->
                      new PriceEntry(
                          p.getDate().getDate(), p.getVolume().getVolume().doubleValue()))
              .toList();
      log.info("notowań[{}] = {}", name, prices.size());

      Map<Integer, Double> vwapByD = toVwapByD(prices);
      log.info("vwapWedługDnia[{}] = {}", name, vwapByD);
      histVWAP.add(vwapByD);
    }

    /* ───────────────────────────────────────────────────────────────────── */
    /* 3. Średnia z 3 lat wg indeksu dnia                                   */
    /* ───────────────────────────────────────────────────────────────────── */
    Map<Integer, Double> avgByD = averageByD(histVWAP);

    String avgFmt = avgByD.entrySet().stream()
        .sorted(Map.Entry.comparingByKey())
        .map(e -> "D=" + e.getKey() + "→"
            + new DecimalFormat("#,##0.#########").format(e.getValue()))
        .collect(Collectors.joining(", "));
    log.info("średniaWedługDnia: {}", avgFmt);

    /* ───────────────────────────────────────────────────────────────────── */
    /* 4. Przycinanie danych do okna zakupu                                 */
    /* ───────────────────────────────────────────────────────────────────── */
    TrimInfo trim = TrimInfo.forContract(current, this::isHoliday, priceRepository);
    log.info("przycięcie: góra={}  dół={}", trim.top(), trim.bottom());

    TreeMap<Integer, Double> tree = new TreeMap<>(avgByD);
    if (tree.isEmpty()) {
      log.info("END    ➜ {}, brak danych VWAP", current.getName());
      return 0;
    }

    int minKey = tree.firstKey();
    int maxKey = tree.lastKey();

    int from = Math.max(trim.top(), minKey); // dolna granica po trimie
    int to = Math.min(maxKey - trim.bottom(), maxKey); // górna granica po trimie

    if (from > to || !tree.containsKey(from) || !tree.containsKey(to)) {
      log.warn("niepoprawne okno przycięcia (from={}, to={}, keys={})",
          from, to, tree.keySet());
      return 0;
    }

    NavigableMap<Integer, Double> trimmed = tree.subMap(from, true, to, true);

    log.info("lista kluczy mianownika: {}", trimmed.keySet());

    /* ─── LOGI: dni w mianowniku po trimie ──────────────────────────────── */
    int denomDays = trimmed.size();
    log.info("DENOM  ➜ klucze[{}..{}]  razem={}  (uciętoGóra={}  uciętoDół={})",
        from, to, denomDays, trim.top(), trim.bottom());

    /* ───────────────────────────────────────────────────────────────────── */
    /* 5. Indeks dnia dzisiejszego vs. koniec kontraktu                     */
    /* ───────────────────────────────────────────────────────────────────── */
    int todayD = dayNumberToLastTrading(current, asOf);
    log.info("dzisiajD={},  zakresPrzycięty=[{}..{}]",
        todayD, trimmed.firstKey(), trimmed.lastKey());

    if (todayD < trimmed.firstKey() || todayD > trimmed.lastKey()) {
      log.info("END    ➜ {}, dzisiajD poza zakresem", current.getName());
      return 0;
    }

    log.info("lista kluczy licznika: {}", trimmed.tailMap(todayD, true).keySet());

    /* ─── LOGI: dni w liczniku ──────────────────────────────────────────── */
    int numeratorDays = trimmed.tailMap(todayD, true).size();
    int excludedBeforeToday = denomDays - numeratorDays;
    log.info("NUM    ➜ klucze[{}..{}]  razem={}  (wykluczonoPrzedDzisiaj={})",
        todayD, to, numeratorDays, excludedBeforeToday);

    /* ───────────────────────────────────────────────────────────────────── */
    /* 6. Obliczenie WZM                                                    */
    /* ───────────────────────────────────────────────────────────────────── */
    double numerator =
        trimmed.tailMap(todayD, true).values().stream().mapToDouble(Double::doubleValue).sum();
    double denominator = trimmed.values().stream().mapToDouble(Double::doubleValue).sum();

    double wzm = denominator == 0 ? 0.0 : numerator / denominator;
    log.info("END    ➜ {}  WZM={}", current.getName(), wzm);

    return wzm;
  }

  /**
   * Retrieves contract names from the previous three years for a given contract.
   *
   * @param currentName The name of the current contract
   * @return List of historical contract names from the previous three years
   */
  public List<String> previous3Years(String currentName) {
    String prefix = extractPrefix(currentName);
    int thisYear = extractYear(currentName);
    if (thisYear < 0) return List.of();

    List<String> years =
        List.of(thisYear - 1, thisYear - 2, thisYear - 3).stream()
            .map(y -> String.format("%02d", y % 100))
            .toList();

    return priceRepository.findHistoricalContractNames(prefix, years);
  }

  /**
   * Extracts the prefix part of a contract name by removing the trailing two-digit year.
   *
   * <p>For example, "BASE_Q-24" becomes "BASE_Q-".
   *
   * @param name the full contract name
   * @return the contract name without the year suffix
   */
  private String extractPrefix(String name) {
    return name.replaceAll("(\\d{2})$", "");
  }

  /**
   * Extracts the two-digit year suffix from a contract name and converts it to a full 4-digit year.
   *
   * <p>For example, "BASE_M-05-24" returns 2024. If the pattern is not matched, returns -1.
   *
   * @param name the contract name containing a two-digit year at the end
   * @return the full year as an integer (e.g., 2024), or -1 if not found
   */
  private int extractYear(String name) {
    var m = Pattern.compile("(\\d{2})$").matcher(name);
    if (m.find()) return 2000 + Integer.parseInt(m.group(1));
    return -1;
  }

  /**
   * Computes the average value for each key (day index) across multiple VWAP maps.
   *
   * <p>Each input map represents VWAP data from a single historical contract, where the key is a
   * working day index (D) and the value is a normalized volume share. The result is a map where
   * each key has the average of its corresponding values across all years.
   *
   * @param list list of maps containing VWAP values indexed by working day
   * @return map of working day index to average VWAP value across all input maps
   */
  private Map<Integer, Double> averageByD(List<Map<Integer, Double>> list) {
    Map<Integer, Double> avg = new HashMap<>();

    var allKeys = list.stream().flatMap(m -> m.keySet().stream()).collect(Collectors.toSet());

    for (Integer d : allKeys) {
      double sum = 0.0;
      for (Map<Integer, Double> yearMap : list) {
        sum += yearMap.getOrDefault(d, 0.0);
      }
      avg.put(d, sum / 3.0);
    }

    return avg;
  }

  /**
   * Converts price entries to a map of day numbers and their corresponding VWAP values.
   *
   * @param prices List of price entries to process
   * @return Map where key is the day number and value is the VWAP for that day
   */
  private Map<Integer, Double> toVwapByD(List<PriceEntry> prices) {
    if (prices.isEmpty()) return Map.of();
    double total = prices.stream().mapToDouble(PriceEntry::volume).sum();
    PriceEntry last = prices.stream().max(Comparator.comparing(PriceEntry::date)).orElseThrow();

    Map<Integer, Double> out = new HashMap<>();
    for (PriceEntry e : prices) {
      int d = workingDaysBetween(e.date(), last.date());
      out.put(d, e.volume() / total);
    }
    return out;
  }

  /**
   * Calculates the number of working days between two dates, excluding holidays.
   *
   * @param a Start date
   * @param b End date
   * @return Number of working days between the dates
   */
  private int workingDaysBetween(LocalDate a, LocalDate b) {
    int days = 0;
    LocalDate iter = a;
    while (iter.isBefore(b)) {
      if (!isHoliday(iter)) days++;
      iter = iter.plusDays(1);
    }
    return days;
  }

  /**
   * Calculates the number of trading days from a given date to the contract's last trading date.
   *
   * @param c The contract to calculate for
   * @param asOf The reference date
   * @return Number of trading days to the last trading date
   */
  public int dayNumberToLastTrading(Contract c, LocalDate asOf) {
    LocalDate lastTrading = c.getAverageCalculation().getEndDate();
    int d = 0;
    for (LocalDate date = lastTrading; date.isAfter(asOf); date = date.minusDays(1)) {
      if (!isHoliday(date)) d++;
    }
    return d;
  }

  /**
   * Determines if a given date is a holiday or weekend.
   *
   * @param d The date to check
   * @return true if the date is a holiday or weekend, false otherwise
   */
  public boolean isHoliday(LocalDate d) {
    if (d.getDayOfWeek() == DayOfWeek.SATURDAY || d.getDayOfWeek() == DayOfWeek.SUNDAY) {
      return true;
    }
    return repo.findByDate(d).map(Holiday::isHoliday).orElse(false);
  }
}
