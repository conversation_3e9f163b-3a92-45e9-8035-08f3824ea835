import { FormProvider, useForm } from 'react-hook-form';
import { Button, Group as MantineGroup, Stack } from '@mantine/core';
import useSupplier from '../hooks/useSupplier';
import SupplierFields from '../SupplierFields/SupplierFields';
import { useRouter } from 'next/router';
import { SupplierService } from '@/services/SupplierService';
import React, { useState } from 'react';
import ConfirmModal from '../../../../components/common/forms/ConfirmModal/ConfirmModal';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { zodResolver } from '@hookform/resolvers/zod';
import { supplierSchema } from '@/components/suppliers/common/schema';
import { PERMISSIONS } from '@/utils/permissions';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import { notify } from '@/utils/notify';
import { NotificationType } from '@/types/Common';

function SupplierForm({ supplierId = '' }) {
  const methods = useForm({
    resolver: zodResolver(supplierSchema),
    mode: 'onChange',
  });

  const { onSubmit, supplier } = useSupplier(methods, supplierId);
  const router = useRouter();
  const [isModalOpen, setModalOpen] = useState(false);

  const handleBack = () => router.back();
  const openModal = () => setModalOpen(true);
  const closeModal = () => setModalOpen(false);

  const handleDelete = async () => {
    try {
      await SupplierService.deleteSupplier(supplierId);
      closeModal();
      await router.push(RouterPaths.SUPPLIERS_LIST);
      notify(NotificationType.SUCCESS, 'Dostawca został usunięty');
    } catch (error) {
      console.error('Error deleting supplier:', error);
      closeModal();

      const messages = error?.response?.data?.validation?.messages;
      if (messages?.length) {
        messages.forEach((msg) => {
          notify(NotificationType.ERROR, msg.reason || 'Nieznany błąd podczas usuwania.');
        });
      } else {
        notify(NotificationType.ERROR, 'Wystąpił błąd podczas usuwania dostawcy.');
      }
    }
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={onSubmit}>
        <Stack>
          <SupplierFields supplier={supplier} />
          <MantineGroup>
            <Button type="submit" color="green" disabled={!methods.formState.isValid}>
              {supplierId ? 'Zapisz zmiany' : 'Utwórz sprzedawcę'}
            </Button>
            <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.DELETE_SUPPLIER] } }}>
              {supplierId && (
                <Button type="button" variant="filled" color="red" onClick={openModal}>
                  Usuń
                </Button>
              )}
            </RoleBasedComponent>
            <Button type="button" variant="filled" onClick={handleBack}>
              Powrót
            </Button>
          </MantineGroup>
        </Stack>
      </form>
      <ConfirmModal
        opened={isModalOpen}
        onClose={closeModal}
        onConfirm={handleDelete}
        title="Potwierdzenie usunięcia"
      >
        Czy na pewno chcesz usunąć tego sprzedawcę?
      </ConfirmModal>
    </FormProvider>
  );
}

export default SupplierForm;
