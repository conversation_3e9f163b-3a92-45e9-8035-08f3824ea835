import React from 'react';
import { <PERSON>, FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Container, PasswordInput, Stack } from '@mantine/core';
import { createChangePasswordSchema } from '@/components/users/change-password/changePasswordSchema';
import { UserService } from '@/services/UserService';
import { NotificationType } from '@/types/Common';
import { notify } from '@/utils/notify';
import { SafeSubmitButton } from '@/components/common/forms/SafeButton/SafeButton';

interface ChangePasswordFormProps {
  firstName?: string;
  lastName?: string;
  username?: string;
}

const ChangePasswordForm: React.FC<ChangePasswordFormProps> = ({
  firstName,
  lastName,
  username,
}) => {
  const changePasswordSchema = createChangePasswordSchema(firstName, lastName, username);

  const methods = useForm({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmNewPassword: '',
    },
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const onSubmit = async (data: any) => {
    try {
      const result = await UserService.changePassword(data);
      if (result.success) {
        notify(NotificationType.SUCCESS, 'Hasło zostało pomyślnie zmienione');
      } else {
        notify(
          NotificationType.ERROR,
          typeof result.error === 'object' ? JSON.stringify(result.error) : result.error
        );
      }
    } catch (error) {
      notify(
        NotificationType.ERROR,
        typeof error === 'object' ? JSON.stringify(error) : String(error)
      );
    }
  };

  return (
    <FormProvider {...methods}>
      <Container>
        <form>
          <Stack>
            <Controller
              name="currentPassword"
              control={control}
              render={({ field }) => (
                <PasswordInput
                  label="Podaj stare hasło"
                  {...field}
                  error={errors.currentPassword?.message}
                  required
                />
              )}
            />
            <Controller
              name="newPassword"
              control={control}
              render={({ field }) => (
                <PasswordInput
                  label="Podaj nowe hasło"
                  {...field}
                  error={errors.newPassword?.message}
                  required
                />
              )}
            />
            <Controller
              name="confirmNewPassword"
              control={control}
              render={({ field }) => (
                <PasswordInput
                  label="Potwierdź nowe hasło"
                  {...field}
                  error={errors.confirmNewPassword?.message}
                  required
                />
              )}
            />
            <SafeSubmitButton onValid={onSubmit} style={{ marginTop: '20px' }}>
              Wyślij
            </SafeSubmitButton>
          </Stack>
        </form>
      </Container>
    </FormProvider>
  );
};

export default ChangePasswordForm;
