import React from 'react';
import {FormProvider, useForm} from 'react-hook-form';
import {Button, Checkbox, Stack, TextInput} from '@mantine/core';
import {UserService} from '@/services/UserService';
import {zodResolver} from '@hookform/resolvers/zod';
import {showNotification} from "@mantine/notifications";
import {useUser} from "@/components/users/edit/useUser";
import {editEmployeeAccountSchema} from "@/components/users/edit/editEmployeeAccountSchema";
import router from "next/router";
import RoleSelect from "@/components/common/forms/RoleSelect/RoleSelect";
import {UserRole} from "@/types/UserRole";
import {RouterPaths} from "@/services/shared/ApiEndpoints";
import TenantSelect from "@/components/common/forms/TenantSelect/TenantSelect";

interface EditEmployeeAccountFormProps {
    userId: string;
}

const EditEmployeeAccountForm: React.FC<EditEmployeeAccountFormProps> = ({ userId }) => {
    const { user, isLoading, error } = useUser(userId);

    const methods = useForm({
        resolver: zodResolver(editEmployeeAccountSchema),
        defaultValues: {
            username: '',
            email: '',
            firstName: '',
            lastName: '',
            enabled: true,
            role: '',
            whiteListTenant: [],
        },
    });

    const { register, handleSubmit, formState: { errors } } = methods;

    React.useEffect(() => {
        if (user) {
            const whiteListTenant = user.attributes?.['whiteList_tenant']
                ? JSON.parse(user.attributes['whiteList_tenant'][0])
                : [];

            methods.reset({
                username: user.username,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                enabled: user.enabled,
                role: user.realmRoles?.[0] || UserRole.EMPLOYEE,
                whiteListTenant,
            });
        }
    }, [user, methods]);

    const onSubmit = async (values: any) => {
        try {
            const payload = {
                ...values,
                whiteListTenant: values.whiteListTenant.map((tenant: { uuid: string; name: string }) => ({
                    uuid: tenant.uuid,
                    name: tenant.name,
                })),
            };

            const result = await UserService.updateUser(userId, payload);

            if (result.success) {
                showNotification({
                    title: 'Sukces!',
                    message: 'Użytkownik został zaktualizowany',
                    color: 'green',
                });
                await router.push(RouterPaths.USERS_LIST)
            } else {
                showNotification({
                    title: 'Błąd',
                    message: result.error.errorMessage || 'Wystąpił nieznany błąd',
                    color: 'red',
                });
            }
        } catch (error) {
            showNotification({
                title: 'Błąd',
                message: 'Wystąpił błąd przy aktualizacji konta',
                color: 'red',
            });
        }
    };

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (error) {
        return <div>Wystąpił błąd: {error}</div>;
    }

    return (
        <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack>
                    <TextInput
                        label="Login"
                        placeholder="Wprowadź nazwę użytkownika"
                        {...register('username', {required: 'Nazwa użytkownika jest wymagana'})}
                        error={errors.username?.message}
                    />
                    <TextInput
                        label="Email"
                        placeholder="Wprowadź adres email"
                        {...register('email', {
                            required: 'Adres email jest wymagany',
                            pattern: {value: /^\S+@\S+$/, message: 'Nieprawidłowy adres email'},
                        })}
                        error={errors.email?.message}
                    />
                    <TextInput
                        label="Imię"
                        placeholder="Wprowadź imię"
                        {...register('firstName', {required: 'Imię jest wymagane'})}
                        error={errors.firstName?.message}
                    />
                    <TextInput
                        label="Nazwisko"
                        placeholder="Wprowadź nazwisko"
                        {...register('lastName', {required: 'Nazwisko jest wymagane'})}
                        error={errors.lastName?.message}
                    />
                    <RoleSelect
                        role={user.realmRoles?.[0]}
                    />
                    <TenantSelect name="whiteListTenant" />
                    <Checkbox
                        label="Aktywne konto"
                        {...register('enabled')}
                    />
                    <Button type="submit">Zaktualizuj konto</Button>
                </Stack>
            </form>
        </FormProvider>
    );
};

export default EditEmployeeAccountForm;