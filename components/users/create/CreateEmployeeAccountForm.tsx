import React, { useState } from 'react';
import { Form<PERSON>rovider, useForm, UseFormReturn, useWatch } from 'react-hook-form';
import { Button, Checkbox, PasswordInput, Stack, TextInput } from '@mantine/core';
import { UserService } from '@/services/UserService';
import { zodResolver } from '@hookform/resolvers/zod';
import { createEmployeeAccountSchema } from '@/components/users/createEmployeeAccountSchema';
import RoleSelect from '@/components/common/forms/RoleSelect/RoleSelect';
import { NotificationType } from '@/types/Common';
import { notify } from '@/utils/notify';
import router from 'next/router';
import { UserAction } from '@/types/User';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import TenantSelect from '@/components/common/forms/TenantSelect/TenantSelect';
import { SafeSubmitButton } from '@/components/common/forms/SafeButton/SafeButton';

interface IFormInputs {
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    role: string;
    requiredActions: UserAction[];
    whiteListTenant: { uuid: string; name: string }[];
}

const CreateEmployeeAccountForm = () => {
    const methods: UseFormReturn<IFormInputs> = useForm({
        resolver: zodResolver(createEmployeeAccountSchema),
        defaultValues: {
            username: '',
            email: '',
            firstName: '',
            lastName: '',
            password: '',
            requiredActions: [],
            role: 'realm_employee',
            whiteListTenant: [],
        },
    });

    const { register, handleSubmit, setValue, formState: { errors } } = methods;
    const [apiError, setApiError] = useState<string | null>(null);
    const watchedRequiredActions = useWatch({ control: methods.control, name: 'requiredActions' });

    const onSubmit = async (values: IFormInputs) => {
        setApiError(null);
        try {
            const payload = {
                ...values,
                whiteListTenant: values.whiteListTenant.map((tenant) => ({
                    uuid: tenant.uuid,
                    name: tenant.name,
                })),
            };

            const result = await UserService.createUser(payload);

            if (result.success && typeof result.data === 'string') {
                const match = result.data.match(/User created with ID: (.+)/);
                if (match) {
                    const userId = match[1];
                    methods.reset();
                    notify(NotificationType.SUCCESS, `Użytkownik został utworzony z ID: ${userId}`);
                    await router.push(RouterPaths.USERS_LIST);
                } else {
                    setApiError('Nie udało się wyodrębnić identyfikatora użytkownika z odpowiedzi serwera.');
                    notify(NotificationType.ERROR, 'Nie udało się wyodrębnić identyfikatora użytkownika z odpowiedzi serwera.');
                }
            } else {
                setApiError(result.error.errorMessage || 'Wystąpił nieznany błąd');
                notify(NotificationType.ERROR, result.error.errorMessage || 'Wystąpił nieznany błąd');
            }
        } catch (error) {
            setApiError('Wystąpił błąd przy tworzeniu konta');
            notify(NotificationType.ERROR, 'Wystąpił błąd przy tworzeniu konta');
        }
    };


    const handleCheckboxChange = (action: UserAction) => {
        const currentActions: UserAction[] = methods.getValues('requiredActions');
        if (currentActions.includes(action)) {
            setValue('requiredActions', currentActions.filter((a) => a !== action));
        } else {
            setValue('requiredActions', [...currentActions, action]);
        }
    };

    return (
        <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack>
                    <TextInput
                        label="Login"
                        placeholder="Wprowadź nazwę użytkownika"
                        {...register('username', { required: 'Nazwa użytkownika jest wymagana' })}
                        error={errors.username?.message}
                    />
                    <TextInput
                        label="Email"
                        placeholder="Wprowadź adres email"
                        {...register('email', {
                            required: 'Adres email jest wymagany',
                            pattern: {value: /^\S+@\S+$/, message: 'Nieprawidłowy adres email'},
                        })}
                        error={errors.email?.message}
                    />
                    <TextInput
                        label="Imię"
                        placeholder="Wprowadź imię"
                        {...register('firstName', {required: 'Imię jest wymagane'})}
                        error={errors.firstName?.message}
                    />
                    <TextInput
                        label="Nazwisko"
                        placeholder="Wprowadź nazwisko"
                        {...register('lastName', {required: 'Nazwisko jest wymagane'})}
                        error={errors.lastName?.message}
                    />
                    <PasswordInput
                        label="Hasło"
                        placeholder="Wprowadź hasło"
                        {...register('password', {required: 'Hasło jest wymagane'})}
                        error={errors.password?.message}
                    />
                    <Checkbox
                        label="Wymuszenie aktywacji email"
                        checked={watchedRequiredActions.includes(UserAction.VERIFY_EMAIL)}
                        onChange={() => handleCheckboxChange(UserAction.VERIFY_EMAIL)}
                    />
                    <Checkbox
                        label="Wymuszenie zmiany hasła"
                        checked={watchedRequiredActions.includes(UserAction.UPDATE_PASSWORD)}
                        onChange={() => handleCheckboxChange(UserAction.UPDATE_PASSWORD)}
                    />
                    <RoleSelect />
                    <TenantSelect
                      name="whiteListTenant"
                      error={errors.whiteListTenant?.message}
                    />
                    {apiError && <div style={{color: 'red'}}>{apiError}</div>}
                  <SafeSubmitButton onValid={onSubmit}>Utwórz konto</SafeSubmitButton>
                </Stack>
            </form>
        </FormProvider>
    );
};

export default CreateEmployeeAccountForm;