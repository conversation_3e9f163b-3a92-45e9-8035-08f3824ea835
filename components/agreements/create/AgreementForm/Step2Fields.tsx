import { useForm, useFormContext } from 'react-hook-form';
import { <PERSON>et, Grid, Menu, rem, Text } from '@mantine/core';
import useCreateContract from '@/components/agreements/create/AgreementForm/useCreateContract';
import { useEffect, useMemo, useState } from 'react';
import ContractForm from '@/components/agreements/create/AgreementForm/ContractForm';
import { ContractForm as EditContractForm } from '@/components/agreements/edit/EditAgreementForm/ContractForm';
import {
  MantineReactTable,
  MRT_ColumnDef,
  MRT_RowData,
  useMantineReactTable,
} from 'mantine-react-table';
import { IconDots, IconPencil, IconTrash } from '@tabler/icons-react';
import { CreateContract, PurchaseModel } from '@/types/Agreement';
import dayjs from 'dayjs';
import CustomModal from '@/components/common/forms/Modal/CustomModal';
import useWalletNotifications from '@/components/common/hooks/useWalletNotifications';
import { v4 as uuidv4 } from 'uuid';
import { zodResolver } from '@hookform/resolvers/zod';
import { ContractSchema } from '@/components/agreements/edit/EditAgreementForm/Schema';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { ConfirmContractDelete } from '@/components/agreements/common/ConfirmContractDelete/ConfirmContractDelete';

// @ts-ignore

function Step2Fields() {
  const methods = useFormContext();
  const {
    control,
    formState: { errors },
    watch,
  } = useFormContext();
  const { contracts, setContracts } = useCreateContract();
  const { successNotification, errorNotification } = useWalletNotifications();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const mediaType = watch('mediaType');
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [contractIndex, setContractIndex] = useState(null);
  const defaultValues = {
    id: uuidv4(),
    year: new Date().getFullYear().toString(),
    name: '',
    averageCalculation: {
      startDate: new Date(),
      endDate: new Date(),
    },
    purchaseModel: PurchaseModel.PERCENTAGE,
    priceReference: '',
    orderTimes: {},
    actionIfNot100: '',
    contractType: '',
    quantity: {
      type: null,
      value: '',
    },
    finalPurchaseDate: {
      type: null,
      value: null,
    },
    volume: {
      type: null,
      value: null,
    },
    orderTypeParameters: {
      type: null,
      value: null,
    },
    rejectionReason: {
      type: null,
      value: null,
    },
    '@type': 'Energy',
  };
  const form = useForm({
    mode: 'onBlur',
    resolver: zodResolver(ContractSchema),
    defaultValues: defaultValues,
  });
  const openEditModal = (row: MRT_RowData) => {
    const original = row?.row?.original;
    form.reset({
      id: original.id,
      year: original.year,
      name: original.name,
      purchaseModel: original.purchaseModel,
      priceReference: original.priceReference,
      orderTimes: original.orderTimes ?? original.orderTime ?? {},
      actionIfNot100: original.actionIfNot100,
      quantity: original.quantity,
      averageCalculation: {
        startDate: new Date(original.averageCalculation?.startDate),
        endDate: new Date(original.averageCalculation?.endDate),
      },
      volume: original.volume,
      orderTypeParameters: original.orderTypeParameters,
      rejectionReason: original.rejectionReason,
      '@type': original['@type'],
      contractType: original.contractType,
    });
    setEditModalOpen(true);
  };
  const closeEditModal = () => {
    errorNotification({ id: 'cacem', title: '', message: 'Kontrakt nie został zaktualizowany' });
    setEditModalOpen(false);
  };
  const submitEditModal = async () => {
    const isValid = await form.trigger();
    if (isValid) {
      const contract = form.getValues();
      (contract as any).orderTime = contract.orderTimes;
      delete (contract as any).orderTimes;
      const allContracts = methods.getValues().contracts;
      const idx = allContracts.findIndex((c) => c.id === contract.id);
      allContracts.splice(idx, 1, contract);
      setContracts(allContracts);
      form.reset(defaultValues);
      successNotification({ id: 'cacem', title: '', message: 'Kontrakt został zaktualizowany.' });
      setEditModalOpen(false);
    }
  };
  const openCreateModal = () => {
    form.reset(defaultValues);
    setCreateModalOpen(true);
  };
  const submitCreateModal = async () => {
    await form.trigger();
    if (form.formState.isValid) {
      const contract = form.getValues();
      const contracts = methods.getValues().contracts;
      // const contracts: [] = values.contracts;
      const idx = contracts.findIndex((c) => c.id === contract.id);
      contracts.splice(idx, 1, contract);
      setContracts(contracts);
      successNotification({ id: 'caccm', title: '', message: 'Kontrakt został dodany.' });
      form.reset(defaultValues);
      setCreateModalOpen(false);
    }
  };
  const closeCreateModal = () => {
    errorNotification({ id: 'caccm', title: '', message: 'Kontrakt nie został zaktualizowany' });
    setCreateModalOpen(false);
  };

  const openDeleteModal = (row: MRT_RowData) => {
    const contractIndex = methods.getValues().contracts?.findIndex((c: CreateContract) => {
      return c.name === row?.row?.original?.name;
    });
    setContractIndex(contractIndex);
    setDeleteModalOpen(true);
  };

  useEffect(() => {
    methods.setValue('contracts', contracts);
  }, [contracts]);
  const columns = useMemo(
    () =>
      [
        {
          accessorKey: 'name',
          header: 'Nazwa',
        },
        {
          accessorKey: 'year',
          header: 'Rok kontraktacji',
        },
        {
          accessorFn: (data) => data.averageCalculation?.startDate,
          header: 'Początek zakupów',
          Cell: ({ cell }) => dayjs(cell.getValue<string>()).format('YYYY-MM-DD'),
        },
        {
          accessorFn: (data) => data.averageCalculation?.endDate,
          header: 'Koniec zakupów',
          Cell: ({ cell }) => dayjs(cell.getValue<string>()).format('YYYY-MM-DD'),
        },
        {
          accessorKey: 'quantity.type',
          header: 'Liczba transz - typ',
        },
        {
          accessorKey: 'quantity.value',
          header: 'Liczba transz - value',
        },
        {
          accessorKey: 'quantity.x',
          header: 'Liczba transz - x',
        },
        {
          accessorKey: 'quantity.y',
          header: 'Liczba transz - y',
        },
        {
          accessorKey: 'quantity.z',
          header: 'Liczba transz - z',
        },
        {
          accessorKey: 'volume.type',
          header: 'Wolumen',
        },
        {
          accessorKey: 'volume.value',
          header: 'Wolumen',
        },
        {
          accessorKey: 'volume.x',
          header: 'Wolumen',
        },
        {
          accessorKey: 'volume.y',
          header: 'Wolumen',
        },
        {
          accessorKey: 'actionIfNot100.type',
          header: 'Co gdy nie kupi 100%?',
        },
        {
          accessorKey: 'actionIfNot100.value',
          header: 'Co gdy nie kupi 100%?',
        },
        {
          accessorKey: 'rejectionReason.type',
          header: 'Powód odrzucenia',
        },
        {
          accessorKey: 'rejectionReason.value',
          header: 'Powód odrzucenia',
        },
        {
          accessorKey: 'priceReference',
          header: 'Cena referencyjna',
        },
        {
          enableSorting: false,
          enableColumnActions: false,
          id: 'actions',
          header: 'Operacje',
          Cell: (row: MRT_RowData) => (
            <>
              <Menu shadow="md" width={200}>
                <Menu.Target>
                  <IconDots style={{ width: rem(14), height: rem(14) }} />
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Label>
                    <Text size="xs">Kontrakt {row?.row?.original?.name}</Text>
                  </Menu.Label>
                  {/*<Menu.Item*/}
                  {/*  onClick={() => openCreateModal()}*/}
                  {/*  leftSection={<IconPlus style={{ width: rem(14), height: rem(14) }} />}*/}
                  {/*>*/}
                  {/*  Dodaj nowy*/}
                  {/*</Menu.Item>*/}
                  <Menu.Item
                    onClick={() => openEditModal(row)}
                    leftSection={<IconPencil style={{ width: rem(14), height: rem(14) }} />}
                  >
                    Edytuj {row?.row?.original?.name}
                  </Menu.Item>
                  <Menu.Item
                    onClick={() => openDeleteModal(row)}
                    leftSection={<IconTrash style={{ width: rem(14), height: rem(14) }} />}
                  >
                    Usuń {row?.row?.original?.name}
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </>
          ),
        },
      ] as MRT_ColumnDef<CreateContract>[],
    []
  );
  const table = useMantineReactTable({
    columns,
    data: contracts,
    manualFiltering: false,
    manualPagination: false,
    manualSorting: false,
    localization: MRT_Localization_PL,
    mantinePaginationProps: {
      rowsPerPageOptions: ['50', '100', '150'],
    },
    initialState: {
      showColumnFilters: true,
      density: 'xs',
      columnVisibility: {
        'quantity.type': false,
        'quantity.value': false,
        'quantity.x': false,
        'quantity.y': false,
        'quantity.z': false,
        'volume.type': false,
        'volume.value': false,
        'volume.x': false,
        'volume.y': false,
        'actionIfNot100.type': false,
        'actionIfNot100.value': false,
        'rejectionReason.type': false,
        'rejectionReason.value': false,
        priceReference: false,
      },
      pagination: {
        pageSize: 50,
        pageIndex: 0,
      },
    },
  });

  return (
    <>
      <Grid>
        <Grid.Col span={12}>
          <Fieldset legend="Dane kontraktów" radius="md">
            <MantineReactTable table={table} />
            <CustomModal
              opened={createModalOpen}
              onClose={closeCreateModal}
              onConfirm={submitCreateModal}
              title="Formularz dodawania kontraktu"
              size="70%"
              radius="md"
            >
              <ContractForm form={form} />
            </CustomModal>
            <CustomModal
              opened={editModalOpen}
              onClose={closeEditModal}
              onConfirm={submitEditModal}
              title="Formularz edycji kontraktu"
              size="70%"
              radius="md"
            >
              <EditContractForm mediaType={mediaType} form={form} />
            </CustomModal>
            <ConfirmContractDelete
              setModalOpen={setDeleteModalOpen}
              isModalOpen={deleteModalOpen}
              setContractIndex={setContractIndex}
              contractIndex={contractIndex}
              setContracts={(contracts) => setContracts(contracts)}
            />
          </Fieldset>
        </Grid.Col>
      </Grid>
    </>
  );
}

export default Step2Fields;
