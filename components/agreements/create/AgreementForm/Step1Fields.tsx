import { useFormContext, useWatch } from 'react-hook-form';
import { Fieldset, Grid, Group, Loader } from '@mantine/core';
import MediaTypeSelect from '@/components/common/forms/MediaTypeSelect/MediaTypeSelect';
import SupplierSelect from '@/components/common/forms/SupplierSelect/SupplierSelect';
import DatePicker from '@/components/common/forms/DatePicker/DatePicker';
import PurchaseModelSelect from '@/components/common/forms/PurchaseModelSelect/PurchaseModelSelect';
import OrderTypeSelect from '@/components/common/forms/OrderTypeSelect/OrderTypeSelect';
import PriceReference from '@/components/common/forms/PurchaseByPriceType/PriceReference';
import AvailableMediaContractSelect from '@/components/common/forms/AvailableMediaContractSelect/AvailableMediaContractSelect';
import MediaActionIfNot100Select from '@/components/common/forms/MediaActionIfNot100Select/MediaActionIfNot100Select';
import MediaRejectionReasonSelect from '@/components/common/forms/MediaRejectionReasonSelect/MediaRejectionReasonSelect';
import CustomerSelect from '@/components/common/forms/CustomerSelect/CustomerSelect';
import MediaFinalPurchaseDate from '@/components/common/forms/MediaFinalPurchaseDate/MediaFinalPurchaseDate';
import MediaQuantity from '@/components/common/forms/MediaQuantity/MediaQuantity';
import MediaVolume from '@/components/common/forms/MediaVolume/MediaVolume';
import React, { useEffect, useState } from 'react';
import PropertyRightsPurchaseModelSelect from '@/components/common/forms/PropertyRights/PropertyRightsPurchaseModelSelect/PropertyRightsPurchaseModelSelect';
import PropertyRightsQuantityValueInput from '@/components/common/forms/PropertyRights/PropertyRightsQuantityValueInput/PropertyRightsQuantityValueInput';
import PropertyRightsAvailableProductsMultiSelect from '@/components/common/forms/PropertyRights/PropertyRightsAvailableProductsMultiSelect/PropertyRightsAvailableProductsMultiSelect';
import PropertyRightsQuantityTypeSelect from '@/components/common/forms/PropertyRights/PropertyRightsQuantityTypeSelect/PropertyRightsQuantityTypeSelect';
import PropertyRightsFinalPurchaseDateSelect from '@/components/common/forms/PropertyRights/PropertyRightsFinalPurchaseDateSelect/PropertyRightsFinalPurchaseDateSelect';
import PropertyRightsActionIfNot100Select from '@/components/common/forms/PropertyRights/PropertyRightsActionIfNot100Select/PropertyRightsActionIfNot100Select';
import PropertyRightsCommentInput from '@/components/common/forms/PropertyRights/PropertyRightsCommentInput/PropertyRightsCommentInput';
import { GroupService } from '@/services/GroupService';
import AuthorizedToOrder from '@/components/common/forms/AuthorizedToOrder/AuthorizedToOrder';
import PropertyRightsVolumeSelect from '@/components/common/forms/PropertyRights/PropertyRightsVolumeTypeSelect/PropertyRightsVolumeTypeSelect';
import PropertyRightsPriceReferenceSelect from '@/components/common/forms/PropertyRights/PropertyRightsPriceReferenceSelect/PropertyRightsPriceReferenceSelect';
import TextInput from '@/components/common/forms/TextInput/TextInput';
import EditableAgreementGroupSelect from '@/components/common/forms/EditableAgreementGroupSelect/EditableAgreementGroupSelect';
import useCustomers from '@/components/agreements/create/useCustomers';
import getMonthsBetweenDates from '@/utils/get-months-between-dates';
import useVolumesStore from '@/stores/agreement-store/volumesStore';
import { MediaType } from '@/types/MediaType';
import Volumes from '@/components/agreements/create/AgreementForm/Volumes';
import useDuplicate from '@/components/agreements/create/useDuplicate';
import { useRouter } from 'next/router';
import { FormType, MediaRejectionReason, PurchaseModel } from '@/types/Agreement';
import RequiresAcceptanceFields from '@/components/agreements/common/RequiresAcceptanceFields/RequiresAcceptanceFields';
import { useAgreementStore } from '@/stores/agreement-store/agreement-provider';

// @ts-ignore
function Step1Fields({}) {
  const { setVolumeMonths, months } = useAgreementStore((state) => state);
  const router = useRouter();
  const customersQuery = useCustomers();
  const agreementIdListParameter: string = router?.query?.agreementIdList as string;
  const duplicateQuery = useDuplicate(agreementIdListParameter);
  const methods = useFormContext();
  const {
    control,
    setValue,
    formState: { errors },
    watch,
  } = useFormContext();
  const customersWatch = watch('customers');
  const formType = watch('formType');
  const startDate = watch('startDate');
  const endDate = watch('endDate');
  const group = watch('group');
  const mediaType = watch('mediaType');
  const propertyRightsPurchaseModel = useWatch({ control, name: 'propertyRights.purchaseModel' });
  const [selectedOrderType, setSelectedOrderType] = useState<string>('');
  const [selectedVolumeType, setSelectedVolumeType] = useState<string>('');
  const { volumesList } = useVolumesStore();

  useEffect(() => {
    if (group) {
      GroupService.fetchGroupById(group).then((g) => {
        const existingCustomers = methods.getValues('customers');
        const newCustomers = g.customers.map((c) => c.id);
        const mergedCustomers = Array.from(new Set([...existingCustomers, ...newCustomers]));
        setValue('customers', mergedCustomers);
      });
    }
  }, [group, setValue]);

  useEffect(() => {
    if (mediaType === MediaType.GAS) {
      setValue('propertyRights.purchaseModel', PurchaseModel.NOT_APPLICABLE);
    }
  }, [mediaType]);

  useEffect(() => {
    if (propertyRightsPurchaseModel && propertyRightsPurchaseModel !== 'TRANCHE') {
      setValue('propertyRights.volume.type', 'OTHER', { shouldValidate: true });
      setValue('propertyRights.rejectionReason.type', 'OTHER', { shouldValidate: true });
    }
  }, [propertyRightsPurchaseModel, setValue]);

  useEffect(() => {
    if (startDate != null && endDate != null) {
      const months = getMonthsBetweenDates(startDate, endDate);
      setVolumeMonths(months);
    }
  }, [startDate, endDate]);

  useEffect(() => {
    if (volumesList.length > 0) {
      setValue('volumesList', volumesList);
    }
  }, [volumesList, setValue]);

  useEffect(() => {
    if (router?.query?.agreementIdList) {
      const customers = duplicateQuery?.data?.multipleResponseList.map(
        (response) => response.customerId
      );
      const volumesList = duplicateQuery?.data?.multipleResponseList.map(
        (response) => response.volumes
      );

      if (duplicateQuery.data) {
        const months = duplicateQuery.data.months;
        setVolumeMonths(months);
        const queryData = duplicateQuery.data;
        methods.reset({
          formType: FormType.CUSTOMER,
          customers: customers,
          volumesList: volumesList,
          mediaType: queryData.mediaType,
          supplier: queryData.supplier.id,
          purchaseModel: queryData.purchaseModel,
          group: '',
          startDate: null,
          endDate: null,
          averageCalculationStartDate: null,
          authorizedBuyers: queryData.authorizedBuyers,
          contracts: [],
          requiresCustomerAcceptance: queryData.requiresCustomerAcceptance,
          sendRecommendation: queryData.sendRecommendation,
          media: {
            purchaseModel: queryData.media.purchaseModel,
            availableProducts: queryData.media.availableProducts,
            priceReference: queryData.media.priceReference,
            orderTime: queryData.media.orderTime,
            actionIfNot100: {
              type: queryData.media.actionIfNot100.type,
              value: queryData.media?.actionIfNot100?.value
                ? queryData.media.actionIfNot100.value
                : '',
            },
            quantity: {
              type: queryData.media.quantity.type,
              value: Number(queryData.media?.quantity?.value),
              year: Number(queryData?.media?.quantity?.year),
              quarter: Number(queryData?.media?.quantity?.quarter),
              month: Number(queryData.media?.quantity?.month),
            },
            finalPurchaseDate: {
              type: queryData.media.finalPurchaseDate.type,
              value: queryData.media.finalPurchaseDate.value,
            },
            volume: queryData.media.volume,
            orderTypeParameters: queryData.media.orderTypeParameters,
            rejectionReason: {
              type: queryData.media.rejectionReason.type,
              value:
                queryData.media.rejectionReason.type ===
                MediaRejectionReason.FIXED_TRANSACTION_NUMBER_NOT_REACHED
                  ? Number(queryData.media.rejectionReason.value)
                  : String(queryData.media.rejectionReason.value ?? ''),
            },
          },
          propertyRights: {
            purchaseModel: queryData.propertyRights.purchaseModel,
            comment: queryData.propertyRights.comment,
            availableProducts: queryData.propertyRights.availableProducts,
            priceReference: queryData.propertyRights.priceReference,
            orderTime: queryData.propertyRights.orderTime,
            actionIfNot100: queryData.propertyRights.actionIfNot100,
            quantity: {
              type: queryData.propertyRights?.quantity?.type,
              value: String(queryData.propertyRights?.quantity?.value ?? ''),
            },
            finalPurchaseDate: {
              type: queryData.propertyRights.finalPurchaseDate.type,
              value: queryData.propertyRights.finalPurchaseDate.value,
            },
            volume: {
              type: queryData.propertyRights.volume.type,
              value:
                queryData.propertyRights.volume.type === 'OTHER'
                  ? String(queryData.propertyRights.volume.value ?? '')
                  : Number(queryData.propertyRights.volume.value),
              x: Number(queryData.propertyRights.volume.value),
              y: Number(queryData.propertyRights.volume.value),
            },
            orderTypeParameters: queryData.propertyRights.orderTypeParameters,
            rejectionReason: {
              type: queryData.propertyRights.rejectionReason.type,
              value:
                queryData.propertyRights.rejectionReason.type ===
                MediaRejectionReason.FIXED_TRANSACTION_NUMBER_NOT_REACHED
                  ? Number(queryData.propertyRights.rejectionReason.value)
                  : String(queryData.propertyRights.rejectionReason.value ?? ''),
            },
          },
        });
      }
    }
  }, [duplicateQuery.data]);
  return (
    <>
      {customersQuery?.isLoading ? (
        <Loader color="orange" />
      ) : (
        <>
          <Fieldset legend="Dane podstawowe" radius="md">
            <Grid>
              <Grid.Col span={3}>
                <CustomerSelect
                  name="customers"
                  label="Klient"
                  placeholder="Wybierz klienta"
                  withAsterisk
                  customers={customersQuery?.data}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <EditableAgreementGroupSelect name="agreementGroup" label="Nazwa grupy zakupowej" />
              </Grid.Col>
              <Grid.Col span={3}>
                <MediaTypeSelect
                  name="mediaType"
                  label="Typ mediów (Nośnik)"
                  placeholder="Wybierz typ mediów"
                  withAsterisk
                />
              </Grid.Col>

              <Grid.Col span={3}>
                <SupplierSelect
                  name="supplier"
                  label="Sprzedawca"
                  placeholder="Wybierz sprzedawcę"
                  withAsterisk
                />
              </Grid.Col>

              <Grid.Col span={3}>
                <DatePicker
                  name="startDate"
                  label="Data początku umowy"
                  placeholder="Data początku umowy"
                  withAsterisk
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <DatePicker
                  name="endDate"
                  label="Data zakończenia umowy"
                  placeholder="Data zakończenia umowy"
                  withAsterisk
                />
              </Grid.Col>

              <Grid.Col span={3}>
                <DatePicker
                  name="averageCalculationStartDate"
                  label="Start zakupów (Początek liczenia średniej)"
                  placeholder="Start zakupów (Początek liczenia średniej)"
                  withAsterisk
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <AuthorizedToOrder
                  name="authorizedBuyers"
                  label="Osoby uprawnione do zakupu"
                  placeholder="Osoby uprawnione do zakupu"
                  withAsterisk
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <TextInput name="description" label="Opis" placeholder="Opis umowy" />
              </Grid.Col>
              <Grid.Col span={3}>
                <RequiresAcceptanceFields />
              </Grid.Col>
            </Grid>
          </Fieldset>
          <Fieldset legend="Warunki zakupowe" radius="md">
            <Grid>
              <Grid.Col span={3}>
                <AvailableMediaContractSelect
                  name="media.availableProducts"
                  label="Dostępne kontrakty"
                  placeholder="Dostępne kontrakty"
                  withAsterisk
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <PurchaseModelSelect
                  name="media.purchaseModel"
                  label="Model zakupu"
                  placeholder="Wybierz model zakupu"
                  withAsterisk
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Group>
                  <OrderTypeSelect
                    name="media.orderTypeParameters"
                    label="Forma zlecenia"
                    placeholder="Forma zlecenia"
                    withAsterisk
                  />
                </Group>
                <Group>
                  <PriceReference
                    name="media.priceReference"
                    label="Zamówienie/Cena na dzień"
                    placeholder="Zamówienie/Cena na dzień"
                    withAsterisk
                  />
                </Group>
                <Group>
                  <MediaQuantity
                    name="media.quantity"
                    label="Maksymalna liczba transz"
                    placeholder="Maksymalna liczba transz"
                    withAsterisk
                  />
                </Group>
                <Group>
                  <MediaVolume
                    name="media.volume"
                    label="Wolumen na transzę"
                    placeholder="Wolumen na transzę"
                    withAsterisk
                  />
                </Group>
                <Group>
                  <MediaFinalPurchaseDate
                    name="media.finalPurchaseDate"
                    label="Ostatni dzień zakupu"
                    placeholder="Ostatni dzień zakupu"
                    withAsterisk
                  />
                </Group>
                <Group>
                  <MediaRejectionReasonSelect
                    name="media.rejectionReason"
                    label="Powód odrzucenia zakupu"
                    placeholder="Powód odrzucenia zakupu"
                    withAsterisk
                  />
                </Group>
              </Grid.Col>
              <Grid.Col span={3}>
                <MediaActionIfNot100Select
                  name="media.actionIfNot100"
                  label="Co gdy nie kupi 100%?"
                  placeholder="Co gdy nie kupi 100%?"
                  withAsterisk
                />
              </Grid.Col>
            </Grid>
          </Fieldset>
          {mediaType === MediaType.ENERGY ? (
            <Fieldset legend="Prawa majątkowe" radius="md">
              <Grid>
                <Grid.Col span={12}>
                  <Group>
                    <PropertyRightsAvailableProductsMultiSelect
                      name="propertyRights.availableProducts"
                      label="Rodzaj praw majątkowych"
                      placeholder="Rodzaj praw majątkowych"
                      withAsterisk
                    />
                  </Group>
                </Grid.Col>
                <Grid.Col span={12}>
                  <Group>
                    <PropertyRightsPurchaseModelSelect
                      name="propertyRights.purchaseModel"
                      label="Model zakupu"
                      placeholder="Wybierz model zakupu"
                      withAsterisk
                    />
                  </Group>
                </Grid.Col>
                {propertyRightsPurchaseModel === 'TRANCHE' && (
                  <>
                    <Grid.Col span={12}>
                      <Group>
                        <OrderTypeSelect
                          name="propertyRights.orderTypeParameters"
                          label="Forma zlecenia"
                          placeholder="Wybierz formę zlecenia"
                          withAsterisk
                        />
                      </Group>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Group>
                        <PropertyRightsPriceReferenceSelect
                          name="propertyRights.priceReference"
                          label="Zamówienie/Cena na dzień"
                          placeholder="Wybierz typ"
                          withAsterisk
                        />
                      </Group>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Group grow style={{ width: '100%' }}>
                        <PropertyRightsQuantityTypeSelect
                          name="propertyRights.quantity.type"
                          label="Liczba transz"
                          placeholder="Liczba transz"
                          withAsterisk
                        />
                        <PropertyRightsQuantityValueInput
                          name="propertyRights.quantity.value"
                          label="Wartość min. X% max. Y%"
                          placeholder="Przykład: 1-4"
                          withAsterisk
                        />
                      </Group>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Group grow style={{ width: '100%' }}>
                        <PropertyRightsVolumeSelect
                          name="propertyRights.volume"
                          label="Wolumen na transzę"
                          placeholder="Wybierz typ"
                          onVolumeTypeChange={setSelectedVolumeType}
                          withAsterisk
                        />
                      </Group>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Group grow>
                        <PropertyRightsFinalPurchaseDateSelect
                          name="propertyRights.finalPurchaseDate"
                          label="Ostatni dzień zakupu"
                          placeholder="Wybierz datę"
                          withAsterisk
                        />
                      </Group>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Group grow>
                        <MediaRejectionReasonSelect
                          name="propertyRights.rejectionReason"
                          label="Powód odrzucenia zakupu"
                          placeholder="Powód odrzucenia zakupu"
                          withAsterisk
                        />
                      </Group>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Group grow>
                        <PropertyRightsActionIfNot100Select
                          name="propertyRights.actionIfNot100"
                          label="Co gdy nie kupi 100%?"
                          placeholder="Co gdy nie kupi 100%?"
                          withAsterisk
                        />
                      </Group>
                    </Grid.Col>
                  </>
                )}
                {propertyRightsPurchaseModel === 'PASSIVE_PURCHASE' && (
                  <Grid.Col span={12}>
                    <PropertyRightsCommentInput
                      name="propertyRights.comment"
                      label="Komentarz"
                      placeholder="Wpisz komentarz"
                      withAsterisk
                    />
                  </Grid.Col>
                )}
              </Grid>
            </Fieldset>
          ) : null}
          <Fieldset
            legend={`Deklarowany wolumen ${mediaType === MediaType.ENERGY ? 'energii (MWh)' : 'gazu (MWh)'}`}
            radius="md"
          >
            {customersWatch.map((element: any, index: number) => {
              const customer = customersQuery?.data?.find((customer) => customer.value === element);
              return <Volumes index={index} customer={customer} months={months} />;
            })}
          </Fieldset>
          {/*<CustomersVolumeForm customers={customersWatch} months={months} />*/}
        </>
      )}
    </>
  );
}

export default Step1Fields;
