import { <PERSON><PERSON>, Group, Stepper } from '@mantine/core';
import { FormProvider, useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import Step1Fields from '@/components/agreements/edit/EditAgreementForm/Step1Fields';
import Step2Fields from '@/components/agreements/edit/EditAgreementForm/Step2Fields';
import { AgreementService } from '@/services/AgreementService';
import useWalletNotifications from '@/components/common/hooks/useWalletNotifications';

import { MediaType } from '@/types/MediaType';
import { ActionIfNot100, Agreement, PurchaseModel, Volumes } from '@/types/Agreement';
import useAgreement from '@/components/agreements/edit/useAgreement';
import useVolumesStore from '@/stores/agreement-store/volumesStore';
import volumes from '@/components/agreements/create/AgreementForm/Volumes';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/router';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import { EditAgreementSchema } from '@/components/agreements/edit/EditAgreementForm/Schema';
import useFormErrors from '@/components/customers/customer/create/hooks/useFormErrors';
import { SafeSubmitButton } from '@/components/common/forms/SafeButton/SafeButton';

export function EditAgreementForm() {
  const query = useAgreement();
  const { successNotification, errorNotification } = useWalletNotifications();
  const { volumesList, setVolumesList, resetVolumes } = useVolumesStore();
  const router = useRouter();
  const { handleFormErrors, handleErrorNotifications } = useFormErrors();
  const methods = useForm<Agreement>({
    mode: 'onBlur',
    resolver: zodResolver(EditAgreementSchema),
    defaultValues: {
      id: '',
      customer: { id: '' },
      mediaType: MediaType.ENERGY,
      supplier: { id: '' },
      agreementGroup: null,
      purchaseModel: PurchaseModel.PERCENTAGE,
      startDate: null,
      endDate: new Date().getFullYear() + '-12-31',
      averageCalculationStartDate: null,
      contracts: [],
      description: '',
      requiresCustomerAcceptance: false,
      sendRecommendation: false,
      volumes: {
        monthly: {
          M1: 0,
          M2: 0,
          M3: 0,
          M4: 0,
          M5: 0,
          M6: 0,
          M7: 0,
          M8: 0,
          M9: 0,
          M10: 0,
          M11: 0,
          M12: 0,
        },
        summary: 0,
      },
      media: {
        purchaseModel: PurchaseModel.PERCENTAGE,
        availableProducts: [],
        priceReference: [],
        orderTime: [],
        actionIfNot100: {
          type: ActionIfNot100.OTHER,
          value: null,
        },
        quantity: {
          type: null,
          value: '',
        },
        finalPurchaseDate: {
          type: null,
          value: null,
        },
        volume: {
          type: null,
          value: null,
        },
        orderTypeParameters: {
          type: null,
          value: null,
        },
        rejectionReason: {
          type: null,
          value: null,
        },
      },
      propertyRights: {
        purchaseModel: '',
        comment: '',
        availableProducts: ['PMOZE_A'],
        priceReference: [],
        orderTime: [],
        actionIfNot100: {
          type: ActionIfNot100.OTHER,
          value: null,
        },
        quantity: {
          type: 'OTHER',
          value: null,
        },
        finalPurchaseDate: {
          type: null,
          value: null,
        },
        volume: {
          type: null,
          value: null,
        },
        orderTypeParameters: {
          type: null,
          value: null,
        },
        rejectionReason: {
          type: null,
          value: null,
        },
      },
    },
  });

  useEffect(() => {
    if (query.data) {
      methods.reset({
        id: query.data.id,
        customer: { id: query.data.customer.id },
        mediaType: query.data.mediaType,
        supplier: { id: query.data.supplier.id },
        purchaseModel: query.data.purchaseModel,
        startDate: query.data.startDate,
        endDate: query.data.endDate,
        averageCalculationStartDate: new Date(query.data.averageCalculationStartDate),
        contracts: query.data.contracts,
        description: query.data.description,
        requiresCustomerAcceptance: query.data.requiresCustomerAcceptance ?? false,
        sendRecommendation: query.data.sendRecommendation ?? false,
        volumes: query.data.volumes,
        agreementGroup: query.data.agreementGroup || null,
        authorizedBuyers: query?.data?.authorizedBuyers ? query?.data?.authorizedBuyers : [],
        media: {
          ...query.data.media,
          purchaseModel: query.data.media.purchaseModel as PurchaseModel,
          actionIfNot100: {
            type: query.data.media.actionIfNot100.type as ActionIfNot100,
            value: query.data.media.actionIfNot100.value,
          },
          rejectionReason: {
            type: query.data.media.rejectionReason.type,
            value: query.data.media.rejectionReason.value,
          },
        },
        propertyRights: {
          ...query.data.propertyRights,
          purchaseModel: query.data.propertyRights.purchaseModel,
          actionIfNot100: {
            type: query.data.propertyRights.actionIfNot100.type as ActionIfNot100,
            value: query.data.propertyRights.actionIfNot100.value,
          },
        },
      });
    }
  }, [query.data]);

  const [activeStep, setActiveStep] = useState(0);

  function updateSummary(volumes: Volumes) {
    const monthlyValues = Object.values(volumes.monthly);
    volumes.summary = monthlyValues.reduce((sum, value) => sum + value, 0);
    return volumes;
  }

  async function onSubmit(values: any) {
    try {
      let values = methods.getValues();
      values.volumes = updateSummary(values.volumes);

      const data = await AgreementService.update(values);
      successNotification({
        id: 'tst',
        title: 'Sukces!',
        message: 'Umowa została zapisana!',
      });
      resetVolumes();
      await router.push(RouterPaths.AGREEMENTS_LIST);
    } catch (e: any) {
      handleErrorNotifications(e);
    }
  }

  const handleNextStep = async () => {
    const isValid = await methods.trigger();
    if (isValid) {
      const currentVolumes = methods.getValues('volumes');
      setVolumesList([{ monthly: currentVolumes.monthly }]);
      setActiveStep(activeStep + 1);
    }
  };

  const handlePreviousStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
    if (activeStep === 1) {
      if (volumesList.length > 0) {
        methods.setValue('volumes', volumes[0]);
      }
    }
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <Stepper active={activeStep} onStepClick={setActiveStep} allowNextStepsSelect={false}>
          <Stepper.Step label="Krok pierwszy" description="Dane podstawowe">
            <Step1Fields agreement={query.data} />
          </Stepper.Step>
          <Stepper.Step label="Krok drugi" description="Kontrakty">
            <Step2Fields />
          </Stepper.Step>
        </Stepper>
        <Group mt="md">
          {activeStep > 0 && (
            <Button variant="default" onClick={handlePreviousStep}>
              Poprzedni krok
            </Button>
          )}
          {activeStep < 1 && (
            <Button variant="default" onClick={handleNextStep}>
              Następny krok
            </Button>
          )}

          <Button onClick={() => router.push(RouterPaths.AGREEMENTS_LIST)}>Anuluj</Button>

          {activeStep == 1 && <SafeSubmitButton onValid={onSubmit}>Zapisz</SafeSubmitButton>}
        </Group>
      </form>
    </FormProvider>
  );
}
