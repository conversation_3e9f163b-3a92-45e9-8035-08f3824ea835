import { useQuery } from '@tanstack/react-query';
import { AgreementService } from '@/services/AgreementService';
import { Agreement } from '@/types/Agreement';
import { useRouter } from 'next/router';

const useAgreement = (agreementId: string | undefined) => {
  return useQuery<Agreement>({
    queryKey: ['agreements', agreementId],
    queryFn: () => AgreementService.fetchById(agreementId),
    staleTime: 0,
    gcTime: 0,
    enabled: !!agreementId,  // działa tylko jeśli mamy agreementId
  });
};

export default useAgreement;