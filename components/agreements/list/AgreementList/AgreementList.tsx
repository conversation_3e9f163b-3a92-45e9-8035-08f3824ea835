import React, { useEffect, useMemo, useState } from 'react';
import { MantineReactTable, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Button, Group, Menu, Switch, Text, Tooltip } from '@mantine/core';
import { IconCopy, IconPencil, IconRefresh, IconTrash } from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { ColumnFiltersState, ColumnSort } from '@tanstack/table-core';
import ConfirmModal from '@/components/common/forms/ConfirmModal/ConfirmModal';
import { AgreementService } from '@/services/AgreementService';
import useGetAgreements from '@/components/agreements/list/hooks/useGetAgreements';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import styles from './AgreementList.module.css';
import { PERMISSIONS } from '@/utils/permissions';
import { LIST_DEFAULT_PAGE_SIZE } from '@/utils/defaults';
import { useMediaTypeList, useStatusList } from '@/utils/filterOptions';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import IconButton from '@/components/common/forms/IconButton/IconButton';
import EventLessComponent from '@/components/common/layout/EventLessComponent/EventLessComponent';
import { labels } from '@/utils/Labels';
import { ButtonMenu } from '@/components/common/layout/ButtonMenu/ButtonMenu';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { formatValue } from '@/utils/formatters';
import { getAgreementExportHandlers } from '@/components/agreements/list/hooks/useExcelAgreementExport';

const AgreementList = () => {
  const router = useRouter();
  const { t } = useTranslation('common');
  type SortingState = ColumnSort[];
  type FiltersState = ColumnFiltersState;
  const [columnFilters, setColumnFilters] = useState<FiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState<SortingState>([]);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: LIST_DEFAULT_PAGE_SIZE });
  const [isModalOpen, setModalOpen] = useState(false);
  const [selectedAgreementId, setSelectedAgreementId] = useState<string | null>(null);
  const [withArchive, setWithArchive] = useState(false);
  const mediaTypeList = useMediaTypeList();
  const statusList = useStatusList();
  const { data, isError, isLoading, refetch } = useGetAgreements({
    withArchive,
    filters: columnFilters,
    globalFilter,
    sorting,
    pagination,
  });
  const totalRowCount = data?.totalElements ?? 0;
  const { handleExportAll, handleExportVisible } = getAgreementExportHandlers({
    headers: [
      'ID umowy',
      'Klient',
      'Nośnik',
      'Wolumen',
      'Okres umowy',
      'Sprzedawca',
      'Opis',
      'Status',
      'Grupa zakupowa',
    ],
    t,
    sorting,
    filters: columnFilters,
    globalFilter,
    currentData: data,
    withArchive,
  });

  useEffect(() => {}, [data]);

  const openModal = (agreementId: string) => {
    setSelectedAgreementId(agreementId);
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setSelectedAgreementId(null);
  };

  const confirmDelete = async () => {
    if (selectedAgreementId) {
      await AgreementService.deleteAgreement(selectedAgreementId);
      refetch();
      closeModal();
    }
  };

  const columns = useMemo(
    () => [
      { accessorKey: 'humanReadableAgreementId', header: 'ID umowy' },
      {
        accessorFn: (data: any) => data?.customer?.name,
        header: 'Klient',
        id: 'customer.name',
        Cell: ({ cell }) => (
          <Tooltip label={cell.getValue()} position="bottom" withArrow>
            <span className="cellEllipsis">
              {cell.getValue()?.slice(0, 10)}
              {cell.getValue()?.length > 10 && '...'}
            </span>
          </Tooltip>
        ),
      },
      {
        accessorKey: 'mediaType',
        header: 'Nośnik',
        enableSorting: true,
        enableColumnFilter: true,
        filterVariant: 'select' as const,
        mantineFilterSelectProps: {
          data: mediaTypeList,
        },
        Cell: ({ cell }) => t(cell.getValue()),
      },
      {
        accessorFn: (data: any) => formatValue(data?.volumes?.summary),
        header: 'Wolumen',
        // enableColumnFilter: false,
        id: 'volumes.summary',
      },
      {
        accessorKey: 'startDate',
        // enableColumnFilter: false,
        header: 'Okres umowy',
        Cell: ({ cell }) => {
          return `${dayjs(cell.row.original.startDate).format('DD-MM-YYYY')} - ${dayjs(cell.row.original.endDate).format('DD-MM-YYYY')}`;
        },
      },
      {
        accessorFn: (data: any) => data?.supplier?.name,
        header: 'Sprzedawca',
        id: 'supplier.name',
        Cell: ({ cell }) => (
          <Tooltip label={cell.getValue()} position="bottom" withArrow>
            <span className="cellEllipsis">
              {cell.getValue()?.slice(0, 10)}
              {cell.getValue()?.length > 10 && '...'}
            </span>
          </Tooltip>
        ),
      },
      {
        accessorKey: 'description',
        header: 'Opis',
        Cell: ({ cell }) => (
          <Tooltip label={cell.getValue()} position="bottom" withArrow>
            <span className="cellEllipsis">
              {cell.getValue()?.slice(0, 14)}
              {cell.getValue()?.length > 14 && '...'}
            </span>
          </Tooltip>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        enableSorting: true,
        enableColumnFilter: true,
        filterVariant: 'select' as const,
        mantineFilterSelectProps: {
          data: statusList,
        },
        Cell: ({ cell }) => t(cell.getValue()),
      },
      {
        accessorFn: (data: any) => data?.agreementGroup?.name,
        header: 'Gr. zakupowa',
        id: 'agreementGroup.name',
        Cell: ({ cell }) => (
          <Tooltip label={cell.getValue()} position="bottom" withArrow>
            <span className="cellEllipsis">
              {cell.getValue()?.slice(0, 10)}
              {cell.getValue()?.length > 10 && '...'}
            </span>
          </Tooltip>
        ),
      },
      {
        id: 'actions',
        header: 'Operacje',
        size: 160,
        Cell: ({ row }: { row: { original: { id: string } } }) => (
          <Group>
            <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.CREATE_AGREEMENT] } }}>
              <EventLessComponent>
                <IconButton
                  icon={IconCopy}
                  onClick={() => router.push(RouterPaths.AGREEMENT_DUPLICATE([row.original.id]))}
                  label={t(labels.COPY)}
                />
              </EventLessComponent>
            </RoleBasedComponent>
            <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.UPDATE_AGREEMENT] } }}>
              <EventLessComponent>
                <IconButton
                  icon={IconPencil}
                  onClick={() => router.push(RouterPaths.AGREEMENTS_EDIT(row.original.id))}
                  label={t(labels.EDIT)}
                />
              </EventLessComponent>
            </RoleBasedComponent>
            <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.DELETE_AGREEMENT] } }}>
              <EventLessComponent>
                <IconButton
                  icon={IconTrash}
                  color={'red'}
                  onClick={() => openModal(row.original.id)}
                  label={t(labels.DELETE)}
                />
              </EventLessComponent>
            </RoleBasedComponent>
          </Group>
        ),
      },
    ],
    [router, refetch]
  );

  const table = useMantineReactTable({
    columns,
    data: data?.content ?? [],
    manualFiltering: true,
    manualPagination: true,
    manualSorting: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    defaultColumn: {
      size: 20,
    },
    mantineTableBodyCellProps: { style: { padding: '4px 2px', fontSize: '0.9em' } },
    mantineTableHeadCellProps: { style: { padding: '2px', fontSize: '0.9em' } },
    initialState: {
      density: 'xs',
      showColumnFilters: true,
      columnVisibility: {
        id: false,
        volumes: false,
      },
    },
    state: { isLoading, pagination, sorting },
    rowCount: totalRowCount,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    mantineTableBodyRowProps: ({ row }) => ({
      onClick: (event) => {
        if (!isMenuTarget(event)) {
          router.push(RouterPaths.AGREEMENTS_DETAILS(row.original.id));
        }
      },
      className: styles.pointerCursor,
    }),
    renderTopToolbarCustomActions: () => (
      <Group>
        <Tooltip label="Odśwież">
          <ActionIcon onClick={() => refetch()}>
            <IconRefresh />
          </ActionIcon>
        </Tooltip>
        <Tooltip label="Archiwalne umowy">
          <Switch
            label="Archiwalne"
            checked={withArchive}
            onClick={() => setWithArchive(!withArchive)}
          />
        </Tooltip>
      </Group>
    ),
  });

  const isMenuTarget = (event: React.MouseEvent<HTMLElement>): boolean => {
    const target = event.target as HTMLElement;
    return target.closest('.mantine-Menu-root') !== null;
  };

  return (
    <>
      <Group justify="flex-end" pb="md">
        <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.CREATE_AGREEMENT] } }}>
          <Button color="orange" onClick={() => router.push(RouterPaths.AGREEMENT_CREATE)}>
            Nowa umowa
          </Button>
        </RoleBasedComponent>

        <ButtonMenu>
          <Menu.Item>
            <Text size={'sm'} onClick={handleExportAll}>
              Eksportuj wszystkie dane
            </Text>
          </Menu.Item>
          <Menu.Item>
            <Text size={'sm'} onClick={handleExportVisible}>
              Eksportuj obecne wiersze
            </Text>
          </Menu.Item>
        </ButtonMenu>
      </Group>
      <MantineReactTable table={table} />
      <ConfirmModal
        opened={isModalOpen}
        onClose={closeModal}
        onConfirm={confirmDelete}
        title="Potwierdzenie usunięcia"
      >
        Czy na pewno chcesz usunąć tę umowę?
      </ConfirmModal>
    </>
  );
};

export default AgreementList;
