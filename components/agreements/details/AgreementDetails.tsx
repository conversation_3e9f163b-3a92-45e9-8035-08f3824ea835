import classes from './AgreementDetails.module.css';
import { useRouter } from 'next/router';
import useAgreement from '@/components/agreements/edit/useAgreement';
import { Box, Button, Container, Loader, Stack, Table, Text, Title, Tooltip } from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { VolumeTable } from '@/components/agreements/details/VolumeTable';
import React from 'react';
import useTranslation from 'next-translate/useTranslation';
import { MediaType } from '@/types/MediaType';
import { PERMISSIONS } from '@/utils/permissions';
import { RouterPaths } from '@/services/shared/ApiEndpoints';

const AgreementDetails = () => {
  const { t, lang } = useTranslation('common');

  const tooltipColor = 'var(--mantine-color-anchor)';

  const router = useRouter();

  const query = useAgreement();

  if (query.isLoading || query.isLoading) {
    return <Loader color="orange" size="md" />;
  }

  const agreementIdList: string[] = [query?.data?.id];

  const agreementMedia = query?.data?.media;
  const agreementPropertyRights = query?.data?.propertyRights;

  const agreementLabel = [
    query.data?.customer?.name,
    query.data?.startDate,
    t(query.data?.mediaType),
    query.data?.supplier?.name,
  ].filter(Boolean).join(' - ');

  const mediaOrderTimes = agreementMedia?.orderTime
    ? Object.keys(agreementMedia?.orderTime).map((key) => {
        const orderTimes = agreementMedia?.orderTime[key];
        const start = orderTimes.start ? `od ${orderTimes.start}` : '';
        const end = orderTimes.end ? `do ${orderTimes.end}` : '';

        return [t(key), `${start ?? start + ','} ${end}`].join(': ');
      })
    : [];

  const propertyRightsOrderTimes = agreementPropertyRights?.orderTime
    ? Object.keys(agreementPropertyRights?.orderTime).map((key) => {
        const orderTimes = agreementPropertyRights?.orderTime[key];
        const start = orderTimes.start ? `od ${orderTimes.start}` : '';
        const end = orderTimes.end ? `do ${orderTimes.end}` : '';

        return [t(key), `${start ?? start + ','} ${end}`].join(': ');
      })
    : [];

  const mediaVolume = agreementMedia?.volume;
  const mediaPurchaseModel = agreementMedia?.purchaseModel;

  const mediaVolumeFormatted = () => {
    switch (mediaVolume?.type) {
      case 'MULTIPLE':
        return mediaPurchaseModel === 'PERCENTAGE'
          ? t('MULTIPLE_PERCENTAGE', {
              x: mediaVolume?.x,
              y: mediaVolume?.y,
            })
          : t('MULTIPLE_VOLUME', {
              x: mediaVolume?.x,
              y: mediaVolume?.y,
            });
      case 'MIN_MAX':
        return mediaPurchaseModel === 'PERCENTAGE'
          ? t('MIN_MAX_PERCENTAGE', {
              x: mediaVolume?.x,
              y: mediaVolume?.y,
            })
          : t('MIN_MAX_VOLUME', {
              x: mediaVolume?.x,
              y: mediaVolume?.y,
            });
      case 'FIXED':
        return t('FIXED_VOLUME', {
          x: mediaVolume?.x,
        });
      default:
        return [t(mediaVolume?.type), mediaVolume?.value].filter(Boolean).join(': ');
    }
  };

  const propertyVolume = agreementPropertyRights?.volume;
  const propertyRightsPurchaseModel = agreementPropertyRights?.purchaseModel;

  const propertyVolumeFormatted = () => {
    switch (propertyVolume?.type) {
      case 'MULTIPLE':
        return propertyRightsPurchaseModel === 'TRANCHE'
          ? t('MULTIPLE_TRANCHE', {
              value: propertyVolume?.value,
            })
          : [t(propertyVolume?.type), propertyVolume?.value].filter(Boolean).join(': ');
      default:
        return [t(propertyVolume?.type), propertyVolume?.value].filter(Boolean).join(': ');
    }
  };

  const formatPropertyRightsQuantity = (agreementPropertyRights) => {
    if (
      agreementPropertyRights?.quantity?.value &&
      agreementPropertyRights?.quantity?.value.includes('-')
    ) {
      const parts = agreementPropertyRights?.quantity?.value.split('-');
      if (parts.length == 2) {
        return `Min. ${parts[0]} max. ${parts[1]} na rok dostawy`;
      }
    }
  };
  const mediaQuantity =
    agreementMedia?.quantity?.type == 'FIXED_PER_CONTRACT'
      ? [
          ['Y-', agreementMedia?.quantity?.year].join(''),
          ['Q-', agreementMedia?.quantity?.quarter].join(''),
          ['M-', agreementMedia?.quantity?.month].join(''),
        ].join(', ')
      : [agreementMedia?.quantity?.value, '-na okres dostawy'].join('');

  const mainElements = [
    { name: 'Klient (klienci)', value: query?.data?.customer?.name },
    { name: 'Typ mediów (nośnik)', value: t(query?.data?.mediaType) },
    { name: 'Sprzedawca', value: query?.data?.supplier?.name },
    { name: 'Data początku umowy', value: query?.data?.startDate?.valueOf() },
    { name: 'Data końca umowy', value: query?.data?.endDate?.valueOf() },
    {
      name: 'Start zakupów (początek liczenia średniej',
      value: query?.data?.averageCalculationStartDate?.valueOf(),
    },
    { name: 'Osoby uprawnione do zakupu', value: query?.data?.authorizedBuyers?.join(', ') },
    { name: 'Opis', value: query?.data?.description },
    { name: 'Nazwa grupy zakupowej', value: query?.data?.agreementGroup?.name },
    {
      name: 'Rekomendacje: Wymagana akceptacja klienta',
      value: query?.data?.requiresCustomerAcceptance ? 'Tak' : 'Nie',
    },
    {
      name: 'Rekomendacje: Wymagana rekomendacja',
      value: query?.data?.sendRecommendation ? 'Tak' : 'Nie',
    },
  ];

  const purchaseElements = [
    { name: 'Kontrakty', value: agreementMedia?.availableProducts?.join(', ') },
    { name: 'Model zakupu', value: t(agreementMedia?.purchaseModel) },
    { name: 'Forma zlecenia', value: t(agreementMedia?.orderTypeParameters?.type) },
    {
      name: 'Zamówienie / Cena na dzień',
      value: mediaOrderTimes?.map((mediaOrderTime) => <Text inherit>{mediaOrderTime}</Text>),
    },
    {
      name: 'Maksymalna liczba transz',
      value: mediaQuantity,
    },
    {
      name: 'Wolumen na transzę',
      value: mediaVolumeFormatted(),
    },
    {
      name: 'Ostatni dzień zakupu',
      value: [
        agreementMedia?.finalPurchaseDate?.value,
        t(agreementMedia?.finalPurchaseDate?.type),
      ].join(' '),
    },
    { name: 'Powód odrzucenia zakupu', value: t(agreementMedia?.rejectionReason?.type) },
    { name: 'Co gdy nie kupi 100%?', value: t(agreementMedia?.actionIfNot100?.type) },
  ];

  const rightsElements = [
    {
      name: 'Rodzaj praw majątkowych',
      value: agreementPropertyRights?.availableProducts?.join(', '),
    },
    { name: 'Model zakupu', value: t(agreementPropertyRights?.purchaseModel) },
    {
      name: 'Forma zlecenia',
      value: t(agreementPropertyRights?.orderTypeParameters?.type ?? ''),
    },
    {
      name: 'Zamówienie / Cena na dzień',
      value: propertyRightsOrderTimes?.map((mediaOrderTime) => (
        <Text inherit>{mediaOrderTime}</Text>
      )),
    },
    { name: 'Liczba transz', value: formatPropertyRightsQuantity(agreementPropertyRights) },
    {
      name: 'Wolumen na transzę',
      value:
        agreementPropertyRights?.purchaseModel !== 'PASSIVE_PURCHASE'
          ? propertyVolumeFormatted()
          : '',
    },
    {
      name: 'Ostatni dzień zakupu',
      value: [
        agreementPropertyRights?.finalPurchaseDate?.value,
        t(agreementPropertyRights?.finalPurchaseDate?.type ?? ''),
      ]
        .filter(Boolean)
        .join(' '),
    },
    {
      name: 'Powód odrzucenia',
      value:
        agreementPropertyRights?.purchaseModel !== 'PASSIVE_PURCHASE'
          ? t(agreementPropertyRights?.rejectionReason?.type)
          : '',
    },
    {
      name: 'Co gdy nie kupi 100%?',
      value: t(agreementPropertyRights?.actionIfNot100?.type ?? ''),
    },
  ];

  const mainRows = mainElements.map((element) => (
    <Table.Tr key={element?.name}>
      <Table.Td className={classes.mainColumn} w={200}>
        {element?.name}
      </Table.Td>
      <Table.Td display={'flex'} align={'center'} className={classes.valueColumn}>
        {element?.value}
      </Table.Td>
    </Table.Tr>
  ));

  const purchaseRows = purchaseElements.map((element) => (
    <Table.Tr key={element?.name}>
      <Table.Td className={classes.mainColumn} w={200}>
        {element?.name}
      </Table.Td>
      <Table.Td display={'flex'} className={classes.informationElement}>
        <Text>{element?.value}</Text>
        {element?.name === 'Forma zlecenia' && agreementMedia?.orderTypeParameters?.value && (
          <Tooltip
            className={classes.toolTipColor}
            color={tooltipColor}
            label={agreementMedia?.orderTypeParameters?.value}
          >
            <IconInfoCircle />
          </Tooltip>
        )}
        {element?.name === 'Powód odrzucenia zakupu' && agreementMedia?.rejectionReason?.value && (
          <Tooltip
            className={classes.toolTipColor}
            color={tooltipColor}
            label={agreementMedia?.rejectionReason?.value}
          >
            <IconInfoCircle />
          </Tooltip>
        )}
        {element?.name === 'Co gdy nie kupi 100%?' && agreementMedia?.actionIfNot100?.value && (
          <Tooltip
            className={classes.toolTipColor}
            color={tooltipColor}
            label={agreementMedia?.actionIfNot100?.value}
          >
            <IconInfoCircle />
          </Tooltip>
        )}
      </Table.Td>
    </Table.Tr>
  ));

  const rightsRows = rightsElements.map((element) => (
    <Table.Tr key={element?.name}>
      <Table.Td className={classes.mainColumn} w={200}>
        {element?.name}
      </Table.Td>
      <Table.Td display={'flex'} className={classes.informationElement}>
        <Text>{element?.value}</Text>
        {element?.name === 'Model zakupu' && agreementPropertyRights?.comment && (
          <Tooltip color={tooltipColor} label={agreementPropertyRights?.comment}>
            <IconInfoCircle />
          </Tooltip>
        )}

        {element?.name === 'Forma zlecenia' &&
          agreementPropertyRights?.orderTypeParameters?.value && (
            <Tooltip
              color={tooltipColor}
              label={agreementPropertyRights?.orderTypeParameters?.value}
            >
              <IconInfoCircle />
            </Tooltip>
          )}
        {element?.name === 'Powód odrzucenia' &&
          agreementPropertyRights?.rejectionReason?.value && (
            <Tooltip color={tooltipColor} label={agreementPropertyRights?.rejectionReason?.value}>
              <IconInfoCircle />
            </Tooltip>
          )}
        {element?.name === 'Co gdy nie kupi 100%?' &&
          agreementPropertyRights?.actionIfNot100?.value && (
            <Tooltip color={tooltipColor} label={agreementPropertyRights?.actionIfNot100?.value}>
              <IconInfoCircle />
            </Tooltip>
          )}
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      <Container size="xs" className={classes.detailsContainer}>
        <Stack>
          <Title order={2} className={classes.title}>
            Dane podstawowe
          </Title>
          <Box>
            <Table withTableBorder withColumnBorders layout={'fixed'}>
              <Table.Tbody>{mainRows}</Table.Tbody>
            </Table>
          </Box>
          <Title order={2} className={classes.title}>
            Warunki zakupowe
          </Title>
          <Box>
            <Table withTableBorder withColumnBorders layout={'fixed'}>
              <Table.Tbody>{purchaseRows}</Table.Tbody>
            </Table>
          </Box>
          {query?.data?.mediaType === MediaType.ENERGY ? (
            <span>
              <Title order={2} className={classes.title}>
                Prawa majątkowe
              </Title>
              <Box>
                <Table withTableBorder withColumnBorders layout={'fixed'}>
                  <Table.Tbody>{rightsRows}</Table.Tbody>
                </Table>
              </Box>
            </span>
          ) : null}
          <Title order={2} className={classes.title}>
            Deklarowany wolumen{' '}
            {query?.data?.mediaType === MediaType.ENERGY ? 'energii (MWh)' : 'gazu (MWh)'}
          </Title>
          <Box>
            <VolumeTable volumes={query?.data?.volumes}></VolumeTable>
          </Box>
          <Title order={2} className={classes.title}>
            Kontrakty
          </Title>
          <Box>
            <Button
              className={classes.contactsButton}
              onClick={() =>
                router.push({
                  pathname: RouterPaths.AGREEMENTS_CONTRACTS(router.query.id as string),
                  query:   { label: agreementLabel },
                })
              }
            >
              Lista kontraktów
            </Button>
          </Box>
        </Stack>

        <Button
          className={classes.returnButton}
          onClick={() => router.push(RouterPaths.AGREEMENTS_LIST)}
        >
          Powrót
        </Button>
        <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.UPDATE_AGREEMENT] } }}>
          <Button
            className={classes.editButton}
            onClick={() => router.push(RouterPaths.AGREEMENTS_EDIT(router.query.id as string))}
          >
            Edytuj umowę
          </Button>
          <Button
            className={classes.copyButton}
            onClick={() =>
              router.push(
                RouterPaths.AGREEMENT_DUPLICATE(agreementIdList),
                RouterPaths.AGREEMENT_CREATE
              )
            }
          >
            Kopiuj umowę
          </Button>
        </RoleBasedComponent>
      </Container>
    </>
  );
};

export default AgreementDetails;
