import classes from './AgreementDetails.module.css';
import {
  <PERSON><PERSON>,
  Loader,
  Table,
  Title,
  Text
} from '@mantine/core';
import React from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';                               // ⬅️  (NOWE)
import { IconSearch } from '@tabler/icons-react';
import useContractList from '@/components/agreements/details/useContractList';
import useAgreement from '@/components/agreements/edit/useAgreement';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';

const ContractList = () => {
  const { t } = useTranslation('common');
  const router = useRouter();

  /* --------- 1. lista kontraktów --------- */
  const contractsQuery = useContractList();

  /* --------- 2. dane umowy (nagł<PERSON>ek) ---- */
  const agreementQuery = useAgreement();
  const agreement = agreementQuery.data;

  /* --------- 3. loading ---------- */
  if (contractsQuery.isLoading || agreementQuery.isLoading) {
    return <Loader color="orange" size="md" />;
  }

  /* --------- 4. klejuch ---------- */
  const agreementGlue = [
    agreement?.customer?.name,
    dayjs(agreement?.startDate).format('DD-MM-YYYY'),
    t(agreement?.mediaType),
    agreement?.supplier?.name,
  ]
  .filter(Boolean)
  .join(' - ');

  /* --------- 5. wiersze tabeli ----- */
  const rows = contractsQuery.data?.content?.map((c) => (
    <Table.Tr key={c.id}>
      <Table.Td>{c.name}</Table.Td>
      <Table.Td>{c.year}</Table.Td>
      <Table.Td>{c.averageCalculation?.startDate}</Table.Td>
      <Table.Td>{c.averageCalculation?.endDate}</Table.Td>
      <Table.Td>
        <IconSearch
          cursor="pointer"
          onClick={() =>
            router.push(
              RouterPaths.AGREEMENTS_CONTRACT_DETAILS(
                router.query.id as string,
                c.id as string
              )
            )
          }
        />
      </Table.Td>
    </Table.Tr>
  ));

  /* --------- 6. render ---------- */
  return (
    <>
      {/* -------------- nagłówek z linkiem -------------- */}
      <Title order={3} mb="sm">
        Lista kontraktów umowy{' '}
        <Text component={Link}
              href={RouterPaths.AGREEMENTS_DETAILS(agreement?.id ?? '')}
              target="_blank"
              rel="noopener noreferrer"
              span
              fw={500}
        >
          {agreementGlue}
        </Text>
      </Title>

      {/* -------------- tabela -------------- */}
      <Table withTableBorder withColumnBorders layout="fixed">
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Nazwa</Table.Th>
            <Table.Th>Rok kontraktacji</Table.Th>
            <Table.Th>Data startu zakupów</Table.Th>
            <Table.Th>Data końcu zakupów</Table.Th>
            <Table.Th>Szczegóły</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>

      {/* -------------- powrót -------------- */}
      <Button
        className={classes.returnButton}
        onClick={() => router.push(RouterPaths.AGREEMENTS_DETAILS(router.query.id as string))}
      >
        Powrót
      </Button>
    </>
  );
};

export default ContractList;