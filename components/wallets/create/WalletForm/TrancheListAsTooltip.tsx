import { Table, ThemeIcon, Tooltip } from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';

export const TrancheListAsTooltip = ({ tranches, timeUnit }) => {
  if (tranches == undefined) {
    return (
      <Tooltip label={'Brak transz'}>
        <IconInfoCircle size={24} color={'orange'} />
      </Tooltip>
    );
  }

  const rows = tranches
    ? tranches.map((tranche) => (
        <Table.Tr key={tranche.contract}>
          <Table.Td>{tranche.contract?.name}</Table.Td>
          <Table.Td>{tranche.executionDate}</Table.Td>
          <Table.Td>{tranche.size}</Table.Td>
          <Table.Td>{tranche.price}</Table.Td>
        </Table.Tr>
      ))
    : [];
  return (
    <Tooltip
      label={
        <Table>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Kontrakt</Table.Th>
              <Table.Th>Data wykonania</Table.Th>
              <Table.Th>Wielkość transzy (%)</Table.Th>
              <Table.Th>Cena (PLN)</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      }
    >
      <ThemeIcon radius="xl" size="md">
        <IconInfoCircle size={24} />
      </ThemeIcon>
    </Tooltip>
  );
};
