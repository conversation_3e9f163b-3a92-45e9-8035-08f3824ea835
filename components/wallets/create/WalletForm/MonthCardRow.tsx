import { Group, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { formatValue } from '@/utils/formatters';
import React from 'react';
import PriceMetadataTooltip from '@/components/wallets/create/WalletForm/PriceMetadata/PriceMetadataTooltip';

const MonthCardRow = ({ price }) => {
  if (!price) return '';
  const { t, lang } = useTranslation('wallet');
  return (
    <Group justify="space-between">
      <Text fw={700}>{t(price.type)}</Text>
      <PriceMetadataTooltip price={price} />
      <Text c={price?.confirmed ? 'green' : 'black'} fw={price?.confirmed ? 600 : 400}>
        {formatValue(price.value)}
      </Text>
    </Group>
  );
};

export default MonthCardRow;
