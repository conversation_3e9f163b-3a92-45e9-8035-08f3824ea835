import React, { useEffect, useMemo, useState } from 'react';
import { MantineReactTable, MRT_PaginationState, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Button, Checkbox, Group, Menu, Select, Text, Tooltip } from '@mantine/core';
import { IconRefresh } from '@tabler/icons-react';
import { LIST_DEFAULT_PAGE_SIZE } from '@/utils/defaults';
import useTranslation from 'next-translate/useTranslation';
import { ButtonMenu } from '@/components/common/layout/ButtonMenu/ButtonMenu';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { usePriceConfirmation } from '@/components/wallets/price-confirmation/usePriceConfirmation';
import { formatValue } from '@/utils/formatters';
import { WalletService } from '@/services/WalletService';
import { extractSuppliers } from '@/utils/extract-supplier';
import { getPriceConfirmationExportHandlers } from '@/components/wallets/price-confirmation/useExcelPriceConfirmationExport';

export enum Media {
  ENERGY = 'ENERGY',
  GAS = 'GAS',
  GREEN_PROPERTY_RIGHTS = 'GREEN_PROPERTY_RIGHTS',
}

const COLORS = {
  GREEN: 'green',
  BLACK: 'black',
};

enum EDIT_MODE {
  NONE,
  DEFAULT,
  CONFIRMED,
}

export interface PriceOverview {
  customer: string;
  walletId: string;
  supplier: string;
  M1: string;
  M2: string;
  M3: string;
  M4: string;
  M5: string;
  M6: string;
  M7: string;
  M8: string;
  M9: string;
  M10: string;
  M11: string;
  M12: string;
}

// Define month labels once to avoid repetition
const MONTHS = Array.from({ length: 12 }, (_, i) => `M${i + 1}`);

const PriceConfirmationList = () => {
  const { t } = useTranslation('common');
  const [sorting, setSorting] = useState([]);
  const [columnFilters, setColumnFilters] = useState([]);
  const [year, setYear] = useState('2025');
  const [media, setMedia] = useState(Media.ENERGY);
  const [supplier, setSupplier] = useState('');
  const [editMode, setEditMode] = useState(EDIT_MODE.NONE); // Build table columns using the MONTHS constant
  const [confirmedEditMode, setConfirmedEditMode] = useState(false);

  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: LIST_DEFAULT_PAGE_SIZE,
  });

  const filters = useMemo(() => {
    const base = [
      { id: 'year', value: year },
      { id: 'media', value: media },
    ];
    return supplier ? [...base, { id: 'supplier', value: supplier }] : base;
  }, [year, media, supplier]);

  const { data, isLoading, refetch } = usePriceConfirmation({
    sorting,
    pagination,
    filters,
  });

  const supplierOptions = useMemo(() => extractSuppliers(data?.content ?? []), [data]);
  // Store table data separately
  const [tableData, setTableData] = useState<any[]>([]);
  useEffect(() => {
    if (data?.content) {
      setTableData(data.content);
    }
  }, [data]);

  // Renderer for month cells, using the current edit mode
  const monthCellRenderer = ({ cell }: { cell: any }) => {
    const priceEntry = cell.getValue();
    const color = priceEntry.confirmed ? COLORS.GREEN : COLORS.BLACK;
    const rowIndex = cell.row.index;
    const monthKey = cell.column.id;

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const checked = event.currentTarget.checked;
      setTableData((prevData) => {
        const newData = [...prevData];
        const updatedRow = { ...newData[rowIndex] };
        updatedRow[monthKey] = {
          ...updatedRow[monthKey],
          checked,
        };
        newData[rowIndex] = updatedRow;
        return newData;
      });
    };

    return (
      <Group style={{ color, fontWeight: priceEntry?.confirmed ? 800 : 'inherit' }}>
        <div style={{ width: '50px', textAlign: 'left' }}>{formatValue(priceEntry.price)}</div>
        {(editMode === EDIT_MODE.DEFAULT || editMode === EDIT_MODE.CONFIRMED) && (
          <Checkbox
            disabled={editMode === EDIT_MODE.DEFAULT ? priceEntry?.confirmed : false}
            checked={priceEntry?.checked}
            onChange={handleChange}
          />
        )}
      </Group>
    );
  };

  const columns = useMemo(() => {
    const walletColumn = {
      accessorFn: (data: any) => `${data.customer}`,
      header: t('Wallet'),
      size: 400,
      id: 'wallet',
    };

    const monthColumns = MONTHS.map((month) => ({
      accessorKey: month,
      header: month,
      Cell: monthCellRenderer,
      size: 90,
    }));

    return [walletColumn, ...monthColumns];
  }, [editMode]);

  const headers = [t('Wallet'), ...MONTHS];

  const overviewTransformer = (overview: any): any[] => [
    `${overview.customer}`,
    ...MONTHS.map((month) => formatValue(overview[month]?.price)),
  ];

  const rowTransformer = (row: any): any[] => [
    `${row.original.customer}`,
    ...MONTHS.map((month) => formatValue(row.original[month]?.price)),
  ];

  const { handleExportVisible, handleExportAll } = getPriceConfirmationExportHandlers({
    headers,
    sorting,
    filters,
    currentData: data,
  });

  const updateConfirmations = async (data: any) => {
    setEditMode(EDIT_MODE.NONE);
    const response = await WalletService.updatePriceConfirmation(data);
    await refetch();
  };
  // Custom toolbar actions extracted for clarity
  const renderToolbarActions = () => (
    <Group style={{ width: '100%' }}>
      <Tooltip label="Odśwież">
        <ActionIcon onClick={() => refetch()}>
          <IconRefresh />
        </ActionIcon>
      </Tooltip>
      {editMode === EDIT_MODE.NONE && (
        <Tooltip label="Edytuj">
          <Button onClick={() => setEditMode(EDIT_MODE.DEFAULT)}>Edytuj</Button>
        </Tooltip>
      )}
      {editMode === EDIT_MODE.DEFAULT && (
        <>
          <Tooltip label="Anuluj">
            <Button onClick={() => setEditMode(EDIT_MODE.NONE)}>Anuluj</Button>
          </Tooltip>
          <Tooltip label="Pozwól mi modyfikować potwierdzone ceny">
            <Button onClick={() => setEditMode(EDIT_MODE.CONFIRMED)}>
              Pozwól mi modyfikować potwierdzone ceny
            </Button>
          </Tooltip>
        </>
      )}
      {(editMode === EDIT_MODE.DEFAULT || editMode === EDIT_MODE.CONFIRMED) && (
        <Tooltip label="Zapisz zmiany">
          <Button onClick={() => updateConfirmations(tableData)}>Zapisz wprowadzone zmiany</Button>
        </Tooltip>
      )}
    </Group>
  );

  const totalRowCount = data?.totalElements ?? 0;

  const table = useMantineReactTable({
    columns,
    data: tableData,
    manualSorting: false,
    manualPagination: true,
    manualFiltering: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    initialState: {
      showColumnFilters: false,
      density: 'xs',
    },
    state: { isLoading, pagination, columnFilters },
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    renderTopToolbarCustomActions: renderToolbarActions,
    rowCount: totalRowCount,
  });

  return (
    <>
      <Group justify="flex-start" pb="md">
        <Select
          label="Rok kontraktacji"
          placeholder="Wybierz rok kontraktacji"
          value={year}
          data={[
            { value: '2024', label: '2024' },
            { value: '2025', label: '2025' },
            { value: '2026', label: '2026' },
            { value: '2027', label: '2027' },
            { value: '2028', label: '2028' },
          ]}
          onChange={(value: string) => setYear(value)}
        />
        <Select
          label="Proszę wybrać nośnik"
          placeholder="Wybierz nośnik"
          value={media}
          data={[
            { value: Media.ENERGY, label: t('ENERGY') },
            { value: Media.GAS, label: t('GAS') },
            { value: Media.GREEN_PROPERTY_RIGHTS, label: t('GREEN_PROPERTY_RIGHTS') },
          ]}
          onChange={(value: Media) => setMedia(value)}
        />
        <Select
          label="Sprzedawca"
          placeholder="Wybierz sprzedawcę"
          value={supplier}
          onChange={(value) => setSupplier(value ?? '')}
          data={supplierOptions.map((s) => ({ value: s, label: s }))}
          searchable
          clearable
        />
      </Group>
      <Group justify="flex-end" pb="md">
        <ButtonMenu>
          <Menu.Item>
            <Text size="sm" onClick={handleExportAll}>
              Eksportuj wszystkie dane
            </Text>
          </Menu.Item>
          <Menu.Item>
            <Text size="sm" onClick={handleExportVisible}>
              Eksportuj obecne wiersze
            </Text>
          </Menu.Item>
        </ButtonMenu>
      </Group>
      <MantineReactTable table={table} />
    </>
  );
};

export default PriceConfirmationList;
