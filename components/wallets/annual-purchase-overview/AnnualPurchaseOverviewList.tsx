import React, { useMemo, useState } from 'react';
import { MantineReactTable, MRT_PaginationState, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Group, Menu, Select, Text, Tooltip } from '@mantine/core';
import { IconRefresh } from '@tabler/icons-react';
import { LIST_DEFAULT_PAGE_SIZE } from '@/utils/defaults';
import useTranslation from 'next-translate/useTranslation';
import { ButtonMenu } from '@/components/common/layout/ButtonMenu/ButtonMenu';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { formatValue } from '@/utils/formatters';
import { useAnnualPurchaseOverview } from '@/components/wallets/annual-purchase-overview/useAnnualPurchaseOverview';
import { extractSuppliers } from '@/utils/extract-supplier';
import { getAnnualPurchaseExportHandlers } from '@/components/wallets/annual-purchase-overview/useExcelAnnualPurchaseExport';

export enum Media {
  ENERGY = 'ENERGY',
  GAS = 'GAS',
  GREEN_PROPERTY_RIGHTS = 'GREEN_PROPERTY_RIGHTS',
}

export interface AnnualOverview {
  customer: string;
  supplier: string;
  M1: string;
  M2: string;
  M3: string;
  M4: string;
  M5: string;
  M6: string;
  M7: string;
  M8: string;
  M9: string;
  M10: string;
  M11: string;
  M12: string;
}

const MONTHS = Array.from({ length: 12 }, (_, i) => `M${i + 1}`);
const monthCellRenderer = ({ cell }: { cell: any }) => {
  const cellValue = Number(cell.getValue());
  let color = 'black';
  if (cellValue === 0 || cellValue > 100) color = 'red';
  if (cellValue === 100) color = 'green';
  return (
    <span style={{ color }}>{cell.getValue() >= 0 ? `${formatValue(cell.getValue())}%` : ''}</span>
  );
};
const useColumns = () =>
  useMemo(() => {
    const walletColumn = {
      accessorFn: (data: any) => `${data.customer}`,
      header: 'Portfel',
      size: 400,
      id: 'wallet',
    };
    const monthColumns = MONTHS.map((month) => ({
      accessorKey: month,
      header: month,
      Cell: monthCellRenderer,
      size: 90,
    }));
    return [walletColumn, ...monthColumns];
  }, []);

interface FiltersProps {
  year: string;
  media: Media;
  supplier: string;
  supplierOptions: string[];
  onYearChange: (year: string) => void;
  onMediaChange: (media: Media) => void;
  onSupplierChange: (supplier: string) => void;
}

const Filters: React.FC<FiltersProps> = ({
  year,
  media,
  supplier,
  supplierOptions,
  onYearChange,
  onMediaChange,
  onSupplierChange,
}) => {
  const { t } = useTranslation('common');
  return (
    <Group justify="flex-start" pb="md">
      <Select
        label="Rok kontraktacji"
        placeholder="Wybierz rok kontraktacji"
        value={year}
        onChange={(value) => onYearChange(value!)}
        data={[
          { value: '2024', label: '2024' },
          { value: '2025', label: '2025' },
          { value: '2026', label: '2026' },
          { value: '2027', label: '2027' },
          { value: '2028', label: '2028' },
        ]}
      />
      <Select
        label="Proszę wybrać nośnik"
        placeholder="Wybierz nośnik"
        value={media}
        onChange={(value) => onMediaChange(Media[value as keyof typeof Media])}
        data={[
          { value: 'ENERGY', label: t('ENERGY') },
          { value: 'GAS', label: t('GAS') },
          { value: 'GREEN_PROPERTY_RIGHTS', label: t('GREEN_PROPERTY_RIGHTS') },
        ]}
      />
      <Select
        label="Sprzedawca"
        placeholder="Wybierz sprzedawcę"
        value={supplier}
        onChange={(value) => onSupplierChange(value!)}
        data={supplierOptions.map((s) => ({ value: s, label: s }))}
        searchable
        clearable
      />
    </Group>
  );
};

interface ExportMenuProps {
  onExportData: () => void;
  onExportRows: () => void;
}

const ExportMenu: React.FC<ExportMenuProps> = ({ onExportData, onExportRows }) => (
  <Group justify="flex-end" pb="md">
    <ButtonMenu>
      <Menu.Item>
        <Text size="sm" onClick={onExportData}>
          Eksportuj wszystkie dane
        </Text>
      </Menu.Item>
      <Menu.Item>
        <Text size="sm" onClick={onExportRows}>
          Eksportuj obecne wiersze
        </Text>
      </Menu.Item>
    </ButtonMenu>
  </Group>
);

const AnnualPurchaseOverviewList = () => {
  const [sorting, setSorting] = useState([]);
  const [year, setYear] = useState('2025');
  const [media, setMedia] = useState(Media.ENERGY);
  const [supplier, setSupplier] = useState('');
  const [columnFilters, setColumnFilters] = useState([]);
  const filters = useMemo(() => {
    const base = [
      { id: 'year', value: year },
      { id: 'media', value: media },
    ];
    return supplier ? [...base, { id: 'supplier', value: supplier }] : base;
  }, [year, media, supplier]);

  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: LIST_DEFAULT_PAGE_SIZE,
  });

  const { data, isLoading, refetch } = useAnnualPurchaseOverview({
    sorting,
    pagination,
    filters,
  });
  const supplierOptions = useMemo(() => extractSuppliers(data?.content ?? []), [data]);
  const columns = useColumns();
  const totalRowCount = data?.totalElements ?? 0;
  const overviewTransformer = (overview: any): any[] => [
    `${overview.customer}`,
    ...MONTHS.map((month) => overview[month]),
  ];

  const rowTransformer = (row: any): any[] => [
    `${row.original.customer}`,
    ...MONTHS.map((month) => row.original[month]),
  ];
  const headers = ['Portfel', ...MONTHS];
  const { handleExportVisible, handleExportAll } = getAnnualPurchaseExportHandlers({
    headers,
    sorting,
    filters,
    currentData: data,
  });

  const table = useMantineReactTable({
    columns,
    data: data?.content ?? [],
    manualPagination: true,
    manualFiltering: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    initialState: {
      showColumnFilters: false,
      density: 'xs',
    },
    state: { isLoading, pagination, columnFilters },
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    renderTopToolbarCustomActions: () => (
      <Group>
        <Tooltip label="Odśwież">
          <ActionIcon onClick={() => refetch()}>
            <IconRefresh />
          </ActionIcon>
        </Tooltip>
      </Group>
    ),
    rowCount: totalRowCount,
  });

  return (
    <>
      <Filters
        year={year}
        media={media}
        supplier={supplier}
        supplierOptions={supplierOptions}
        onYearChange={setYear}
        onMediaChange={setMedia}
        onSupplierChange={setSupplier}

      />
      <ExportMenu onExportData={handleExportAll} onExportRows={handleExportVisible} />
      <MantineReactTable table={table} />
    </>
  );
};

export default AnnualPurchaseOverviewList;
