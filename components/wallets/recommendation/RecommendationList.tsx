import React, { useCallback, useMemo, useState } from 'react';
import { MantineReactTable, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Button, Group, Menu, Stack, Switch, Text, Tooltip } from '@mantine/core';
import ConfirmModal from '@/components/common/forms/ConfirmModal/ConfirmModal';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import CustomDropdownMenu from '@/components/common/layout/CustomDropdownMenu/CustomDropdownMenu';
import { useForm } from 'react-hook-form';
import useGetRecommendations from '@/components/wallets/recommendation/hooks/useGetRecommendations';
import useRecommendationListState from '@/components/wallets/recommendation/hooks/useRecommendationListState';
import useRecommendationListHandlers from '@/components/wallets/recommendation/hooks/useRecommendationListHandlers';
import RecommendationForm from '@/components/wallets/recommendation/RecommendationForm';
import { RecommendationService } from '@/services/RecommendationService';
import { useRouter } from 'next/router';
import useValidationStore from '@/stores/wallet-store/useValidationStore';
import useWalletNotifications from '@/components/common/hooks/useWalletNotifications';
import { generateActions } from '@/components/wallets/recommendation/RecommendationActionsHandlers';
import styles from './RecommendationList.module.css';
import StatusCell from '@/components/common/forms/StatusCell/StatusCell';
import {
  mapLabelToStatus,
  RecommendationAction,
  RecommendationStatus,
  RecommendationStatusMap,
} from '@/types/Recommendation';
import RecommendationActionsTop from '@/components/wallets/recommendation/components/RecommendationActionsTop/RecommendationActionsTop';
import EditablePriceCell from '@/components/wallets/recommendation/components/EditablePriceCell/EditablePriceCell';
import RecommendationListFilter from '@/components/wallets/recommendation/components/RecommendationListFilter/RecommendationListFilter';
import EditableExecutorCell from '@/components/wallets/recommendation/components/EditableExecutorCell/EditableExecutorCell';
import { useBooleanFilterOptions } from '@/utils/filterOptions';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import FileUpload from '@/components/common/layout/FIleUpload/FileUpload';
import { IconCloudUpload, IconDownload } from '@tabler/icons-react';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { getRecommendationExportHandlers } from '@/components/wallets/recommendation/hooks/useExcelRecommendationExport';

const RecommendationList = () => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const [includeArchived, setIncludeArchived] = useState(false);
  const { setValidationResult } = useValidationStore();
  const { successNotification, errorNotification } = useWalletNotifications();
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(['new']);
  const booleanFilterOptions = useBooleanFilterOptions();
  const methods = useForm({
    defaultValues: {
      customerId: '',
      contractId: '',
      deadline: '',
      volume: '',
      price: 0,
      carrier: '',
      requiresCustomerAcceptance: false,
      sendRecommendation: false,
      purchaseMethod: '',
      emailTemplateComment: '',
      executor: '',
      agreementGroup: null,
    },
  });

  const handleToggleArchived = useCallback(() => {
    setIncludeArchived((prev) => !prev);
  }, []);

  const {
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,
    sorting,
    setSorting,
    isViewModalOpen,
    setViewModalOpen,
    pagination,
    setPagination,
    isDeleteModalOpen,
    setDeleteModalOpen,
    isEditModalOpen,
    setEditModalOpen,
    isSendModalOpen,
    setSendModalOpen,
    isCopyModalOpen,
    setCopyModalOpen,
    selectedRecommendation,
    setSelectedRecommendation,
  } = useRecommendationListState();

  const { data, isError, isLoading, refetch } = useGetRecommendations({
    filters: columnFilters,
    globalFilter,
    sorting,
    pagination,
    includeArchived,
    status: selectedStatuses,
  });
  const totalRowCount = data?.totalElements ?? 0;
  const {
    openAddModal,
    openEditModal,
    openDeleteModal,
    openSendModal,
    closeModal,
    confirmDelete,
    handleSubmit,
    openViewModal,
    openCopyModal,
  } = useRecommendationListHandlers({
    setSelectedRecommendation,
    setDeleteModalOpen,
    setEditModalOpen,
    setViewModalOpen,
    setSendModalOpen,
    setCopyModalOpen,
    refetch,
    methods,
    selectedRecommendation,
  });

  const handleSend = useCallback(async () => {
    try {
      const response = await RecommendationService.performAction(
        [selectedRecommendation.id],
        RecommendationAction.SEND
      );
      const result = response[0];

      const notification = result.success ? successNotification : errorNotification;
      notification({
        title: result.success ? 'Sukces!' : 'Błąd',
        message: result.message,
      });
      refetch();
      closeModal();
    } catch (error) {
      errorNotification({
        title: 'Błąd',
        message: `${error.message}`,
      });
    }
  }, [selectedRecommendation, refetch, closeModal, successNotification, errorNotification]);

  const { handleExportVisible, handleExportAll } = getRecommendationExportHandlers({
    headers: [
      'ID',
      'Status',
      'Utworzono',
      'ID umowy',
      'Klient',
      'Nośnik',
      'Kontrakt',
      'Wielkość transzy',
      'Jednostka czasu',
      'Wymagana akceptacja klienta',
      'Wymagana rekomendacja',
      'Cena',
      'Termin zakupu',
      'Sprzedawca',
      'Sposób zakupu',
      'Komentarz do szablonu e-maila',
      'Wykonawca po stronie ES',
      'Uprawnieni',
      'Grupa zakupowa',
    ],
    sorting,
    filters: columnFilters,
    currentData: data,
    includeArchived
  });

  const handleFileUpload = useCallback(
    async (event) => {
      const fileInput = event.target;
      const file = fileInput.files?.[0];

      if (file) {
        try {
          const response = await RecommendationService.uploadFile(file);

          fileInput.value = '';

          if (response.errors && response.errors.length > 0) {
            setValidationResult(response);
            await router.push(RouterPaths.WALLET_RECOMMENDATION_VALIDATION_RESULTS);
          } else {
            successNotification({
              id: 'upload-success-notification',
              title: 'Sukces!',
              message: 'Rekomendacje zostały zapisane prawidłowo',
            });
            refetch();
          }
        } catch (error) {
          errorNotification({
            id: 'upload-error-notification',
            title: 'Błąd',
            message: 'Wystąpił problem podczas ładowania pliku. Spróbuj ponownie.',
          });
          console.log('Error uploading file:', error);
        }
      }
    },
    [router, setValidationResult, refetch, successNotification, errorNotification]
  );

  const columns = useMemo(
    () => [
      { accessorKey: 'id', header: 'ID' },
      {
        accessorKey: 'status',
        header: 'Status',
        Cell: ({ cell }) => (
          <StatusCell status={cell.getValue()} statusMap={RecommendationStatusMap} />
        ),
        enableColumnFilter: false,
        filterFn: undefined,
      },
      {
        accessorKey: 'createdAt',
        header: 'Utworz.',
        Cell: ({ cell }) => (
          <Tooltip
            label={dayjs(cell.getValue()).format('DD-MM-YY HH:mm')}
            position="bottom"
            withArrow
          >
            <span>{dayjs(cell.getValue()).format('DD-MM-YY')}</span>
          </Tooltip>
        ),
      },
      { accessorKey: 'humanReadableAgreementId', header: 'ID umowy' },
      {
        accessorKey: 'customerName',
        header: 'Klient',
        Cell: ({ cell }) => (
          <Tooltip label={cell.getValue()} position="bottom" withArrow>
            <span className="cellEllipsis">
              {cell.getValue()?.slice(0, 10)}
              {cell.getValue()?.length > 10 && '...'}
            </span>
          </Tooltip>
        ),
      },
      { accessorKey: 'carrier', header: 'Nośnik' },
      { accessorKey: 'contract', header: 'Kontrakt' },
      {
        accessorKey: 'volume',
        header: 'Transza',
        Cell: ({ cell }) => `${cell.getValue()}%`,
      },
      { accessorKey: 'timeUnit', header: 'Jednostka czasu' },
      {
        accessorKey: 'requiresCustomerAcceptance',
        header: 'Wymagana akceptacja klienta',
        Cell: ({ cell }) => (cell.getValue() ? 'TAK' : 'NIE'),
        enableSorting: true,
        enableColumnFilter: true,
        filterVariant: 'select' as const,
        mantineFilterSelectProps: {
          data: booleanFilterOptions,
        },
      },
      {
        accessorKey: 'sendRecommendation',
        header: 'Wymagana rekomendacja',
        Cell: ({ cell }) => (cell.getValue() ? 'TAK' : 'NIE'),
        enableSorting: true,
        enableColumnFilter: true,
        filterVariant: 'select' as const,
        mantineFilterSelectProps: {
          data: booleanFilterOptions,
        },
      },
      {
        accessorKey: 'price',
        header: 'Cena',
        Cell: ({ cell, row }) => (
          <EditablePriceCell
            cell={cell}
            row={row}
            refetch={refetch}
            disabled={
              mapLabelToStatus(row.original.status) === RecommendationStatus.ADDED_TO_WALLET
            }
          />
        ),
      },
      {
        accessorKey: 'deadline',
        header: 'Termin zak.',
        Cell: ({ cell, row }: { cell: any; row: any }) => (
          <span className={row.original.isOverdue ? styles.redText : styles.normal}>
            {dayjs(cell.getValue()).add(20, 'minute').format('DD-MM-YY HH:mm')}
          </span>
        ),
      },
      {
        accessorKey: 'supplierName',
        header: 'Sprzedawca',
        Cell: ({ cell }) => (
          <Tooltip label={cell.getValue()} position="bottom" withArrow>
            <span className="cellEllipsis">
              {cell.getValue()?.slice(0, 8)}
              {cell.getValue()?.length > 8 && '...'}
            </span>
          </Tooltip>
        ),
      },
      {
        accessorKey: 'purchaseMethod',
        header: 'Sposób zakupu',
        Cell: ({ cell }) => t(cell.getValue() || 'Brak'),
      },
      { accessorKey: 'emailTemplateComment', header: 'Komentarz do szablonu e-maila' },
      {
        accessorKey: 'executor',
        header: 'Wykonawca',
        Cell: ({ cell, row }) => (
          <EditableExecutorCell
            cell={cell}
            row={row}
            refetch={refetch}
            disabled={
              mapLabelToStatus(row.original.status) === RecommendationStatus.ADDED_TO_WALLET
            }
          />
        ),
      },
      {
        accessorKey: 'agreementGroup',
        header: 'Gr. zakup.',
        Cell: ({ cell }) => (
          <Tooltip label={cell.getValue()} position="bottom" withArrow>
            <span className="cellEllipsis">
              {cell.getValue()?.slice(0, 8)}
              {cell.getValue()?.length > 8 && '...'}
            </span>
          </Tooltip>
        ),
      },
      {
        accessorKey: 'authorizedBuyers',
        header: 'Uprawnieni',
        Cell: ({ cell }) =>
          cell
            .getValue()
            ?.join(', ')
            .replace(/pełnomocnictwem/g, 'pełn.') || 'Brak',
      },
      {
        id: 'actions',
        header: 'Akcje',
        Cell: ({ row }) => {
          const availableActions = row.original.availableActions;
          const actions = generateActions(
            availableActions,
            row.original,
            openEditModal,
            openViewModal,
            openDeleteModal,
            openSendModal,
            refetch,
            openCopyModal
          );

          // Extract the first action as the primary button
          const [firstAction, ...otherActions] = actions;

          return (
            <Group>
              <div className={styles.actionContainer}>
                {firstAction && (
                  <Button
                    onClick={firstAction.onClick}
                    size="xs"
                    color="orange"
                    style={{ fontSize: '10px' }}
                  >
                    {firstAction.label}
                  </Button>
                )}
                {otherActions.length > 0 && (
                  <CustomDropdownMenu label="Zarządzanie rekomendacją" menuItems={otherActions} />
                )}
              </div>
            </Group>
          );
        },
      },
    ],
    [openEditModal, openViewModal, openDeleteModal, openSendModal, refetch, openCopyModal]
  );

  const table = useMantineReactTable({
    columns,
    data: data?.content ?? [],
    defaultColumn: {
      size: 20,
    },
    displayColumnDefOptions: {
      'mrt-row-select': {
        size: 10,
      },
      'mrt-row-expand': {
        size: 10,
        maxSize: 20,
      },
    },
    manualFiltering: true,
    manualPagination: true,
    manualSorting: true,
    enableRowSelection: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    positionToolbarAlertBanner: 'none',
    initialState: {
      showColumnFilters: true,
      density: 'xs',
      columnVisibility: {
        id: false,
        humanReadableAgreementId: false,
        carrier: false,
        requiresCustomerAcceptance: false,
        sendRecommendation: false,
        purchaseMethod: false,
        emailTemplateComment: false,
        timeUnit: false,
      },
    },
    state: { isLoading, pagination, sorting, columnFilters },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    rowCount: totalRowCount,
    getRowId: (row) => row.id,
    mantineTableBodyCellProps: { style: { padding: '4px', fontSize: '0.9em' } },
    mantineTableHeadCellProps: { style: { padding: '2px', fontSize: '0.7em' } },
    renderTopToolbarCustomActions: () => (
      <RecommendationActionsTop
        table={table}
        openAddModal={openAddModal}
        refetch={refetch}
        handleExportData={handleExportAll}
        handleExportRows={handleExportVisible}
        handleFileUpload={handleFileUpload}
      />
    ),
    renderDetailPanel: ({ row }) => {
      return (
        <Group p="sm" style={{ width: '100%', gap: '2rem' }}>
          <Stack style={{ alignItems: 'flex-start' }}>
            <Text size="sm" fw={400}>
              Sposób zakupu:
            </Text>
            <Text size="xs">
              {row.original.purchaseMethod ? t(row.original.purchaseMethod) : 'Brak'}
            </Text>
          </Stack>
          <Stack style={{ alignItems: 'flex-start' }}>
            <Text size="sm" fw={400}>
              Forma zlecenia:
            </Text>
            <Text size="xs">{row.original.orderType || 'Brak'}</Text>
          </Stack>
          <Stack style={{ alignItems: 'flex-start' }}>
            <Text size="sm" fw={400}>
              Jednostka czasu:
            </Text>
            <Text size="xs">{row.original.timeUnit || 'Brak'}</Text>
          </Stack>
          <Stack style={{ alignItems: 'flex-start' }}>
            <Text size="sm" fw={400}>
              Komentarz do szablonu e-mail:
            </Text>
            <Text size="xs">{row.original.emailTemplateComment || 'Brak'}</Text>
          </Stack>

          <Stack style={{ alignItems: 'flex-start' }}>
            <Text size="sm" fw={400}>
              Wymagana akceptacja klienta:
            </Text>
            <Switch
              size="xs"
              readOnly
              checked={row.original.requiresCustomerAcceptance}
              color="green"
              offLabel="NIE"
              onLabel="TAK"
            />
          </Stack>

          <Stack style={{ alignItems: 'flex-start' }}>
            <Text size="sm" fw={400}>
              Wymagana rekomendacja:
            </Text>
            <Switch
              size="xs"
              readOnly
              checked={row.original.sendRecommendation}
              color="green"
              offLabel="NIE"
              onLabel="TAK"
            />
          </Stack>
        </Group>
      );
    },
  });

  return (
    <div className={styles.mainContainer}>
      <Group justify="flex-end" pb="md">
        <Tooltip label="Dodaj rekomendację" position="bottom" withArrow>
          <Button
            onClick={openAddModal}
            variant="filled"
            color="orange"
            className={styles.addButton}
          >
            Dodaj rekomendację
          </Button>
        </Tooltip>
        <FileUpload handleFileUpload={handleFileUpload} />
        <Tooltip label="Wczytaj z pliku" position="bottom" withArrow>
          <ActionIcon
            onClick={() => document.getElementById('fileUpload').click()}
            variant="filled"
            color="orange"
            className={styles.actionIcon}
          >
            <IconCloudUpload size={20} />
          </ActionIcon>
        </Tooltip>
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Tooltip label="Eksportuj dane" position="bottom" withArrow>
              <ActionIcon variant="filled" color="orange" className={styles.actionIcon}>
                <IconDownload size={20} />
              </ActionIcon>
            </Tooltip>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item onClick={handleExportAll}>Eksportuj wszystkie dane</Menu.Item>
            <Menu.Item onClick={handleExportVisible}>Eksportuj obecne wiersze</Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Group>
      <Text size="sm" color="dimmed" mt={-1}>
        Filtruj po statusach:
      </Text>
      <RecommendationListFilter
        setSelectedStatuses={setSelectedStatuses}
        selectedStatuses={selectedStatuses}
      />
      <Group className={styles.switchGroup}>
        <Switch
          label={
            <span className={styles.switchLabel}>
              Uwzględnij archiwalne (termin zakupu &lt; data bieżąca - 14 dni)
            </span>
          }
          checked={includeArchived}
          onChange={handleToggleArchived}
        />
      </Group>
      <MantineReactTable table={table} />
      <RecommendationForm
        isEditModalOpen={isEditModalOpen}
        isViewModalOpen={isViewModalOpen}
        isSendModalOpen={isSendModalOpen}
        isCopyModalOpen={isCopyModalOpen}
        closeModal={closeModal}
        handleSubmit={handleSubmit}
        handleSend={handleSend}
        selectedRecommendation={selectedRecommendation}
      />
      <ConfirmModal
        opened={isDeleteModalOpen}
        onClose={closeModal}
        onConfirm={confirmDelete}
        title="Potwierdzenie usunięcia"
      >
        Czy na pewno chcesz usunąć tę rekomendację?
      </ConfirmModal>
    </div>
  );
};

export default RecommendationList;
