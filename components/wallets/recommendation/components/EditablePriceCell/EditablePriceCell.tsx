import React, { useState } from 'react';
import { ActionIcon, TextInput, Tooltip } from '@mantine/core';
import { IconCheck, IconPencil, IconX } from '@tabler/icons-react';
import { useUpdatePrice } from './useUpdatePrice';
import TruncatedTextWithTooltip from '@/components/common/forms/TruncatedTextWithTooltip/TruncatedTextWithTooltip';
import useTranslation from 'next-translate/useTranslation';
import { labels } from '@/utils/Labels';

import styles from './EditablePriceCell.module.css';

const EditablePriceCell = ({ cell, row, refetch, disabled = false }) => {
  const { t } = useTranslation('common');

  const { value, setValue, handleSave } = useUpdatePrice(row, refetch);
  const [isEditing, setIsEditing] = useState(false);
  const originalValue = cell.getValue();

  const toComma = (val: any) =>
    typeof val === 'number' || typeof val === 'string' ? val.toString().replace(/\./g, ',') : '';

  const handleEditClick = () => {
    setIsEditing(true);
    setValue(toComma(originalValue));
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    setValue(toComma(originalValue));
  };

  const handleSaveClick = async () => {
    await handleSave();
    setIsEditing(false);
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSaveClick();
    }
  };

  return (
    <div className={styles.editablePriceContainer}>
      {isEditing ? (
        <div className={styles.editablePriceRow}>
          <TextInput
            size="xs"
            value={value}
            onChange={(e) => setValue(e.target.value.replace(/\./g, ','))}
            onKeyDown={handleKeyDown}
            className={styles.editablePriceInput}
            autoFocus
          />
          <ActionIcon color="green" onClick={handleSaveClick} size="24">
            <IconCheck size={12} />
          </ActionIcon>
          <ActionIcon color="red" onClick={handleCancelClick} size="24">
            <IconX size={12} />
          </ActionIcon>
        </div>
      ) : (
        <div className={styles.editablePriceRow}>
          <TruncatedTextWithTooltip content={toComma(originalValue)} maxLength={8} />
          <Tooltip label={t(labels.EDIT)}>
            <ActionIcon color="orange" onClick={handleEditClick} size="24" disabled={disabled}>
              <IconPencil size={12} />
            </ActionIcon>
          </Tooltip>
        </div>
      )}
    </div>
  );
};

export default EditablePriceCell;
