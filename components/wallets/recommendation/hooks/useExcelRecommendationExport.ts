import {
  Recommendation,
  RecommendationStatus,
  RecommendationStatusMap,
} from '@/types/Recommendation';
import { RecommendationService } from '@/services/RecommendationService';
import { dashify } from '@/utils/dashify';
import { exportDataToXlsx } from '@/utils/exportDataToXlsx';
import dayjs from 'dayjs';
import { mapStatus } from '@/components/wallets/recommendation/hooks/useGetRecommendations';

interface ExportParams {
  headers: string[];
  sorting: any;
  filters: any;
  currentData: { content: Recommendation[] };
  includeArchived: boolean;
}

export const getRecommendationExportHandlers = ({
  headers,
  sorting,
  filters,
  currentData,
  includeArchived,
}: ExportParams) => {
  const formatItem = (r: Recommendation): any[] => {
    let statusKey: RecommendationStatus | undefined;
    let statusLabel = r.status;

    try {
      statusKey = mapStatus(r.status);
      statusLabel = RecommendationStatusMap[statusKey]?.label || r.status;
    } catch (e) {
      console.warn(`Nieznany status: ${r.status}`);
    }

    return [
      r.id,
      statusLabel,
      dayjs(r.createdAt).format('DD-MM-YYYY HH:mm'),
      r.humanReadableAgreementId,
      dashify(r.customerName),
      dashify(r.carrier),
      r.contract,
      `${r.volume}%`,
      r.timeUnit,
      r.requiresCustomerAcceptance ? 'TAK' : 'NIE',
      r.sendRecommendation ? 'TAK' : 'NIE',
      r.price?.toString().replace('.', ',') ?? '',
      r.deadline ? dayjs(r.deadline).add(20, 'minute').format('DD-MM-YY HH:mm') : 'Brak terminu',
      r.supplierName,
      r.purchaseMethod,
      r.emailTemplateComment,
      r.executor,
      r.authorizedBuyers?.join(', ') || '',
      r.agreementGroup,
    ];
  };

  const handleExportVisible = () => {
    const data = currentData?.content?.map(formatItem) ?? [];
    exportDataToXlsx(data, headers, 'RecommendationList');
  };

  const handleExportAll = async () => {
    const response = await RecommendationService.fetchRecommendations({
      sorting,
      filters,
      includeArchived,
      pagination: {
        pageIndex: 0,
        pageSize: 999999,
      },
    });

    const data = response.content.map(formatItem);
    exportDataToXlsx(data, headers, 'RecommendationList-All');
  };

  return { handleExportVisible, handleExportAll };
};
