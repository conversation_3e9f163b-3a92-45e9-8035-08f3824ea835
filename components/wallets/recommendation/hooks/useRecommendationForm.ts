import { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { AgreementService } from '@/services/AgreementService';
import dayjs from 'dayjs';
import { RecommendationSchema } from '@/components/wallets/recommendation/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { ContractService } from '@/services/ContractService';

const useRecommendationForm = (selectedRecommendation, isEditModalOpen, isViewModalOpen) => {
    const initialValues = {
        contractId: [],
        contract: '',
        timeUnit: '',
        deadline: dayjs().format('YYYY-MM-DD'),
        volume: '',
        price: '',
        carrier: '',
        requiresCustomerAcceptance: false,
        sendRecommendation: false,
        purchaseMethod: '',
        emailTemplateComment: '',
        executor: '',
        status: '',
        agreementGroup: null,
    };

    const methods = useForm({
        resolver: zodResolver(RecommendationSchema),
        defaultValues: initialValues,
    });

    const requiresCustomerAcceptance = useWatch({control: methods.control, name: 'requiresCustomerAcceptance'});
    const sendRecommendation = useWatch({control: methods.control, name: 'sendRecommendation'});
    const agreementGroup = useWatch({control: methods.control, name: 'agreementGroup'});
    const contract = useWatch({ control: methods.control, name: 'contract' });
  const { dirtyFields } = methods.formState;

    useEffect(() => {
        if (isEditModalOpen && !selectedRecommendation) {
            methods.reset(initialValues);
        }
    }, [isEditModalOpen, selectedRecommendation, methods]);

    useEffect(() => {
        if (agreementGroup?.id) {
            AgreementService.getAgreements().then(response => {
                const agreements = response.content
                    .filter(agreement => agreement.agreementGroup?.id === agreementGroup.id)
                    .map(agreement => agreement.id);
                methods.setValue('contractId', agreements);
            });
        } else {
            methods.setValue('contractId', []);
        }
    }, [agreementGroup?.id, methods]);

    useEffect(() => {
        if (selectedRecommendation) {
            methods.reset({
                ...selectedRecommendation,
                contractId: Array.isArray(selectedRecommendation.contractId)
                    ? selectedRecommendation.contractId
                    : [selectedRecommendation.contractId],
                agreementGroup: selectedRecommendation.agreementGroup || null,
                contract: selectedRecommendation.contract || '',
                emailTemplateComment: selectedRecommendation.emailTemplateComment || '',
                deadline: selectedRecommendation.deadline ? dayjs(selectedRecommendation.deadline).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
            });
        }
    }, [selectedRecommendation, isEditModalOpen, isViewModalOpen]);

    useEffect(() => {
        if (requiresCustomerAcceptance) {
            methods.setValue('sendRecommendation', true);
        }
    }, [requiresCustomerAcceptance, methods]);

    useEffect(() => {
       const fetchTimeUnit = async () => {
            const unit = await ContractService.getContractTimeUnit(contract);
             methods.setValue('timeUnit', unit);
           };
           // nie rób nic w trybie podglądu lub wysyłki
             if (!isEditModalOpen) return;
           // uruchamiaj tylko jeśli pole "contract" zostało zmienione ręcznie
             if (!dirtyFields.contract) return;
           if (contract) fetchTimeUnit();
       }, [contract, dirtyFields.contract, isEditModalOpen]);
    return { methods, requiresCustomerAcceptance, sendRecommendation };
};

export default useRecommendationForm;