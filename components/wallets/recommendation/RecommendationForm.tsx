import React, { useEffect } from 'react';
import { Button, Checkbox, Group, Modal, Textarea } from '@mantine/core';
import { FormProvider } from 'react-hook-form';
import TextInput from '@/components/common/forms/TextInput/TextInput';
import NumberInput from '@/components/common/forms/NumberInput/NumberInput';
import AgreementMultiSelect from '@/components/common/forms/AgreementMultiSelect/AgreementMultiSelect';
import ReadOnlyAgreementGroupSelect from '@/components/common/forms/ReadOnlyAgreementGroupSelect/ReadOnlyAgreementGroupSelect';
import useRecommendationForm from '@/components/wallets/recommendation/hooks/useRecommendationForm';
import DatePicker from '@/components/common/forms/DatePicker/DatePicker';
import TimeUnitSelect from '@/components/common/forms/TimeUnitSelect/TimeUnitSelect';
import ExecutorSelect from '@/components/wallets/recommendation/components/ExecutorComboBox/ExecutorSelect';
import PriceReferenceSelect from '@/components/common/forms/PriceReferenceSelect/PriceReferenceSelect';
import ContractSingleSelect from '@/components/common/forms/ContractSingleSelect/ContractSingleSelect';
import useContracts from '@/components/common/hooks/useContracts';
import { SafeSubmitButton } from '@/components/common/forms/SafeButton/SafeButton';

const RecommendationForm = ({
  isEditModalOpen,
  isViewModalOpen,
  isSendModalOpen,
  isCopyModalOpen,
  closeModal,
  handleSubmit,
  handleSend,
  selectedRecommendation,
}) => {
  const { methods, requiresCustomerAcceptance, sendRecommendation } = useRecommendationForm(
    selectedRecommendation,
    isEditModalOpen,
    isViewModalOpen
  );
  const { contractList, loadContracts, getAgreementId } = useContracts();

  const agreementIds: string[] = methods.watch('contractId');
  useEffect(() => {
    getAgreementId(null, agreementIds).then((id) => {
      if (id) loadContracts(id, true, true);
    });
  }, [agreementIds]);
  const isReadOnly = isViewModalOpen || isSendModalOpen;
  return (
    <Modal
      opened={isEditModalOpen || isViewModalOpen || isSendModalOpen || isCopyModalOpen}
      onClose={closeModal}
      title={
        isSendModalOpen
          ? 'Wyślij rekomendację'
          : isCopyModalOpen
            ? 'Kopiuj rekomendację'
            : selectedRecommendation
              ? isViewModalOpen
                ? 'Podgląd rekomendacji'
                : 'Edytuj rekomendację'
              : 'Dodaj rekomendację'
      }
    >
      <FormProvider {...methods}>
        <form>
          {!selectedRecommendation && (
            <ReadOnlyAgreementGroupSelect
              name="agreementGroup"
              label="Wybierz grupę zakupową"
              placeholder="Wybierz grupę"
              disabled={isReadOnly}
            />
          )}
          <AgreementMultiSelect
            name="contractId"
            label="Wybierz umowę"
            placeholder="Wybierz umowę"
            withAsterisk
            disabled={isReadOnly}
            contractYear={null}
          />
          <ContractSingleSelect
            name="contract"
            label="Kontrakt"
            placeholder="Wybierz z listy"
            contracts={contractList}
            withAsterisk
            disabled={isReadOnly}
          />
          <TimeUnitSelect
            name="timeUnit"
            label="Jednostka czasu"
            placeholder="Wybierz jednostkę czasu"
            withAsterisk
            disabled={isReadOnly}
          />
          <DatePicker
            name="deadline"
            label="Termin decyzji"
            placeholder="Wybierz datę"
            withAsterisk
            disabled={isReadOnly}
          />
          <NumberInput
            name="volume"
            label="Wielkość transzy (%)"
            placeholder="Wpisz wolumen"
            withAsterisk
            disabled={isReadOnly}
          />
          <PriceReferenceSelect
            name="purchaseMethod"
            label="Sposób zamawiania"
            placeholder="Wybierz sposób zamawiania"
            withAsterisk
            disabled={isReadOnly}
          />
          <TextInput
            name="price"
            label="Cena"
            placeholder="Wpisz cenę"
            withAsterisk
            disabled={isReadOnly}
          />
          <Textarea
            name="emailTemplateComment"
            label="Komentarz do szablonu e-mail"
            placeholder="Wpisz komentarz"
            {...methods.register('emailTemplateComment')}
            disabled={isReadOnly}
          />
          <ExecutorSelect
            name="executor"
            label="Wykonawca po stronie ES"
            placeholder="Wybierz lub wpisz wykonawcę"
            disabled={isReadOnly}
          />
          {isViewModalOpen && (
            <>
              <Checkbox
                name="requiresCustomerAcceptance"
                label="Wymagana akceptacja klienta"
                checked={requiresCustomerAcceptance}
                {...methods.register('requiresCustomerAcceptance')}
                disabled={isReadOnly}
              />

              <Checkbox
                name="sendRecommendation"
                label="Wymagana rekomendacja"
                checked={sendRecommendation}
                disabled={requiresCustomerAcceptance || isReadOnly}
                {...methods.register('sendRecommendation')}
              />
            </>
          )}
          <Group mt="md">
            {!isReadOnly && (
              <SafeSubmitButton onValid={handleSubmit}>
                {isCopyModalOpen || (selectedRecommendation && !selectedRecommendation.id)
                  ? 'Dodaj rekomendację'
                  : selectedRecommendation && selectedRecommendation.id
                    ? 'Zapisz zmiany'
                    : 'Dodaj rekomendację'}
              </SafeSubmitButton>
            )}
            {isSendModalOpen && (
              <Button type="button" onClick={handleSend}>
                Wyślij
              </Button>
            )}
          </Group>
        </form>
      </FormProvider>
    </Modal>
  );
};

export default RecommendationForm;
