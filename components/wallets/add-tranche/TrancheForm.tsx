import { Box, Button, Group, Radio, Tooltip } from '@mantine/core';
import { FormProvider } from 'react-hook-form';
import PurchaseMethodSingleSelect from '@/components/common/forms/PurchaseMethodSingleSelect/PurchaseMethodSingleSelect';
import CustomYearPicker from '@/components/common/forms/YearPicker/YearPicker';
import NumberInput from '@/components/common/forms/NumberInput/NumberInput';
import ReadOnlyAgreementGroupSelect from '@/components/common/forms/ReadOnlyAgreementGroupSelect/ReadOnlyAgreementGroupSelect';
import AgreementMultiSelect from '@/components/common/forms/AgreementMultiSelect/AgreementMultiSelect';
import TimeUnitSelect from '@/components/common/forms/TimeUnitSelect/TimeUnitSelect';
import useTrancheForm from '@/components/wallets/add-tranche/useTrancheForm';
import WalletMultiSelect from '@/components/common/forms/WalletMultiSelect/WalletMultiSelect';
import { TrancheFormType } from '@/types/Wallet';
import DatePicker from '@/components/common/forms/DatePicker/DatePicker';
import ErrorModal from '@/components/common/forms/ErrorModal/ErrorModal';
import React, { useEffect } from 'react';
import { ContractService } from '@/services/ContractService';
import useContracts from '@/components/common/hooks/useContracts';
import ContractSingleSelect from '@/components/common/forms/ContractSingleSelect/ContractSingleSelect';
import { SafeButton } from '@/components/common/forms/SafeButton/SafeButton';

export function TrancheForm({}) {
  const { methods, formType, setFormType, onSubmit, errors, isErrorModalOpen, setErrorModalOpen } =
    useTrancheForm();
  const { contractList, loadContracts, getAgreementId } = useContracts();
  const contractYear = methods.watch('contractYear');
  const contract = methods.watch('contract');
  const walletIds: string[] = methods.watch('walletIds');
  const agreementIds: string[] = methods.watch('contractIds');
  useEffect(() => {
    const getTimeUnit = async () => {
      if (contract) {
        const timeUnit = await ContractService.getContractTimeUnit(contract);
        methods.setValue('timeUnit', timeUnit);
      } else {
        methods.setValue('timeUnit', null);
      }
    };
    const loadPriceReference = async () => {
      try {
        let priceReferenceOptions = [];
        if (agreementIds.length > 0 && contract) {
          priceReferenceOptions = await ContractService.getAgreementPriceReference(
            agreementIds[0],
            contract
          );
          console.log(priceReferenceOptions);
          const refs = priceReferenceOptions || [];
          methods.setValue('priceReference', refs[0] || '');
        }
        if (walletIds.length > 0 && contract) {
          priceReferenceOptions = await ContractService.getWalletPriceReference(
            walletIds[0],
            contract
          );
          console.log(priceReferenceOptions);
          const refs = priceReferenceOptions || [];
          methods.setValue('priceReference', refs[0] || '');
        }
      } catch {
        methods.setValue('priceReference', '');
      }
    };

    loadPriceReference();
    getTimeUnit();
  }, [contract]);

  useEffect(() => {
    getAgreementId(walletIds, agreementIds).then((id) => {
      if (id) loadContracts(id, false, true);
    });
  }, [walletIds, agreementIds]);
  return (
    <>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Radio.Group
            value={formType}
            onChange={(value) => setFormType(value as TrancheFormType)}
            name="formType"
            withAsterisk
            description="Wybierz, czy chcesz dodać transzę dla portfeli, czy dla umów (grup zakupowych)"
          >
            <Group mt="xs">
              <Tooltip label="Dodaj transzę do portfela">
                <Radio value="WALLET" label="Utwórz transzę dla portfeli" />
              </Tooltip>
              <Tooltip label="Dodaj transzę do grup zakupowych">
                <Radio value="AGREEMENT_GROUP" label="Utwórz transzę dla umów (grup zakupowych)" />
              </Tooltip>
            </Group>
          </Radio.Group>
          <Box mt="md">
            <CustomYearPicker
              name="contractYear"
              label="Rok kontraktacji"
              placeholder="Wybierz rok"
              withAsterisk
            />
          </Box>
          {formType === TrancheFormType.WALLET && (
            <>
              <WalletMultiSelect
                name="walletIds"
                label="Portfel"
                placeholder="Wybierz portfel"
                withAsterisk
                contractYear={contractYear}
              />
              <ErrorModal
                errors={errors}
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalOpen={setErrorModalOpen}
                header={'Brak kontraktu w niektórych portfelach'}
              />
            </>
          )}

          {formType === TrancheFormType.AGREEMENT_GROUP && (
            <>
              <ReadOnlyAgreementGroupSelect
                name="agreementGroup"
                label="Grupa zakupowa"
                placeholder="Wybierz grupę zakupową"
              />
              <AgreementMultiSelect
                name="contractIds"
                label="Wybierz umowę"
                placeholder="Wybierz umowę"
                withAsterisk
                contractYear={contractYear}
              />
              <ErrorModal
                errors={errors}
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalOpen={setErrorModalOpen}
                header={'Brak kontraktu w niektórych umowach'}
              />
            </>
          )}
          <ContractSingleSelect
            name="contract"
            label="Kontrakt"
            placeholder="Wybierz z listy"
            contracts={contractList}
            withAsterisk
          />

          <TimeUnitSelect
            name="timeUnit"
            label="Jednostka czasu"
            placeholder="Wybierz jednostkę czasu"
            withAsterisk
          />

          <DatePicker
            name="executionDate"
            label="Data zakupu"
            placeholder="Wybierz datę zakupu"
            withAsterisk
          />

          <PurchaseMethodSingleSelect
            name="priceReference"
            label="Sposób zakupu"
            placeholder="Wybierz z listy"
            disabled={false}
            withAsterisk
          />

          <NumberInput
            name="volume"
            label="Wielkość transzy (%)"
            placeholder="Wpisz wielkość transzy"
            disabled={false}
          />

          <NumberInput name="price" label="Cena" placeholder="Wpisz cenę" disabled={false} />

          <SafeButton
            mt="md"
            onSafeClick={methods.handleSubmit(onSubmit)}
          >
            Dodaj transzę
          </SafeButton>
        </form>
      </FormProvider>
    </>
  );
}
