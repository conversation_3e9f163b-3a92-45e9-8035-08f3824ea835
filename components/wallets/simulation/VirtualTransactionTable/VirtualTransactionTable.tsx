import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { IconP<PERSON>cil, IconTrash } from '@tabler/icons-react';
import { FormProvider, useWatch } from 'react-hook-form';
import { ActionIcon, Button, Divider, Group, Stack, Switch } from '@mantine/core';
import useContracts from '@/components/common/hooks/useContracts';
import ContractSingleSelect from '@/components/common/shared/ContractSingleSelect/ContractSingleSelect';
import CustomModal from '@/components/common/shared/Modal/CustomModal';
import NumberInput from '@/components/common/shared/NumberInput/NumberInput';
import TimeUnitSelect from '@/components/common/shared/TimeUnitSelect/TimeUnitSelect';
import {
  defaultFormValues,
  useVirtualTransactionForm,
} from '@/components/wallets/simulation/VirtualTransactionTable/useVirtualTransactionForm';
import { SimulationService } from '@/services/SimulationService';
import { NotificationType } from '@/types/Common';
import { formatValue } from '@/utils/formatters';
import { notify } from '@/utils/notify';
import styles from './VirtualTransactionTable.module.css';

type VirtualTransactionTableProps = {
  onTransactionUpdate: (transactions: any[]) => void;
  agreementId: string | null;
  handleFullPurchaseSimulation: () => void;
};

export type VirtualTransactionTableHandle = {
  handleAddTransaction: (data: any) => void;
  autoCompleteTransactions: (transactions: any[]) => void;
  setContracts: (contracts: { value: string; label: string }[]) => void;
  clearAllVirtualTransactions: () => void;
  getTransactions: () => any[];
};

const SPECIAL_CONTRACTS = [
  'TGe24',
  'TGEgasID',
  'TGEgasDA',
  'PMOZE_A',
  'PMOZE_BIO',
  'PMEF_F',
] as const;
type SpecialContract = (typeof SPECIAL_CONTRACTS)[number];

const VirtualTransactionTable = forwardRef<
  VirtualTransactionTableHandle,
  VirtualTransactionTableProps
>(({ onTransactionUpdate, agreementId, handleFullPurchaseSimulation }, ref) => {
  const { setContractList, contractList, loadSimulationContracts } = useContracts();
  const {
    transactions,
    modalOpen,
    form,
    editIndex,
    setModalOpen,
    handleAddTransaction,
    handleEditTransaction,
    handleDeleteTransaction,
    autoCompleteTransactions,
  } = useVirtualTransactionForm(onTransactionUpdate);

  useImperativeHandle(ref, () => ({
    handleAddTransaction,
    autoCompleteTransactions,
    setContracts: (newContracts) => setContractList(newContracts),
    clearAllVirtualTransactions: () => {
      autoCompleteTransactions([]);
    },
    getTransactions: () => transactions,
  }));

  useEffect(() => {
    loadSimulationContracts(agreementId, false, false);
  }, [agreementId]);

  const getContractName = (contractId) => {
    const contract = contractList.find((c) => c.value === contractId);
    return contract ? contract.label : contractId;
  };

  const useLastPrice = useWatch({
    control: form.control,
    name: 'useLastPrice',
    defaultValue: false,
  });

  const selectedContractId = useWatch({
    control: form.control,
    name: 'contract',
  });

  useEffect(() => {
    const clearTimeUnit = () => form.setValue('timeUnit', null);

    if (!selectedContractId || contractList.length === 0) {
      clearTimeUnit();
      return;
    }

    const selectedContract = contractList.find((contract) => contract.value === selectedContractId);
    form.setValue('timeUnit', selectedContract?.timeUnit || null);
  }, [selectedContractId, contractList, form]);

  const handleOpenModal = () => {
    form.reset(defaultFormValues);
    setModalOpen(true);
  };

  const selectedContractName = getContractName(selectedContractId);
  const handleSwitchChange = async (event) => {
    const isChecked = event.currentTarget.checked;
    form.setValue('useLastPrice', isChecked);

    if (isChecked && selectedContractName) {
      try {
        const { value } = await SimulationService.getLastPriceForContract(selectedContractName);
        form.setValue('price', value);
      } catch (error: any) {
        const validationMessage = error.response?.data?.validation?.messages?.[0]?.reason;
        notify(
          NotificationType.ERROR,
          validationMessage || 'Błąd podczas pobierania ceny kontraktu.'
        );
      }
    }
  };

  const onSubmit = async () => {
    const isValid = await form.trigger();
    if (isValid) {
      handleAddTransaction(form.getValues());
    }
  };

  return (
    <>
      {transactions.length > 0 && (
        <>
          <div className={styles.tableTitle}>Lista symulowanych transz</div>
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th>Kontrakt</th>
                  <th>Wielkość transzy (%)</th>
                  <th>Cena (zł/MWh)</th>
                  <th>Jednostka czasu</th>
                  <th>Operacje</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map((transaction, index) => (
                  <tr key={transaction.id || `${index}-${transaction.contract}`}>
                    <td>{getContractName(transaction.contract)}</td>
                    <td>{transaction.size}</td>
                    <td>{formatValue(transaction.price)}</td>
                    <td>{transaction.timeUnit}</td>
                    <td>
                      <Group>
                        <ActionIcon onClick={() => handleEditTransaction(index)}>
                          <IconPencil />
                        </ActionIcon>
                        <ActionIcon onClick={() => handleDeleteTransaction(index)} color="red">
                          <IconTrash />
                        </ActionIcon>
                      </Group>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <Divider className={styles.divider} />
        </>
      )}
      <p className={styles.infoText}>
        Aby wykonać symulację, zacznij dodawać wirtualne transze do portfela lub skorzystaj z opcji{' '}
        <b>Zasymuluj do 100%</b>
      </p>
      <div className={styles.buttonContainer}>
        <Button onClick={handleOpenModal}>Dodaj wirtualną transzę</Button>
        <Button onClick={handleFullPurchaseSimulation}>Zasymuluj do 100%</Button>
        {transactions.length > 0 && (
          <ActionIcon
            onClick={() => autoCompleteTransactions([])}
            color="red"
            size="lg"
            variant="filled"
            title="Usuń wszystkie transze"
          >
            <IconTrash />
          </ActionIcon>
        )}
      </div>

      <CustomModal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        onConfirm={onSubmit}
        size="30%"
        radius="md"
        title={editIndex !== null ? 'Edytuj transzę' : 'Dodaj nową transzę'}
      >
        <FormProvider {...form}>
          <form>
            <Stack>
              <ContractSingleSelect
                name="contract"
                label="Kontrakt"
                placeholder="Wybierz z listy"
                contracts={contractList}
                withAsterisk
              />
              <NumberInput
                name="size"
                label="Wielkość transzy (%)"
                placeholder="Wpisz wielkość transzy"
                withAsterisk
              />
              <TimeUnitSelect
                name="timeUnit"
                label="Jednostka czasu"
                placeholder="Wybierz jednostkę czasu"
                disabled={
                  selectedContractId
                    ? !SPECIAL_CONTRACTS.includes(
                        getContractName(selectedContractId) as SpecialContract
                      )
                    : true
                }
                withAsterisk
              />
              <Switch
                label="Pobierz cenę z ostatniego notowania"
                checked={useLastPrice}
                onChange={handleSwitchChange}
                disabled={!selectedContractId}
              />
              <NumberInput
                name="price"
                label="Cena (zł/MWh)"
                placeholder="Wpisz cenę"
                withAsterisk
                disabled={useLastPrice}
              />
            </Stack>
          </form>
        </FormProvider>
      </CustomModal>
    </>
  );
});

export default VirtualTransactionTable;
