import { useMemo } from 'react';
import { IconInfoCircle } from '@tabler/icons-react';
import { Divider, Table, Tooltip } from '@mantine/core';
import { formatValue } from '@/utils/formatters';
import { splitByPropertyRights, TrancheLite } from '@/utils/trancheSort';
import styles from './SimulationMonthCard.module.css';

type Props = { tranches?: TrancheLite[]; timeUnit?: string };

const SimulationTrancheListTooltip = ({ tranches }: Props) => {
  const { base, rights } = useMemo(() => {
    return splitByPropertyRights(tranches ?? []);
  }, [tranches]);

  if (base.length + rights.length === 0) return null;

  const renderRows = (list: TrancheLite[]) =>
    list.map((t) => (
      <Table.Tr key={t.id ?? `${t.contract?.name}-${t.executionDate}`}>
        <Table.Td>{t.contract?.name}</Table.Td>
        <Table.Td>{t.executionDate}</Table.Td>
        <Table.Td>{t.size}%</Table.Td>
        <Table.Td>{formatValue(t.price)}</Table.Td>
        <Table.Td>{t.virtual ? 'TAK' : 'NIE'}</Table.Td>
      </Table.Tr>
    ));

  return (
    <Tooltip
      multiline
      withArrow
      label={
        <Table>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Kontrakt</Table.Th>
              <Table.Th>Data wykonania</Table.Th>
              <Table.Th>Wielkość transzy (%)</Table.Th>
              <Table.Th>Cena (zł/MWh)</Table.Th>
              <Table.Th>Wirtualna?</Table.Th>
            </Table.Tr>
          </Table.Thead>

          <Table.Tbody>
            {renderRows(base)}

            {/* separator */}
            {rights.length > 0 && (
              <>
                <Table.Tr>
                  <Table.Td colSpan={5} style={{ padding: 0 }}>
                    <Divider label="Prawa majątkowe" labelPosition="center" my="xs" size="sm" />
                  </Table.Td>
                </Table.Tr>
                {renderRows(rights)}
              </>
            )}
          </Table.Tbody>
        </Table>
      }
    >
      <div className={styles.infotip}>
        <span>Pokaż zakupy</span>
        <IconInfoCircle size={20} />
      </div>
    </Tooltip>
  );
};

export default SimulationTrancheListTooltip;
