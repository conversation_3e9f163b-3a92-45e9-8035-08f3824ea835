import { useEffect, useState } from 'react';
import { SimulationService } from '@/services/SimulationService';
import { useWalletFormStore } from '@/stores/wallet-form-store/wallet-form-provider';
import { NotificationType } from '@/types/Common';
import { notify } from '@/utils/notify';

const createTranche = (transaction) => ({
  contract: { id: transaction.contract },
  size: transaction.size,
  price: transaction.price,
  timeUnit: transaction.timeUnit,
  virtual: transaction.virtual || false,
  executionDate: transaction.executionDate,
  priceReference: transaction.priceReference,
});

export const createWalletPayload = (walletData) => ({
  walletId: walletData.id ?? walletData.walletId,
  mediaType: walletData.mediaType,
  startDate: walletData.startDate,
  year: walletData.year,
  agreement: walletData.agreement.id,
  tranches: walletData.tranches.map((tranche) => ({
    executionDate: tranche.executionDate,
    contract: tranche.contract.id,
    timeUnit: tranche.timeUnit,
    size: tranche.size,
    price: tranche.price,
    priceReference: tranche.priceReference,
    virtual: tranche.virtual,
  })),
  elements: walletData.elements.map((element) => ({
    type: element.type,
    timeUnit: element.timeUnit,
    value: element.value,
  })),
  prices: walletData.prices.map((price) => ({
    type: price.type,
    timeUnit: price.timeUnit,
    value: price.value,
  })),
  description: walletData.description,
});

export const useSimulationComponent = (selectedWallet) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [wallet, setWallet] = useState(null);
  const { setPrices, setTranches } = useWalletFormStore((state) => state);

  const fetchWalletData = async (walletId) => {
    setLoading(true);
    setError('');
    try {
      const data = await SimulationService.fetchSimulateById(walletId);
      setPrices(data.prices);
      setTranches(data.tranches);
      setWallet({ ...data, walletId });
    } catch (err) {
      const errorMessage = err?.response?.data?.message || 'Nie udało się pobrać danych portfela';
      setError(errorMessage);
      notify(NotificationType.ERROR, errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const simulateWallet = async (newTransactions) => {
    if (!wallet) return;
    const updatedWallet = createWalletPayload({
      ...wallet,
      tranches: [...wallet.tranches, ...newTransactions.map(createTranche)],
    });

    try {
      const simulatedData = await SimulationService.simulate(updatedWallet);
      setPrices(simulatedData.prices);
      setTranches(simulatedData.tranches);
    } catch (err: any) {
      const status = err?.response?.status;
      const messages = err?.response?.data?.messages;

      if (status === 409 && Array.isArray(messages)) {
        messages.forEach((m) =>
          notify(NotificationType.ERROR, m.reason || 'Błąd walidacji symulacji')
        );
      } else {
        const errorMessage =
          err?.response?.data?.message || 'Nie udało się zaktualizować symulacji';
        setError(errorMessage);
        notify(NotificationType.ERROR, errorMessage);
      }
      // aby rodzic mógł odwrócić zmiany w UI po przekroczneiu manualnym 100%
      throw err;
    }
  };

  useEffect(() => {
    if (selectedWallet) {
      fetchWalletData(selectedWallet);
    }
  }, [selectedWallet]);

  return { wallet, loading, error, simulateWallet, fetchWalletData };
};
