import { MantineReactTable, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Button, Container, Group } from '@mantine/core';
import React, { useMemo } from 'react';
import { IconPencil, IconTrash } from '@tabler/icons-react';
import { TimeUnit } from '@/types/Wallet';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { formatValue } from '@/utils/formatters';

const TrancheList = ({
  walletQuery,
  setIsTrancheEditForm,
  setTrancheModalOpen,
  setSelectedTranche,
  setDeleteModalOpen,
  trancheForm,
}) => {
  const { t } = useTranslation('common');
  const router = useRouter();

  const trancheColumns = useMemo(
    () => [
      { accessorKey: 'executionDate', header: 'Data wykonania' },
      { accessorKey: 'size', header: 'Wielkość transzy (%)' },
      { accessorKey: 'contract.name', header: 'Kontrakt' },
      {
        accessorFn: (data) => formatValue(data.price),
        header: 'Cena (zł/MWh)',
      },
      { accessorKey: 'timeUnit', header: 'Jednostka czasu' },
      { accessorFn: (data) => t(data.priceReference), header: 'Sposób zakupu' },
      {
        id: 'actions',
        header: 'Operacje',
        Cell: ({ row }) => (
          <Group>
            <ActionIcon
              size="md"
              onClick={() => {
                handleTrancheEdit(row.original);
              }}
            >
              <IconPencil size={24} stroke={1.5} />
            </ActionIcon>
            <ActionIcon
              size="md"
              color="red"
              onClick={() => {
                openDeleteModal(row.original.id);
              }}
            >
              <IconTrash size={24} stroke={1.5} />
            </ActionIcon>
          </Group>
        ),
      },
    ],
    []
  );

  const trancheTable = useMantineReactTable({
    columns: trancheColumns,
    data: walletQuery?.data?.tranches ?? [],
    manualFiltering: false,
    manualPagination: false,
    manualSorting: false,
    defaultColumn: {
      size: 20,
    },
    mantineTableBodyCellProps: { style: { padding: '4px 2px', fontSize: '0.9em' } },
    mantineTableHeadCellProps: { style: { padding: '2px', fontSize: '0.9em' } },
    localization: MRT_Localization_PL,
    renderTopToolbarCustomActions: () => (
      <Group mt="md">
        <Button onClick={handleTrancheAdd} color="orange">
          Dodaj transzę
        </Button>
      </Group>
    ),
  });

  const handleTrancheAdd = () => {
    setIsTrancheEditForm(false);
    setTrancheModalOpen(true);
    trancheForm.reset({
      executionDate: new Date(),
      volume: 0.0,
      price: 0.0,
      timeUnit: TimeUnit.Y,
      priceReference: '',
      walletIds: [router.query.id],
      contractIds: [],
      contract: '',
    });
  };

  const handleTrancheEdit = (component: any) => {
    setIsTrancheEditForm(true);
    setTrancheModalOpen(true);
    trancheForm.reset({
      executionDate: component.executionDate,
      volume: component.size,
      price: component.price,
      timeUnit: component.timeUnit,
      priceReference: component.priceReference,
      contract: component.contract.id,
      id: component.id,
      walletId: component.wallet.id,
    });
  };

  const openDeleteModal = (trancheId: string) => {
    setSelectedTranche(trancheId);
    setDeleteModalOpen(true);
  };

  return (
    <Container fluid mt={20}>
      <MantineReactTable table={trancheTable} />
    </Container>
  );
};
export default TrancheList;
