import { MantineReactTable, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Button, Container, Group } from '@mantine/core';
import { IconPencil, IconTrash } from '@tabler/icons-react';
import React, { useMemo } from 'react';
import { TimeUnit } from '@/types/Wallet';
import useTranslation from 'next-translate/useTranslation';

const ResultList = ({
  walletQuery,
  setModalOpen,
  setIsEditForm,
  resultForm,
  setDeleteModalOpen,
  setSelectedId,
}) => {
  const { t } = useTranslation('common');

  const handleAdd = () => {
    setModalOpen(true);
    setIsEditForm(null);
    resultForm.reset({
      mediaType: '',
      value: 0,
      timeUnit: TimeUnit.Y,
      walletId: walletQuery?.data?.walletId,
    });
  };

  const handleEdit = (row: any) => {
    setIsEditForm(row);
    resultForm.reset(row);
    setModalOpen(true);
  };

  const openDeleteModal = (id: string) => {
    setSelectedId(id);
    setDeleteModalOpen(true);
  };

  const columns = useMemo(
    () => [
      { accessorFn: (data) => t(data.mediaType), header: 'Nośnik' },
      { accessorKey: 'value', header: 'Benchmark / taryfa' },
      { accessorKey: 'timeUnit', header: 'Jednostka czasu' },
      {
        id: 'actions',
        header: 'Operacje',
        Cell: ({ row }) => (
          <Group>
            <ActionIcon onClick={() => handleEdit(row.original)}>
              <IconPencil />
            </ActionIcon>
            <ActionIcon color="red" onClick={() => openDeleteModal(row.original.id)}>
              <IconTrash />
            </ActionIcon>
          </Group>
        ),
      },
    ],
    []
  );

  const table = useMantineReactTable({
    columns,
    data: walletQuery?.data?.results ?? [],
    renderTopToolbarCustomActions: () => (
      <Group mt="md">
        <Button onClick={handleAdd} color="orange">
          Dodaj kwotę
        </Button>
      </Group>
    ),
  });

  return (
    <Container fluid mt={20}>
      <MantineReactTable table={table} />
    </Container>
  );
};

export default ResultList;
