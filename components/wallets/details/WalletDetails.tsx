import classes from './Details.module.css';
import {
  Button,
  Center,
  Fieldset,
  Flex,
  Grid,
  Group,
  Loader,
  Stack,
  Tabs,
  Text,
  Title,
} from '@mantine/core';
import {
  IconChartBar,
  IconFileDescription,
  IconStack2,
  IconTransitionBottom,
} from '@tabler/icons-react';
import useWallet from '@/components/wallets/useWallet';
import React, { useState } from 'react';
import MonthsCards from '@/components/wallets/details/MonthsCards';
import useTranslation from 'next-translate/useTranslation';
import EditableDescription from '@/components/wallets/details/EditableDescription';
import EditableGreenType from '@/components/wallets/details/EditableGreenType';
import ElementTab from '@/components/wallets/details/ElementTab';
import TrancheTab from '@/components/wallets/details/TrancheTab';
import GreenPropertyIcon from '@/components/common/forms/GreenPropertyIcon/GreenPropertyIcon';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import router from 'next/router';
import { NotificationType } from '@/types/Common';
import { notify } from '@/utils/notify';
import Link from 'next/link';
import { WalletService } from '@/services/WalletService';
import ResultTab from '@/components/wallets/details/ResultTab/ResultTab';
import { useExportToExcel } from '../simulations/useExportToExcel';

const WalletDetails = () => {
  const walletQuery = useWallet();
  const { id: walletId } = router.query;
  const { t } = useTranslation('common');

  const [defaultTab, setDefaultTab] = useState<string>('details');

  const wallet = walletQuery?.data;
  const { exportToExcel } = useExportToExcel(wallet, wallet?.tranches ?? []);

  /**
   * Returns true if simulation should be disabled because the wallet's start year is in the past.
   */
  const isSimulationDisabled = () => {
    if (!wallet?.startDate) return false;
    const currentYear = new Date().getFullYear();
    const walletYear = new Date(wallet.startDate).getFullYear();
    return walletYear < currentYear;
  };

  const handleSimulate = async () => {
    const id = typeof walletId === 'string' ? walletId : null;
    if (!id) {
      notify(NotificationType.ERROR, 'Nieprawidłowe lub brakujące ID portfela');
      return;
    }
    await router.push(RouterPaths.SIMULATION_ID(id));
  };

  const handleRecalculate = async () => {
    const id = typeof walletId === 'string' ? walletId : null;
    if (!id) {
      notify(NotificationType.ERROR, 'Nieprawidłowe lub brakujące ID portfela');
      return;
    }
    await WalletService.recalculate(id);
    window.location.reload();
  };

  const handleExportToExcel = () => {
    exportToExcel();
  };

  if (walletQuery.isLoading || walletQuery.isFetching) {
    return (
      <Center>
        <Loader color="orange" size="md" />
      </Center>
    );
  }

  const groupedPrices = Object.groupBy(
    wallet?.prices ? wallet?.prices : [],
    ({ timeUnit }) => timeUnit
  );
  const groupedProducts = wallet?.monthlyProducts || [];
  const groupedTranches = wallet?.monthlyTranches || [];

  return (
    <Tabs defaultValue={defaultTab}>
      <Flex justify="flex-end" mt="md">
        <Button
          onClick={handleSimulate}
          color="blue"
          mr="md"
          size="sm"
          disabled={isSimulationDisabled()}
        >
          Symulacja
        </Button>
        <Button onClick={handleExportToExcel} color="orange" mr="md" size="sm">
          Eksport do Excela
        </Button>
        <Button onClick={handleRecalculate} color="orange" size="sm">
          Przeliczenie
        </Button>
      </Flex>
      <Tabs.Panel value="details" pb="xs">
        <Text size="xl">Detale portfela</Text>
      </Tabs.Panel>
      <Tabs.Panel value="elements" pb="xs">
        <Text size="xl">Elementy portfela</Text>
      </Tabs.Panel>
      <Tabs.Panel value="tranches" pb="xs">
        <Text size="xl">Transze portfela</Text>
      </Tabs.Panel>
      <Tabs.Panel value="result" pb="xs">
        <Text size="xl">Rezultat portfela</Text>
      </Tabs.Panel>
      <Tabs.List grow>
        <Tabs.Tab
          value="details"
          size="xl"
          leftSection={<IconFileDescription className={classes.iconStyle} />}
          onClick={() => {
            setDefaultTab('details');
          }}
        >
          <Text size="xl">Detale</Text>
        </Tabs.Tab>
        <Tabs.Tab
          value="elements"
          leftSection={<IconStack2 className={classes.iconStyle} />}
          onClick={() => {
            setDefaultTab('elements');
          }}
        >
          <Text size="xl">Elementy</Text>
        </Tabs.Tab>
        <Tabs.Tab
          value="tranches"
          leftSection={<IconTransitionBottom className={classes.iconStyle} />}
          onClick={() => {
            setDefaultTab('tranches');
          }}
        >
          <Text size="xl">Transze</Text>
        </Tabs.Tab>
        <Tabs.Tab
          value="result"
          leftSection={<IconChartBar className={classes.iconStyle} />}
          onClick={() => {
            setDefaultTab('result');
          }}
        >
          <Text size="xl">Rezultat</Text>
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="details">
        <Fieldset mt={20} radius="md">
          <Grid>
            <Grid.Col span={3}>
              <Stack>
                <Title size="xl">Umowa</Title>
                <Text>
                  <Link
                    target="_blank"
                    rel="noopener noreferrer"
                    href={RouterPaths.AGREEMENTS_DETAILS(wallet?.agreement?.id ?? '')}
                  >
                    {wallet?.agreement?.customer?.name} - {wallet?.agreement?.startDate} -{' '}
                    {t(wallet?.agreement?.mediaType)} - {wallet?.agreement?.supplier?.name}
                  </Link>
                </Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={3}>
              <Stack>
                <Title size="xl">Data</Title>
                <Text>{wallet?.startDate}</Text>
              </Stack>
            </Grid.Col>
            <Grid.Col span={3}>
              <Stack>
                <Group>
                  <Title size="xl">Sposób liczenia zielonych</Title>
                  <GreenPropertyIcon />
                </Group>
                <EditableGreenType walletQuery={walletQuery} />
              </Stack>
            </Grid.Col>
            <Grid.Col span={3}>
              <EditableDescription walletQuery={walletQuery} />
            </Grid.Col>
          </Grid>
        </Fieldset>
        <MonthsCards
          groupedPrices={groupedPrices}
          groupedTranches={groupedTranches}
          products={groupedProducts}
        />
      </Tabs.Panel>

      <Tabs.Panel value="elements">
        <ElementTab walletQuery={walletQuery} />
      </Tabs.Panel>

      <Tabs.Panel value="tranches">
        <TrancheTab walletQuery={walletQuery} />
      </Tabs.Panel>
      <Tabs.Panel value="result">
        <ResultTab walletQuery={walletQuery} />
      </Tabs.Panel>
    </Tabs>
  );
};

export default WalletDetails;
