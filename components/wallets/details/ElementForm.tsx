import { FieldValues, FormProvider } from 'react-hook-form';
import { <PERSON><PERSON>, Stack } from '@mantine/core';
import ElementTypeSelect from '@/components/common/forms/ElementTypeSelect/ElementTypeSelect';
import NumberInput from '@/components/common/forms/NumberInput/NumberInput';
import TimeUnitSelect from '@/components/common/forms/TimeUnitSelect/TimeUnitSelect';
import CustomModal from '@/components/common/forms/Modal/CustomModal';
import React, { useEffect } from 'react';
import ElementService from '@/services/ElementService';
import { notify } from '@/utils/notify';
import { NotificationType } from '@/types/Common';
import isDuplicate from '@/utils/is-duplicate';
import _ from 'lodash';
import useElementMedia from '@/components/wallets/details/useElementMedia';

const ElementForm = ({
  isElementModalOpen,
  setElementModalOpen,
  isElementEditForm,
  elementForm,
  walletQuery,
}) => {
  const { watch } = elementForm;
  const { applyMediaForElementType } = useElementMedia();
  const handleElementModalSubmit = async (data: FieldValues) => {
    await elementForm.trigger();
    const oldValues = _.clone(data);
    // Przeszukuje tablicę elementów w poszukiwaniu duplikatów na podstawie kolumn typu elementu, mediów i time unitu.
    const duplicate = isDuplicate(data, walletQuery?.data?.elements, ['type', 'media', 'timeUnit']);
    if (elementForm.formState.isValid && !duplicate) {
      try {
        isElementEditForm
          ? await ElementService.updateElement(oldValues, oldValues.id)
          : await ElementService.createElement(oldValues);
        notify(NotificationType.SUCCESS, 'Element został zapisany!');
      } catch (error) {
        const message = error?.response?.data?.detail
          ? error?.response?.data?.detail
          : 'Zapis elementu nie powiódł się';
        notify(NotificationType.ERROR, message);
      }
      await walletQuery.refetch();
      setElementModalOpen(false);
    }
    if (duplicate) {
      notify(NotificationType.ERROR, 'Duplikaty elementów nie są dozwolone.');
    }
  };

  const elementType = watch('type');
  useEffect(() => {
    elementForm.setValue('media', applyMediaForElementType(elementType));
  }, [elementType]);
  return (
    <CustomModal
      opened={isElementModalOpen}
      onClose={() => setElementModalOpen(false)}
      onConfirm={() => handleElementModalSubmit(elementForm.getValues())}
      size="30%"
      radius="md"
      title={isElementEditForm ? 'Edycja elementu' : 'Tworzenie nowego elementu'}
    >
      <FormProvider {...elementForm}>
        <Fieldset legend={'Dane elementu'} radius={'md'}>
          <form>
            <Stack>
              <ElementTypeSelect
                name="type"
                label="Element"
                placeholder="Wybierz typ elementu"
                withAsterisk
                disabled={false}
              />
              <NumberInput
                name="value"
                label="Wartość"
                placeholder="Wpisz wartość"
                disabled={false}
                hideControls
                withAsterisk
                allowNegative={true}
              />
              <TimeUnitSelect
                name="timeUnit"
                label="Jednostka czasu"
                placeholder="Wybierz jednostkę czasu"
                withAsterisk
                disabled={false}
              />
            </Stack>
          </form>
        </Fieldset>
      </FormProvider>
    </CustomModal>
  );
};

export default ElementForm;
