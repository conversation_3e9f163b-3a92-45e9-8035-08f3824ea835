import { FieldVal<PERSON>, FormProvider } from 'react-hook-form';
import { Stack } from '@mantine/core';
import DatePicker from '@/components/common/forms/DatePicker/DatePicker';
import NumberInput from '@/components/common/forms/NumberInput/NumberInput';
import ContractSingleSelect from '@/components/common/forms/ContractSingleSelect/ContractSingleSelect';
import TimeUnitSelect from '@/components/common/forms/TimeUnitSelect/TimeUnitSelect';
import PurchaseMethodSingleSelect, {
  priceReferenceOptions,
} from '@/components/common/forms/PurchaseMethodSingleSelect/PurchaseMethodSingleSelect';
import CustomModal from '@/components/common/forms/Modal/CustomModal';
import React, { useEffect, useState } from 'react';
import { TrancheService } from '@/services/TrancheService';
import { notify } from '@/utils/notify';
import { NotificationType } from '@/types/Common';
import { ContractService } from '@/services/ContractService';

const TrancheForm = ({
  walletQuery,
  isTrancheModalOpen,
  setTrancheModalOpen,
  isTrancheEditForm,
  trancheForm,
}) => {
  const wallet = walletQuery?.data;
  const [contractList, setContractList] = useState([]);
  const [availablePriceReferences, setAvailablePriceReferences] = useState([]);
  useEffect(() => {
    async function loadContracts() {
      try {
        const response = await ContractService.getContractsByAgreement(wallet.agreement.id);
        setContractList(
          response.content.map((contract) => ({
            value: isTrancheEditForm ? contract.id : contract.name,
            label: contract.name,
          }))
        );
      } catch (error) {
        console.error('Błąd podczas ładowania kontraktów:', error);
      }
    }

    loadContracts();
  }, [wallet?.agreement?.id, isTrancheEditForm]);
  const handleTrancheModalSubmit = async (data: FieldValues) => {
    const isFormValid = await trancheForm.trigger();
    if (isFormValid) {
      try {
        isTrancheEditForm
          ? await TrancheService.updateTranche(data, data.id)
          : await TrancheService.createTranche(data);
        notify(NotificationType.SUCCESS, 'Transza została zapisana!');
        await walletQuery.refetch();
        setTrancheModalOpen(false);
      } catch (error) {
        if (
          error.response &&
          error.response.data &&
          error.response.data.messages &&
          error.response.data.messages.length > 0
        ) {
          error.response.data.messages.forEach((message) => {
            notify(NotificationType.ERROR, message.reason);
          });
        } else {
          notify(NotificationType.ERROR, 'Zapis transzy nie powiódł się');
        }
      }
    } else {
      notify(NotificationType.ERROR, 'Formularz zawiera błędy. Proszę poprawić.');
    }
  };

  const contract = trancheForm.watch('contract');

  useEffect(() => {
    if (isTrancheEditForm || !contract || contractList.length === 0) {
      trancheForm.setValue('timeUnit', null);
      trancheForm.setValue('priceReference', '');
      setAvailablePriceReferences([]);
      return;
    }

    const selected = contractList.find((c) => c.value === contract);
    const full = wallet?.agreement?.contracts?.find((c) => c.name === selected?.label);

    if (!selected || !full) {
      trancheForm.setValue('timeUnit', null);
      trancheForm.setValue('priceReference', '');
      setAvailablePriceReferences([]);
      return;
    }

    const loadDetails = async () => {
      try {
        const timeUnit = await ContractService.getContractTimeUnit(selected.label);
        trancheForm.setValue('timeUnit', timeUnit);

        const refs = full.priceReference || [];
        trancheForm.setValue('priceReference', refs[0] || '');
        setAvailablePriceReferences(
          refs.map((ref) => ({
            value: ref,
            label: priceReferenceOptions.find((opt) => opt.value === ref)?.label || ref,
          }))
        );
      } catch {
        trancheForm.setValue('timeUnit', null);
        trancheForm.setValue('priceReference', '');
        setAvailablePriceReferences([]);
      }
    };

    loadDetails();
  }, [contract, contractList, trancheForm, wallet, isTrancheEditForm]);

  return (
    <CustomModal
      opened={isTrancheModalOpen}
      onClose={() => setTrancheModalOpen(false)}
      onConfirm={() => handleTrancheModalSubmit(trancheForm.getValues())}
      size="30%"
      radius="md"
      title={isTrancheEditForm ? 'Edycja transzy' : 'Tworzenie nowej transzy'}
    >
      <FormProvider {...trancheForm}>
        <form>
          <Stack>
            <DatePicker
              name="executionDate"
              label="Data wykonania"
              placeholder="Wybierz datę z kalendarza"
              withAsterisk
            />
            <NumberInput
              name="volume"
              label="Wielkość transzy (%)"
              placeholder="Wpisz wielkość transzy"
              hideControls
              withAsterisk
            />
            <ContractSingleSelect
              name="contract"
              label="Kontrakt"
              placeholder="Wybierz z listy"
              contracts={contractList}
              withAsterisk
            />
            <NumberInput
              name="price"
              label="Cena (zł/MWh)"
              placeholder="Wpisz cenę"
              hideControls
              withAsterisk
            />
            <TimeUnitSelect
              name="timeUnit"
              label="Jednostka czasu"
              placeholder="Wybierz z listy"
              withAsterisk
            />
            <PurchaseMethodSingleSelect
              name="priceReference"
              label="Sposób zakupu"
              placeholder="Wybierz z listy"
              withAsterisk
              disabled={false}
              options={availablePriceReferences}
            />
          </Stack>
        </form>
      </FormProvider>
    </CustomModal>
  );
};

export default TrancheForm;
