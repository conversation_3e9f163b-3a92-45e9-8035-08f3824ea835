import ConfirmModal from '@/components/common/forms/ConfirmModal/ConfirmModal';
import React from 'react';
import { TrancheService } from '@/services/TrancheService';

const TrancheDeleteForm = ({
  setDeleteModalOpen,
  setSelectedTranche,
  selectedTrancheId,
  walletQuery,
  isDeleteModalOpen,
}) => {
  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setSelectedTranche(null);
  };

  const confirmDelete = async () => {
    if (selectedTrancheId) {
      await TrancheService.deleteTranche(selectedTrancheId);
      walletQuery.refetch();
      closeDeleteModal();
    }
  };

  return (
    <ConfirmModal
      opened={isDeleteModalOpen}
      onClose={closeDeleteModal}
      onConfirm={confirmDelete}
      title={'Potwierdzenie usunięcia'}
    >
      <PERSON>zy na pewno chcesz usunąć tę tranchę?
    </ConfirmModal>
  );
};

export default TrancheDeleteForm;
