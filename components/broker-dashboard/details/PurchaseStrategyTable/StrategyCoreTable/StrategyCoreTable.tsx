import React from 'react';
import styles from './StrategyCoreTable.module.css';
import TrancheTooltip from '@/components/broker-dashboard/details/PurchaseStrategyTable/StrategyCoreTable/TrancheTooltip/TrancheTooltip';
import OverlayCell from '@/components/broker-dashboard/details/PurchaseStrategyTable/StrategyCoreTable/OverlayCell/OverlayCell';
import {
  getDefaultContractForM,
  getDefaultContractForQ,
  getDefaultContractForY,
} from '@/utils/brokerDashboardUtils';

// Funkcje pomocnicze – oddzielne filtrowanie transz normalnych i rekomendowanych i wirtualnych
function getNormalTranchesForTimeUnit(wallet: any, timeUnit: string) {
  return (wallet?.tranches?.filter((t: any) => t.timeUnit === timeUnit && !t.recommendation && !t.virtual) || []);
}

function getRecTranchesForTimeUnit(wallet: any, timeUnit: string) {
  return (
    wallet?.tranches?.filter((t: any) => t.timeUnit === timeUnit && t.recommendation && !t.virtual) || []);
}

function getVirtTranchesForTimeUnit(wallet: any, timeUnit: string) {
  return wallet?.tranches?.filter((t: any) => t.timeUnit === timeUnit && t.virtual) || [];
}

interface QGroup {
  group: string;
  normalFill: number;
  recFill: number;
  virtFill: number;
}

interface MFill {
  normal: number;
  rec: number;
  virt: number;
}

interface StrategyCoreTableProps {
  generateYScale: () => number[];
  y: number;
  q: number;
  mSpot: number;
  colorMap: string[][];
  fillYNormal: number;
  fillYRec: number;
  fillYVirt: number;
  fillQGroups: QGroup[];
  fillM: MFill[];
  wallet: any;
  onRefetch: () => void;
  dashedLines?: DashedLines;
}

interface DashedLines {
  Y?: number;
  Q?: number;
  M?: number;
}

export default function StrategyCoreTable({
  generateYScale,
  y,
  q,
  mSpot,
  colorMap,
  fillYNormal,
  fillYRec,
  fillYVirt,
  fillQGroups,
  fillM,
  wallet,
  onRefetch,
  dashedLines,
}: StrategyCoreTableProps) {
  return (
    <div className={styles.chartWrapper}>
      {/* Lewa oś (etykiety 0..100 co 10) */}
      <div className={styles.leftAxis}>
        {generateYScale()
          .slice()
          .reverse()
          .filter((val) => val !== 0 && val !== 100)
          .map((val) => (
            <div key={val} className={styles.axisLabel} style={{ bottom: `${val}%` }}>
              {val}
            </div>
          ))}
      </div>

      {/* Wewnętrzny obszar wykresu */}
      <div className={styles.chartInner}>
        {/* Linie poziome – wyświetlamy tylko granice segmentów */}
        {generateYScale().map((val) => {
          if (val === 0 || val === 100 || val === y || val === y + q) {
            return (
              <div key={`hline-${val}`} className={styles.hLine} style={{ bottom: `${val}%` }} />
            );
          }
          return null;
        })}
        {/* Jeśli granice nie są wielokrotnościami 10, dodajemy je dodatkowo */}
        {y % 10 !== 0 && <div key="hline-y" className={styles.hLine} style={{ bottom: `${y}%` }} />}
        {(y + q) % 10 !== 0 && (
          <div key="hline-q" className={styles.hLine} style={{ bottom: `${y + q}%` }} />
        )}
        {/* ⇢ Przerywane linie VWAP */}
        {dashedLines && (
          <>
            {dashedLines.Y !== undefined && dashedLines.Y > 0 && dashedLines.Y < y && (
              <div className={styles.customHLine} style={{ bottom: `${dashedLines.Y}%` }} />
            )}

            {dashedLines.Q !== undefined && dashedLines.Q > 0 && dashedLines.Q < q && (
              <div className={styles.customHLine} style={{ bottom: `${y + dashedLines.Q}%` }} />
            )}

            {dashedLines.M !== undefined && dashedLines.M > 0 && dashedLines.M < mSpot && (
              <div className={styles.customHLine} style={{ bottom: `${y + q + dashedLines.M}%` }} />
            )}
          </>
        )}
        {/* Linie pionowe – renderujemy osobno dla segmentów Q i M */}
        {/* Linie pionowe dla segmentu Q: co 3 kolumny, od bottom: y% do height: q% */}
        {Array.from({ length: 13 }, (_, col) => col)
          .filter((col) => col % 3 === 0)
          .map((col) => (
            <div
              key={`vline-q-${col}`}
              className={styles.vLine}
              style={{
                left: `${(col / 12) * 100}%`,
                bottom: `${y}%`,
                height: `${q}%`,
              }}
            />
          ))}

        {/* Linie pionowe dla segmentu M: co kolumnę, od bottom: y+q% do góry (height = 100 - (y+q)) */}
        {Array.from({ length: 13 }, (_, col) => col).map((col) => (
          <div
            key={`vline-m-${col}`}
            className={styles.vLine}
            style={{
              left: `${(col / 12) * 100}%`,
              bottom: `${y + q}%`,
              height: `${100 - (y + q)}%`,
            }}
          />
        ))}

        {/* Komórki 12 kolumn x 100 wierszy */}
        {Array(12)
          .fill(null)
          .map((_, monthIndex) =>
            Array.from({ length: 100 }, (_, rowIndex) => (
              <div
                key={`cell-${monthIndex}-${rowIndex}`}
                className={styles.cell}
                style={{
                  left: `${(monthIndex / 12) * 100}%`,
                  bottom: `${rowIndex}%`,
                  height: '1%',
                  backgroundColor: colorMap[monthIndex][rowIndex] || 'transparent',
                }}
              />
            ))
          )}

        {/*
          ======================
          SEGMENT Y
          ======================
        */}
        {/* Overlay dla niezakolorowanego obszaru Y – zaczynamy od końca normalnych + rekomendowanych transz */}
        {fillYNormal + fillYRec + fillYVirt < y && (
          <OverlayCell
            wallet={wallet}
            defaultContract={getDefaultContractForY(wallet.year)}
            onRefetch={onRefetch}
            style={{
              left: 0,
              width: '100%',
              bottom: `${fillYNormal + fillYRec + fillYVirt}%`,
              height: `${y - (fillYNormal + fillYRec + fillYVirt)}%`,
            }}
          />
        )}
        {/* Tooltip dla normalnych transz Y */}
        {fillYNormal > 0 && getNormalTranchesForTimeUnit(wallet, 'Y').length > 0 && (
          <TrancheTooltip
            wallet={wallet}
            timeUnit="Y"
            filteredTranches={getNormalTranchesForTimeUnit(wallet, 'Y')}
            style={{
              left: 0,
              width: '100%',
              bottom: '0%',
              height: `${fillYNormal}%`,
            }}
          />
        )}
        {/* Tooltip dla rekomendowanych transz Y */}
        {fillYRec > 0 && getRecTranchesForTimeUnit(wallet, 'Y').length > 0 && (
          <TrancheTooltip
            wallet={wallet}
            timeUnit="Y"
            filteredTranches={getRecTranchesForTimeUnit(wallet, 'Y')}
            style={{
              left: 0,
              width: '100%',
              bottom: `${fillYNormal}%`,
              height: `${fillYRec}%`,
            }}
          />
        )}
        {/* Tooltip virtual Y */}
        {fillYVirt > 0 && getVirtTranchesForTimeUnit(wallet, 'Y').length > 0 && (
          <TrancheTooltip
            wallet={wallet}
            timeUnit="Y"
            filteredTranches={getVirtTranchesForTimeUnit(wallet, 'Y')}
            style={{
              left: 0,
              width: '100%',
              bottom: `${fillYNormal + fillYRec}%`,
              height: `${fillYVirt}%`,
            }}
          />
        )}

        {/*
          ======================
          SEGMENTY Q (Q1..Q4)
          ======================
        */}
        {fillQGroups.map((qGroup) => {
          const { group, normalFill, recFill, virtFill } = qGroup;
          const defaultContractQ = getDefaultContractForQ(group, wallet.year);
          const left =
            group === 'Q1'
              ? '0%'
              : group === 'Q2'
                ? `${(3 / 12) * 100}%`
                : group === 'Q3'
                  ? `${(6 / 12) * 100}%`
                  : `${(9 / 12) * 100}%`;
          const widthPercent = `${(3 / 12) * 100}%`;
          const bottomRec = y + normalFill;
          const bottomVirt = y + normalFill + recFill;

          return (
            <React.Fragment key={`q-frag-${group}`}>
              {/* Overlay dla niezakolorowanego obszaru Q */}
              {normalFill + recFill + virtFill < q && (
                <OverlayCell
                  wallet={wallet}
                  defaultContract={defaultContractQ}
                  onRefetch={onRefetch}
                  style={{
                    left,
                    width: widthPercent,
                    bottom: `${y + normalFill + recFill + virtFill}%`,
                    height: `${q - (normalFill + recFill + virtFill)}%`,
                  }}
                />
              )}
              {/* Tooltip dla normalnych transz Q */}
              {normalFill > 0 && getNormalTranchesForTimeUnit(wallet, group).length > 0 && (
                <TrancheTooltip
                  wallet={wallet}
                  timeUnit={group}
                  filteredTranches={getNormalTranchesForTimeUnit(wallet, group)}
                  style={{
                    left,
                    width: widthPercent,
                    bottom: `${y}%`,
                    height: `${normalFill}%`,
                  }}
                />
              )}
              {/* Tooltip dla rekomendowanych transz Q */}
              {recFill > 0 && getRecTranchesForTimeUnit(wallet, group).length > 0 && (
                <TrancheTooltip
                  wallet={wallet}
                  timeUnit={group}
                  filteredTranches={getRecTranchesForTimeUnit(wallet, group)}
                  style={{
                    left,
                    width: widthPercent,
                    bottom: `${bottomRec}%`,
                    height: `${recFill}%`,
                  }}
                />
              )}
              {/* Tooltip virt */}
              {virtFill > 0 && getVirtTranchesForTimeUnit(wallet, group).length > 0 && (
                <TrancheTooltip
                  wallet={wallet}
                  timeUnit={group}
                  filteredTranches={getVirtTranchesForTimeUnit(wallet, group)}
                  style={{
                    left,
                    width: widthPercent,
                    bottom: `${bottomVirt}%`,
                    height: `${virtFill}%`,
                  }}
                />
              )}
            </React.Fragment>
          );
        })}

        {/*
          ======================
          SEGMENTY M (M1..M12)
          ======================
        */}
        {fillM.map((fillVal, colIndex) => {
          const leftPx = `${(colIndex / 12) * 100}%`;
          const widthPx = `${100 / 12}%`;
          const defaultContractM = getDefaultContractForM(colIndex, wallet.year);

          // bottom do rec i virt
          const bottomRec = y + q + fillVal.normal;
          const bottomVirt = y + q + fillVal.normal + fillVal.rec;

          return (
            <React.Fragment key={`m-frag-${colIndex}`}>
              {/* Overlay dla niezakolorowanego obszaru M */}
              {fillVal.normal + fillVal.rec + fillVal.virt < mSpot && (
                <OverlayCell
                  wallet={wallet}
                  defaultContract={defaultContractM}
                  onRefetch={onRefetch}
                  style={{
                    left: leftPx,
                    width: widthPx,
                    bottom: `${y + q + fillVal.normal + fillVal.rec + fillVal.virt}%`,
                    height: `${mSpot - (fillVal.normal + fillVal.rec + fillVal.virt)}%`,
                  }}
                />
              )}
              {/* Tooltip dla normalnych transz M */}
              {fillVal.normal > 0 &&
                getNormalTranchesForTimeUnit(wallet, `M${colIndex + 1}`).length > 0 && (
                  <TrancheTooltip
                    wallet={wallet}
                    timeUnit={`M${colIndex + 1}`}
                    filteredTranches={getNormalTranchesForTimeUnit(wallet, `M${colIndex + 1}`)}
                    style={{
                      left: leftPx,
                      width: widthPx,
                      bottom: `${y + q}%`,
                      height: `${fillVal.normal}%`,
                    }}
                  />
                )}
              {/* Tooltip dla rekomendowanych transz M */}
              {fillVal.rec > 0 &&
                getRecTranchesForTimeUnit(wallet, `M${colIndex + 1}`).length > 0 && (
                  <TrancheTooltip
                    wallet={wallet}
                    timeUnit={`M${colIndex + 1}`}
                    filteredTranches={getRecTranchesForTimeUnit(wallet, `M${colIndex + 1}`)}
                    style={{
                      left: leftPx,
                      width: widthPx,
                      bottom: `${bottomRec}%`,
                      height: `${fillVal.rec}%`,
                    }}
                  />
                )}
              {/* Tooltip virt */}
              {fillVal.virt > 0 &&
                getVirtTranchesForTimeUnit(wallet, `M${colIndex + 1}`).length > 0 && (
                  <TrancheTooltip
                    wallet={wallet}
                    timeUnit={`M${colIndex + 1}`}
                    filteredTranches={getVirtTranchesForTimeUnit(wallet, `M${colIndex + 1}`)}
                    style={{
                      left: leftPx,
                      width: widthPx,
                      bottom: `${bottomVirt}%`,
                      height: `${fillVal.virt}%`,
                    }}
                  />
                )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Podsumowanie strategii – wizualizacja 3 segmentów */}
      <div className={styles.strategySummary}>
        <div className={styles.summaryBlock} style={{ height: `${mSpot}%` }}>
          <span>M/SPOT</span>
        </div>
        <div className={styles.summaryBlock} style={{ height: `${q}%` }}>
          <span>Q</span>
        </div>
        <div className={styles.summaryBlock} style={{ height: `${y}%` }}>
          <span>Y</span>
        </div>
      </div>
    </div>
  );
}
