import React, { useEffect } from 'react';
import { Button, Group, Modal, Textarea } from '@mantine/core';
import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import dayjs from 'dayjs';
import { RecommendationSchema } from '@/components/wallets/recommendation/schema';
import { RecommendationService } from '@/services/RecommendationService';
import { SimulationService } from '@/services/SimulationService';
import useWalletNotifications from '@/components/common/hooks/useWalletNotifications';
import AgreementMultiSelect from '@/components/common/forms/AgreementMultiSelect/AgreementMultiSelect';
import ContractOptions from '@/components/common/forms/ContractOptions/ContractOptions';
import TimeUnitSelect from '@/components/common/forms/TimeUnitSelect/TimeUnitSelect';
import DatePicker from '@/components/common/forms/DatePicker/DatePicker';
import NumberInput from '@/components/common/forms/NumberInput/NumberInput';
import PriceReferenceSelect from '@/components/common/forms/PriceReferenceSelect/PriceReferenceSelect';
import ExecutorSelect from '@/components/wallets/recommendation/components/ExecutorComboBox/ExecutorSelect';
import TextInput from '@/components/common/forms/TextInput/TextInput';
import { createWalletPayload } from '@/components/wallets/simulations/useSimulationComponent';
import { ContractService } from '@/services/ContractService';
import useErrorHandler from '@/components/common/hooks/useErrorHandler';

export interface RecommendationCreateModalProps {
  opened: boolean;
  onClose: () => void;
  defaultAgreement?: string;
  defaultContract?: string;
  wallet: any;
  onSuccess?: (simulatedData: any) => void;
}

const RecommendationCreate: React.FC<RecommendationCreateModalProps> = ({
  opened,
  onClose,
  defaultAgreement,
  defaultContract,
  wallet,
  onSuccess,
}) => {
  const { successNotification, errorNotification } = useWalletNotifications();

  const methods = useForm({
    resolver: zodResolver(RecommendationSchema),
    defaultValues: {
      contractId: defaultAgreement ? [defaultAgreement] : [],
      contract: defaultContract || '',
      timeUnit: '',
      deadline: dayjs().format('YYYY-MM-DD'),
      volume: '',
      price: '',
      carrier: '',
      requiresCustomerAcceptance: false,
      sendRecommendation: false,
      purchaseMethod: '',
      emailTemplateComment: '',
      executor: '',
      status: '',
    },
  });

  const { handleSubmit, watch, setValue } = methods;
  const contract = watch('contract');
  const { decorateWithErrorHandling } = useErrorHandler();

  useEffect(() => {
    const fetchTimeUnit = async () => {
      if (contract) {
        try {
          const timeUnit = await ContractService.getContractTimeUnit(contract);
          setValue('timeUnit', timeUnit);
        } catch (error) {
          console.error('Error fetching time unit:', error);
          setValue('timeUnit', '');
        }
      } else {
        setValue('timeUnit', '');
      }
    };
    fetchTimeUnit();
  }, [contract, setValue]);

  const handleCreate = async (formData: any) => {
    await decorateWithErrorHandling(async () => {
      await RecommendationService.create({
        contractId: formData.contractId,
        contract: formData.contract,
        timeUnit: formData.timeUnit,
        volume: formData.volume,
        price: formData.price,
        carrier: formData.carrier,
        requiresCustomerAcceptance: formData.requiresCustomerAcceptance,
        sendRecommendation: formData.sendRecommendation,
        purchaseMethod: formData.purchaseMethod,
        emailTemplateComment: formData.emailTemplateComment,
        executor: formData.executor,
        deadline: formData.deadline,
        status: 'NEW',
      });

      // nie dopinamy rekomendacji do localnego wallet, budujemy i wywołujemy ten sam simulatedData
      const payload = createWalletPayload(wallet);
      const simulatedData = await SimulationService.simulateDashboard(payload);

      successNotification({
        title: 'Sukces!',
        message: 'Rekomendacja zapisana.',
      });
      onClose();
      if (onSuccess) {
        onSuccess(simulatedData);
      }
    });
  };

  return (
    <Modal opened={opened} onClose={onClose} title="Dodaj rekomendację">
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(handleCreate)}>
          <AgreementMultiSelect
            name="contractId"
            label="Wybierz umowę"
            placeholder="Wybierz umowę"
            withAsterisk
            contractYear={null}
            disabled={true}
          />
          <ContractOptions
            name="contract"
            label="Kontrakt"
            placeholder="Wybierz kontrakt"
            withAsterisk
          />
          <TimeUnitSelect
            name="timeUnit"
            label="Jednostka czasu"
            placeholder="Wybierz jednostkę czasu"
            withAsterisk
          />
          <DatePicker
            name="deadline"
            label="Termin decyzji"
            placeholder="Wybierz datę"
            withAsterisk
          />
          <NumberInput
            name="volume"
            label="Wielkość transzy (%)"
            placeholder="Wpisz wolumen"
            withAsterisk
          />
          <PriceReferenceSelect
            name="purchaseMethod"
            label="Sposób zamawiania"
            placeholder="Wybierz sposób zamawiania"
            withAsterisk
            disabled={false}
          />
          <TextInput name="price" label="Cena" placeholder="Wpisz cenę" withAsterisk />
          <Textarea
            name="emailTemplateComment"
            label="Komentarz do szablonu e-mail"
            placeholder="Wpisz komentarz"
          />
          <ExecutorSelect
            name="executor"
            label="Wykonawca po stronie ES"
            placeholder="Wybierz lub wpisz wykonawcę"
            disabled={false}
          />
          <Group mt="md">
            <Button type="submit">Dodaj rekomendację</Button>
          </Group>
        </form>
      </FormProvider>
    </Modal>
  );
};

export default RecommendationCreate;
