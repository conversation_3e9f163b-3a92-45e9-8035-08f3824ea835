/* 1) główny obszar wykresu */
.chartWrapper {
  display: flex;
  width: 100%;
  min-width: 800px;
  position: relative;
  border: 1px solid #ccc;
  height: 400px;
}

/* lewa oś (0..100) */
.leftAxis {
  position: relative;
  width: 80px;  /* dopasowanie szerokości do górnej kolumny wcześniejszej tabeli */
  min-width: 80px;
  height: 100%; /* To kluczowe! Tyle samo co .chartWrapper */
  background-color: #fdfdfd;
  border-right: 1px solid #ccc;
  padding: 0;
}

/* każda etykieta skali (0,10,20,...,100) */
.axisLabel {
  position: absolute;
  right: 8px;
  transform: translateY(50%);
  font-size: 12px;
  color: #444;
}

/* prawa częś<PERSON> z siatką i pasami */
.chartInner {
  flex: 1;
  position: relative;
  transform-origin: center;
  background-color: #fff;
}

/* poziome linie co 10% (od dołu) */
.hLine {
  position: absolute;
  left: 0;
  width: 100%;
  height: 0;
  border-top: 1px dashed #ccc;
}

/* pionowe linie dla kolumn M1..M12 */
.vLine {
  position: absolute;
  width: 0;
  border-right: 1px solid #ccc;
}

.cell {
  position: absolute;
  width: calc(100% / 12);
  height: 10%;
  border: 1px solid rgba(0, 0, 0, 0.1);  TODO tutaj linie po odkomentowaniu co 1px
  transition: background-color 0.2s ease-in-out;
}

.cell:hover {
  background-color: rgba(249, 165, 165, 0.5);
  cursor: pointer;
}

.strategySummary {
  width: 80px;
  min-width: 80px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #ccc;
  text-align: center;
}

.summaryBlock {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #ccc;
}

.summaryBlock span {
  writing-mode: vertical-rl;
  transform: rotate(-90deg);
}