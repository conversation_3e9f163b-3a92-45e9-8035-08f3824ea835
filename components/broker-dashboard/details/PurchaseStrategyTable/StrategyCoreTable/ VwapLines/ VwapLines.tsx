import React from 'react';
import styles from './VwapLines.module.css';

interface Props {
  y: number;
  q: number;
  mSpot: number;
  dashedLines: {
    Y?: number;
    Q?: Record<'Q1' | 'Q2' | 'Q3' | 'Q4', number>;
    M?: number[];
  };
}

export default function VwapLines({ y, q, mSpot, dashedLines }: Props) {
  return (
    <>
      {/* Y – pełna <PERSON> */}
      {typeof dashedLines.Y === 'number' && dashedLines.Y > 0 && dashedLines.Y < y && (
        <div
          className={styles.customHLine}
          style={{ bottom: `${dashedLines.Y}%`, left: 0, width: '100%' }}
        />
      )}

      {/* Q1‑Q4 – po 3 kolumny */}
      {dashedLines.Q &&
        (['Q1', 'Q2', 'Q3', 'Q4'] as const).map((qKey, idx) => {
          const val = dashedLines.Q?.[qKey];
          if (typeof val !== 'number' || val <= 0 || val >= q) return null;
          const left = `${((idx * 3) / 12) * 100}%`;
          const width = `${(3 / 12) * 100}%`;
          return (
            <div
              key={`dashed-${qKey}`}
              className={styles.customHLine}
              style={{ bottom: `${y + val}%`, left, width }}
            />
          );
        })}

      {/* M1‑M12 – po 1 kolumnie */}
      {Array.isArray(dashedLines.M) &&
        dashedLines.M.length === 12 &&
        dashedLines.M.map((val, idx) => {
          if (typeof val !== 'number' || val <= 0 || val >= mSpot) return null;
          const left = `${(idx / 12) * 100}%`;
          const width = `${100 / 12}%`;
          return (
            <div
              key={`dashed-M${idx + 1}`}
              className={styles.customHLine}
              style={{ bottom: `${y + q + val}%`, left, width }}
            />
          );
        })}
    </>
  );
}
