import React, {useEffect, useMemo, useState} from 'react';
import { <PERSON><PERSON>, Card } from '@mantine/core';
import StrategyHeader from './StrategyHeader/StrategyHeader';
import styles from './StrategyChart.module.css';
import StrategyCoreTable from '@/components/broker-dashboard/details/PurchaseStrategyTable/StrategyCoreTable/StrategyCoreTable';
import FullSimulation from '@/components/broker-dashboard/details/PurchaseStrategyTable/StrategyCoreTable/SimulationCreate/FullSimulation/FullSimulation';
import { useDeleteSimulation } from '@/components/broker-dashboard/details/PurchaseStrategyTable/StrategyCoreTable/SimulationCreate/useDeleteSimulation';
import StrategyControls from '@/components/broker-dashboard/details/PurchaseStrategyTable/StrategyInput/StrategyControls/StrategyControls';
import { NotificationType } from '@/types/Common';
import { notify } from '@/utils/notify';
import AnalyticTable from "@/components/broker-dashboard/details/AnalyticTable/AnalyticTable";
import {
  useUpdateStrategies
} from "@/components/broker-dashboard/details/PurchaseStrategyTable/StrategyInput/useUpdateStrategies";

interface Wallet {
  walletId: string;
  tranches: Array<{
    timeUnit: string;
    size: number;
    recommendation?: boolean;
    virtual?: boolean;
  }>;
  analytical?: any;
  title?: string;
  year?: string;
  strategyY?: number;
  strategyQ?: number;
  strategyM?: number;
}

interface StrategyChartProps {
  wallet: Wallet;
  onRefetch: () => void;
}

const StrategyChart: React.FC<StrategyChartProps> = ({ wallet, onRefetch }) => {

  // Stan strategii: ile % idzie w M/SPOT, Q i Y
  const initialStrategies = useMemo(() => wallet.analytical?.strategies ?? {}, [wallet]);
  const [mSpot, setMSpot] = useState(Number(initialStrategies.M) || 0);
  const [q, setQ] = useState(Number(initialStrategies.Q) || 0);
  const [y, setY] = useState(Number(initialStrategies.Y) || 0);

  // Tryb edycji oraz widoczność tabeli symulacji
  const [editMode, setEditMode] = useState(false);
  const [simulationVisible, setSimulationVisible] = useState(false);
  const { updateStrategies } = useUpdateStrategies(wallet.walletId, onRefetch);

  const strategy = { strategyY: y, strategyQ: q, strategyM: mSpot };
  const { handleDeleteSimulation } = useDeleteSimulation(wallet, onRefetch);

  // Przerywane linie VWAP - póki co sztywniutko lecimy docelowo backend musi zwracać dashedLines
  const dashedLines = wallet.analytical?.dashedLines ?? { Y: 20, Q: 8, M: 15 };

  // Mapa kolorów: 12 kolumn (M1..M12) x 100 wierszy
  const [colorMap, setColorMap] = useState(
    Array(12)
      .fill(null)
      .map(() => Array(100).fill('transparent'))
  );

  // Funkcja generująca etykiety osi Y co 10% (od 0 do 100)
  const generateYScale = () => {
    const scale = [];
    for (let i = 0; i <= 100; i += 10) {
      scale.push(i);
    }
    return scale;
  };

  // Generujemy mapę kolorów – rozdzielamy transze normalne od rekomendowanych
  useEffect(() => {
    // Przygotuj pustą mapę (12 kolumn x 100 wierszy)
    const newMap = Array(12)
      .fill(null)
      .map(() => Array(100).fill('transparent'));

    // Wylicz granice segmentów na osi Y:
    //  0..yBoundary => Y,
    //  yBoundary..qBoundary => Q,
    //  qBoundary..mBoundary => M/SPOT
    const yBoundary = y;
    const qBoundary = y + q;
    const mBoundary = y + q + mSpot; // docelowo = 100

    // Kolory dla transz normalnych:
    const yColor = '#a9a9a9';
    const blue = 'rgb(32,115,223)';
    const green = 'rgb(22,197,104)';
    const red = 'rgb(229,26,26)';
    const orange = 'rgb(228,171,0)';

    // Kolory dla rekomendowanych – z większą transparentnością:
    const yRecColor = 'rgba(189,187,187,0.49)';
    const blueRec = 'rgba(101,163,245,0.49)';
    const greenRec = 'rgba(142,236,186,0.49)';
    const redRec = 'rgba(243,165,165,0.49)';
    const orangeRec = 'rgba(238,221,173,0.49)';

    // Kolor dla virtual
    const virtualColor = 'rgba(239,119,9,0.36)';

    // Funkcja pomocnicza do wypełniania komórek
    const fillCells = (
      colFrom: number,
      colTo: number,
      rowStart: number,
      fillCount: number,
      color: string
    ) => {
      for (let col = colFrom; col <= colTo; col++) {
        for (let row = rowStart; row < rowStart + fillCount; row++) {
          if (row >= 0 && row < 100) {
            newMap[col][row] = color;
          }
        }
      }
    };

    // Sumy transz – osobno dla normalnych, rekomendowanych i wirtualnych
    const normalSums: Record<string, number> = {};
    const recSums: Record<string, number> = {};
    const virtSums: Record<string, number> = {};

    wallet.tranches.forEach((tranche) => {
      const { timeUnit, size, recommendation, virtual } = tranche;
      const fill = Math.round(size);
      if (virtual) {
        virtSums[timeUnit] = (virtSums[timeUnit] || 0) + fill;
      } else if (recommendation) {
        recSums[timeUnit] = (recSums[timeUnit] || 0) + fill;
      } else {
        normalSums[timeUnit] = (normalSums[timeUnit] || 0) + fill;
      }
    });

    // Najpierw wypełniamy normal + rec, na końcu virtual
    // SEGMENT Y
    const normalYFill = normalSums['Y'] ? Math.min(normalSums['Y'], yBoundary) : 0;
    const recYFill = recSums['Y'] ? Math.min(recSums['Y'], yBoundary - normalYFill) : 0;
    const virtYFill = virtSums['Y']
      ? Math.min(virtSums['Y'], yBoundary - normalYFill - recYFill)
      : 0;

    // normal
    fillCells(0, 11, 0, normalYFill, yColor);
    // rec
    fillCells(0, 11, normalYFill, recYFill, yRecColor);
    // virtual
    fillCells(0, 11, normalYFill + recYFill, virtYFill, virtualColor);

    // Segmenty Q – Q1: kolumny 0-2, Q2: 3-5, Q3: 6-8, Q4: 9-11
    const Qs = ['Q1', 'Q2', 'Q3', 'Q4'];
    const colStart = { Q1: 0, Q2: 3, Q3: 6, Q4: 9 };
    const baseColors = { Q1: blue, Q2: green, Q3: red, Q4: orange };
    const recColors = { Q1: blueRec, Q2: greenRec, Q3: redRec, Q4: orangeRec };

    Qs.forEach((qKey) => {
      const normalQFill = normalSums[qKey] ? Math.min(normalSums[qKey], q) : 0;
      const recQFill = recSums[qKey] ? Math.min(recSums[qKey], q - normalQFill) : 0;
      const virtQFill = virtSums[qKey] ? Math.min(virtSums[qKey], q - normalQFill - recQFill) : 0;

      fillCells(colStart[qKey], colStart[qKey] + 2, yBoundary, normalQFill, baseColors[qKey]);
      fillCells(
        colStart[qKey],
        colStart[qKey] + 2,
        yBoundary + normalQFill,
        recQFill,
        recColors[qKey]
      );
      fillCells(
        colStart[qKey],
        colStart[qKey] + 2,
        yBoundary + normalQFill + recQFill,
        virtQFill,
        virtualColor
      );
    });

    // Segment M/SPOT (12 kolumn M1..M12)
    for (let i = 0; i < 12; i++) {
      const key = `M${i + 1}`;
      const normalMFill = normalSums[key] ? Math.min(normalSums[key], mSpot) : 0;
      const recMFill = recSums[key] ? Math.min(recSums[key], mSpot - normalMFill) : 0;
      const virtMFill = virtSums[key] ? Math.min(virtSums[key], mSpot - normalMFill - recMFill) : 0;

      // Podstawowe kolory kolumn M
      let baseColor = orange;
      let baseRec = orangeRec;
      if (i < 3) {
        baseColor = blue;
        baseRec = blueRec;
      } else if (i < 6) {
        baseColor = green;
        baseRec = greenRec;
      } else if (i < 9) {
        baseColor = red;
        baseRec = redRec;
      }

      fillCells(i, i, y + q, normalMFill, baseColor);
      fillCells(i, i, y + q + normalMFill, recMFill, baseRec);
      // virtual na wierzch
      fillCells(i, i, y + q + normalMFill + recMFill, virtMFill, virtualColor);
    }

    // Zapisz zaktualizowaną mapę kolorów
    setColorMap(newMap);
  }, [wallet, mSpot, q, y]);

  // Wyliczamy sums do overlay/tooltipów –  normalnych i rekomendowanych i wirtualnych
  const computeSums = () => {
    const normalSums: Record<string, number> = {};
    const recSums: Record<string, number> = {};
    const virtSums: Record<string, number> = {};

    wallet.tranches.forEach((tranche) => {
      const { timeUnit, size, recommendation, virtual } = tranche;
      const fill = Math.round(size);
      if (virtual) {
        virtSums[timeUnit] = (virtSums[timeUnit] || 0) + fill;
      } else if (recommendation) {
        recSums[timeUnit] = (recSums[timeUnit] || 0) + fill;
      } else {
        normalSums[timeUnit] = (normalSums[timeUnit] || 0) + fill;
      }
    });

    return { normalSums, recSums, virtSums };
  };

  const { normalSums, recSums, virtSums } = computeSums();

  // Segment Y – wartości dla overlay’ów
  const fillYNormal = normalSums['Y'] ? Math.min(normalSums['Y'], y) : 0;
  const fillYRec = recSums['Y'] ? Math.min(recSums['Y'], y - fillYNormal) : 0;
  const fillYVirt = virtSums['Y'] ? Math.min(virtSums['Y'], y - fillYNormal - fillYRec) : 0;

  // Segmenty Q – tworzymy tablicę z obiektami dla każdej grupy (Q1..Q4)
  const fillQGroups = ['Q1', 'Q2', 'Q3', 'Q4'].map((group) => {
    const normalFill = normalSums[group] ? Math.min(normalSums[group], q) : 0;
    const recFill = recSums[group] ? Math.min(recSums[group], q - normalFill) : 0;
    const virtFill = virtSums[group] ? Math.min(virtSums[group], q - normalFill - recFill) : 0;
    return { group, normalFill, recFill, virtFill };
  });

  // Segment M – dla każdej kolumny M1..M12
  const fillM = Array(12)
    .fill(0)
    .map((_, i) => {
      const key = `M${i + 1}`;
      const normalFill = normalSums[key] ? Math.min(normalSums[key], mSpot) : 0;
      const recFill = recSums[key] ? Math.min(recSums[key], mSpot - normalFill) : 0;
      const virtFill = virtSums[key] ? Math.min(virtSums[key], mSpot - normalFill - recFill) : 0;
      return { normal: normalFill, rec: recFill, virt: virtFill };
    });

  const handleEditClick = () => {
    setEditMode(true);
    setSimulationVisible(false);
  };

  const handleSimulateClick = () => {
    if (mSpot + q + y !== 100) {
      notify(NotificationType.INFO, 'Suma wartości M/SPOT, Q i Y musi wynosić 100!');
      return;
    }
    setSimulationVisible(true);
  };

  const handleSaveClick = async () => {
    if (mSpot + q + y !== 100) {
      notify(NotificationType.INFO, 'Suma wartości M/SPOT, Q i Y musi wynosić 100!');
      return;
    }
    await updateStrategies(mSpot, q, y);
    setEditMode(false);
  };

  return (
    <Card shadow="sm" p="lg">
      <StrategyControls
        mSpot={mSpot}
        q={q}
        y={y}
        editMode={editMode}
        onMSpotChange={setMSpot}
        onQChange={setQ}
        onYChange={setY}
        onEdit={handleEditClick}
        onSimulate={handleSimulateClick}
        onSave={handleSaveClick}
      />

      {(simulationVisible || !editMode) && mSpot + q + y === 100 && (
        <>
          <div className={styles.buttonWrapper}>
            <FullSimulation wallet={wallet} onSimulationComplete={onRefetch} strategy={strategy} />
            <Button className={styles.deleteButton} onClick={handleDeleteSimulation}>
              Usuń symulację!
            </Button>
          </div>
          <StrategyHeader wallet={wallet} />
          <StrategyCoreTable
            generateYScale={generateYScale}
            y={y}
            q={q}
            mSpot={mSpot}
            colorMap={colorMap}
            fillYNormal={fillYNormal}
            fillYRec={fillYRec}
            fillYVirt={fillYVirt}
            fillQGroups={fillQGroups}
            fillM={fillM}
            wallet={wallet}
            onRefetch={onRefetch}
            dashedLines={dashedLines}
          />
          <AnalyticTable wallet={wallet.analytical} />
        </>
      )}
    </Card>
  );
};

export default StrategyChart;
