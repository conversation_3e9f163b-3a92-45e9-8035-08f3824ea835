import React from 'react';
import dayjs from 'dayjs';
import styles from './StrategyHeader.module.css';
import { ContractType } from '@/types/Agreement';

const DEFAULT_MONTHS = Array.from({ length: 12 }, (_, i) => ({
  label: `M${i + 1}`,
  endDate: '',
  daysToPurchase: '',
}));

const formatDate = (dateStr: string) => {
  const d = dayjs(dateStr);
  return d.isValid() ? d.format('DD.MM.YYYY') : '-';
};

const getMonthNumber = (name: string) => {
  const match = name.match(/(?:^|_)M-(\d{1,2})-/);
  return match ? parseInt(match[1], 10) : null;
};

type Props = {
  wallet: any;
};

const StrategyHeader: React.FC<Props> = ({ wallet }) => {
  const mediaType = wallet?.agreement?.mediaType;

  const contractsM =
    wallet?.agreement?.contracts?.filter(
      (c: any) => c.contractType === ContractType.M && (!mediaType || c.media === mediaType)
    ) || [];

  const computedMonths = DEFAULT_MONTHS.map((month) => {
    const monthIndex = parseInt(month.label.slice(1), 10);

    const matchingContract = contractsM.find(
      (contract: any) => getMonthNumber(contract.name) === monthIndex
    );

    return matchingContract
      ? {
          ...month,
          endDate: matchingContract.averageCalculation.endDate,
          daysToPurchase: matchingContract.daysToPurchase,
        }
      : month;
  });

  return (
    <div className={styles.topTableWrapper}>
      <table className={styles.topTable}>
        <thead>
          <tr>
            {/* 1. month labels */}
            <th className={styles.empty}></th>
            {computedMonths.map((m) => (
              <th key={m.label} className={styles.monthHeader}>
                {m.label}
              </th>
            ))}
            <th className={styles.extraColumn}></th>
          </tr>
          <tr>
            {/* 2. end date (data graniczna) */}
            <th className={styles.daysToPurchase}>Data graniczna</th>
            {computedMonths.map((m) => (
              <th key={m.label} className={styles.monthDateRange}>
                <span className={styles.endDate}>{formatDate(m.endDate)}</span>
              </th>
            ))}
            <th className={styles.extraColumn}></th>
          </tr>
          <tr>
            {/* 3. days to purchase (dni do zakupu) */}
            <th className={styles.daysToPurchase}>Dni do zakupu</th>
            {computedMonths.map((m) => (
              <th key={m.label} className={styles.monthDateRange}>
                {m.daysToPurchase === 0 ? (
                  <span className={styles.tradeEnded}>
                    koniec <br />
                    handlu
                  </span>
                ) : m.daysToPurchase !== undefined &&
                  m.daysToPurchase !== null &&
                  m.daysToPurchase !== '' ? (
                  <span className={styles.purchaseDays}>{m.daysToPurchase}</span>
                ) : (
                  <span className={styles.purchaseDays}>-</span>
                )}
              </th>
            ))}
            <th className={styles.extraColumn}></th>
          </tr>
        </thead>
      </table>
    </div>
  );
};

export default StrategyHeader;
