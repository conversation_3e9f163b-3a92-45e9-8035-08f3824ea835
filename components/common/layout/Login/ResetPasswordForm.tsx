import { Button, Container, Group, Paper, PasswordInput, Title } from '@mantine/core';
import { useForm } from '@mantine/form';

const ResetPasswordForm = () => {
  const form = useForm({
    initialValues: {
      newPassword: '',
      confirmPassword: '',
    },
    validate: {
      confirmPassword: (value, values) =>
        value !== values.newPassword ? 'Hasła muszą się zgadzać' : null,
    },
  });

  const handleSubmit = (values: typeof form.values) => {
    alert('Not implemented');
    // logika
  };

  return (
    <Container
      size={800}
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        padding: 0,
      }}
    >
      <Paper radius="md" p="xl" withBorder style={{ width: '100%' }}>
        <Title order={2} mb="xl">
          Zapomniałeś hasło i chcesz zresetować?
        </Title>
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <PasswordInput
            label="Nowe hasło"
            required
            mt="md"
            {...form.getInputProps('newPassword')}
          />
          <PasswordInput
            label="Wpisz jeszcze raz nowe hasło"
            required
            mt="md"
            {...form.getInputProps('confirmPassword')}
          />
          <Group mt="xl">
            <Button type="submit">Wyślij!</Button>
          </Group>
        </form>
      </Paper>
    </Container>
  );
};

export default ResetPasswordForm;
