import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { FullScreenLoader } from './FullScreenLoader';
import { LoginService } from '@/services/auth/AuthService';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import useErrorHandler from '@/components/common/hooks/useErrorHandler';

const AuthCallbackHandler = () => {
  const router = useRouter();
  const { decorateWithErrorHandling } = useErrorHandler();

  useEffect(() => {
    if (!router.isReady) return;

    const { code } = router.query;
    if (!code || Array.isArray(code)) return;

    decorateWithErrorHandling(async () => {
      await LoginService.exchangeCode(code as string);
      router.replace(RouterPaths.WALLET_LIST);
    }).catch(() => {
      router.replace('/login');
    });
  }, [router.isReady]);

  return <FullScreenLoader label="Trwa logowanie…" />;
};

export default AuthCallbackHandler;
