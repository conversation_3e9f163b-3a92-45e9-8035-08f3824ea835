import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { showNotification } from '@mantine/notifications';
import { FullScreenLoader } from './FullScreenLoader';
import { LoginService } from '@/services/auth/AuthService';

const AuthCallbackHandler = () => {
  const router = useRouter();

  useEffect(() => {
    if (!router.isReady) return;

    const { code } = router.query;
    if (!code || Array.isArray(code)) return;

    (async () => {
      try {
        await LoginService.exchangeCode(code as string);
        console.log('poszło');
      } catch (err) {
        console.error('Błąd wymiany kodu', err);
        showNotification({
          title: 'Błąd logowania',
          message: 'Nie udało się zalogować użytkownika.',
          color: 'red',
        });
        router.replace('/login');
      }
    })();
  }, [router.isReady]);

  return <FullScreenLoader label="Logowanie trwa…" />;
};

export default AuthCallbackHandler;
