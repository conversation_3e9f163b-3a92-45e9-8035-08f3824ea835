import { Button, Checkbox, Container, Paper, PasswordInput, TextInput, Title } from '@mantine/core';
import { useForm } from '@mantine/form';
import { useRouter } from 'next/router';
import { LoginService } from '@/services/auth/AuthService';
import { SessionStorageService } from '@/services/auth/SessionStorage';
import { showNotification } from '@mantine/notifications';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import classes from './LoginForm.module.css';
import { AuthService } from '@/services/auth/RoleService';
import { UserRole } from '@/types/UserRole';

const LoginForm = () => {
  const form = useForm({
    initialValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  });

  const router = useRouter();

  const handleSubmit = async (values) => {
    try {
      const { accessToken, refreshToken } = await LoginService.login(
        values.username,
        values.password
      );

      const realmRoles = AuthService.getRealmRoles(accessToken);
      const BLOCKED_ROLES = [UserRole.EXTERNAL_CLIENT, UserRole.ES_ADMINISTRATION];

      const hasBlockedRole = realmRoles.some((role) => BLOCKED_ROLES.includes(role as UserRole));

      if (hasBlockedRole) {
        SessionStorageService.clearTokens();
        showNotification({
          title: 'Brak dostępu',
          message: 'Nie masz uprawnień do logowania do tej aplikacji.',
          color: 'red',
        });
        return;
      }

      SessionStorageService.setTokens(accessToken, refreshToken);
      await router.push(RouterPaths.WALLET_LIST);
    } catch (error) {
      console.error('Błąd logowania', error);
      const errorMessage =
        error.response?.data?.error_description || 'Wystąpił problem z logowaniem';
      showNotification({
        title: 'Błąd logowania',
        message: errorMessage,
        color: 'red',
      });
    }
  };
  return (
    <Container className={classes.container}>
      <div className={classes.mainBox}>
        <Paper p="xl" className={classes.loginFormSide}>
          <div className={classes.logoContainer}>
            <img src="/logo_round.png" alt="Energy Solution Logo" />
          </div>
          <Title order={2} mb="xl">
            Panel Administracyjny
          </Title>
          <form className={classes.formContainer} onSubmit={form.onSubmit(handleSubmit)}>
            <TextInput
              placeholder="Twój adres e-mail"
              label="E-mail"
              required
              {...form.getInputProps('username')}
            />
            <PasswordInput
              placeholder="Wpisz hasło"
              label="Hasło"
              required
              mt="md"
              {...form.getInputProps('password')}
            />
            <div className={classes.linkContainer}>
              <Checkbox
                color={'#fa850e'}
                label="Zapamiętaj mnie"
                {...form.getInputProps('rememberMe', { type: 'checkbox' })}
              />
              <a style={{ color: '#4d4d4d' }} href="/forgot-password">
                Nie pamiętasz hasła?
              </a>
            </div>
            <Button color="#fa850e" type="submit" fullWidth mt="xl">
              Zaloguj się
            </Button>
          </form>
          <a style={{ color: '#4d4d4d' }} href="https://customer.wallet.dev.es-t.pl/login">
            Zaloguj się do aplikacji klienckiej
          </a>
        </Paper>
        <Paper className={classes.sideImageContainer}>
          <img src="/login_bg.png" alt="Energy Solution Logo" />
        </Paper>
      </div>
    </Container>
  );
};

export default LoginForm;
