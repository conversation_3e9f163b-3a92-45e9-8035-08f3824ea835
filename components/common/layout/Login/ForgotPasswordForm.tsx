import {<PERSON><PERSON>, <PERSON><PERSON>, Container, Group, Paper, Text, TextInput, Title} from '@mantine/core';
import {useForm} from '@mantine/form';
import {useState} from "react";
import {NotificationType} from "@/types/Common";
import {notify} from "@/utils/notify";
import {LoginService} from "@/services/auth/AuthService";
import {SchemaForgotPassword} from "@/components/common/layout/Login/schema";
import {zodResolver} from "mantine-form-zod-resolver";
import { z } from 'zod';


type ForgotPasswordFormValues = z.infer<typeof SchemaForgotPassword>;

const ForgotPasswordForm = () => {
    const [loading, setLoading] = useState(false);
    const form = useForm<ForgotPasswordFormValues>({
        initialValues: {
            email: '',
        },
        validate: zodResolver(SchemaForgotPassword),
    });


    const handleError = (error: any) => {
        console.error('Error:', error);
        const errorMessage = error;
        notify(NotificationType.ERROR, errorMessage);
    };

    const handleSubmit = async (values: typeof form.values) => {
        setLoading(true);
        try {
            const response = await LoginService.resetPassword(values.email);
            if (response.success) {
                notify(NotificationType.SUCCESS, response.message);
            } else {
                notify(NotificationType.ERROR, response.error);
            }
        } catch (error) {
            handleError(error.response?.data?.message);
        } finally {
            setLoading(false);
        }
    };


    return (
        <Container
            size={800}
            style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', padding: 0 }}
        >
            <Paper radius="md" p="xl" withBorder style={{ width: '100%' }}>
                <Title order={2} mb="xl">
                    Zapomniałeś hasło i chcesz zresetować?
                </Title>
                <Text mb="md">
                    Wpisz swój adres e-mail, a wyślemy na niego instrukcję resetowania hasła:
                </Text>
                <form onSubmit={form.onSubmit(handleSubmit)}>
                    <TextInput
                        label="Adres e-mail"
                        placeholder="<EMAIL>"
                        required
                        {...form.getInputProps('email')}
                    />
                    <Group mt="xl">
                        <Button type="submit" fullWidth loading={loading}>
                            Wyślij
                        </Button>
                    </Group>
                </form>
                <Text mt="xl">
                    <Anchor href="/login">
                        Powrót do menu logowania
                    </Anchor>
                </Text>
            </Paper>
        </Container>
    );
};

export default ForgotPasswordForm;