import React, { useEffect, useState } from 'react';
import { Select } from '@mantine/core';
import { TokenUtils } from '@/services/auth/TokenUtils';
import { UserService } from '@/services/UserService';
import { NotificationType } from '@/types/Common';
import { notify } from '@/utils/notify';
import {SessionStorageService} from "@/services/auth/SessionStorageService";

export interface SwitchTenantResponse {
  success: boolean;
  data?: {
    access_token: string;
    refresh_token: string;
  };
  error?: string;
}

export const TenantSelector: React.FC = () => {
  const [tenants, setTenants] = useState<{ value: string; label: string }[]>([]);
  const [currentTenant, setCurrentTenant] = useState<string>('');
  const [isVisible, setIsVisible] = useState<boolean>(false);

  const initializeTenants = () => {
    const whiteList = TokenUtils.getWhiteListTenants();
    const currentTenantId = TokenUtils.getTenantId();

    if (whiteList && whiteList.length > 0) {
      const tenantOptions = whiteList.map((tenant) => ({
        value: tenant.uuid,
        label: tenant.name,
      }));
      setTenants(tenantOptions);
      setIsVisible(true);

      if (currentTenantId) {
        setCurrentTenant(currentTenantId);
      }
    } else {
      setIsVisible(false);
    }
  };

  const handleTenantChange = async (newTenantId: string) => {
    try {
      const response: SwitchTenantResponse = await UserService.switchTenant(newTenantId);

      if (response.success && response.data) {
        SessionStorageService.setTokens(response.data.access_token, response.data.refresh_token);
        setCurrentTenant(newTenantId);
        window.location.reload();
      } else {
        throw new Error(response.error || "Nie udało się zmienić tenanta");
      }
    } catch (error) {
      console.error("Błąd podczas zmiany tenanta:", error);
      notify(NotificationType.ERROR, error.message || "Nie udało się zmienić tenanta");
    }
  };

  useEffect(() => {
    initializeTenants();
  }, []);

  if (!isVisible) {
    return null;
  }

  return (
    <Select
      placeholder="Wybierz klienta"
      data={tenants}
      value={currentTenant}
      onChange={(value) => handleTenantChange(value!)}
      clearable={false}
      searchable
    />
  );
};
