import React, { useState } from "react";
import { IconChevronDown, IconChevronUp, IconChevronsDown, IconChevronsUp, } from '@tabler/icons-react';
import { Tooltip } from '@mantine/core';
import styles from './Accordion.module.css';

type AccordionProps = {
    items: {
        title: string;
        content: string | JSX.Element;
    }[],
    expandAllVisibility?: boolean;
}

const Accordion = ({ items, expandAllVisibility = false }: AccordionProps) => {
    const [openIndices, setOpenIndices] = useState([]);

    const toggleItem = (index: number) => {
        setOpenIndices((prev) =>
            prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
        );
    };
    ;

    const expandAll = () => {
        setOpenIndices(items.map((_, index) => index));
    };

    const collapseAll = () => {
        setOpenIndices([]);
    };

    return (
        <div>
            {expandAllVisibility &&
                <div className={styles.expandToggle}>
                    <div onClick={expandAll}><Tooltip label="Rozwiń wszystko"><IconChevronsDown size={16} /></Tooltip></div>
                    <div onClick={collapseAll}><Tooltip label="Zwiń wszystko"><IconChevronsUp size={16} /></Tooltip></div>
                </div>}
            <div>
                {items.map(({ title, content }, index) => (
                    <div key={index} style={{ marginBottom: "10px" }}>
                        <div
                            className={styles.questionBox}
                            onClick={() => toggleItem(index)}
                        >
                            <span className={openIndices.includes(index) ? styles.selectedQuestion : null}> {title}</span>
                            <span>{openIndices.includes(index) ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}</span>
                        </div>
                        {openIndices.includes(index) && (
                            <div
                                className={styles.answerBox}
                            >
                                {content}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div >
    );
};

export default Accordion;
