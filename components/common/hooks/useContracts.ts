import { useState } from 'react';
import { ContractService } from '@/services/ContractService';
import { notify } from '@/utils/notify';
import { NotificationType } from '@/types/Common';
import { WalletService } from '@/services/WalletService';
import { TimeUnit } from '@/types/Wallet';

/**
 * Custom hook for managing contracts.
 *
 * This hook handles the state for contract lists and provides methods
 * to fetch contracts and determine agreement IDs from wallet or agreement data.
 *
 * @returns {Object} An object containing:
 *  - {Array<Object>} contractList - The current list of contract objects.
 *  - {Function} setContractList - Function to update the contract list state.
 *  - {Function} loadContracts - Function to load contracts for a given agreement.
 *  - {Function} getAgreementId - Function to get the agreement ID from wallet and agreement IDs.
 *  - {Function} loadSimulationContracts - Function to load and filter simulation contracts.
 */
export default function useContracts() {
  const [contractList, setContractList] = useState([]);
  const PMOZE = 'PMOZE_A';

  /**
   * Retrieves the agreement ID from wallet and agreement IDs.
   *
   * It first attempts to fetch the agreement ID using the first wallet ID (if provided).
   * Then, if any agreement IDs are provided, it will override the previous value with the first agreement ID.
   *
   * @async
   * @param {string[]} walletIds - Array of wallet IDs.
   * @param {string[]} agreementIds - Array of agreement IDs.
   * @returns {Promise<string>} The agreement ID.
   */
  async function getAgreementId(walletIds: string[], agreementIds: string[]): Promise<string> {
    let agreementId = null;
    if (walletIds && walletIds.length > 0) {
      const wallet = await WalletService.getWallet(walletIds[0]);
      agreementId = wallet.agreement.id;
    }
    if (agreementIds && agreementIds.length > 0) {
      agreementId = agreementIds[0];
    }
    return agreementId;
  }

  /**
   * Loads contracts for a given agreement.
   *
   * This function fetches contracts from the ContractService based on the provided agreement ID.
   * The returned contracts are mapped into objects with `value` and `label` properties.
   *
   * @async
   * @param {string} id - The agreement ID.
   * @param {boolean} [applyFilter=false] - Optional flag to apply additional filtering.
   * @returns {Promise<Array<{value: string, label: string}>>} An array of contract objects.
   * @param {boolean} [valueAsContractName=false] - Returns contract name for both result fields (value/label)
   */
  async function loadContracts(
    id: string,
    applyFilter: boolean = false,
    valueAsContractName: boolean = false
  ) {
    let contracts = [];
    try {
      const response = await ContractService.getContractsByAgreement(id, applyFilter);
      contracts = response.content.map((contract) => ({
        value: valueAsContractName ? contract.name : contract.id,
        label: contract.name,
        timeUnit: contract.timeUnit,
      }));
      setContractList(contracts);
    } catch (error) {
      notify(NotificationType.ERROR, 'Nie udało się pobrać listy kontraktów.');
    }
    return contracts;
  }

  /**
   * Loads simulation contracts and applies a specific filter.
   *
   * This function loads contracts using `loadContracts` and then filters out contracts whose names
   * contain the "PMOZE_A" prefix. If any contracts were filtered out (indicating the presence of green certificates),
   * it adds a simulation contract with the value and label set to "PMOZE_A".
   *
   * @async
   * @param {string} id - The agreement ID.
   * @param {boolean} [applyFilter=false] - Optional flag to apply additional filtering when fetching contracts.
   * @param {boolean} [valueAsContractName=false] - Returns contract name for both result fields (value/label)
   * @returns {Promise<void>}
   */
  async function loadSimulationContracts(
    id: string,
    applyFilter: boolean = false,
    valueAsContractName: boolean = false
  ) {
    const contracts = await loadContracts(id, applyFilter, valueAsContractName);
    const contractsLeft = contracts.filter((contract) => contract.label.indexOf(PMOZE) === -1);
    const hasGreenCertificates = contractsLeft.length !== contractList.length;
    if (hasGreenCertificates) {
      contractsLeft.push({ value: PMOZE, label: PMOZE, timeUnit: TimeUnit.Y });
    }
    setContractList(contractsLeft);
  }

  return { contractList, setContractList, loadContracts, getAgreementId, loadSimulationContracts };
}
