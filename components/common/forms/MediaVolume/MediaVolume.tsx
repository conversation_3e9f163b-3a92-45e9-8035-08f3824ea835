import { Select } from '@mantine/core';
import { Controller, useFormContext } from 'react-hook-form';
import { getNestedValue } from '@/utils/get-nested-value';
import { useEffect, useState } from 'react';
import { MediaVolumeType, PurchaseModel } from '@/types/Agreement';
import TextInput from '@/components/common/forms/TextInput/TextInput';

// wielokrotność x% maksymalnie y% / wielokrotność x MW max. y MW / x kWh/h
const MediaVolume: React.FC<Partial<BaseInputProps>> = ({
  name,
  label,
  placeholder,
  withAsterisk,
  disabled = false,
}) => {
  const [options, setOptions] = useState([]);
  const {
    control,
    formState: { errors },
    watch,
  } = useFormContext();
  const fieldValue = watch(name);
  const purchaseModel = watch('media.purchaseModel') || watch('purchaseModel');
  const allOptions = [
    {
      label: 'Wielokrotność x % maksymalnie y %',
      value: MediaVolumeType.MULTIPLE,
      model: PurchaseModel.PERCENTAGE,
    },
    {
      label: 'Minimalnie X% maksymalnie Y%',
      value: MediaVolumeType.MIN_MAX,
      model: PurchaseModel.PERCENTAGE,
    },
    {
      label: 'wielokrotność x MW maksymalnie y MW',
      value: MediaVolumeType.MULTIPLE,
      model: PurchaseModel.VOLUME,
    },
    {
      label: 'x kWh/h',
      value: MediaVolumeType.FIXED,
      model: PurchaseModel.VOLUME,
    },
  ];
  useEffect(() => {
    if (purchaseModel) {
      setOptions(allOptions.slice().filter((e) => e.model === purchaseModel));
    }
  }, [purchaseModel]);
  const renderPercentageComponent = [MediaVolumeType.MULTIPLE, MediaVolumeType.MIN_MAX].includes(
    fieldValue?.type
  );
  const renderFixedComponent = [MediaVolumeType.FIXED].includes(fieldValue?.type);
  return (
    <Controller
      name={`${name}.type`}
      control={control}
      render={({ field }) => (
        <>
          <Select
            {...field}
            withAsterisk={withAsterisk}
            style={{ width: 500 }}
            label={label}
            data={options}
            placeholder={placeholder}
            disabled={disabled}
            onChange={(value) => field.onChange(value)}
            value={field.value}
            error={getNestedValue(errors, `${name}.type`)?.message}
          />
          {renderPercentageComponent && (
            <TextInput
              style={{ width: 500 }}
              name={`${name}.value`}
              label="Wartość x-y"
              disabled={disabled}
              withAsterisk={true}
            />
          )}
          {renderFixedComponent && (
            <TextInput
              style={{ width: 500 }}
              name={`${name}.value`}
              label="Wartość x"
              disabled={disabled}
              withAsterisk={true}
            />
          )}
        </>
      )}
    />
  );
};

export default MediaVolume;
