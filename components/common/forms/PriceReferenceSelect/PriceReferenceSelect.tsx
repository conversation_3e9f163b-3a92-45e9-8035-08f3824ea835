import React, { useEffect, useState } from 'react';
import { Select } from '@mantine/core';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import { ContractService } from '@/services/ContractService';
import { getNestedValue } from '@/utils/get-nested-value';
import useTranslation from 'next-translate/useTranslation';

const PriceReferenceSelect = ({ name, label, placeholder, disabled, withAsterisk }) => {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext();
  const selectedAgreements = useWatch({ control, name: 'contractId' });
  const selectedContract = useWatch({ control, name: 'contract' });
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (selectedAgreements?.length > 0 && selectedContract) {
      const agreementId = selectedAgreements[0];

      setLoading(true);
      ContractService.getAgreementPriceReference(agreementId, selectedContract)
        .then((response) => {
          const fetchedOptions = response.map((ref) => ({ value: ref, label: t(ref) }));
          setOptions(fetchedOptions);
          setValue(name, response[0] || '');
        })
        .catch(() => setOptions([]))
        .finally(() => setLoading(false));
    } else {
      setOptions([]);
    }
  }, [selectedAgreements, selectedContract]);

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Select
          {...field}
          label={label}
          placeholder={placeholder}
          data={options}
          disabled={disabled || loading}
          withAsterisk={withAsterisk}
          value={field.value || ''}
          error={getNestedValue(errors, name)?.message}
          onChange={(value) => {
            field.onChange(value);
            setValue(name, value);
          }}
        />
      )}
    />
  );
};

export default PriceReferenceSelect;
