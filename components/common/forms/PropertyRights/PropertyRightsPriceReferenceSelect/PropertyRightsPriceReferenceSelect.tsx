import { MultiSelect, Stack } from '@mantine/core';
import { Controller, useFormContext } from 'react-hook-form';
import { getNestedValue } from '@/utils/get-nested-value';
import TimeInput from '@/components/common/forms/TimeInput/TimeInput';
import useTranslation from 'next-translate/useTranslation';

const PropertyRightsPriceReferenceSelect: React.FC<Partial<BaseInputProps>> = ({
  name,
  label,
  placeholder,
  withAsterisk,
  disabled = false,
}) => {
  const { t } = useTranslation('common');
  const {
    control,
    formState: { errors },
    watch,
    unregister,
  } = useFormContext();
  const priceReferenceValue = watch(name) || [];
  const options = [
    { value: 'DKR', label: 'DKR' },
    { value: 'TARGET_PRICE', label: 'Target Price' },
    { value: 'YESTERDAY', label: 'n-1' },
  ];

  let children = [];
  if (Array.isArray(priceReferenceValue)) {
    children = priceReferenceValue.map((item, i) => (
      <Stack key={i}>
        <TimeInput
          name={`propertyRights.orderTime.${item}.start`}
          label={`Godzina zlecenia od ${t(item)}`}
          placeholder="Godzina od"
          withAsterisk={false}
          disabled={disabled}
        />
        <TimeInput
          name={`propertyRights.orderTime.${item}.end`}
          label={`Godzina zlecenia do ${t(item)}`}
          placeholder="Godzina do"
          withAsterisk
          disabled={disabled}
        />
      </Stack>
    ));
  }
  const onChange = (field: any, value: any) => {
    const allPriceReferenceOptions = options.map((e) => e.value);
    const onChangeValue = Array.isArray(value) ? value : [value];
    const toUnregister = allPriceReferenceOptions.filter((item) => onChangeValue.indexOf(item) < 0);
    toUnregister.map((item) => unregister(`media.orderTime.${item}`));
    field.onChange(onChangeValue);
  };
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <>
          <MultiSelect
            {...field}
            withAsterisk={withAsterisk}
            style={{ width: 500 }}
            label={label}
            data={options}
            placeholder={placeholder}
            disabled={disabled}
            onChange={(value) => onChange(field, value)}
            value={Array.isArray(field.value) ? field.value : []}
            error={getNestedValue(errors, name)?.message}
          />
          {children}
        </>
      )}
    />
  );
};

export default PropertyRightsPriceReferenceSelect;
