import { Button, ButtonProps } from '@mantine/core';
import { useState } from 'react';

type SafeButtonProps = ButtonProps & {
  onSafeClick?: () => Promise<void> | void;
};

export function SafeButton({ onSafeClick, disabled, ...props }: SafeButtonProps) {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    if (loading || disabled) return;

    setLoading(true);
    try {
      await onSafeClick?.();
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button {...props} onClick={handleClick} loading={loading} disabled={disabled || loading} />
  );
}
