import { Button, ButtonProps } from '@mantine/core';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

type SafeSubmitButtonProps = ButtonProps & {
  onValid: (values: any) => Promise<void> | void;
};

export function SafeSubmitButton({ onValid, disabled, ...props }: SafeSubmitButtonProps) {
  const { handleSubmit } = useFormContext();
  const [loading, setLoading] = useState(false);

  const handleClick = handleSubmit(async (values) => {
    if (loading || disabled) return;

    setLoading(true);
    try {
      await onValid(values);
    } finally {
      setLoading(false);
    }
  });

  return (
    <Button {...props} onClick={handleClick} loading={loading} disabled={disabled || loading} />
  );
}
