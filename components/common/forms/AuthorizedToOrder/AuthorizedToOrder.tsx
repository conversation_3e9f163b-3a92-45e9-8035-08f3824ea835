import {TagsInput} from '@mantine/core';
import {useFormContext, Controller} from 'react-hook-form';
import {getNestedValue} from "@/utils/get-nested-value";


const AuthorizedToOrder: React.FC<Partial<BaseInputProps>> = ({
                                                                             name,
                                                                             label,
                                                                             placeholder,
                                                                             withAsterisk, disabled = false
                                                                         }) => {
    const {control, formState: {errors}, watch, setValue} = useFormContext();
    /**
     * KM,/ AM, /ED,/ JW, /JKa, /BK, MS/Klient, /DPM z pełnomocnictwem,/ Konto Cushman, /Konto KM, ED, / Konto KM, ED, AM
     */
    const allOptions: { value: string, label: string }[] = [
        {value: 'KM', label: 'KM'},
        {value: 'AM', label: 'AM'},
        {value: 'ED', label: 'ED'},
        {value: 'JW', label: 'JW'},
        {value: 'JKa', label: 'JKa'},
        {value: 'BK', label: 'BK'},
        {value: 'Klient', label: 'Klient'},
        {value: 'DPM z pełnomocnictwem', label: 'DPM z pełnomocnictwem'},
        {value: 'Konto Cushman', label: 'Konto Cushman'},
        {value: 'Konto KM, ED', label: 'Konto KM, ED'},
        {value: 'Konto KM, ED, AM', label: 'Konto KM, ED, AM',},
        {value: 'Konto KM, AM', label: 'Konto KM, AM'}
    ];
    let options = allOptions.slice()
    return (
        <Controller
            name={name}
            control={control}
            render={({field}) => (
                <TagsInput
                    {...field}
                    withAsterisk={withAsterisk}
                    label={label}
                    data={options}
                    placeholder={placeholder}
                    disabled={disabled}
                    onChange={(value) => field.onChange(value)}
                    value={field.value}
                    error={getNestedValue(errors, name)?.message}
                />
            )}
        />
    );
};

export default AuthorizedToOrder;
