import React, { useEffect, useState } from 'react';
import { Combobox, InputBase, Loader, useCombobox } from '@mantine/core';
import { useFormContext } from 'react-hook-form';
import { AgreementService } from '@/services/AgreementService';
import { AgreementGroupSelectProps } from '@/types/Agreement';
import { getNestedValue } from '@/utils/get-nested-value';

const EditableAgreementGroupSelect: React.FC<AgreementGroupSelectProps> = ({ name, label }) => {
  const { setValue, watch } = useFormContext();
  const [options, setOptions] = useState<{ value: string; label: string }[]>([]);
  const [loading, setLoading] = useState(false);
  const selectedGroup = watch(name);
  const combobox = useCombobox();

  const fetchAgreementGroups = async () => {
    setLoading(true);
    try {
      const response = await AgreementService.getAgreementGroups();
      const groups = response.content.map((group) => ({
        value: group.id,
        label: group.name,
      }));
      setOptions(groups);
    } catch (error) {
      console.error('Error fetching agreement groups:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAgreementGroups();
  }, []);

  const handleDelete = async (id: string) => {
    try {
      await AgreementService.deleteAgreementGroup(id);
      setOptions((prev) => prev.filter((item) => item.value !== id));
      if (selectedGroup?.id === id) {
        setValue(name, null);
      }
    } catch (error) {
      console.error('Error deleting group:', error);
    }
  };

  const handleOptionSelect = (value: string) => {
    const selected = options.find((opt) => opt.value === value);
    if (selected) {
      setValue(name, { id: selected.value, name: selected.label });
    } else {
      setValue(name, { id: '', name: value });
      setOptions((prev) => [...prev, { value, label: value }]);
    }
    combobox.closeDropdown();
  };

  return (
    <Combobox
      store={combobox}
      onOptionSubmit={handleOptionSelect}
      withinPortal
    >
      <Combobox.Target>
        <InputBase
          label={label}
          placeholder="Wybierz lub wpisz nową grupę"
          value={selectedGroup?.name || ''}
          onChange={(event) => {
            combobox.openDropdown();
            const value = event.currentTarget.value;
            const existing = options.find((opt) => opt.label === value);
            if (existing) {
              setValue(name, { id: existing.value, name: existing.label });
            } else {
              setValue(name, { id: '', name: value });
            }
          }}
          rightSection={loading ? <Loader size="xs" /> : null}
          error={getNestedValue(useFormContext().formState.errors, name)?.message}
          onClick={(