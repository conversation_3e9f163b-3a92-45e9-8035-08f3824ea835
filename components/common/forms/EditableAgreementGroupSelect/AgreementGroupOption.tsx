import { ActionIcon, Box, Text } from '@mantine/core';
import { IconTrash } from '@tabler/icons-react';

const AgreementGroupOption: React.FC<{
  id: string;
  label: string;
  onDelete: (id: string) => void;
}> = ({ id, label, onDelete }) => (
  <Box
    sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}
  >
    <Text truncate="end">{label}</Text>

    <ActionIcon
      size="xs"
      color="red"
      variant="subtle"
      onClick={(e) => {
        e.stopPropagation();
        onDelete(id);
      }}
      aria-label="Usuń grupę"
    >
      <IconTrash size={14} />
    </ActionIcon>
  </Box>
);

export default AgreementGroupOption;
