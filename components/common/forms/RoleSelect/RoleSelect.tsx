import {Select} from "@mantine/core";
import {Controller, useFormContext} from "react-hook-form";
import {UserRole} from "@/types/UserRole";

const RoleSelect: React.FC<{ role?: UserRole }> = ({role}) => {
    const { control, setValue } = useFormContext();

    return (
        <Controller
            name="role"
            control={control}
            defaultValue={role || UserRole.EMPLOYEE}
            render={({ field }) => (
                <Select
                    label="Rola użytkownika"
                    placeholder="Wybierz rolę"
                    data={[
                        { value: UserRole.EMPLOYEE, label: 'Użytkownik' },
                        { value: UserRole.ADMINISTRATOR, label: 'Administrator' },
                    ]}
                    value={field.value}
                    onChange={field.onChange}
                    allowDeselect={false}
                />
            )}
        />
    );
};

export default RoleSelect;