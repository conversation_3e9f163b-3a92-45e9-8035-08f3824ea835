import { Anchor } from '@mantine/core';
import Link from 'next/link';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import classes from './WalletLink.module.css';
import { ComponentPropsWithoutRef } from 'react';

interface WalletLinkProps extends Omit<ComponentPropsWithoutRef<'a'>, 'href'> {
  walletId: string;
  label: string;
  openInNewTab?: boolean;
}

const WalletLink: React.FC<WalletLinkProps> = ({
  walletId,
  label,
  openInNewTab = true,
  ...anchorProps
}) => (
  <Anchor
    component={Link}
    className={classes.link}
    href={RouterPaths.WALLET_DETAILS(walletId)}
    target={openInNewTab ? '_blank' : undefined}
    rel={openInNewTab ? 'noopener noreferrer' : undefined}
    {...anchorProps}
  >
    {label}
  </Anchor>
);

export default WalletLink;
