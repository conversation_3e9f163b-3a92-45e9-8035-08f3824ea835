import { useEffect, useState } from 'react';
import { Box, Group, Select, TextInput } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import styles from './CustomerRemunerationForm.module.css';
import dayjs from 'dayjs';
import { MediaType } from '@/types/MediaType';

interface CustomerRemunerationFormProps {
  form: any;
  index: number;
  remuneration: any;
}

function CustomerRemunerationForm({ form, index, remuneration }: CustomerRemunerationFormProps) {
  const initialCarrier = form.getValues().remunerations[index]?.carrier || MediaType.ENERGY;
  const [carrier, setCarrier] = useState(initialCarrier);

  const handleCarrierChange = (value: string) => {
    setCarrier(value);
    form.setFieldValue(`remunerations.${index}.carrier`, value);
  };

  const formatToComma = (value: string | number) => {
    if (value === null || value === undefined) return '';
    return value.toString().replace('.', ',');
  };

  useEffect(() => {
    const currentCarrier = form.getValues().remunerations[index]?.carrier;
    if (currentCarrier !== carrier) {
      setCarrier(currentCarrier);
    }
  }, [form.values.remunerations, index, carrier, form]);

  return (
    <Box className={styles.remunerationBox}>
      {/* ------------------- Wspólne ------------------- */}
      <Group align="flex-end" grow>
        <Select
          label="Nośnik"
          data={[
            { value: 'ENERGY', label: 'Energia' },
            { value: 'GAS', label: 'Gaz' },
            { value: 'GREEN_PROPERTY_RIGHTS', label: 'Prawa majątkowe' },
          ]}
          value={carrier}
          onChange={handleCarrierChange}
        />

        <DatePickerInput
          label="Data od"
          placeholder="data od"
          withAsterisk
          locale="pl"
          valueFormat="YYYY-MM-DD"
          value={
            form.values.remunerations?.[index]?.period?.from
              ? dayjs(form.values.remunerations[index].period.from).toDate()
              : null
          }
          onChange={(date) => {
            const dateWithTime = date ? dayjs(date).hour(12).minute(0).second(0).toDate() : null;
            form.setFieldValue(`remunerations.${index}.period.from`, dateWithTime);
            form.validate();
          }}
          error={form.errors[`remunerations.${index}.period.from`]}
        />

        <DatePickerInput
          label="Data do"
          placeholder="data do"
          withAsterisk
          locale="pl"
          valueFormat="YYYY-MM-DD"
          value={
            form.values.remunerations?.[index]?.period?.to
              ? dayjs(form.values.remunerations[index].period.to).toDate()
              : null
          }
          onChange={(date) => {
            const dateWithTime = date ? dayjs(date).hour(12).minute(0).second(0).toDate() : null;
            form.setFieldValue(`remunerations.${index}.period.to`, dateWithTime);
            form.validate();
          }}
          error={form.errors[`remunerations.${index}.period.to`]}
        />

        <TextInput
          label="CAP (łączny)"
          placeholder="np. 100"
          value={formatToComma(form.values.remunerations?.[index]?.cap)}
          onChange={(event) =>
            form.setFieldValue(`remunerations.${index}.cap`, event.currentTarget.value)
          }
          error={form.errors[`remunerations.${index}.cap`]}
        />

        <Select
          label="Jednostka"
          placeholder="Wybierz"
          data={[
            { value: 'PLN_PER_MWH', label: 'zł/MWh' },
            { value: 'PLN_PER_YEAR', label: 'zł/rok' },
          ]}
          {...form.getInputProps(`remunerations.${index}.capUnit`)}
        />
      </Group>

      {/* ------------------- ENERGIA / GAZ ------------------- */}
      {(carrier === 'ENERGY' || carrier === 'GAS') && (
        <>
          <Group align="flex-end" mt="md">
            <Select
              label="Wynagrodzenie stałe"
              placeholder="Wybierz typ"
              data={[
                { value: 'FIXED_PLN_PER_MWH', label: 'Stałe – zł/MWh' },
                { value: 'FIXED_PLN_PER_MONTH', label: 'Stałe – zł/mies' },
              ]}
              {...form.getInputProps(`remunerations.${index}.fixedRemunerationType`)}
            />
            <TextInput
              label="Wartość"
              placeholder="np. 50"
              value={formatToComma(form.values.remunerations?.[index]?.fixedRemunerationValue)}
              onChange={(event) =>
                form.setFieldValue(
                  `remunerations.${index}.fixedRemunerationValue`,
                  event.currentTarget.value
                )
              }
              error={form.errors[`remunerations.${index}.fixedRemunerationValue`]}
            />
          </Group>

          <Group align="flex-end" mt="md">
            <Select
              label="Wynagrodzenie zmienne"
              data={[
                { value: 'TARIFF_PERCENT', label: 'Zmienne – taryfa (%)' },
                { value: 'MARKET_ANNUAL_PERCENT', label: 'Zmienne – rynkowy roczny (%)' },
                { value: 'MARKET_MIXED_PERCENT', label: 'Zmienne – rynkowy mieszany (%)' },
              ]}
              placeholder="Wybierz typ"
              {...form.getInputProps(`remunerations.${index}.remunerationType`)}
            />
            <TextInput
              label="Wartość"
              placeholder="np. 0,1"
              value={formatToComma(form.values.remunerations?.[index]?.remunerationValue)}
              onChange={(event) =>
                form.setFieldValue(
                  `remunerations.${index}.remunerationValue`,
                  event.currentTarget.value
                )
              }
              error={form.errors[`remunerations.${index}.remunerationValue`]}
            />
          </Group>

          <Group align="flex-end" mt="md">
            <TextInput
              label="Przetarg (zł)"
              placeholder="zł"
              value={formatToComma(form.values.remunerations?.[index]?.tenderCost)}
              onChange={(event) =>
                form.setFieldValue(`remunerations.${index}.tenderCost`, event.currentTarget.value)
              }
              error={form.errors[`remunerations.${index}.tenderCost`]}
            />
          </Group>
        </>
      )}

      {/* ------------------- PRAWA MAJĄTKOWE ------------------- */}
      {carrier === 'GREEN_PROPERTY_RIGHTS' && (
        <Group align="flex-end" mt="md">
          <TextInput
            label="Opłata zastępcza (%)"
            placeholder="np. 20"
            value={formatToComma(form.values.remunerations?.[index]?.substitutionFee)}
            onChange={(event) =>
              form.setFieldValue(
                `remunerations.${index}.substitutionFee`,
                event.currentTarget.value
              )
            }
            error={form.errors[`remunerations.${index}.substitutionFee`]}
          />
          <TextInput
            label="Rynkowy PMOZE_A (%)"
            placeholder="np. 10"
            value={formatToComma(form.values.remunerations?.[index]?.marketPmozea)}
            onChange={(event) =>
              form.setFieldValue(`remunerations.${index}.marketPmozea`, event.currentTarget.value)
            }
            error={form.errors[`remunerations.${index}.marketPmozea`]}
          />
        </Group>
      )}
    </Box>
  );
}

export default CustomerRemunerationForm;
