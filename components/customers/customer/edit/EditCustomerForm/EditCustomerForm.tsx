import {useEffect} from 'react';
import {Button, Group} from "@mantine/core";

import {useQuery} from "@tanstack/react-query";
import {CustomerService} from "@/services/CustomerService";
import {useRouter} from "next/router";

import EditCustomerFields
    from "@/components/customers/customer/edit/EditCustomerFields/EditCustomerFields";
import useCustomerEdit from "@/components/customers/customer/edit/EditCustomerForm/useCustomerEdit";


export function EditCustomerForm(props: any) {
    const {form, onSubmit, onRemoveFile, onDrop} = useCustomerEdit()
    const router = useRouter();
    const query = useQuery({
        queryKey: ['customer', router.query.id],
        staleTime: 0,
        gcTime: 0,
        queryFn: async () => {
            return await CustomerService.fetchCustomerById(router.query.id);
        },
        enabled: router.isReady
    });
    useEffect(() => {
        if (query?.data?.customer) {
            // Even if query.data changes, form will be initialized only once
            form.initialize({
                id: query.data?.customer?.id,
                name: query.data?.customer?.name,
                country: query.data?.customer?.country.code,
                taxNumber: query.data?.customer?.taxNumber,
                segment: query.data?.customer?.segment,
                configuration: {
                    realisation: query.data?.customer?.configuration?.realisation,
                    simulation: query.data?.customer?.configuration?.simulation,
                },
                group: query.data?.customer?.group?.id,
                attachments: query.data?.customer?.attachments,
                notes: query.data?.customer?.notes,
                contacts: query.data?.customer?.contacts
            });
        }
    }, [query.data]);

    return (
        <form onSubmit={form.onSubmit(onSubmit)}>
            <EditCustomerFields form={form} onDrop={onDrop} onRemoveFile={onRemoveFile}/>
            <Group justify="flex-end" mt="md">
                <Button type="submit" >Zapisz</Button>
            </Group>
        </form>
    );
}
