import { useForm } from '@mantine/form';
import { zodResolver } from 'mantine-form-zod-resolver';

import useFormErrors from '@/components/customers/customer/create/hooks/useFormErrors';
import { useState } from 'react';
import { CustomerContactService } from '@/services/CustomerContactService';
import { ModuleType, NotificationType } from '@/types/Common';
import { ContactLanguage, CustomerContact } from '@/types/Contacts';
import { CustomerContactFormSchema } from '@/components/customers/contacts/common/schema';
import { notify } from '@/utils/notify';
import { ContactFormValues } from '@/components/customers/contacts/common/types';

const getDefaultFormValues = (): ContactFormValues => ({
  name: { firstName: '', lastName: '' },
  email: '',
  number: '',
  type: ModuleType.CUSTOMER,
  configuration: {
    email: false,
    phone: false,
    newsletter: false,
    prices: false,
    recommendation: false,
    tranches: false,
  },
  language: ContactLanguage.PL,
});

export default function useEditForm(refetch) {
  const [editModalOpen, setEditModalOpen] = useState(false);

  const form = useForm({
    mode: 'uncontrolled',
    validateInputOnChange: true,
    validateInputOnBlur: true,
    initialValues: getDefaultFormValues,
    validate: zodResolver(CustomerContactFormSchema),
  });

  const { handleFormErrors } = useFormErrors(form);

  const openEditModal = (contact: CustomerContact) => {
    if (!contact?.id) {
      console.error('Błąd: Brak ID kontaktu do edycji.');
      return;
    }
    form.setValues({
      id: contact.id,
      name: {
        firstName: contact.name?.firstName || '',
        lastName: contact.name?.lastName || '',
      },
      email: contact.email || '',
      number: contact.number || '',
      customerId: contact.customer?.id,
      configuration: {
        email: Boolean(contact.configuration?.email),
        phone: Boolean(contact.configuration?.phone),
        newsletter: Boolean(contact.configuration?.newsletter),
        prices: Boolean(contact.configuration?.prices),
        recommendation: Boolean(contact.configuration?.recommendation),
        tranches: Boolean(contact.configuration?.tranches),
      },
      language: contact.language || ContactLanguage.PL,
    });

    setEditModalOpen(true);
  };

  const closeEditModal = async () => {
    await refetch();
    form.reset();
    setEditModalOpen(false);
  };

  const handleSubmit = async (values: CustomerContact): Promise<void> => {
    try {
      await CustomerContactService.update(values);
      notify(NotificationType.SUCCESS, 'Kontakt klienta został zapisany!');
      await closeEditModal();
    } catch (error: unknown) {
      handleFormErrors(error);
      notify(
        NotificationType.ERROR,
        'Zapis kontaktu klienta nie powiódł się. Sprawdź błędy zaznaczone w formularzu.'
      );
    }
  };

  return {
    editForm: form,
    editOnSubmit: handleSubmit,
    editModalOpen,
    openEditModal,
    closeEditModal,
  };
}
