import { useForm } from '@mantine/form';
import { zodResolver } from 'mantine-form-zod-resolver';
import { useState } from 'react';

import useFormErrors from '@/components/customers/customer/create/hooks/useFormErrors';
import { CustomerContactService } from '@/services/CustomerContactService';
import { ModuleType, NotificationType } from '@/types/Common';
import { ContactLanguage, CustomerContact } from '@/types/Contacts';
import { CustomerContactFormSchema } from '@/components/customers/contacts/common/schema';
import { notify } from '@/utils/notify';
import { ContactFormValues } from '@/components/customers/contacts/common/types';

const getDefaultFormValues = (): ContactFormValues => ({
  name: { firstName: '', lastName: '' },
  email: '',
  number: '',
  type: ModuleType.CUSTOMER,
  configuration: {
    email: false,
    phone: false,
    newsletter: false,
    prices: false,
    recommendation: false,
    tranches: false,
  },
  language: ContactLanguage.PL,
});

export default function useCopyForm(refetch: any) {
  const copyForm = useForm({
    mode: 'uncontrolled',
    validateInputOnChange: true,
    validateInputOnBlur: true,
    initialValues: getDefaultFormValues, //customerContactFormDefaultValues()
    validate: zodResolver(CustomerContactFormSchema),
  });

  const { handleFormErrors } = useFormErrors(copyForm);
  const [copyModalOpen, setCopyModalOpen] = useState(false);

  const copyContact = (contact: CustomerContact) => {
    if (!contact?.id) {
      console.error('Błąd: Brak ID kontaktu do skopiowania.');
      return;
    }

    copyForm.setValues({
      name: {
        firstName: contact.name?.firstName || '',
        lastName: contact.name?.lastName || '',
      },
      email: contact.email || '',
      number: contact.number || '',
      customerId: contact.customer?.id,
      configuration: {
        email: Boolean(contact.configuration?.email),
        phone: Boolean(contact.configuration?.phone),
        newsletter: Boolean(contact.configuration?.newsletter),
        prices: Boolean(contact.configuration?.prices),
        recommendation: Boolean(contact.configuration?.recommendation),
        tranches: Boolean(contact.configuration?.tranches),
      },
      language: contact.language || ContactLanguage.PL,
    });

    setCopyModalOpen(true);
  };

  const closeCopyContact = async () => {
    await refetch();
    copyForm.reset();
    setCopyModalOpen(false);
  };

  const handleSubmit = async (values: CustomerContact) => {
    try {
      await CustomerContactService.create(values);
      notify(NotificationType.SUCCESS, 'Kontakt klienta został zapisany!');
      await closeCopyContact();
    } catch (error: unknown) {
      handleFormErrors(error);
      notify(
        NotificationType.ERROR,
        'Zapis kontaktu klienta nie powiódł się. Sprawdź błędy zaznaczone w formularzu.'
      );
    }
  };

  return {
    copyForm,
    copyOnSubmit: handleSubmit,
    copyContact,
    closeCopyContact,
    copyModalOpen,
  };
}
