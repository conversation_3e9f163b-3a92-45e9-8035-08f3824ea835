/* (C)2024-2025 */
package com.codemonkeys.wallet.supplier.application.find;

import com.codemonkeys.wallet.common.framework.shared.dto.Response;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidationResult;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.SupplierContact;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Response object for finding a supplier by ID operations, including validation results and
 * supplier details.
 */
@Data
@Slf4j
// @AllArgsConstructor
public class FindSupplierByIdResponse implements Response {

  private ValidationResult validation;
  private Supplier supplier;

  public FindSupplierByIdResponse(ValidationResult validation, Supplier supplier) {
    this.validation = validation;
    this.supplier = supplier;

    // Krok 1: Grupowanie kontaktów po emailu z listą ID klientów
    Map<String, List<String>> emailToClientIds = new HashMap<>();
    for (SupplierContact contact : supplier.getContacts()) {
      String email = contact.getEmail().getValue();
      String clientId = contact.getCustomer().getId().getId();
      emailToClientIds.computeIfAbsent(email, k -> new ArrayList<>()).add(clientId);
    }

    // Krok 2: Utworzenie nowej listy unikalnych kontaktów
    List<SupplierContact> uniqueContacts = new ArrayList<>();

    // Dla każdego emaila, wybierz jeden kontakt jako reprezentanta
    for (String email : emailToClientIds.keySet()) {
      supplier.getContacts().stream()
          .filter(c -> email.equals(c.getEmail().getValue()))
          .findFirst()
          .ifPresent(
              contact -> {
                contact.setCustomerIds(emailToClientIds.get(email));
                uniqueContacts.add(contact);
              });
    }

    // Krok 3: Podmień oryginalną listę kontaktów na listę unikalnych kontaktów
    supplier.setContacts(uniqueContacts);
  }

  /**
   * Determines if the find operation was valid based on the included validation results.
   *
   * @return true if the validation results are positive, otherwise false.
   */
  @Override
  public boolean isValid() {
    return validation.isValid();
  }
}
