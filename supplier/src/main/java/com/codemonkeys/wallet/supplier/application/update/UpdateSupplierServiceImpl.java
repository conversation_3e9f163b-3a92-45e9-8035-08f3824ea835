/* (C)2024-2025 */
package com.codemonkeys.wallet.supplier.application.update;

import com.codemonkeys.wallet.common.framework.crud.update.UpdateByIdServiceImpl;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactConfiguration;
import com.codemonkeys.wallet.common.framework.domain.vo.Email;
import com.codemonkeys.wallet.common.framework.domain.vo.PersonName;
import com.codemonkeys.wallet.common.framework.domain.vo.PhoneNumber;
import com.codemonkeys.wallet.common.framework.shared.translations.I18n;
import com.codemonkeys.wallet.contact.application.crud.common.TenantIdContactService;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.SupplierContact;
import com.codemonkeys.wallet.domain.supplier.SupplierContactRepository;
import com.codemonkeys.wallet.domain.supplier.SupplierRepository;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierName;
import com.codemonkeys.wallet.supplier.application.common.CreateUpdateSupplierResponse;
import com.codemonkeys.wallet.supplier.application.common.SupplierMapper;
import com.codemonkeys.wallet.supplier.application.create.CreateSupplierContactRequest;
import com.google.common.collect.Lists;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** Service implementation for updating Supplier entities. */
@Slf4j
@Service
public class UpdateSupplierServiceImpl
    extends UpdateByIdServiceImpl<
        UpdateSupplierRequest,
        CreateUpdateSupplierResponse,
        Supplier,
        SupplierId,
        SupplierRepository,
        SupplierMapper,
        UpdateSupplierValidator> {

  private final SupplierMapper supplierMapper;
  private final SupplierContactRepository supplierContactRepository;
  private final CustomerRepository customerRepository;
  private final TenantIdContactService tenantIdContactService;

  @Autowired
  public UpdateSupplierServiceImpl(
      SupplierRepository repository,
      SupplierMapper mapper,
      UpdateSupplierValidator validator,
      SupplierMapper supplierMapper,
      SupplierContactRepository supplierContactRepository,
      CustomerRepository customerRepository,
      TenantIdContactService tenantIdContactService) {
    super(repository, mapper, validator);
    this.supplierMapper = supplierMapper;
    this.supplierContactRepository = supplierContactRepository;
    this.customerRepository = customerRepository;
    this.tenantIdContactService = tenantIdContactService;
  }

  /**
   * Updates a Supplier entity by its ID.
   *
   * @param id the ID of the supplier to update
   * @param request the update request containing the new supplier data
   * @return the response containing the updated supplier data
   * @throws IllegalArgumentException if the supplier is not found
   */
  @Transactional
  @Override
  public CreateUpdateSupplierResponse updateById(SupplierId id, UpdateSupplierRequest request) {
    Supplier currentSupplier =
        repository
            .findById(id)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        I18n.translate("error.supplier.not.found", id.getId())));
    updateSupplierDetails(currentSupplier, request);
    repository.save(currentSupplier);
    updateContactsAndRelationships(currentSupplier, request.getContacts());
    return supplierMapper.toResponse(currentSupplier);
  }

  /**
   * Updates the details of a Supplier entity.
   *
   * @param supplier the supplier entity to update
   * @param request the update request containing the new supplier data
   */
  private void updateSupplierDetails(Supplier supplier, UpdateSupplierRequest request) {
    supplier.setName(SupplierName.of(request.getName()));
  }

  /**
   * Updates the contacts and relationships of a Supplier entity.
   *
   * @param supplier the supplier entity to update
   * @param contactsRequest the list of contact requests containing the new contact data
   */
  private void updateContactsAndRelationships(
      Supplier supplier, List<CreateSupplierContactRequest> contactsRequest) {
    removeOldContacts(supplier);
    addNewContacts(supplier, contactsRequest);
  }

  /**
   * Removes all old contacts associated with a Supplier entity.
   *
   * @param supplier the supplier entity whose contacts are to be removed
   */
  private void removeOldContacts(Supplier supplier) {
    supplierContactRepository.deleteAllBySupplierId(supplier.getId());
  }

  /**
   * Adds new contacts to a Supplier entity.
   *
   * @param supplier the supplier entity to add contacts to
   * @param contactsRequest the list of contact requests containing the new contact data
   */
  private void addNewContacts(
      Supplier supplier, List<CreateSupplierContactRequest> contactsRequest) {
    for (CreateSupplierContactRequest contactRequest : contactsRequest) {
      List<SupplierContact> contacts = toSupplierContacts(contactRequest, supplier);
      supplierContactRepository.saveAll(contacts);
    }
  }

  /// **
  // * Converts a CreateSupplierContactRequest to a SupplierContact entity.
  // *
  // * @param contactRequest the contact request containing the new contact data
  // * @param supplier the supplier entity to associate the contact with
  // * @return the SupplierContact entity created from the contact request
  // */
  // private SupplierContact toSupplierContact(
  //    CreateSupplierContactRequest contactRequest, Supplier supplier) {
  //  Set<Customer> customers =
  //      contactRequest.getCustomerIds().stream()
  //          .map(this::findCustomerById)
  //          .collect(Collectors.toSet());
  //
  //  SupplierContact contact =
  //      SupplierContact.create(
  //          PersonName.of(contactRequest.getFirstName(), contactRequest.getLastName()),
  //          supplier,
  //          PhoneNumber.of(contactRequest.getPhoneNumber()),
  //          Email.of(contactRequest.getEmail()),
  //          ContactConfiguration.of(false, false, false, false, false, false),
  //          customers);
  //
  //  contact.setTenantId(tenantIdContactService.determineTenantId(customers));
  //  return contact;
  // }

  /**
   * Converts a CreateSupplierContactRequest to a SupplierContact entity.
   *
   * @param contactRequest the contact request containing the new contact data
   * @param supplier the supplier entity to associate the contact with
   * @return the SupplierContact entity created from the contact request
   */
  private List<SupplierContact> toSupplierContacts(
      CreateSupplierContactRequest contactRequest, Supplier supplier) {
    Set<Customer> customers =
        contactRequest.getCustomerIds().stream()
            .map(this::findCustomerById)
            .collect(Collectors.toSet());
    List<SupplierContact> supplierContacts = Lists.newArrayList();
    for (Customer customer : customers) {
      log.debug("Processing customer: {}", customer);
      SupplierContact contact =
          SupplierContact.create(
              PersonName.of(contactRequest.getFirstName(), contactRequest.getLastName()),
              supplier,
              PhoneNumber.of(contactRequest.getPhoneNumber()),
              Email.of(contactRequest.getEmail()),
              ContactConfiguration.of(false, false, false, false, false, false),
              customer);

      contact.setTenantId(tenantIdContactService.determineTenantId(customers));
      supplierContacts.add(contact);
    }

    return supplierContacts;
  }

  /**
   * Finds a Customer entity by its ID.
   *
   * @param customerId the ID of the customer to find
   * @return the Customer entity
   * @throws IllegalArgumentException if the customer is not found
   */
  private Customer findCustomerById(UUID customerId) {
    return customerRepository
        .findById(CustomerId.of(customerId))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    I18n.translate("error.customer.not.found", customerId.toString())));
  }
}
