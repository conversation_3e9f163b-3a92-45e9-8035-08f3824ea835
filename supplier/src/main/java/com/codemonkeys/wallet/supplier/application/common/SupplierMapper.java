/* (C)2024-2025 */
package com.codemonkeys.wallet.supplier.application.common;

import com.codemonkeys.wallet.common.framework.crud.create.CreateMapper;
import com.codemonkeys.wallet.common.framework.crud.update.UpdateMapper;
import com.codemonkeys.wallet.common.framework.domain.vo.ContactConfiguration;
import com.codemonkeys.wallet.common.framework.domain.vo.Email;
import com.codemonkeys.wallet.common.framework.domain.vo.PersonName;
import com.codemonkeys.wallet.common.framework.domain.vo.PhoneNumber;
import com.codemonkeys.wallet.common.framework.shared.validation.InvalidResult;
import com.codemonkeys.wallet.common.framework.shared.validation.ValidResult;
import com.codemonkeys.wallet.domain.customer.Customer;
import com.codemonkeys.wallet.domain.customer.CustomerRepository;
import com.codemonkeys.wallet.domain.customer.vo.CustomerId;
import com.codemonkeys.wallet.domain.supplier.Supplier;
import com.codemonkeys.wallet.domain.supplier.SupplierContact;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierId;
import com.codemonkeys.wallet.domain.supplier.vo.SupplierName;
import com.codemonkeys.wallet.supplier.application.create.CreateSupplierContactRequest;
import com.codemonkeys.wallet.supplier.application.create.CreateSupplierRequest;
import com.codemonkeys.wallet.supplier.application.update.UpdateSupplierRequest;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** Mapper for converting Supplier-related requests to domain entities and vice versa. */
@Slf4j
@Service
public class SupplierMapper
    implements CreateMapper<
            Supplier, SupplierId, CreateSupplierRequest, CreateUpdateSupplierResponse>,
        UpdateMapper<Supplier, SupplierId, UpdateSupplierRequest, CreateUpdateSupplierResponse> {

  private final CustomerRepository customerRepository;

  public SupplierMapper(CustomerRepository customerRepository) {
    this.customerRepository = customerRepository;
  }

  /**
   * Maps a CreateSupplierRequest to a Supplier domain entity.
   *
   * @param request The request object containing data for creating a supplier.
   * @return The Supplier domain entity created from the request data.
   */
  @Override
  public Supplier toDomain(CreateSupplierRequest request) {
    SupplierName supplierName = SupplierName.of(request.getName());
    Supplier supplier = new Supplier(supplierName);

    if (request.getContacts() != null) {
      List<SupplierContact> contacts =
          request.getContacts().stream()
              .map(contactRequest -> toSupplierContact(contactRequest, supplier))
              .collect(Collectors.toList());
      contacts.forEach(supplier::addContact);
    }

    return supplier;
  }

  /**
   * Maps the provided UpdateSupplierRequest along with the existing Supplier entity to an updated
   * Supplier entity. This method updates supplier properties based on the request data.
   *
   * @param currentSupplier The current Supplier entity that is to be updated.
   * @param request The UpdateSupplierRequest containing update data.
   * @return The updated Supplier entity reflecting changes specified in the UpdateSupplierRequest.
   */
  @Override
  public Supplier toDomain(Supplier currentSupplier, UpdateSupplierRequest request) {
    Supplier updatedSupplier =
        new Supplier(
            SupplierName.of(request.getName()),
            request.getContacts().stream()
                .map(contactRequest -> toSupplierContact(contactRequest, currentSupplier))
                .collect(Collectors.toList()));

    currentSupplier.update(updatedSupplier);
    return currentSupplier;
  }

  /**
   * Converts a CreateSupplierContactRequest into a SupplierContact entity.
   *
   * @param contactRequest The request object containing contact data.
   * @param supplier The supplier entity associated with the contact.
   * @return The SupplierContact entity created from the contact request.
   */
  private SupplierContact toSupplierContact(
      CreateSupplierContactRequest contactRequest, Supplier supplier) {
    Set<Customer> customers =
        contactRequest.getCustomerIds().stream()
            .map(this::findCustomerById)
            .collect(Collectors.toSet());

    return SupplierContact.create(
        PersonName.of(contactRequest.getFirstName(), contactRequest.getLastName()),
        supplier,
        PhoneNumber.of(contactRequest.getPhoneNumber()),
        Email.of(contactRequest.getEmail()),
        ContactConfiguration.of(false, false, false, false, false, false),
        customers);
  }

  /**
   * Converts a Supplier entity into a CreateUpdateSupplierResponse for successful supplier creation
   * or update operations.
   *
   * @param entity The Supplier entity to be transformed into a response.
   * @return A CreateUpdateSupplierResponse encapsulating the successful result and the supplier
   *     data.
   */
  @Override
  public CreateUpdateSupplierResponse toResponse(Supplier entity) {
    return CreateUpdateSupplierResponse.of(entity.getId(), ValidResult.of(), entity);
  }

  /**
   * Converts InvalidResult errors into a CreateUpdateSupplierResponse indicating failure in
   * supplier creation or update operations.
   *
   * @param errors The InvalidResult containing details of what went wrong during the operation.
   * @return A CreateUpdateSupplierResponse encapsulating the errors without a supplier entity.
   */
  @Override
  public CreateUpdateSupplierResponse toResponse(InvalidResult errors) {
    return CreateUpdateSupplierResponse.of(null, errors, null);
  }

  /**
   * Finds a Customer entity by its ID.
   *
   * @param customerId the ID of the customer to find
   * @return the Customer entity
   * @throws IllegalArgumentException if the customer is not found
   */
  private Customer findCustomerById(UUID customerId) {
    return customerRepository
        .findById(CustomerId.of(customerId))
        .orElseThrow(
            () -> new IllegalArgumentException(STR."Customer not found for ID: \{customerId}"));
  }
}
