import axios from 'axios';
import { SessionStorageService } from '@/services/auth/SessionStorage';
import { LoginService } from '@/services/auth/AuthService';
import { AuthService } from '@/services/auth/RoleService';

const API = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
});

let isRefreshing = false;
let refreshSubscribers = [];

function onRefreshed(token) {
  refreshSubscribers.forEach((callback) => callback(token));
  refreshSubscribers = [];
}

function addRefreshSubscriber(callback) {
  refreshSubscribers.push(callback);
}

API.interceptors.request.use(
  async (config) => {
    if (
      typeof window !== 'undefined' &&
      ['/login', '/forgot-password'].includes(window.location.pathname)
    ) {
      return config;
    }
    let token = SessionStorageService.getAccessToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;

      try {
        const decodedToken = AuthService.decodeToken(token);
        const tokenExpiration = decodedToken.exp * 1000;
        if (Date.now() >= tokenExpiration - 60000) {
          if (!isRefreshing) {
            isRefreshing = true;
            console.log('Token is about to expire, refreshing...');
            try {
              const { accessToken, refreshToken } = await LoginService.refreshToken();
              token = accessToken;
              SessionStorageService.setTokens(accessToken, refreshToken);
              onRefreshed(token);
              config.headers.Authorization = `Bearer ${token}`;
            } catch (error) {
              console.error('Problem z odświeżeniem tokena:', error);
              SessionStorageService.clearTokens();
              return Promise.reject(error);
            } finally {
              isRefreshing = false;
            }
          } else {
            return new Promise((resolve) => {
              addRefreshSubscriber((newToken) => {
                config.headers.Authorization = `Bearer ${newToken}`;
                resolve(config);
              });
            });
          }
        }
      } catch (error) {
        console.error('Problem z dekodowaniem tokenu:', error);
        SessionStorageService.clearTokens();
        return Promise.reject(error);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

API.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const { accessToken, refreshToken } = await LoginService.refreshToken();
        SessionStorageService.setTokens(accessToken, refreshToken);
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        return API(originalRequest);
      } catch (refreshError) {
        SessionStorageService.clearTokens();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export { API };
