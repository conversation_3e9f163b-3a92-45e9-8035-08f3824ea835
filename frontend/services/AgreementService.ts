import { ApiListResponse, ListRequestGlobalProps } from '@/types/Common';
import {
  Agreement,
  CreateAgreementResponse,
  DuplicateResponse,
  UpdateAgreementResponse,
} from '@/types/Agreement';
import { BaseService } from '@/services/shared/BaseService';
import { ListSelectService } from '@/services/shared/ListSelectService';
import { AgreementEndpoints } from '@/services/shared/ApiEndpoints';

class AgreementService {
  async fetchById(agreementId: any): Promise<Agreement> {
    return await BaseService.handleApiGetById<Agreement>(AgreementEndpoints.BY_ID(agreementId));
  }

  async create(data: any): Promise<CreateAgreementResponse> {
    return await BaseService.handleApiPost<CreateAgreementResponse>(AgreementEndpoints.BASE, data);
  }

  async update(data: any): Promise<UpdateAgreementResponse> {
    return await BaseService.handleApiPut<UpdateAgreementResponse>(
      AgreementEndpoints.UPDATE(data.id),
      data
    );
  }

  async duplicate(data: any): Promise<DuplicateResponse> {
    return await BaseService.handleApiPost<DuplicateResponse>(AgreementEndpoints.DUPLICATE, data);
  }

  async getAgreements(): Promise<ApiListResponse<Agreement>> {
    return await ListSelectService.fetchList<Agreement>(AgreementEndpoints.BASE);
  }

  async getAgreementsByYear(year: number): Promise<ApiListResponse<Agreement>> {
    return await ListSelectService.fetchList<Agreement>(AgreementEndpoints.BASE, {
      contractYear: year.toString(),
    });
  }

  async getAgreementGroups(): Promise<ApiListResponse<Agreement>> {
    return await ListSelectService.fetchList<Agreement>(AgreementEndpoints.GROUPS);
  }

  async fetchAgreements(params: ListRequestGlobalProps): Promise<ApiListResponse<Agreement>> {
    return await BaseService.fetchList<Agreement>(AgreementEndpoints.BASE, params);
  }

  async deleteAgreement(agreementId: string): Promise<void> {
    return await BaseService.handleApiDelete(AgreementEndpoints.DELETE(agreementId));
  }
}

const instance = new AgreementService()
export {instance as AgreementService}