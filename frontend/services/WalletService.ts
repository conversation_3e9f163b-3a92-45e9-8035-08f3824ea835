import { API } from '@/utils/api';
import { Wallet } from '@/types/Wallet';
import { ApiListResponse, ListRequestGlobalProps, NotificationType } from '@/types/Common';
import { BaseService } from '@/services/shared/BaseService';
import { FieldValues } from 'react-hook-form';
import { ListSelectService } from '@/services/shared/ListSelectService';
import {
  RealisationEndpoints,
  SimulationEndpoints,
  WalletEndpoints,
} from '@/services/shared/ApiEndpoints';
import { PUBLIC_URL } from '@/utils/url';
import { notify } from '@/utils/notify';
import { AnnualOverview } from '@/components/wallets/annual-purchase-overview/AnnualPurchaseOverviewList';
import { DEFAULT_GC_TIME, DEFAULT_STALE_TIME } from '@/utils/defaults';

class WalletService {
  async createWallet(data: FieldValues) {
    return await BaseService.handleApiPost<FieldValues>(WalletEndpoints.BASE, data);
  }

  async updateWallet(data: FieldValues, walletId: string) {
    try {
      const response = await API.put(WalletEndpoints.WALLET_BY_ID(walletId), data);
      return response.data;
    } catch (error) {
      console.error(`Problem with updating wallet`);
    }
  }

  async fetchWallet(params: ListRequestGlobalProps): Promise<ApiListResponse<Wallet>> {
    return await BaseService.fetchList<Wallet>(WalletEndpoints.BASE, params);
  }

  async deleteWallet(walletId: string): Promise<void> {
    try {
      await API.delete(WalletEndpoints.WALLET_BY_ID(walletId));
    } catch (error) {
      console.error(`Problem with deleting wallet with ID: ${walletId}`, error);
      throw error;
    }
  }

  async fetchShoppingReminder() {
    try {
      const data = await API.get(WalletEndpoints.SHOPPING_REMINDER);
      return data.data;
    } catch (error) {
      console.error(`Oopsie`, error);
      throw error;
    }
  }

  async fetchShoppingOverview() {
    try {
      const data = await API.get(WalletEndpoints.SHOPPING_OVERVIEW);
      return data.data;
    } catch (error) {
      console.error(`Oopsie`, error);
      throw error;
    }
  }

  async fetchRealisation(walletId: string) {
    try {
      const data = await API.get(RealisationEndpoints.REALISATION(walletId));
      return data.data;
    } catch (error) {
      console.error(`Oopsie`, error);
      throw error;
    }
  }

  async getWallet(walletId: any): Promise<Wallet> {
    try {
      const response = await API.get<Wallet>(WalletEndpoints.WALLET_BY_ID(walletId));
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async getWallets(): Promise<ApiListResponse<Wallet>> {
    return await ListSelectService.fetchList<Wallet>(WalletEndpoints.BASE);
  }

  //TODO: może getWallets(options: {year:number}}): Promise<ApiListResponse<Wallet>>
  // mielibyśmy załatwione kolejne filtrowania i spójne podejście w appce.
  async getWalletsByYear(year: number): Promise<ApiListResponse<Wallet>> {
    return await ListSelectService.fetchList<Wallet>(WalletEndpoints.BASE, {
      contractYear: year.toString(),
    });
  }

  async exportToExcel(simulationData: any): Promise<void> {
    const fetchURL = new URL(SimulationEndpoints.WALLET_EXPORT, PUBLIC_URL);
    try {
      const response = await API.post(fetchURL.href, simulationData, {
        responseType: 'blob',
      });
      // Generate the file name based on simulation ID
      const simulationId = simulationData.agreement || 'simulation';
      const fileName = `symulacja_${simulationId}.xlsx`;
      const blob = new Blob([response.data]);
      const fileUrl = window.URL.createObjectURL(blob);
      this.downloadFile(fileUrl, fileName);
      window.URL.revokeObjectURL(fileUrl);
      notify(NotificationType.SUCCESS, 'Plik Excel został wygenerowany i pobrany.');
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message || 'Wystąpił błąd podczas generowania pliku Excel.';
      console.error('Error exporting Excel:', error);
      notify(NotificationType.ERROR, errorMessage);
    }
  }

  async recalculate(walletId: string): Promise<void> {
    try {
      await API.get(WalletEndpoints.RECALCULATE_BY_ID(walletId));
    } catch (error) {
      console.error(`Problem with deleting wallet with ID: ${walletId}`, error);
      throw error;
    }
  }

  async fetchAnnualPurchasesOverview(
    params: ListRequestGlobalProps
  ): Promise<ApiListResponse<AnnualOverview>> {
    try {
      return await BaseService.fetchList<AnnualOverview>(WalletEndpoints.OVERVIEW, params);
    } catch (error) {
      notify(
        NotificationType.ERROR,
        `Problem with overview for media and year: ${params}. ${error.message}` || ''
      );
      throw error;
    }
  }

  async fetchPriceConfirmation(
    params: ListRequestGlobalProps
  ): Promise<ApiListResponse<AnnualOverview>> {
    try {
      return await BaseService.fetchList<AnnualOverview>(
        WalletEndpoints.PRICE_CONFIRMATION_LIST,
        params
      );
    } catch (error) {
      notify(
        NotificationType.ERROR,
        `Problem with overview for media and year: ${params}. ${error.message}` || ''
      );
      throw error;
    }
  }

  async updatePriceConfirmation(data: any) {
    try {
      const response = await API.put(WalletEndpoints.UPDATE_PRICE_CONFIRMATIONS, data);
      return response.data;
    } catch (error) {
      notify(
        NotificationType.ERROR,
        `Problem with updating price confirmations: ${error?.message}` || ''
      );
      throw error;
    }
  }

  private downloadFile(fileUrl: string, fileName: string): void {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  getOverviewQueryConfig(
    keyPrefix: string,
    rawParams: ListRequestGlobalProps,
    fetchFn: (params: ListRequestGlobalProps) => Promise<ApiListResponse<AnnualOverview>>
  ) {
    return {
      queryKey: [keyPrefix, rawParams],
      queryFn: () => fetchFn(rawParams),
      staleTime: DEFAULT_STALE_TIME,
      gcTime: DEFAULT_GC_TIME,
    };
  }
}

const instance = new WalletService();
export { instance as WalletService };
