import {BaseService} from "@/services/shared/BaseService";
import {SimulationEndpoints} from "@/services/shared/ApiEndpoints";


class SimulationService {

    async fetchSimulateById(walletId: string): Promise<any> {
        return await BaseService.handleApiGetById<any>(SimulationEndpoints.BY_ID(walletId));
    }

    async simulate(wallet: any): Promise<any> {
        return await BaseService.handleApiPost<any>(SimulationEndpoints.BASE, wallet);
    }

    async fullPurchase(wallet: any): Promise<any> {
        return await BaseService.handleApiPost<any>(SimulationEndpoints.FULL_PURCHASE, wallet);
    }

    async getLastPriceForContract(contractId: string): Promise<any> {
        return await BaseService.handleApiGetById<any>(SimulationEndpoints.LAST_PRICE(contractId));
    }
}

const instance = new SimulationService();
export {instance as SimulationService};