import { API } from '@/utils/api';
import { FetchSuppliersParams, Supplier } from '@/types/Supplier';
import { ApiListResponse } from '@/types/Common';
import {SupplierEndpoints} from "@/services/shared/ApiEndpoints";

class SupplierService {

  async fetchSuppliers({
    columnFilters,
    globalFilter,
    sorting,
    pagination,
  }: FetchSuppliersParams): Promise<ApiListResponse<Supplier>> {
    const params = new URLSearchParams();

    if (globalFilter) {
      params.append('name', globalFilter);
    }

    if (sorting && sorting.length > 0) {
      sorting.forEach((sort) => {
        params.append('sort', `${sort.id}:${sort.desc ? 'desc' : 'asc'}`);
      });
    } else {
      params.append('sort', 'name:asc');
    }

    params.append('page', pagination.pageIndex.toString());
    params.append('limit', pagination.pageSize.toString());

    try {
      const response = await API.get(SupplierEndpoints.BASE, { params });
      return response.data;
    } catch (error) {
      console.error('Problem with fetching suppliers:', error);
      throw new Error('Problem with fetching suppliers');
    }
  }

  async fetchSupplierById(supplierId: any) {
    try {
      const response = await API.get(SupplierEndpoints.SUPPLIER_BY_ID(supplierId));
      return response.data.supplier;
    } catch (error) {
      console.error(`Wystąpił problem z pobraniem danych sprzedawcy o ID: ${supplierId}`, error);
      throw error;
    }
  }

  async createSupplier(supplierData: Supplier): Promise<any> {
    try {
      const response = await API.post(SupplierEndpoints.BASE, supplierData);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Wystąpił problem z utworzeniem sprzedawcy:', error);
      return { success: false, error: error.response?.data || error.message };
    }
  }

  async updateSupplier(supplierId: string, supplierData: Supplier): Promise<any> {
    try {
      const response = await API.put(SupplierEndpoints.SUPPLIER_BY_ID(supplierId), supplierData);
      return { success: true, data: response.data };
    } catch (error) {
      console.error(`Wystąpił problem z aktualizacją sprzedawcy o ID: ${supplierId}`, error);
      return { success: false, error: error.response?.data || error.message };
    }
  }

  async deleteSupplier(supplierId: any) {
    try {
      const response = await API.delete(SupplierEndpoints.SUPPLIER_BY_ID(supplierId));
      return response.data;
    } catch (error) {
      console.error(`Wystąpił problem z usunięciem sprzedawcy o ID: ${supplierId}`, error);
      throw error;
    }
  }
}

const instance = new SupplierService();
export { instance as SupplierService };
