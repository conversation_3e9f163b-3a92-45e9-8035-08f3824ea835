import {API} from "@/utils/api";
import {User} from "@/types/User";
import {UserEndpoints} from "@/services/shared/ApiEndpoints";
import { SecureLogger } from "@/utils/secureLogger";

class UserService {

    async fetchUsers(): Promise<User[]> {
        try {
            const response = await API.get<User[]>(UserEndpoints.BASE);
            return response.data;
        } catch (error) {
            console.error('Wystąpił problem z pobraniem użytkowników:', error);
            throw error;
        }
    }

    async createUser(userData: any) {
        try {
            const response = await API.post(UserEndpoints.BASE, userData);
            return {success: true, data: response.data};
        } catch (error) {
            console.error('Wystąpił problem z utworzeniem użytkownika:', error);
            return {success: false, error: error.response?.data || error.message};
        }
    }

    async getUserById(userId: string): Promise<{ success: boolean, data?: User, error?: any }> {
        try {
            const response = await API.get<User>(UserEndpoints.USER_BY_ID(userId));
            return {success: true, data: response.data};
        } catch (error) {
            SecureLogger.errorWithId('Wystąpił problem z pobraniem użytkownika o ID', userId, error);
            return {success: false, error: error.response?.data || error.message};
        }
    }

    async updateUser(userId: string, userData: any): Promise<{ success: boolean, data?: any, error?: any }> {
        try {
            const response = await API.put(UserEndpoints.USER_BY_ID(userId), userData);
            return {success: true, data: response.data};
        } catch (error) {
            SecureLogger.errorWithId('Wystąpił problem z aktualizacją użytkownika o ID', userId, error);
            return {success: false, error: error.response?.data || error.message};
        }
    }

    async changePassword(passwordData: any) {
        try {
            const response = await API.post(UserEndpoints.CHANGE_PASSWORD, passwordData);
            return {success: true, data: response.data};
        } catch (error) {
            console.error('Wystąpił problem ze zmianą hasła:', error);
            return {success: false, error: error.response?.data || error.message};
        }
    }

    async deleteUser(userId: string): Promise<void> {
        try {
            await API.delete(UserEndpoints.USER_BY_ID(userId));
        } catch (error) {
            console.error(`Problem with deleting user with ID: ${userId}`, error);
            throw error;
        }
    }
}

const instance = new UserService();
export {instance as UserService};