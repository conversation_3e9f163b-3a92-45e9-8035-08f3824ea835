import { API } from '@/utils/api';
import { AxiosResponse } from 'axios';
import { ApiListResponse } from '@/types/Common';
import { Contract } from '@/types/Contract';
import { ListSelectService } from '@/services/shared/ListSelectService';
import { BaseService } from '@/services/shared/BaseService';
import { ContractEndpoints } from '@/services/shared/ApiEndpoints';
import { CreateContract } from '@/types/Agreement';

class ContractService {
  /**
   * Despite it's grayed out its used in file useCreateContract.tsx
   * @param data
   */
  async createContractPreview(data: any): Promise<CreateContract[]> {
    return await BaseService.handleApiPost<CreateContract[]>(
      ContractEndpoints.GENERATE_FINANCIAL_INSTRUMENTS,
      data
    );
  }

  async getContractTimeUnit(name: string): Promise<string> {
    try {
      const response: AxiosResponse<string> = await API.get<string>(
        ContractEndpoints.TIME_UNIT_BY_NAME(name)
      );
      return response.data;
    } catch (error) {
      console.error('Wystąpił problem z pobraniem kontraktów:', error);
      throw error;
    }
  }

  async getContractsOption(): Promise<string[]> {
    try {
      const response: AxiosResponse<string[]> = await API.get<string[]>(ContractEndpoints.OPTIONS);
      return response.data;
    } catch (error) {
      console.error('Wystąpił problem z pobraniem kontraktów:', error);
      throw error;
    }
  }

  async getContractsOptionByYear(year: number): Promise<ApiListResponse<string[]>> {
    try {
      const response: ApiListResponse<string[]> = await ListSelectService.fetchList<string[]>(
        ContractEndpoints.OPTIONS,
        { contractYear: year.toString() }
      );
      return response;
      // return response.content;
    } catch (error) {
      console.error('Wystąpił problem z pobraniem kontraktów:', error);
      throw error;
    }
  }

  async getContractPriceOptions(): Promise<string[]> {
    try {
      const response: AxiosResponse<string[]> = await API.get<string[]>(
        ContractEndpoints.OPTIONS_FROM_PRICES
      );
      return response.data;
    } catch (error) {
      console.error('Wystąpił problem z pobraniem kontraktów:', error);
      throw error;
    }
  }

  async getContractById(contractId: string): Promise<Contract> {
    return await BaseService.handleApiGetById<Contract>(ContractEndpoints.BY_ID(contractId));
  }

  async getContractsByAgreement(
    agreementId: string,
    applyFilter = false
  ): Promise<ApiListResponse<Contract>> {
    return await ListSelectService.fetchList<Contract>(ContractEndpoints.BY_AGREEMENT, {
      agreementId: agreementId,
      applyFilter: applyFilter.toString(),
    });
  }

  async getPriceReference(agreementId: string, contractName: string): Promise<string[]> {
    try {
      const response: AxiosResponse<string[]> = await API.get<string[]>(
        ContractEndpoints.PRICE_REFERENCE(agreementId, contractName)
      );
      return response.data;
    } catch (error) {
      console.error('Wystąpił problem z pobraniem priceReference:', error);
      return [];
    }
  }
}

const instance = new ContractService();
export { instance as ContractService };
