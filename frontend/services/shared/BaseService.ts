import { AxiosResponse } from 'axios';
import { ApiListResponse, ListRequestGlobalProps } from '@/types/Common';
import { API } from '@/utils/api';

export class BaseService {
  public static async handleApiRequest<T>(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    data?: T | { params?: Record<string, any> }
  ): Promise<any> {
    try {
      const response: AxiosResponse<any> = await API[method](url, data);
      return response.data;
    } catch (error) {
      console.error(`Problem z operacją ${method.toUpperCase()} dla ${url}:`, error);
      throw error;
    }
  }

  static async fetchList<T>(
    url: string,
    params: ListRequestGlobalProps
  ): Promise<ApiListResponse<T>> {
    const queryParams = new URLSearchParams();

    // pagination
    queryParams.append('page', params.pagination.pageIndex.toString());
    queryParams.append('limit', params.pagination.pageSize.toString());

    // sort
    if (params.sorting.length > 0) {
      params.sorting.forEach((sort) => {
        queryParams.append('sort', `${sort.id}:${sort.desc ? 'desc' : 'asc'}`);
      });
    } else {
      queryParams.append('sort', 'createdAt:desc');
    }

    // filter
    params.filters?.forEach((filter) => {
      if (filter.id && filter.value) {
        queryParams.append(filter.id, String(filter.value));
      }
    });

    // global filter
    if (params.globalFilter) {
      queryParams.append('globalFilter', params.globalFilter);
    }

    // status(singular)
    if (params.status && params.status.length > 0) {
      queryParams.append('status', params.status.join(','));
    }

    // for recommendation
    if (params.includeArchived !== undefined) {
      queryParams.append('includeArchived', params.includeArchived.toString());
    }

    // for agreement
    if (params.withArchive !== undefined) {
      queryParams.append('withArchive', params.withArchive.toString());
    }
    const urlWithParams = `${url}?${queryParams.toString()}`;
    return await this.handleApiRequest('get', urlWithParams);
  }

  static async handleApiPost<T>(url: string, data: T): Promise<any> {
    return await this.handleApiRequest('post', url, data);
  }

  static async handleApiPut<T>(url: string, data: T): Promise<any> {
    return await this.handleApiRequest('put', url, data);
  }

  static async handleApiDelete(url: string): Promise<any> {
    return await this.handleApiRequest('delete', url);
  }

  static async handleApiGetById<R>(url: string): Promise<R> {
    return await this.handleApiRequest('get', url);
  }
}
