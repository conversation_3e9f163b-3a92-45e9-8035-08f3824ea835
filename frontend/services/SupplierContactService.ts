import {API} from '@/utils/api';
import {SupplierEndpoints} from "@/services/shared/ApiEndpoints";
import {ApiListResponse, ListRequestGlobalProps} from "@/types/Common";
import {BaseService} from "@/services/shared/BaseService";
import {SupplierContact, SupplierContactEdit} from "@/types/Supplier";
import { SecureLogger } from '@/utils/secureLogger';

class SupplierContactService {

    async listContacts(params: ListRequestGlobalProps): Promise<ApiListResponse<SupplierContact>> {
        return await BaseService.fetchList<any>(SupplierEndpoints.CONTACTS, params);
    }

    async getContactById(contactId: string): Promise<SupplierContactEdit> {
        try {
            const response = await API.get(SupplierEndpoints.CONTACT_BY_ID(contactId));
            return response.data;
        } catch (error) {
            SecureLogger.errorWithId('Problem with fetching contact by ID', contactId, error);
            throw new Error(`Problem with fetching contact by ID: ${contactId}`);
        }
    }

    // Not used; update occurs along with the entire supplier object.
    async updateContact(contactId: string, contactData: Partial<SupplierContactEdit>): Promise<void> {
        try {
            await API.put(SupplierEndpoints.CONTACT_UPDATE(contactId), { ...contactData });
        } catch (error) {
            SecureLogger.errorWithId('Problem with updating contact by ID', contactId, error);
            throw new Error(`Problem with updating contact by ID: ${contactId}`);
        }
    }

    async deleteContact(contactId: string): Promise<void> {
        try {
            await API.delete(SupplierEndpoints.CONTACT_BY_ID(contactId));
        } catch (error) {
            SecureLogger.errorWithId('Problem with deleting contact by ID', contactId, error);
            throw new Error(`Problem with deleting contact by ID: ${contactId}`);
        }
    }
}

const instance = new SupplierContactService();
export { instance as SupplierContactService };