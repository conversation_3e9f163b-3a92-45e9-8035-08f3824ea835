import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // The middleware can be used for authentication checks, logging, etc.
  // For now, we'll just pass through all requests
  
  // You can add authentication checks here if needed
  // For example, check if the user has a valid token for certain routes
  
  return NextResponse.next();
}

export const config = {
  // Apply middleware to all routes except static files and Next.js internals
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
