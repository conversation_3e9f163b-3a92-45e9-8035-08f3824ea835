import { createTheme, MantineColorsTuple } from '@mantine/core';

const baseOrange: MantineColorsTuple = [
  '#fff4e2',
  '#ffe7cd',
  '#fecd9c',
  '#fcb268',
  '#fa9b3b',
  '#fa8d1f',
  '#fa850e', // base orange
  '#df7201',
  '#c76500',
  '#ad5500'
];

const gray: MantineColorsTuple = [
  "#f1f3f8", // background
  "#e1e3ea",
  "#c0c5d6",
  "#9ca5c3",
  "#7e8ab2",
  "#6a78a8",
  "#6070a5",
  "#505f90",
  "#465482",
  "#3a4874"
]

const blackGray: MantineColorsTuple = [
  "#f9f9f9",
  "#e6e6e6",
  "#cccccc",
  "#b3b3b3",
  "#999999",
  "#808080",
  "#666666",
  "#4d4d4d",
  "#333333",
  "#1a1a1a"
];

export const theme = createTheme({
  colors: {
    baseOrange,
  },
  primaryColor: 'baseOrange',
  fontFamily: 'Poppins, sans-serif',
});
