{"name": "wallet-frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "analyze": "ANALYZE=true next build", "start": "next start", "export": "next build && next export", "typecheck": "tsc --noEmit", "lint": "next lint && npm run lint:stylelint", "lint:stylelint": "stylelint '**/*.css' --cache", "jest": "jest", "jest:watch": "jest --watch", "prettier:check": "prettier --check \"**/*.{ts,tsx}\"", "prettier:write": "prettier --write \"**/*.{ts,tsx}\"", "test": "npm run prettier:check && npm run lint && npm run typecheck && npm run jest", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@mantine/modals": "^7.11.1", "@types/validator": "^13.11.10", "date-fns": "^4.1.0", "export-to-csv": "^1.3.0", "file-saver": "^2.0.5", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "http-proxy": "^1.18.1", "jsonwebtoken": "^9.0.2", "next": "14.2.25", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.51.3", "recharts": "^2.13.0", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.24.0", "@mantine/core": "7.8.0", "@mantine/dates": "^7.8.1", "@mantine/dropzone": "^7.8.0", "@mantine/form": "^7.8.0", "@mantine/hooks": "7.8.0", "@mantine/notifications": "^7.8.1", "@next/bundle-analyzer": "^14.1.3", "@next/eslint-plugin-next": "^14.1.3", "@storybook/addon-essentials": "^8.0.0", "@storybook/addon-styling-webpack": "^0.0.6", "@storybook/blocks": "^8.0.0", "@storybook/nextjs": "^8.0.0", "@storybook/react": "^8.0.0", "@storybook/testing-library": "^0.2.2", "@tabler/icons-react": "^3.2.0", "@tanstack/react-query": "^5.29.2", "@testing-library/dom": "^9.3.4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/http-proxy": "^1.17.16", "@types/jest": "^29.5.12", "@types/node": "^20.11.26", "@types/react": "18.2.65", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "axios": "^1.6.8", "babel-loader": "^9.1.3", "dayjs": "^1.11.11", "eslint": "^8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-mantine": "3.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mantine-form-zod-resolver": "^1.1.0", "mantine-react-table": "^2.0.0-beta.1", "next-translate": "^2.6.2", "next-translate-plugin": "^2.6.2", "postcss": "^8.4.35", "postcss-preset-mantine": "1.14.4", "postcss-simple-vars": "^7.0.1", "prettier": "^3.2.5", "storybook": "^8.0.0", "storybook-dark-mode": "^3.0.3", "stylelint": "^16.2.1", "stylelint-config-standard-scss": "^13.0.0", "ts-jest": "^29.1.2", "typescript": "5.4.2", "validate-polish": "^2.1.40", "validator": "^13.12.0", "zod": "^3.23.8", "zustand": "^4.5.2"}, "resolutions": {"nanoid": "^3.3.8"}, "packageManager": "yarn@4.1.1"}