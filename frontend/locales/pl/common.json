{"title": "Portfel 2.0", "ENERGY": "Energia", "GAS": "Gaz", "CERTIFICATES": "Certyfikaty", "MAIL": "E-mail", "LOCAL_GOVERNMENT_UNIT": "JST", "INDUSTRY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "REAL_ESTATE": "Nieruch<PERSON>ś<PERSON>", "SHOPPING_CHAINS": "<PERSON><PERSON><PERSON><PERSON>", "ACTIVE": "Aktywny", "ARCHIVE": "Archiwu<PERSON>", "POLAND": "Polska", "ENGLAND": "Anglia", "GERMANY": "<PERSON><PERSON><PERSON>", "true": "Tak", "TRUE": "Tak", "false": "<PERSON><PERSON>", "FALSE": "<PERSON><PERSON>", "realm_employee": "Pracownik", "realm_administrator": "Administrator", "NET": "Cena T<PERSON>", "COSTS": "<PERSON><PERSON><PERSON>", "EXCISE": "<PERSON><PERSON><PERSON><PERSON>", "PROFILE": "Profil", "PROFILE_PERCENT": "Profil (%)", "GREEN_PROPERTY_RIGHTS": "Zielone certyfikaty", "TOTAL_PROPERTY_RIGHTS": "Certyfikaty razem", "BLUE_PROPERTY_RIGHTS": "Błękitne certyfikaty", "WHITE_PROPERTY_RIGHTS": "Białe certyfikaty", "MAIN_MEDIA": "Główne medium", "TOTAL": "<PERSON>na <PERSON>", "PERCENTAGE": "Procentowy", "VOLUME": "Wolumenowy", "FIXED_PRICE": "Stała cena", "TRANCHE": "Transze", "PASSIVE_PURCHASE": "<PERSON><PERSON><PERSON>", "CUSTOMER": "<PERSON><PERSON> kupuje", "ENEA_TRADE": "EneaTrade24", "POI": "POI", "PORTAL_FORTUM": "Portal Fortum", "PORTAL_TELNOM": "Telnom", "SUPPLIER_EMAIL": "E-mail do sprzedawcy", "ATTACHMENT_EMAIL": "E-mail z załącznikiem", "OTHER": "<PERSON><PERSON>", "TGe24": "TGe24", "INTERVENTION_PURCHASE": "Zakup interwencyjny", "TGEgasDA": "TGEgasDA", "TGEgasID": "TGEgasID", "PROPERTY_RIGHT": "praw maj<PERSON><PERSON>wy<PERSON>", "YESTERDAY": "n-1", "TRADER_REQUEST": "Zapytanie <PERSON>ra", "TARGET_PRICE_DKR": "Target Price po DKR", "DKR": "DKR", "TARGET_PRICE": "Target Price", "FIXED_PER_CONTRACT": "X na kontrakt", "FIXED_FOR_DELIVERY_PERIOD": "X na okres dostawy", "NO_TRADING": "Obrót na kontrakcie = 0", "FIXED_TRANSACTION_NUMBER_NOT_REACHED": "Liczba transakcji < X", "MULTIPLE": "Wielokrotność x % maksymalnie y %", "MIN_MAX": "Minimalnie x % maksymalnie y %", "FIXED": "x kWh/h", "PAYMENT": "Opła<PERSON> zastę<PERSON>cza", "AVERAGE_SESSION": "Średnia ważona z notowań sesyjnych,", "ARITHMETIC_AVERAGE_SESSION": "Średnia arytmetyczna z notowań sesyjnych", "Details": "Szczegóły", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Show": "Podgląd", "Download": "<PERSON><PERSON><PERSON>", "Delete": "Usuń", "MULTIPLE_PERCENTAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{x}}% maksymalnie {{y}}%", "MIN_MAX_PERCENTAGE": "Minimalnie {{x}}% maksymalnie {{y}}%", "MULTIPLE_VOLUME": "Wiel<PERSON><PERSON><PERSON>ść {{x}}MW maksymalnie {{y}}MW", "MIN_MAX_VOLUME": "Minimalnie {{x}}MW maksymalnie {{y}}MW", "FIXED_VOLUME": "<PERSON><PERSON><PERSON> wartość: {{x}}kW/h", "MULTIPLE_TRANCHE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{value}}%", "WORK_DAY_BEFORE_LAST_TRADING": "WD przed ostatnim dniem obrotu", "WORK_DAY_BEFORE_DELIVERY": "WD przed dostawą ", "CALENDAR_DAY_BEFORE_LAST_TRADING": "CD przed ostatnim dniem obrotu", "CALENDAR_DAY_BEFORE_DELIVERY": "CD przed dostawą", "DAY_OF_MONTH_BEFORE_DELIVERY": "<PERSON><PERSON>ń miesiąca przed dostawą", "DATE_15_02_R_PLUS_1": "15.02.R+1", "END_OF_YEAR": "31.12.R-1", "DAYS_BEFORE_MONTH_END": "dni przed końcem miesiąca dostawy", "DAYS_BEFORE_YEAR": "18.12.R-1", "DATE_21_12_R_MINUS_1": "21.12.R-1", "DATE_15_02_YEAR_PLUS_1": "15.02.R+1", "DATE_21_12_YEAR_MINUS_1": "21.12.R-1", "DATE_31_12_YEAR_MINUS_1": "31.12.R-1", "DATE_18_12_YEAR_MINUS_1": "18.12.R-1", "EE": "Energia elektryczna", "weekly": "Tygodniowy", "monthly": "<PERSON><PERSON><PERSON>ny", "CORRECTION_FACTOR": "Składnik korygujący (widoczny)", "TGE_CORRECTION_FACTOR": "Składnik korygujący (widoczny)", "HIDDEN_CORRECTION_FACTOR": "Składnik korygujący (niewidoczny)", "MULTIPLIER": "Mnożnik", "LAST_CJ": "Zielone - CJ (Rok portfela - 1)", "CJ": "Zielone - CJ (Rok portfela)", "WF": "Zielone - WF", "MARGIN": "Zielone - Marża", "WIBOR": "Zielone - Wibor (%)", "INDUSTRIAL_STATUS_DISCOUNT": "Zielone - Status odbiorcy przemysłowego obowiązek (%)", "M_PARAMETER": "Zielone - Parametr M", "GREEN_PROPERTY_RIGHTS_COSTS": "Zielone - Koszty", "SUPPLIER_SERVICE_PRICE": "Zielone - cena <PERSON>i", "SUPPLIER_DISCOUNT_PRICE": "Zielone - cena o<PERSON>", "Wallet": "Portfel", "wallet": "portfel"}