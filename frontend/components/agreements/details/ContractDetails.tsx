import classes from './AgreementDetails.module.css';
import { <PERSON><PERSON>, Container, Divider, Loader, Table, Text, Title } from '@mantine/core';
import React from 'react';
import { useRouter } from 'next/router';
import useContract from '@/components/agreements/details/useContract';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import useTranslation from 'next-translate/useTranslation';

const ContractDetails = () => {
  const { t, lang } = useTranslation('common');

  const router = useRouter();

  const query = useContract();

  if (query.isLoading || query.isLoading) {
    return <Loader color="orange" size="md" />;
  }

  const contract = query?.data;

  const formatTimes = (priceReference: string): string => {
    const orderTimes = contract?.orderTimes[priceReference] || {};
    const start = orderTimes.start ? `Od ${orderTimes.start}` : '';
    const end = orderTimes.end ? `Do ${orderTimes.end}` : '';

    return `${start ?? start + ','} ${end}`;
  };

  const formatPriceReference = (priceReference: string): string => {
    return [t(priceReference), formatTimes(priceReference)].join(': ');
  };

  const priceReferenceTimes = contract?.priceReference?.map(formatPriceReference);

  const volumeFormatted = () => {
    switch (contract?.volume?.type) {
      case 'MULTIPLE':
        switch(contract?.purchaseModel) {
          case 'TRANCHE':
            return t('MULTIPLE_TRANCHE', {
              value: contract?.volume?.value,
            });
          case 'PERCENTAGE':
            return t('MULTIPLE_PERCENTAGE', {
              x: contract?.volume?.x,
              y: contract?.volume?.y,
            });
          case 'VOLUME':
            return t('MULTIPLE_VOLUME', {
              x: contract?.volume?.x,
              y: contract?.volume?.y,
            });
          default:
            return [t(contract?.volume?.type), contract?.volume?.value].filter(Boolean).join(': ');
        }
      case 'MIN_MAX':
        return contract?.purchaseModel === 'PERCENTAGE'
          ? t('MIN_MAX_PERCENTAGE', {
              x: contract?.volume?.x,
              y: contract?.volume?.y,
            })
          : t('MIN_MAX_VOLUME', {
              x: contract?.volume?.x,
              y: contract?.volume?.y,
            });
      case 'FIXED':
        return t('FIXED_VOLUME', {
          x: contract?.volume?.x,
        });
      default:
        return [t(contract?.volume?.type), contract?.volume?.value].filter(Boolean).join(': ');
    }
  };

  const contractElements = [
    { name: 'Nazwa kontraktu', value: contract?.name },
    { name: 'Data początku zakupów', value: contract?.averageCalculation?.startDate },
    { name: 'Data zakończenia zakupów', value: contract?.averageCalculation?.endDate },
    { name: 'Model zakupów', value: t(contract?.purchaseModel) },
    {
      name: 'Zamówienie/Cena na dzień',
      value: priceReferenceTimes?.map((priceReferenceTime) => (
        <Text inherit>{priceReferenceTime}</Text>
      )),
    },
    { name: 'Co gdy nie kupi 100%?', value: t(contract?.actionIfNot100?.type) },
    { name: 'Liczba transz', value: t(contract?.quantity?.type) },
    {
      name: 'Wolumen na transzę',
      value: volumeFormatted(),
    },
    { name: 'Forma zlecenia', value: t(contract?.orderTypeParameters?.type) },
    { name: 'Powód odrzucenia', value: t(contract?.rejectionReason?.type) },
  ];

  const contractRows = contractElements.map((element) => (
    <Table.Tr key={element?.name}>
      <Table.Td w={200} className={classes.mainColumn}>
        {element?.name}
      </Table.Td>
      <Table.Td>{element?.value}</Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      <Container size="md" className={classes.detailsContainer}>
        <Table withTableBorder withColumnBorders layout={'fixed'}>
          <Table.Tbody>{contractRows}</Table.Tbody>
        </Table>
        <Button
          className={classes.returnButton}
          onClick={() => router.push(RouterPaths.AGREEMENTS_CONTRACTS(router.query.id as string))}
        >
          Powrót
        </Button>
      </Container>
    </>
  );
};

export default ContractDetails;
