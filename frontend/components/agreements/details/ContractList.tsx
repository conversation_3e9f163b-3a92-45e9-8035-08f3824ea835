import classes from './AgreementDetails.module.css';
import { <PERSON><PERSON>, Divider, Loader, Table, Title } from '@mantine/core';
import React from 'react';
import { useRouter } from 'next/router';
import { IconSearch } from '@tabler/icons-react';
import useContractList from '@/components/agreements/details/useContractList';
import { RouterPaths } from '@/services/shared/ApiEndpoints';

const ContractList = () => {
  const router = useRouter();

  const query = useContractList();

  if (query.isLoading || query.isLoading) {
    return <Loader color="orange" size="md" />;
  }

  const mainElements = [];

  const headerElements = [
    { name: '<PERSON>z<PERSON>' },
    { name: '<PERSON>ok kontraktacji' },
    { name: 'Data startu zakupów' },
    { name: 'Data końcu zakupów' },
    { name: 'Szczegóły' },
  ];

  const contractsHeader = headerElements.map((element) => (
    <Table.Th className={classes.mainRow}>{element?.name}</Table.Th>
  ));

  query?.data?.content?.map((contract) => {
    mainElements.push({
      name: contract?.name,
      year: contract?.year,
      startDate: contract?.averageCalculation?.startDate,
      endDate: contract?.averageCalculation?.endDate,
      id: contract?.id,
    });
  });

  const mainRows = mainElements.map((element) => (
    <Table.Tr key={element?.name}>
      <Table.Td>{element?.name}</Table.Td>
      <Table.Td>{element?.year}</Table.Td>
      <Table.Td>{element?.startDate}</Table.Td>
      <Table.Td>{element?.endDate}</Table.Td>
      <Table.Td>
        <IconSearch
          cursor={'pointer'}
          onClick={() => {
              router.push(RouterPaths.AGREEMENTS_CONTRACT_DETAILS(router.query.id as string, element?.id as string));
          }}
        />
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      <Table withTableBorder withColumnBorders layout={'fixed'}>
        <Table.Thead>
          <Table.Tr>{contractsHeader}</Table.Tr>
        </Table.Thead>
        <Table.Tbody>{mainRows}</Table.Tbody>
      </Table>
      <Button
        className={classes.returnButton}
        onClick={() => router.push(RouterPaths.AGREEMENTS_DETAILS(router.query.id as string))}
      >
        Powrót
      </Button>
    </>
  );
};

export default ContractList;
