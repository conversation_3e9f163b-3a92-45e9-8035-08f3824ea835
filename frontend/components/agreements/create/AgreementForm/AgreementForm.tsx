import { Button, Group, Stepper } from '@mantine/core';
import { FormProvider, useForm } from 'react-hook-form';
import Step1Fields from '@/components/agreements/create/AgreementForm/Step1Fields';
import Step2Fields from '@/components/agreements/create/AgreementForm/Step2Fields';
import { AgreementService } from '@/services/AgreementService';
import useWalletNotifications from '@/components/common/hooks/useWalletNotifications';
import { MediaType } from '@/types/MediaType';
import { FormType, PurchaseModel } from '@/types/Agreement';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/router';
import { useAgreementStore } from '@/stores/agreement-store/agreement-provider';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import React, { useState } from 'react';
import CopyModal from '@/components/agreements/create/AgreementForm/CopyModal';
import { Step1SchemaNew } from '@/components/agreements/create/AgreementForm/Step1SchemaNew';

export function AgreementForm() {
  const router = useRouter();
  const { step, setStep, resetStore, volumesList, setVolumesList } = useAgreementStore(
    (state) => state
  );
  const { successNotification, errorNotification } = useWalletNotifications();
  // const { volumesList, setVolumesList } = useVolumesStore();
  const [copyModalOpen, setCopyModalOpen] = useState(false);
  const [agreementIdList, setAgreementIdList] = useState([]);
  const methods = useForm({
    mode: 'onBlur',
    resolver: zodResolver(Step1SchemaNew),
    defaultValues: {
      formType: FormType.CUSTOMER,
      customer: '',
      customers: [],
      volumesList: [],
      mediaType: MediaType.ENERGY,
      authorizedBuyers: [],
      supplier: '',
      purchaseModel: PurchaseModel.PERCENTAGE,
      group: '',
      startDate: null,
      endDate: new Date().getFullYear() + '-12-31',
      averageCalculationStartDate: null,
      contracts: [],
      media: {
        purchaseModel: PurchaseModel.PERCENTAGE,
        availableProducts: [],
        priceReference: [],
        orderTime: {},
        actionIfNot100: '',
        quantity: {
          type: null,
          value: '',
        },
        finalPurchaseDate: {
          type: null,
          value: null,
        },
        volume: {
          type: null,
          value: null,
        },
        orderTypeParameters: {
          type: null,
          value: null,
        },
        rejectionReason: {
          type: null,
          value: null,
        },
      },
      propertyRights: {
        purchaseModel: '',
        comment: '',
        availableProducts: ['PMOZE_A'],
        priceReference: [],
        actionIfNot100: {
          type: null,
          value: null,
        },
        quantity: {
          type: 'OTHER',
          value: null,
        },
        finalPurchaseDate: {
          type: null,
          value: null,
        },
        volume: {
          type: null,
          value: null,
        },
        orderTypeParameters: {
          type: null,
          value: null,
        },
        rejectionReason: {
          type: null,
          value: null,
        },
      },
    },
  });

  const openCopyModal = () => {
    setCopyModalOpen(true);
  };

  const closeCopyModal = async () => {
    setCopyModalOpen(false);
    await router.push(RouterPaths.LIST);
  };

  async function onSubmit(values: any) {
    try {
      const data = await AgreementService.create(methods.getValues());
      setAgreementIdList(data.ids);
      successNotification({
        title: 'Sukces!',
        message: 'Umowa została zapisana!',
      });
      resetStore();
      openCopyModal();
    } catch (e: any) {
      errorNotification({
        title: 'Niepowodzenie',
        message: 'Zapis umowy nie powiódł się. Sprawdź błędy zaznaczone w formularzu.',
      });
    }
  }

  const handleNextStep = async () => {
    const isValid = await methods.trigger();
    if (isValid) {
      const volumes = methods.getValues('volumesList');
      setVolumesList(volumes);
      setStep(step + 1);
    }
  };

  const handlePreviousStep = () => {
    if (step > 0) {
      setStep(step - 1);
    }
    if (step === 1) {
      methods.setValue('contracts', []);
      if (volumesList.length > 0) {
        methods.reset({ ...methods.getValues(), volumesList });
      }
    }
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <Stepper active={step} allowNextStepsSelect={false}>
          <Stepper.Step label="Krok pierwszy" description="Dane podstawowe">
            <Step1Fields />
          </Stepper.Step>
          <Stepper.Step label="Krok drugi" description="Kontrakty">
            <Step2Fields />
          </Stepper.Step>
        </Stepper>
        <CopyModal
          copyModalOpen={copyModalOpen}
          closeCopyModal={closeCopyModal}
          setCopyModalOpen={setCopyModalOpen}
          agreementIdList={agreementIdList}
        />
        <Group mt="md">
          {step > 0 && (
            <Button
              variant="default"
              onClick={() => {
                handlePreviousStep();
              }}
            >
              Poprzedni krok
            </Button>
          )}
          {step < 1 && (
            <Button variant="default" onClick={() => handleNextStep().then()}>
              Następny krok
            </Button>
          )}

          {step == 1 && <Button type="submit">Zapisz</Button>}
        </Group>
      </form>
    </FormProvider>
  );
}
