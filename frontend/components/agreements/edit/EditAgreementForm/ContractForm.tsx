import { Form<PERSON>rovider } from 'react-hook-form';
import { Fieldset, Grid, Group } from '@mantine/core';
import MediaActionIfNot100Select from '@/components/common/forms/MediaActionIfNot100Select/MediaActionIfNot100Select';
import MediaQuantity from '@/components/common/forms/MediaQuantity/MediaQuantity';
import MediaVolume from '@/components/common/forms/MediaVolume/MediaVolume';
import OrderTypeSelect from '@/components/common/forms/OrderTypeSelect/OrderTypeSelect';
import MediaRejectionReasonSelect from '@/components/common/forms/MediaRejectionReasonSelect/MediaRejectionReasonSelect';
import TextInput from '@/components/common/forms/TextInput/TextInput';
import PurchaseModelSelect from '@/components/common/forms/PurchaseModelSelect/PurchaseModelSelect';
import DatePicker from '@/components/common/forms/DatePicker/DatePicker';
import EditPriceReference from '@/components/common/forms/PurchaseByPriceType/EditPriceReference';
import PropertyRightsPurchaseModelSelect from '@/components/common/forms/PropertyRights/PropertyRightsPurchaseModelSelect/PropertyRightsPurchaseModelSelect';
import PropertyRightsActionIfNot100Select from '@/components/common/forms/PropertyRights/PropertyRightsActionIfNot100Select/PropertyRightsActionIfNot100Select';
import React from 'react';
import PropertyRightsQuantityTypeSelect from '@/components/common/forms/PropertyRights/PropertyRightsQuantityTypeSelect/PropertyRightsQuantityTypeSelect';
import PropertyRightsQuantityValueInput from '@/components/common/forms/PropertyRights/PropertyRightsQuantityValueInput/PropertyRightsQuantityValueInput';
import PropertyRightsVolumeSelect from '@/components/common/forms/PropertyRights/PropertyRightsVolumeTypeSelect/PropertyRightsVolumeTypeSelect';

// @ts-ignore
export function ContractForm({ form, mediaType }) {
  const rights = ['PMOZE_A', 'PMOZE_BIO', 'PMEF_F'];
  const isPropertyContract = rights.indexOf(form.getValues()['name']) > -1;
  return (
    <FormProvider {...form}>
      <form>
        <Fieldset legend="Dane kontraktu" radius="md">
          <Grid style={{ width: '100%' }}>
            <Grid.Col span={3}>
              <TextInput
                label="Nazwa kontraktu"
                placeholder="Nazwa kontraktu"
                name="name"
                disabled={false}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePicker
                name="averageCalculation.startDate"
                label="Data początku zakupów"
                placeholder="Data początku zakupów"
                disabled={false}
                withAsterisk
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePicker
                name="averageCalculation.endDate"
                label="Data zakończenia zakupów"
                placeholder="Data zakończenia zakupów"
                disabled={false}
                withAsterisk
              />
            </Grid.Col>
            <Grid.Col span={3}>
              {!isPropertyContract && (
                <PurchaseModelSelect
                  name="purchaseModel"
                  label="Model zakupu"
                  placeholder="Wybierz model zakupu"
                  disabled={false}
                  withAsterisk
                />
              )}
              {isPropertyContract && (
                <PropertyRightsPurchaseModelSelect
                  name="purchaseModel"
                  label="Model zakupu"
                  placeholder="Wybierz model zakupu"
                  disabled={false}
                  withAsterisk
                />
              )}
            </Grid.Col>
            <Grid.Col span={3}>
              <EditPriceReference
                name="priceReference"
                label="Zamówienie/Cena na dzień"
                disabled={false}
                withAsterisk
              />
            </Grid.Col>
            <Grid.Col span={3}>
              {!isPropertyContract && (
                <MediaActionIfNot100Select
                  name="actionIfNot100"
                  label="Co gdy nie kupi 100%?"
                  placeholder="Co gdy nie kupi 100%?"
                  disabled={false}
                  withAsterisk
                />
              )}
              {isPropertyContract && (
                <PropertyRightsActionIfNot100Select
                  name="actionIfNot100"
                  label="Co gdy nie kupi 100%?"
                  placeholder="Co gdy nie kupi 100%?"
                  withAsterisk
                />
              )}
            </Grid.Col>
            <Grid.Col span={12}>
              <Group>
                {!isPropertyContract && (
                  <MediaQuantity
                    name="quantity"
                    label="Liczba transz"
                    placeholder="Liczba transz"
                    disabled={false}
                    withAsterisk
                  />
                )}
                {isPropertyContract && (
                  <>
                    <PropertyRightsQuantityTypeSelect
                      name="quantity.type"
                      label="Liczba transz"
                      placeholder="Liczba transz"
                      withAsterisk
                    />
                    <PropertyRightsQuantityValueInput
                      name="quantity.value"
                      label="Wartość min. X% max. Y%"
                      placeholder="Przykład: 1-4"
                      withAsterisk
                    />
                  </>
                )}
              </Group>
            </Grid.Col>
            <Grid.Col span={12}>
              <Group>
                {!isPropertyContract && (
                  <MediaVolume
                    name="volume"
                    label="Wolumen na transzę"
                    placeholder="Wolumen na transzę"
                    disabled={false}
                    withAsterisk
                  />
                )}
                {isPropertyContract && (
                  <PropertyRightsVolumeSelect
                    name="volume"
                    label="Wolumen na transzę"
                    placeholder="Wybierz typ"
                    withAsterisk
                  />
                )}
              </Group>
            </Grid.Col>
            <Grid.Col span={12}>
              <Group>
                <OrderTypeSelect
                  name="orderTypeParameters"
                  label="Forma zlecenia"
                  placeholder="Forma zlecenia"
                  disabled={false}
                  withAsterisk
                  mediaType={mediaType}
                />
              </Group>
            </Grid.Col>
            <Grid.Col span={12}>
              <Group>
                <MediaRejectionReasonSelect
                  name="rejectionReason"
                  label="Powód odrzucenia zakupu"
                  placeholder="Powód odrzucenia zakupu"
                  disabled={false}
                  withAsterisk
                />
              </Group>
            </Grid.Col>
          </Grid>
        </Fieldset>
      </form>
    </FormProvider>
  );
}

export default ContractForm;
