import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import 'mantine-react-table/styles.css';
import React, { useMemo, useState } from 'react';
import {
  MantineReactTable,
  MRT_ColumnFiltersState,
  type MRT_PaginationState,
  type MRT_SortingState,
  useMantineReactTable,
} from 'mantine-react-table';
import { Button, Group, Menu, Text } from '@mantine/core';
import { IconPencil } from '@tabler/icons-react';
import { LIST_DEFAULT_PAGE_SIZE } from '@/utils/defaults';
import router from 'next/router';
import { PERMISSIONS } from '@/utils/permissions';
import { useSupplierContactList } from '@/components/suppliers/contacts/ContactList/useSupplierContactList';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import IconButton from '@/components/common/forms/IconButton/IconButton';
import useTranslation from 'next-translate/useTranslation';
import { labels } from '@/utils/Labels';
import { ButtonMenu } from '@/components/common/layout/ButtonMenu/ButtonMenu';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { getSupplierContactExportHandlers } from './useExcelSupplierContactExport';

export default function SupplierContactList() {
  const { t } = useTranslation('common');

  const [sorting, setSorting] = useState<MRT_SortingState>([]);
  const [filters, setFilters] = useState<MRT_ColumnFiltersState>([]);
  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: LIST_DEFAULT_PAGE_SIZE,
  });

  const {
    data: contacts,
    isLoading,
    refetch,
  } = useSupplierContactList({
    sorting,
    pagination,
    filters,
  });

  const totalRowCount = contacts?.totalElements ?? 0;
  const fetchedContacts = contacts?.content ?? [];

  const columns = useMemo(
    () => [
      {
        accessorKey: 'supplierName',
        header: 'Sprzedawca',
      },
      {
        accessorKey: 'customerNames',
        header: 'Klient',
      },
      {
        accessorKey: 'fullName',
        header: 'Kontakt',
      },
      {
        accessorKey: 'number',
        header: 'Numer telefonu',
      },
      {
        accessorKey: 'email',
        header: 'Email',
      },
      {
        id: 'actions',
        header: 'Operacje',
        Cell: ({ row }: any) => (
          <Group>
            <RoleBasedComponent
              roles={{ resource: { wallet: [PERMISSIONS.UPDATE_SUPPLIER_CONTACT] } }}
            >
              <IconButton
                icon={IconPencil}
                onClick={() =>
                  router.push({
                    pathname: RouterPaths.SUPPLIER_EDIT(row.original.supplierId),
                    query: {
                      supplierName: row.original.supplierName,
                      fullName: row.original.fullName,
                    },
                  })
                }
                label={t(labels.EDIT)}
              />
            </RoleBasedComponent>
          </Group>
        ),
      },
    ],
    []
  );

  const { handleExportAll, handleExportVisible } = getSupplierContactExportHandlers({
    headers: ['Nazwa klienta', 'Sprzedawca', 'Kontakt', 'Telefon', 'E-mail'],
    sorting,
    filters,
    currentData: contacts,
  });

  const table = useMantineReactTable({
    columns,
    data: fetchedContacts,
    manualFiltering: true,
    manualSorting: false,
    manualPagination: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    initialState: {
      showColumnFilters: true,
      density: 'xs',
    },
    state: { isLoading, pagination, sorting },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    onColumnFiltersChange: setFilters,
    rowCount: totalRowCount,
  });

  return (
    <>
      <Group justify="flex-end" pb="md">
        <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.UPDATE_SUPPLIER_CONTACT] } }}>
          <Button color="orange" onClick={() => router.push(RouterPaths.SUPPLIERS_LIST)}>
            Dodaj kontakt
          </Button>
        </RoleBasedComponent>
        <ButtonMenu>
          <Menu.Item>
            <Text size={'sm'} onClick={handleExportAll}>
              Eksportuj wszystkie dane
            </Text>
          </Menu.Item>
          <Menu.Item>
            <Text size={'sm'} onClick={handleExportVisible}>
              Eksportuj obecne wiersze
            </Text>
          </Menu.Item>
        </ButtonMenu>
      </Group>
      <MantineReactTable table={table} />
    </>
  );
}
