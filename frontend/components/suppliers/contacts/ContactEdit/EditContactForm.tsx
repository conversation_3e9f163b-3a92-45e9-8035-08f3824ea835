import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { <PERSON><PERSON>, Button, Container, Group, Loader, TextInput } from '@mantine/core';
import { SupplierContactService } from '@/services/SupplierContactService';
import { CustomerService } from '@/services/CustomerService';
import {SupplierContactEdit} from "@/types/Supplier";
import CustomerMultiSelect from '@/components/common/forms/CustomerSelectOld/CustomerMultiSelect';
import { useRouter } from 'next/router';
import ConfirmModal from '@/components/common/forms/ConfirmModal/ConfirmModal';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { PERMISSIONS } from '@/utils/permissions';
import {RouterPaths} from "@/services/shared/ApiEndpoints";
import {notify} from "@/utils/notify";
import {NotificationType} from "@/types/Common";

interface EditContactFormProps {
  contactId: string;
}

const EditContactForm: React.FC<EditContactFormProps> = ({ contactId }) => {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<SupplierContactEdit>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customers, setCustomers] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const router = useRouter();
  const { supplierName, fullName } = router.query;
  const handleBack = () => router.back();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const contact = await SupplierContactService.getContactById(contactId);
        setValue('contactId', contact.contactId);
        setValue('supplierId', contact.supplierId);
        setValue('customerIds', contact.customerIds);

        const customersData = await CustomerService.getCustomers();
        if (customersData.content) {
          setCustomers(
            customersData.content.map((customer: any) => ({
              value: customer.id,
              label: customer.name,
            }))
          );
        } else {
          throw new Error('Invalid customers data format');
        }
      } catch (err) {
        console.error('Error fetching contact details:', err);
        setError('Failed to load contact details.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [contactId, setValue]);

  const onSubmit = async (data: SupplierContactEdit) => {
    setLoading(true);
    try {
      const updateData = {
        supplierId: data.supplierId,
        customerIds: data.customerIds,
      };
      await SupplierContactService.updateContact(contactId, updateData);
      notify(NotificationType.SUCCESS, 'Kontakt został zaktualizowany pomyślnie.');
    } catch (err) {
      setError('Failed to update contact.');
      notify(NotificationType.ERROR, 'Wystąpił błąd podczas aktualizacji kontaktu. Spróbuj ponownie.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    try {
      await SupplierContactService.deleteContact(contactId);
      router.push(RouterPaths.SUPPLIERS_CONTACTS_LIST);
    } catch (err) {
      setError('Failed to delete contact.');
    } finally {
      setLoading(false);
      setModalOpen(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return <Alert color="red">{error}</Alert>;
  }

  return (
    <Container>
      <form onSubmit={handleSubmit(onSubmit)}>
        <TextInput label="Sprzedawca" value={supplierName as string} disabled />
        <TextInput label="Kontakt" value={fullName as string} disabled />
        <Controller
          name="supplierId"
          control={control}
          render={({ field }) => <TextInput {...field} type="hidden" />}
        />
        <Controller
          name="contactId"
          control={control}
          render={({ field }) => <TextInput {...field} type="hidden" />}
        />
        <CustomerMultiSelect
          control={control}
          name="customerIds"
          label="Klienci"
          placeholder="Wybierz klientów"
          data={customers}
          error={errors.customerIds?.message}
        />
        <Group mt="sm">
          <RoleBasedComponent
            roles={{ resource: { wallet: [PERMISSIONS.UPDATE_SUPPLIER_CONTACT] } }}
          >
            <Button type="submit" color="green">
              Aktualizuj
            </Button>
          </RoleBasedComponent>
          <RoleBasedComponent
            roles={{ resource: { wallet: [PERMISSIONS.DELETE_SUPPLIER_CONTACT] } }}
          >
            <Button color="red" onClick={() => setModalOpen(true)}>
              Usuń
            </Button>
          </RoleBasedComponent>
          <Button type="button" color="blue" onClick={handleBack}>
            Powrót
          </Button>
        </Group>
      </form>

      <ConfirmModal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        onConfirm={handleDelete}
        title="Potwierdź usunięcie"
      >
        Czy na pewno chcesz usunąć ten kontakt?
      </ConfirmModal>
    </Container>
  );
};

export default EditContactForm;
