import { useState } from 'react';
import { PricesAndChartsService } from '@/services/PricesAndChartsService';
import { notify } from '@/utils/notify';
import { formatValue } from '@/utils/formatters';
import { NotificationType } from '@/types/Common';

const useSpotAverage = () => {
  const [loading, setLoading] = useState(false);
  const [overallData, setOverallData] = useState([]);
  const [monthlyData, setMonthlyData] = useState([]);

  const calculateAverages = async (startDate: string, endDate: string) => {
    setLoading(true);

    try {
      const contracts = ['TGe24', 'TGEgasDA', 'TGEgasID'];
      const { overall, monthly } = await PricesAndChartsService.fetchAverageSpot(
        startDate,
        endDate,
        contracts
      );

      const overallResult = overall.map((item) => ({
        instrument: item.contract,
        average: item.dataPoints > 0 ? `${formatValue(item.averagePrice)} zł` : 'Brak danych',
      }));

      const monthlyResult = monthly.map((item) => ({
        instrument: item.contract,
        average: item.dataPoints > 0 ? `${formatValue(item.averagePrice)} zł` : 'Brak danych',
        month: item.periodStart, // YYYY-MM-DD
      }));

      setOverallData(overallResult);
      setMonthlyData(monthlyResult);
      notify(NotificationType.SUCCESS, 'Średnie ceny zostały pomyślnie obliczone.');
    } catch (error) {
      console.error('Error fetching data:', error);
      notify(NotificationType.ERROR, 'Wystąpił błąd podczas obliczania średnich cen.');
    } finally {
      setLoading(false);
    }
  };

  return { loading, overallData, monthlyData, calculateAverages };
};

export default useSpotAverage;