import { Group, Select } from '@mantine/core';
import { Controller, useFormContext } from 'react-hook-form';
import { getNestedValue } from '@/utils/get-nested-value';
import React from 'react';
import GreenPropertyIcon from '@/components/common/forms/GreenPropertyIcon/GreenPropertyIcon';

interface GreenPropertyRightsCalculationTypeSelectProps {
  name: string;
  label: string;
  placeholder: string;
  withAsterisk: boolean;
  maxValues: number;
  disabled: boolean;
}

const GreenPropertyRightsCalculationTypeSelect: React.FC<
  Partial<GreenPropertyRightsCalculationTypeSelectProps>
> = ({ name, label, placeholder, withAsterisk, maxValues = Infinity, disabled = false }) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const options = [
    { value: 'ENEA', label: 'Enea' },

    {
      value: 'ENERGA_COMPLEX',
      label: ' Energa wzór 1',
    },
    {
      value: 'ENERGA_SIMPLE',
      label: 'Energa wzór 2',
    },
    { value: 'EON', label: 'E.ON' },
    {
      value: 'PGE',
      label: 'PGE',
    },
    {
      value: 'TIEW',
      label: 'TIEW',
    },
  ];
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <div style={{ width: '100%' }}>
          {label && (
            <Group justify="space-between">
              <span style={{fontSize: '0.875rem', fontWeight:500}}>{label}</span>
              <GreenPropertyIcon />
            </Group>
          )}
          <Select
            {...field}
            withAsterisk={withAsterisk}
            data={options}
            placeholder={placeholder}
            disabled={disabled}
            onChange={(value) => field.onChange(value)}
            value={field.value}
            error={getNestedValue(errors, name)?.message}
            searchable
            style={{ width: '100%' }}
          />
        </div>
      )}
    />
  );
};

export default GreenPropertyRightsCalculationTypeSelect;
