import { Dropzone, FileWithPath } from '@mantine/dropzone';
import classes from './CustomDropzone.module.css';
import { ActionIcon, Divider, Group, rem, Table, Text, useMantineTheme } from '@mantine/core';
import { IconCloudUpload, IconDownload, IconSearch, IconTrash, IconX } from '@tabler/icons-react';
import React, { useEffect, useState } from 'react';
import { API } from '@/utils/api';
import { AttachmentService } from '@/services/AttachmentService';

export function CustomDropzone(props: any) {
  const [localFiles, setLocalFiles] = useState([]);
  const MAX_SIZE = 30_000_000; // in bytes
  const theme = useMantineTheme();

  async function onDrop(files: any) {
    setLocalFiles(files);
  }

  useEffect(() => {
    const uploadAttachments = async (files: FileWithPath[]) => {
      let formData = new FormData();
      files.forEach((file, idx, arr) => formData.append(`files`, file));
      const response = await API.post('/api/attachments', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data.attachments;
    };
    if (localFiles.length > 0) {
      uploadAttachments(localFiles).then((f) => {
        setLocalFiles(f);
        props.onDrop(f);
      });
    }
  }, [localFiles]);

  async function deleteAttachment(file: any) {
    const response = await API.delete(`/api/attachments/${file.id}`);
    let idx = localFiles.indexOf(file as never);
    localFiles.splice(idx, 1);
    setLocalFiles(localFiles);
    props.onRemoveFile(file);
  }

  let selectedFilesTable = props.attachments?.map((file: any, _: number) => (
    <Table.Tr key={file.name}>
      <Table.Td>{file.id}</Table.Td>
      <Table.Td>{file.name}</Table.Td>
      <Table.Td>
        <ActionIcon
          size="md"
          onClick={async () => {
            await AttachmentService.downloadFileById(file.id, file.name);
          }}
          style={{ marginRight: '10px' }}
        >
          <IconSearch size={24} stroke={1.5} />
        </ActionIcon>
        <ActionIcon size="md" color="red" onClick={() => deleteAttachment(file)}>
          <IconTrash size={24} stroke={1.5} />
        </ActionIcon>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      {selectedFilesTable && selectedFilesTable.length > 0 && (
        <>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Id</Table.Th>
                <Table.Th>Nazwa pliku</Table.Th>
                <Table.Th>Operacje</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>{selectedFilesTable}</Table.Tbody>
          </Table>
          <Divider />
        </>
      )}
      <Dropzone
        className={classes.dropzone}
        radius="md"
        maxSize={MAX_SIZE}
        multiple
        onDrop={onDrop}
        pt="lg"
      >
        <div style={{ pointerEvents: 'none' }}>
          <Group justify="center">
            <Dropzone.Accept>
              <IconDownload
                style={{ width: rem(50), height: rem(50) }}
                color={theme.colors.blue[6]}
                stroke={1.5}
              />
            </Dropzone.Accept>
            <Dropzone.Reject>
              <IconX
                style={{ width: rem(50), height: rem(50) }}
                color={theme.colors.red[6]}
                stroke={1.5}
              />
            </Dropzone.Reject>
            <Dropzone.Idle>
              <IconCloudUpload style={{ width: rem(50), height: rem(50) }} stroke={1.5} />
            </Dropzone.Idle>
          </Group>

          <Text ta="center" fw={700} fz="lg" mt="xl">
            <Dropzone.Accept>Upuść pliki tutaj</Dropzone.Accept>
            <Dropzone.Reject>Plik nie może być większy niż 50 mb.</Dropzone.Reject>
            <Dropzone.Idle>Załączniki</Dropzone.Idle>
          </Text>
          <Text ta="center" fz="sm" mt="xs" c="dimmed">
            Przeciągnij pliki, które chcesz dołączyć jako załączniki do dodawanego klienta.
            Maksymalny dopuszczalny rozmiar jednego pliku to 50mb.
          </Text>
        </div>
      </Dropzone>
    </>
  );
}
