import React, { useEffect, useState } from 'react';
import { Autocomplete } from '@mantine/core';
import { useFormContext } from 'react-hook-form';
import { AgreementService } from '@/services/AgreementService';
import { AgreementGroupSelectProps } from '@/types/Agreement';
import { getNestedValue } from '@/utils/get-nested-value';

const EditableAgreementGroupSelect: React.FC<AgreementGroupSelectProps> = ({ name, label }) => {
  const { setValue, watch } = useFormContext();
  const [options, setOptions] = useState<{ value: string; label: string }[]>([]);
  const selectedGroup = watch(name);
  const {
    control,
    formState: { errors },
  } = useFormContext();
  useEffect(() => {
    const fetchAgreementGroups = async () => {
      try {
        const response = await AgreementService.getAgreementGroups();
        const groups = response.content.map((group) => ({ value: group.id, label: group.name }));
        setOptions(groups);
      } catch (error) {
        console.error('Error fetching agreement groups:', error);
      }
    };

    fetchAgreementGroups();
  }, []);

  const handleChange = (value: string) => {
    const existingGroup = options.find((option) => option.label === value);

    if (existingGroup) {
      setValue(name, { id: existingGroup.value, name: existingGroup.label });
    } else if (value === '') {
      setValue(name, null);
    } else {
      setOptions([...options, { value, label: value }]);
      setValue(name, { id: '', name: value });
    }
  };

  return (
    <Autocomplete
      label={label}
      placeholder="Wybierz nazwę grupy umów lub wpisz nową"
      data={options}
      error={getNestedValue(errors, name)?.message}
      value={selectedGroup?.name}
      onChange={handleChange}
    />
  );
};

export default EditableAgreementGroupSelect;
