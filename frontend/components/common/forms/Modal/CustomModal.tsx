import React from 'react';
import { Button, Group, Modal, ModalProps } from '@mantine/core';
import styles from './CustomModal.module.css';

interface ConfirmModalProps extends Partial<ModalProps> {
  opened: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  title?: string;
  children: React.ReactNode;
  confirmLabel?: string;
  cancelLabel?: string;
  showButtons?: boolean;
}

const CustomModal: React.FC<ConfirmModalProps> = ({
  opened,
  onClose,
  onConfirm,
  title,
  children,
  confirmLabel = 'Zapisz',
  cancelLabel = 'Anuluj',
  showButtons = true,
  size = 'md',
  radius = 'sm',
  classNames,
  ...modalProps
}) => {
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={title}
      size={size}
      radius={radius}
      classNames={{
        header: styles.modalHeader,
        body: styles.modalBody,
        inner: styles.modalContent,
        ...classNames,
      }}
      {...modalProps}
    >
      {children}

      {showButtons && (
        <Group style={{ justifyContent: 'flex-end', gap: '8px' }} mt="md">
          {onConfirm && (
            <Button onClick={onConfirm} style={{ marginRight: '8px' }}>
              {confirmLabel}
            </Button>
          )}
          <Button color="red" onClick={onClose}>
            {cancelLabel}
          </Button>
        </Group>
      )}
    </Modal>
  );
};

export default CustomModal;
