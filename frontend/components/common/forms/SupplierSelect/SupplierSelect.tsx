import { Title, Text, Anchor, Select, Loader } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';

import { GroupService } from '@/services/GroupService';
import { Controller, useFormContext } from 'react-hook-form';
import { SupplierService } from '@/services/SupplierService';
import { getNestedValue } from '@/utils/get-nested-value';

const SupplierSelect: React.FC<Partial<BaseInputProps>> = ({
  name,
  label,
  placeholder,
  withAsterisk,
  disabled = false,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const query = useQuery({
    queryKey: ['suppliers-select'],
    staleTime: 5 * 60 * 1000,
    gcTime: 0,
    queryFn: async () => {
      let data = await SupplierService.fetchSuppliers({
        pagination: {
          pageIndex: 0,
          pageSize: 9999,
        },
      });

      if (!data && data.size <= 0) {
        return [];
      }
      return data.content.map((group) => ({
        value: group.id,
        label: group.name,
      }));
    },
  });
  if (query.isLoading || query.isFetching) {
    return <Loader color="orange" size="md" />;
  }
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Select
          {...field}
          withAsterisk={withAsterisk}
          label={label}
          data={query?.data}
          searchable
          placeholder={placeholder}
          disabled={disabled}
          onChange={(value) => field.onChange(value)}
          value={field.value}
          error={getNestedValue(errors, name)?.message}
        />
      )}
    />
  );
};
export default SupplierSelect;
