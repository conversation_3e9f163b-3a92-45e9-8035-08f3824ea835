interface BreadcrumbEntry {
  label: string;
  href: string;
}

export const HOME: BreadcrumbEntry = { label: 'Strona główna', href: '/' };
export const CUSTOMER: BreadcrumbEntry = { label: 'Klient', href: '/customers/list' };

export const CUSTOMER_CREATE: BreadcrumbEntry = {
  label: 'Formularz tworzenia',
  href: '/customers/create',
};
export const CUSTOMER_EDIT: BreadcrumbEntry = {
  label: 'Formularz edycji',
  href: '/customers/edit',
};

export const CONTACT: BreadcrumbEntry = { label: 'Kontakty', href: '/contacts/list' };

export const CONTACT_CREATE: BreadcrumbEntry = { label: 'Klient', href: '/contacts/create' };

export const AGREEMENT: BreadcrumbEntry = { label: 'Umowa', href: '/agreements/list' };

export const AGREEMENT_CREATE: BreadcrumbEntry = { label: 'Tworzenie', href: '/agreements/create' };

export const WALLET: BreadcrumbEntry = { label: 'Portfel', href: '/wallets/list' };

export const WALLET_CREATE: BreadcrumbEntry = {
  label: 'Formularz tworzenia',
  href: '/wallets/create',
};

export const WALLET_TRANCHE_CREATE: BreadcrumbEntry = {
  label: 'Formularz tworzenia',
  href: '/wallets/add-tranche',
};

export const WALLET_SIMULATION: BreadcrumbEntry = {
  label: 'Symulacja portfela',
  href: '/wallets/simulation',
};

export const USERS: BreadcrumbEntry = { label: 'Konta pracowników', href: '/users/list' };
