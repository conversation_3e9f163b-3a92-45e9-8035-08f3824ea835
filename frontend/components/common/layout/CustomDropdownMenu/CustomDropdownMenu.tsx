import {Menu, rem, Text} from "@mantine/core";
import {IconDots} from '@tabler/icons-react';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import styles from './CustomDropdownMenu.module.css';

const MenuItemWithIcon = ({ icon: Icon, onClick, children }) => (
    <Menu.Item
        leftSection={<Icon className={styles.icon} />}
        onClick={(event) => {
            event.stopPropagation();
            onClick();
        }}
    >
        {children}
    </Menu.Item>
);

const CustomDropdownMenu = ({ label, menuItems }) => (
    <Menu shadow="xl">
        <Menu.Target>
            <div onClick={(e) => e.stopPropagation()} className={styles.dropdownTarget}>
                <IconDots className={styles.iconDots} />
            </div>
        </Menu.Target>
        <Menu.Dropdown>
            {label && (
                <Menu.Label>
                    <Text size="xs">{label}</Text>
                </Menu.Label>
            )}
            {menuItems.map((item, index) => (
                item?.roles ? (
                    <RoleBasedComponent key={index} roles={item.roles}>
                        <MenuItemWithIcon icon={item.icon} onClick={item.onClick}>
                            {item.label}
                        </MenuItemWithIcon>
                    </RoleBasedComponent>
                ) : (
                    <MenuItemWithIcon icon={item.icon} onClick={item.onClick} key={index}>
                        {item.label}
                    </MenuItemWithIcon>
                )
            ))}
        </Menu.Dropdown>
    </Menu>
);

export default CustomDropdownMenu;