import { Button } from '@mantine/core';
import { FullScreenLoader } from './FullScreenLoader';
import { useState } from 'react';

const KEYCLOAK_BASE_URL = process.env.NEXT_PUBLIC_KEYCLOAK_BASE_URL!;
const REALM = process.env.NEXT_PUBLIC_KEYCLOAK_REALM!;
const CLIENT_ID = process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID!;
const REDIRECT_URI = process.env.NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI!;

const buildEntraLoginUrl = () => {
  const params = new URLSearchParams({
    client_id: CLIENT_ID,
    redirect_uri: REDIRECT_URI,
    response_type: 'code',
    scope: 'openid',
    kc_idp_hint: 'oidc',
  });

  return `${KEYCLOAK_BASE_URL}/realms/${REALM}/protocol/openid-connect/auth?${params.toString()}`;
};

const EntraLoginButton = () => {
  const [loading, setLoading] = useState(false);
  const handleLogin = () => {
    setLoading(true);
    window.location.href = buildEntraLoginUrl();
  };

  return (
    <>
      {loading && <FullScreenLoader label="Logowanie przy pomocy Entra ID…" />}
      <Button variant="outline" color="gray" fullWidth mt="md" onClick={handleLogin}>
        Zaloguj się przez Entra ID
      </Button>
    </>
  );
};

export default EntraLoginButton;
