import {
  IconAt,
  IconBuilding,
  IconChartLine, IconFileBroken,
  IconFileDescription,
  IconFolder,
  IconUsers,
} from '@tabler/icons-react';
import { LinksGroupProps } from '../MenuLinksGroup/MenuLinksGroup';
import { PERMISSIONS } from '@/utils/permissions';

const entries: LinksGroupProps[] = [
  {
    label: 'Klient',
    icon: IconBuilding,
    initiallyOpened: false,
    notifications: 0,
    links: [
      {
        label: 'Rejestracja klienta',
        link: '/customers/create',
        roles: { resource: { wallet: [PERMISSIONS.CREATE_CUSTOMER] } },
      },
      {
        label: 'Lista klientów',
        link: '/customers/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_CUSTOMER] } },
      },
      {
        label: 'Zarządzanie kontaktami',
        link: '/customers/contacts/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_CUSTOMER_CONTACT] } },
      },
    ],
  },
  {
    label: 'Umowy',
    icon: IconFileDescription,
    notifications: 0,
    links: [
      {
        label: 'Lista umów',
        link: '/agreements/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_AGREEMENT] } },
      },
      {
        label: 'Rejestrowanie umowy',
        link: '/agreements/create',
        roles: { resource: { wallet: [PERMISSIONS.CREATE_AGREEMENT] } },
      },
    ],
  },
  {
    label: 'Sprzedawcy',
    icon: IconUsers,
    links: [
      {
        label: 'Lista sprzedawców',
        link: '/suppliers/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_SUPPLIER] } },
      },
      {
        label: 'Opiekunowie',
        link: '/suppliers/contacts/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_SUPPLIER_CONTACT] } },
      },
    ],
  },
  {
    label: 'Portfele',
    icon: IconFolder,
    initiallyOpened: true,
    notifications: 0,
    links: [
      {
        label: 'Lista portfeli',
        link: '/wallets/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_WALLET] } },
      },
      {
        label: 'Lista realizacji',
        link: '/wallets/realisations/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_WALLET_REALISATION] } },
      },
      {
        label: 'Rejestracja portfela',
        link: '/wallets/create',
        roles: { resource: { wallet: [PERMISSIONS.CREATE_WALLET] } },
      },
      {
        label: 'Lista rekomendacji',
        link: '/wallets/recommendations/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_RECOMMENDATION] } },
      },
      {
        label: 'Przegląd zakupów',
        link: '/wallets/annual-purchase-overview',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_WALLET_REALISATION] } },
      },
      {
        label: 'Potwierdzanie cen',
        link: '/wallets/price-confirmation',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_WALLET] } },
      },
      {
        label: 'Dodawanie wielu transz',
        link: '/wallets/add-tranche',
        roles: { resource: { wallet: [PERMISSIONS.UPDATE_WALLET] } },
      },
      {
        label: 'Symulacje',
        link: '/wallets/simulation',
        roles: { resource: { wallet: [PERMISSIONS.SIMULATE_WALLET] } },
      },
    ],
  },
  {
    label: 'Newsletter',
    icon: IconAt,
    notifications: 0,
    links: [
      {
        label: 'Lista wysłanych',
        link: '/newsletter/list',
        roles: { resource: { wallet: [PERMISSIONS.VIEW_NEWSLETTER] } },
      },
      {
        label: 'Wyślij newsletter',
        link: '/newsletter/send',
        roles: { resource: { wallet: [PERMISSIONS.CREATE_NEWSLETTER] } },
      },
    ],
  },
  {
    label: 'Ceny i wykresy',
    icon: IconChartLine,
    link: '/prices-and-charts/list',
    notifications: 0,
    roles: { resource: { wallet: [] } },
  },
  {
    label: 'Dashboard maklera',
    icon: IconFileBroken,
    link: '/broker-dashboard/list',
    notifications: 0,
    roles: { resource: { wallet: [] } },
  },
];

export default entries;
