import CircleProgress from '../CircleProgress/CircleProgress';
import styles from './RealisationDetailsTable.module.css';
import {formatValue} from "@/utils/formatters";

type RealisationDetailsTableProps = {
  purchased: number;
  contracts: {
    Y: number;
    Q: number;
    M: number;
    TGe24?: number;
    TGEgasID?: number;
    TGEgasDA?: number;
  };
  clientMean: number;
  marketMean: number;
  benchmark: number;
  propertyRightVariant?: boolean;
};


const RealisationDetailsTable = ({
  purchased,
  contracts,
  clientMean,
  marketMean,
  benchmark,
  propertyRightVariant = false,
}: RealisationDetailsTableProps) => {

  return (
    <table className={styles.container}>
      <thead>
        <tr>
          <th>Zakupiono</th>
          <th>Kontrakt</th>
          {!propertyRightVariant && <th>Kupione</th>}
          <th>Średnia cena zakupu</th>
          <th>Średnia rynkowa</th>
          <th>Rezultat</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td rowSpan={4}>
            <CircleProgress size={100} percentage={purchased} />
          </td>
          {propertyRightVariant ? <td rowSpan={4}>PMOZE_A</td> : <td>Y</td>}
          {!propertyRightVariant && <td>{`${contracts.Y}%`}</td>}
          <td rowSpan={4}>{`${formatValue(clientMean)} zł`}</td>
          <td rowSpan={4}>{`${formatValue(marketMean)} zł`}</td>
          <td rowSpan={4}>{`${formatValue(benchmark)} zł`}</td>
        </tr>
        {!propertyRightVariant && (
          <>
            <tr>
              <td>Q</td>
              <td>{`${contracts.Q}%`}</td>
            </tr>
            <tr>
              <td>M</td>
              <td>{`${contracts.M}%`}</td>
            </tr>
            <tr>
              <td>SPOT</td>
              <td>{`${contracts.TGe24 ?? contracts.TGEgasID ?? contracts.TGEgasDA}%`}</td>{' '}
            </tr>
          </>
        )}
      </tbody>
    </table>
  );
};

export default RealisationDetailsTable;
