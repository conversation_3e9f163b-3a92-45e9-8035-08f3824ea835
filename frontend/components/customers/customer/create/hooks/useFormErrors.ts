import { notify } from '@/utils/notify';
import { NotificationType } from '@/types/Common';

export default function useFormErrors(form: any = null) {
  function handleFormErrors(e: any) {
    if (e.response?.data?.messages?.length > 0) {
      for (let message of e.response.data.messages) {
        let errors = {};
        // @ts-ignore
        errors[message.field] = message.reason;
        form.setErrors(errors);
        notify(NotificationType.ERROR, message.reason);
      }
    }
  }

  function handleErrorNotifications(e: any) {
    if (e?.response?.data?.detail) {
      notify(NotificationType.ERROR, e?.response?.data?.detail);
    }
    if (e.response?.data?.messages?.length > 0) {
      for (let message of e.response.data.messages) {
        let errors = {};
        // @ts-ignore
        notify(NotificationType.ERROR, message.reason);
      }
    }
  }

  return { handleFormErrors, handleErrorNotifications };
}
