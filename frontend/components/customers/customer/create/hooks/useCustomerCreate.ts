import {FormEvent} from "react";
import {CustomerService} from "@/services/CustomerService";
import {useForm} from "@mantine/form";
import {zodResolver} from "mantine-form-zod-resolver";
import useWalletNotifications from "@/components/common/hooks/useWalletNotifications";
import {
  createCustomerFormSchema,
  CreateCustomerFormValues,
  customerFormValuesDefaultValues
} from "@/components/customers/customer/create/schema";
import useFormErrors from "@/components/customers/customer/create/hooks/useFormErrors";
import {Customer} from "@/types/Customer";
import {useRouter} from "next/router";
import {RouterPaths} from "@/services/shared/ApiEndpoints";


export default function useCustomerCreate() {
    const router = useRouter();
    const form = useForm<CreateCustomerFormValues>({
        mode: 'controlled',
        validateInputOnChange: true,
        validateInputOnBlur: true,
        initialValues: customerFormValuesDefaultValues(),
        validate: zodResolver(createCustomerFormSchema),
    });

    form.initialize({
      segment: '3',
    })

    const {successNotification, errorNotification} = useWalletNotifications();
    const {handleFormErrors} = useFormErrors(form);

    async function onSubmit(values: any, event: FormEvent<HTMLFormElement> | undefined) {
        try {
            const data = await CustomerService.create(values as Customer);
            successNotification({
                id: 'create-customer-notification',
                title: 'Sukces!',
                message: "Klient został zapisany!"
            })
            await router.push(RouterPaths.CUSTOMERS_LIST)
            // redirect?
        } catch (e: any) {
            handleFormErrors(e)
            errorNotification(
                {
                    id: 'create-customer-notification',
                    title: 'Niepowodzenie',
                    message: 'Zapis klienta nie powiódł się. Sprawdź błędy zaznaczone w formularzu.',
                }
            );
        }
    }

    async function onRemoveFile(file: any) {
        form.setFieldValue(
            'attachments',
            form.getValues().attachments.filter((_: any, i: number) => _ !== file)
        );
    }

    async function onDrop(attachments: []) {
        form.setFieldValue('attachments', attachments);
    }

    return {
        form, onSubmit, onRemoveFile, onDrop
    }

}