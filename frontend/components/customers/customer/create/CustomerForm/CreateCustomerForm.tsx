import useCustomerCreate from '@/components/customers/customer/create/hooks/useCustomerCreate';
import CreateCustomerFields from '@/components/customers/customer/common/CreateCustomerFields/CreateCustomerFields';
import { Button, Fieldset, Group } from '@mantine/core';
import CustomerRemunerationFields from '@/components/customers/customer/common/CustomerRemuneration/CustomerRemunerationFields';

export function CreateCustomerForm() {
  const { form, onSubmit, onRemoveFile, onDrop } = useCustomerCreate();

  return (
    <form onSubmit={form.onSubmit(onSubmit)}>
      <CreateCustomerFields form={form} onDrop={onDrop} onRemoveFile={onRemoveFile} />

      <Fieldset legend="Wynagrodzenia" radius="md" mt="md">
        <CustomerRemunerationFields form={form} />
      </Fieldset>

      <Group justify="flex-end" mt="md">
        <Button type="submit">Zapisz</Button>
      </Group>
    </form>
  );
}
