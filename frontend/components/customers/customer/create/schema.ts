import { z } from 'zod';
import { validatePolish } from 'validate-polish';

const remunerationSchema = z
  .object({
    id: z.string().optional(),
    carrier: z.string().min(1, 'Nośnik jest wymagany'),
    period: z.object({
      from: z.any().refine((val) => !!val, 'Data od jest wymagana'),
      to: z.any().refine((val) => !!val, 'Data do jest wymagana'),
    }),
    cap: z.string().optional(),
    capUnit: z.string().optional(),
    fixedRemunerationType: z.string().optional(),
    fixedRemunerationValue: z.string().optional(),
    remunerationType: z.string().optional(),
    remunerationValue: z.string().optional(),
    tenderCost: z.string().optional(),
    substitutionFee: z.string().optional(),
    marketPmozea: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.cap && !data.capUnit) {
        return false;
      }
      return true;
    },
    {
      message: 'Jednostka (capUnit) jest wymagana, gdy CAP jest wypełniony.',
      path: ['capUnit'],
    }
  )
  .refine(
    (data) => {
      if (data.fixedRemunerationType && !data.fixedRemunerationValue) {
        return false;
      }
      return true;
    },
    {
      message: 'Wartość wynagrodzenia stałego jest wymagana, gdy wybrano jego typ.',
      path: ['fixedRemunerationValue'],
    }
  )
  .refine(
    (data) => {
      if (data.remunerationType && !data.remunerationValue) {
        return false;
      }
      return true;
    },
    {
      message: 'Wartość wynagrodzenia zmiennego jest wymagana, gdy wybrano jego typ.',
      path: ['remunerationValue'],
    }
  );

export const createCustomerFormSchema = z.object({
  name: z
    .string({
      required_error: 'Nazwa klienta jest wymagana',
      invalid_type_error: 'Nazwa klienta jest wymagana',
    })
    .min(3, { message: 'Nazwa klienta nie powinna być krótsza niż 3 znaki' })
    .max(100, { message: 'Nazwa klienta nie powinna być dłuższa niż 100 znaków' }),

  taxNumber: z
    .string({
      required_error: 'NIP jest wymagany',
      invalid_type_error: 'NIP jest wymagany',
    })
    .refine((val) => validatePolish.nip(val), { message: 'Niepoprawny NIP' }),

  segment: z
    .string({
      required_error: 'Segment jest wymagany',
      invalid_type_error: 'Segment jest wymagany',
    })
    .min(1, { message: 'Proszę wybrać segment klienta' }),

  country: z
    .string({
      required_error: 'Proszę wybrać kraj',
      invalid_type_error: 'Proszę wybrać kraj',
    })
    .min(1, { message: 'Proszę wybrać kraj' })
    .optional(),

  configuration: z.object({
    wallet: z.boolean().optional(),
    realisation: z.boolean().optional(),
    simulation: z.boolean().optional(),
    recommendation: z.boolean().optional(),
  }),

  attachments: z.array(z.object({}).optional()).optional(),

  remunerations: z
    .array(remunerationSchema)
    .optional()
    .superRefine((remunerations, ctx) => {
      if (!remunerations || remunerations.length === 0) return;

      for (let i = 0; i < remunerations.length; i++) {
        const current = remunerations[i];
        if (!current.period?.from || !current.period?.to) continue;

        const currentFrom = new Date(current.period.from).getTime();
        const currentTo = new Date(current.period.to).getTime();

        for (let j = i + 1; j < remunerations.length; j++) {
          const next = remunerations[j];
          if (current.carrier !== next.carrier) continue;
          if (!next.period?.from || !next.period?.to) continue;

          const nextFrom = new Date(next.period.from).getTime();
          const nextTo = new Date(next.period.to).getTime();

          const overlap = currentFrom <= nextTo && nextFrom <= currentTo;
          if (overlap) {
            const message = 'Okres rozliczeniowy nie może się pokrywać dla tego samego nośnika.';
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message,
              path: [i, 'period', 'from'],
            });
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message,
              path: [i, 'period', 'to'],
            });
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message,
              path: [j, 'period', 'from'],
            });
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message,
              path: [j, 'period', 'to'],
            });
          }
        }
      }
    }),
});

export type CreateCustomerFormValues = z.infer<typeof createCustomerFormSchema>;

export interface CustomerFormValues {
  id: string;
  name: string;
  taxNumber: string;
  segment: string;
  country: string;
  configuration: {
    wallet: boolean;
    realisation: boolean;
    simulation: boolean;
    recommendation: boolean;
  };
  group: string;
  attachments: [];
  contacts: [];
  notes: [];
  remunerations?: any[];
}

export function customerFormValuesDefaultValues(): CustomerFormValues {
  return {
    id: '',
    name: '',
    taxNumber: '',
    segment: '',
    country: 'PL',
    configuration: {
      wallet: false,
      realisation: false,
      simulation: false,
      recommendation: false,
    },
    group: '',
    attachments: [],
    contacts: [],
    notes: [],
    remunerations: [],
  };
}
