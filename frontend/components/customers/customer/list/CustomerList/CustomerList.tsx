import React, { useMemo, useState } from 'react';
import {
  MantineReactTable,
  MRT_ColumnDef,
  MRT_PaginationState,
  useMantineReactTable,
} from 'mantine-react-table';
import { ActionIcon, Button, Group, Menu, Text, Tooltip } from '@mantine/core';
import { useCustomerList } from '@/components/customers/customer/list/CustomerList/useCustomerList';
import { IconPencil, IconRefresh, IconTrash } from '@tabler/icons-react';
import router from 'next/router';
import { LIST_DEFAULT_PAGE_SIZE } from '@/utils/defaults';
import { PERMISSIONS } from '@/utils/permissions';
import CustomerSegmentDisplay from '@/components/groups/common/CustomerSegmentDisplay/CustomerSegmentDisplay';
import { Segment } from '@/types/Customer';
import ConfirmModal from '@/components/common/forms/ConfirmModal/ConfirmModal';
import { useCustomerListModal } from '@/components/customers/customer/list/CustomerList/useCustomerListModal';
import { CustomerService } from '@/services/CustomerService';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import IconButton from '@/components/common/forms/IconButton/IconButton';
import useTranslation from 'next-translate/useTranslation';
import { labels } from '@/utils/Labels';
import useExcelListExport from '@/components/common/hooks/useExcelExport';
import { ButtonMenu } from '@/components/common/layout/ButtonMenu/ButtonMenu';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';

const CustomerList = () => {
  const { t } = useTranslation('customer');

  const [sorting, setSorting] = useState([]);
  const [filters, setFilters] = useState([]);
  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: LIST_DEFAULT_PAGE_SIZE,
  });

  const {
    data: customers,
    isLoading,
    refetch,
  } = useCustomerList({
    sorting,
    pagination,
    filters,
  });

  const { isModalOpen, openModal, closeModal, selectedCustomerId, setSelectedCustomerId } =
    useCustomerListModal();

  const confirmDelete = async () => {
    if (selectedCustomerId) {
      await CustomerService.deleteCustomer(selectedCustomerId);
      await refetch();
      closeModal();
    }
  };

  const totalRowCount = customers?.totalElements ?? 0;

  const segmentList = useMemo(
    () =>
      Object.keys(Segment).map((segment) => ({
        value: Segment[segment],
        label: t(`segments.${segment}`),
      })),
    [t]
  );

  const columns = useMemo<MRT_ColumnDef<any>[]>(
    () => [
      { accessorKey: 'id', header: 'ID Klienta' },
      {
        accessorKey: 'name',
        header: 'Nazwa klienta',
        enableSorting: true,
        enableColumnFilter: true,
      },
      {
        accessorKey: 'segment',
        header: 'Segment',
        enableSorting: true,
        enableColumnFilter: true,
        filterVariant: 'select',
        mantineFilterSelectProps: {
          data: segmentList,
        },
        Cell: ({ cell }) => <CustomerSegmentDisplay segmentCode={String(cell.getValue())} />,
      },
      {
        accessorKey: 'taxNumber',
        header: 'NIP',
        enableSorting: false,
        enableColumnFilter: false,
      },
      {
        accessorKey: 'country.name',
        header: 'Kraj',
        enableSorting: false,
        enableColumnFilter: false,
        Cell: ({ cell }) => t(String(cell.getValue())) || String(cell.getValue()),
      },
      {
        accessorKey: 'country.code',
        header: 'Kod kraju',
        enableSorting: false,
        enableColumnFilter: false,
      },
      {
        header: 'Operacje',
        Cell: ({ row }) => (
          <Group>
            <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.UPDATE_CUSTOMER] } }}>
              <IconButton
                icon={IconPencil}
                onClick={() => router.push(RouterPaths.CUSTOMERS_EDIT(row.original.id))}
                label={t(labels.EDIT)}
              />
            </RoleBasedComponent>
            <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.DELETE_CUSTOMER] } }}>
              <IconButton
                icon={IconTrash}
                color={'red'}
                onClick={() => openModal(row.original.id)}
                label={t(labels.DELETE)}
              />
            </RoleBasedComponent>
          </Group>
        ),
      },
    ],
    [segmentList, openModal]
  );

  const { handleExportRows, handleExportData } = useExcelListExport(
    ['ID', 'Nazwa klienta', 'Segment', 'NIP', 'Grupa', 'Kraj'],
    (customer) => [
      customer.id,
      customer.name,
      t(customer.segment),
      customer.taxNumber,
      customer.group?.name,
      customer.country?.name ? t(customer.country.name) : customer.country?.name || '',
    ],
    (row) => [
      row.original.id,
      row.original.name,
      t(row.original.segment),
      row.original.taxNumber,
      row.original.group?.name,
      row.original.country?.name ? t(row.original.country.name) : row.original.country?.name || '',
    ],
    customers,
    'CustomerList'
  );

  const table = useMantineReactTable({
    columns,
    data: customers?.content ?? [],
    manualSorting: true,
    manualPagination: true,
    manualFiltering: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    state: { isLoading, pagination, sorting },
    initialState: {
      density: 'xs',
      showColumnFilters: true,
      columnVisibility: {
        taxNumber: false,
        id: false,
        'country.name': false,
        'country.code': false,
        'mrt-row-expand': false,
      },
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    onColumnFiltersChange: setFilters,
    renderTopToolbarCustomActions: () => (
      <Tooltip label="Odśwież">
        <ActionIcon onClick={() => refetch()}>
          <IconRefresh />
        </ActionIcon>
      </Tooltip>
    ),
    rowCount: totalRowCount,
  });

  return (
    <>
      <ConfirmModal
        opened={isModalOpen}
        onClose={closeModal}
        onConfirm={confirmDelete}
        title="Potwierdzenie usunięcia"
      >
        Czy na pewno chcesz usunąć tego klienta?
      </ConfirmModal>
      <Group justify="flex-end" pb="md">
        <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.CREATE_CUSTOMER] } }}>
          <Button color="orange" onClick={() => router.push(RouterPaths.CUSTOMERS_CREATE)}>
            Nowy klient
          </Button>
        </RoleBasedComponent>
        <ButtonMenu>
          <Menu.Item>
            <Text size={'sm'} onClick={handleExportData}>
              Eksportuj wszystkie dane
            </Text>
          </Menu.Item>
          <Menu.Item>
            <Text size={'sm'} onClick={() => handleExportRows(table.getRowModel().rows)}>
              Eksportuj obecne wiersze
            </Text>
          </Menu.Item>
        </ButtonMenu>
      </Group>
      <MantineReactTable table={table} />
    </>
  );
};

export default CustomerList;
