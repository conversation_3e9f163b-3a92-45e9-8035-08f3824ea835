import {Button, Group as MantineGroup} from "@mantine/core";
import GroupFields from '../GroupFields/GroupFields';
import CustomerTable from '../CustomerTable/CustomerTable';
import useGroup from '../hooks/useGroup';
import {FormProvider} from "react-hook-form";
import {useRouter} from "next/router";

interface GroupFormProps {
    groupId?: string;
}

function GroupForm({groupId = ''}: GroupFormProps) {
    const {
        methods,
        handleCustomerChange,
        handleRemoveCustomer,
        onSubmit,
        customers,
        availableCustomers,
        allCustomerIDs
    } = useGroup(groupId);

    const router = useRouter();

    const handleBack = () => {
        router.back();
    };


    return (
        <FormProvider {...methods}>
            <form onSubmit={onSubmit}>
                <GroupFields
                    allCustomerIDs={allCustomerIDs}
                    handleCustomerChange={handleCustomerChange}
                    availableCustomers={availableCustomers}
                    isEditMode={Boolean(groupId)}
                />
                <CustomerTable
                    customers={customers}
                    onRemoveCustomer={handleRemoveCustomer}
                />
                <MantineGroup>
                    <Button type="submit">
                        {groupId ? 'Zapisz zmiany' : 'Utwórz Grupę'}
                    </Button>
                    <Button type="button" variant="outline" onClick={handleBack}>
                        Powrót
                    </Button>
                </MantineGroup>
            </form>
        </FormProvider>
    );
}

export default GroupForm;