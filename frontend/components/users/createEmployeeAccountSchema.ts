import * as z from 'zod';
import { UserRole } from '@/types/UserRole';
import { UserAction } from '@/types/User';

export const createEmployeeAccountSchema = z
.object({
  username: z.string().min(1, '<PERSON>zwa użytkownika jest wymagana'),
  email: z.string().email('Nieprawidłowy adres email'),
  firstName: z.string().min(1, '<PERSON><PERSON><PERSON> jest wymagane'),
  lastName: z.string().min(1, 'Nazwisko jest wymagane'),
  password: z
  .string()
  .min(8, 'Hasło musi mieć co najmniej 8 znaków')
  .regex(/[A-Z]/, 'Hasło musi zawierać co najmniej jedną wielką literę (A-Z)')
  .regex(/[a-z]/, 'Hasło musi zawierać co najmniej jedną małą literę (a-z)')
  .regex(/[0-9]/, 'Hasło musi zawierać co najmniej jedną cyfrę (0-9)')
  .regex(/[!@#$%^&*]/, 'Hasło musi zawierać co najmniej jeden znak specjalny (np. !@#$%^&*)'),
  role: z
  .nativeEnum(UserRole, {
    errorMap: () => ({ message: 'Nieprawidłowa rola' }),
  })
  .default(UserRole.EMPLOYEE),
  requiredActions: z.array(z.nativeEnum(UserAction)).default([]),
  whiteListTenant: z
  .array(
      z.object({
        uuid: z.string().min(1, 'UUID jest wymagany'),
        name: z.string().min(1, 'Nazwa jest wymagana'),
      })
  )
  .optional(),
})
.superRefine((data, ctx) => {
  const pwd = data.password.toLowerCase();

  if (data.firstName && data.firstName.trim() !== '' && pwd.includes(data.firstName.toLowerCase())) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Hasło nie może zawierać Twojego imienia',
      path: ['password'],
    });
  }
  if (data.lastName && data.lastName.trim() !== '' && pwd.includes(data.lastName.toLowerCase())) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Hasło nie może zawierać Twojego nazwiska',
      path: ['password'],
    });
  }
  if (data.username && data.username.trim() !== '' && pwd.includes(data.username.toLowerCase())) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Hasło nie może zawierać nazwy użytkownika',
      path: ['password'],
    });
  }
});