import { useCallback, useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { AgreementService } from '@/services/AgreementService';
import { TrancheFormType } from '@/types/Wallet';
import { useRouter } from 'next/router';
import useWalletNotifications from '@/components/common/hooks/useWalletNotifications';
import { TrancheService } from '@/services/TrancheService';
import { zodResolver } from '@hookform/resolvers/zod';
import { trancheFormSchema } from '@/components/wallets/add-tranche/schema';
import dayjs from 'dayjs';
import { RouterPaths } from '@/services/shared/ApiEndpoints';
import { showNotification } from '@mantine/notifications';
import { Anchor, Text } from '@mantine/core';

const defaultFormValues = {
  formType: 0,
  agreementGroup: null,
  contractIds: [],
  walletIds: [],
  contract: '',
  timeUnit: '',
  executionDate: dayjs().format('YYYY-MM-DD'),
  priceReference: '',
  contractYear: '',
  volume: 0,
  price: 0,
};

const useTrancheForm = () => {
  const [errors, setErrors] = useState(Object);
  const [isErrorModalOpen, setErrorModalOpen] = useState(false);
  const { successNotification } = useWalletNotifications();
  const router = useRouter();
  const [formType, setFormType] = useState<TrancheFormType>(TrancheFormType.WALLET);

  const methods = useForm({
    resolver: zodResolver(trancheFormSchema),
    defaultValues: {
      ...defaultFormValues,
      formType: 0,
    },
  });

  const agreementGroup = useWatch({ control: methods.control, name: 'agreementGroup' });
  const resetFields = useCallback(() => {
    methods.reset({
      ...defaultFormValues,
      formType: formType === TrancheFormType.WALLET ? 0 : 1,
    });
  }, [formType, methods]);

  useEffect(() => {
    resetFields();
  }, [formType, resetFields]);

  useEffect(() => {
    if (agreementGroup?.id) {
      AgreementService.getAgreements().then((response) => {
        const agreements = response.content
          .filter((agreement) => agreement.agreementGroup?.id === agreementGroup.id)
          .map((agreement) => agreement.id);
        methods.setValue('contractIds', agreements);
      });
    } else {
      methods.setValue('contractIds', []);
    }
  }, [agreementGroup?.id, methods]);

  const onSubmit = async (values) => {
    try {
      await TrancheService.createTranche(values);

      successNotification({
        title: 'Sukces!',
        message: 'Transza została zapisana!',
      });
      await router.push(RouterPaths.WALLET_LIST);
    } catch (error) {
      if (['contracts'].includes(error?.response?.data?.detail)) {
        setErrors(error);
        setErrorModalOpen(true);
      }
      if (error.response && error.response.data && error.response.data.messages) {
        error.response.data.messages.forEach((message) => {
          if (message.subtype) {
            showNotification({
              title: 'Błąd',
              message: messageComponentWithLink(message),
              color: 'red',
            });
          } else {
            showNotification({
              title: 'Błąd',
              message: `${message.reason}`,
              color: 'red',
            });
          }
        });
      } else {
        showNotification({
          title: 'Błąd',
          message: 'Wystąpił nieoczekiwany problem. Spróbuj ponownie.',
          color: 'red',
        });
      }
    }
  };

  function messageComponentWithLink(message) {
    return (
      <Text>
        {message.reason}

        <Anchor href={generateSubtypeAnachor(message)} target="_blank">
          {message.uuid}
        </Anchor>
      </Text>
    );
  }

  function generateSubtypeAnachor(message) {
    switch (message.subtype) {
      case 'AGREEMENT':
        return `/agreements/details/${message.uuid}`;
      case 'CONTRACT':
      case 'WALLET':
        return `/wallets/details/${message.uuid}`;
    }
  }

  return {
    methods,
    formType,
    setFormType,
    onSubmit,
    errors,
    isErrorModalOpen,
    setErrorModalOpen,
  };
};

export default useTrancheForm;
