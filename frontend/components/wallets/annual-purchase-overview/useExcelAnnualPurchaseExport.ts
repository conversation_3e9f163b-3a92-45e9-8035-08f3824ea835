import { exportDataToXlsx } from '@/utils/exportDataToXlsx';
import { WalletService } from '@/services/WalletService';
import type { AnnualOverview } from './AnnualPurchaseOverviewList';
import type { ListRequestGlobalProps } from '@/types/Common';

interface Params {
  headers: string[];
  currentData: { content: AnnualOverview[] };
  filters: any;
  sorting: any;
}

export function getAnnualPurchaseExportHandlers({
  headers,
  currentData,
  filters,
  sorting,
}: Params) {
  const formatOverview = (item: AnnualOverview): any[] => [
    item.customer,
    ...Array.from({ length: 12 }, (_, i) => item[`M${i + 1}`]),
  ];

  const handleExportVisible = () => {
    const data = currentData?.content?.map(formatOverview) ?? [];
    exportDataToXlsx(data, headers, 'AnnualPurchaseOverview');
  };

  const handleExportAll = async () => {
    const allData = await fetchAllAnnualOverviewsForExport({ sorting, filters });
    const data = allData.map(formatOverview);
    exportDataToXlsx(data, headers, 'AnnualPurchaseOverview-All');
  };

  return { handleExportVisible, handleExportAll };
}

export const fetchAllAnnualOverviewsForExport = async (
  params: Omit<ListRequestGlobalProps, 'pagination'>
): Promise<AnnualOverview[]> => {
  const response = await WalletService.fetchAnnualPurchasesOverview({
    ...params,
    pagination: { pageIndex: 0, pageSize: 999999 },
  });
  return response.content;
};
