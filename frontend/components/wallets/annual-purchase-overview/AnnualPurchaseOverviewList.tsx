import React, { useEffect, useMemo, useState } from 'react';
import { MantineReactTable, MRT_PaginationState, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Group, Menu, MultiSelect, Text, Tooltip } from '@mantine/core';
import { IconRefresh, IconWallet } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { LIST_DEFAULT_PAGE_SIZE } from '@/utils/defaults';
import { formatValue } from '@/utils/formatters';
import { useAnnualPurchaseOverview } from '@/components/wallets/annual-purchase-overview/useAnnualPurchaseOverview';
import { getAnnualPurchaseExportHandlers } from '@/components/wallets/annual-purchase-overview/useExcelAnnualPurchaseExport';
import WalletLink from '@/components/common/forms/WalletLink/WalletLink';
import Truncate from '@/components/common/forms/TruncatedTextWithTooltip/Truncate';
import { ButtonMenu } from '@/components/common/layout/ButtonMenu/ButtonMenu';
import { RealisationType } from '@/types/Wallet';

const MONTHS = Array.from({ length: 12 }, (_, i) => `M${i + 1}`);

const segLabel = (s: string) =>
  (
    ({
      LOCAL_GOVERNMENT_UNIT: 'JST',
      INDUSTRY: 'Przemysł',
      REAL_ESTATE: 'Nieruchomości',
      SHOPPING_CHAINS: 'Sieciowi',
    }) as Record<string, string>
  )[s] ?? s;

const monthCell = ({ cell }: { cell: any }) => {
  const v = Number(cell.getValue());
  const color = v === 100 ? 'green' : v === 0 || v > 100 ? 'red' : 'black';
  return <span style={{ color }}>{v >= 0 ? `${formatValue(v)}%` : ''}</span>;
};

const useColumns = () =>
  useMemo(() => {
    const walletCol = {
      accessorKey: 'customerName',
      header: 'Klient',
      enableColumnFilter: true,
      size: 200,
      Cell: ({ row }: { row: any }) => (
        <WalletLink
          walletId={row.original.walletId}
          label={
            <Group>
              <IconWallet size={16} color="#F7931A" />
              <Truncate text={row.original.customerName} />
            </Group>
          }
        />
      ),
    };

    const meta = [
      { accessorKey: 'year', header: 'Rok', enableColumnFilter: false },
      { accessorKey: 'mediaType', header: 'Nośnik', enableColumnFilter: false },
      {
        accessorKey: 'supplierName',
        header: 'Sprzedawca',
        Cell: ({ cell }: { cell: any }) => <Truncate text={cell.getValue()} />,
        enableColumnFilter: false,
      },
      {
        accessorKey: 'description',
        header: 'Opis',
        Cell: ({ cell }: { cell: any }) => <Truncate text={cell.getValue()} />,
      },
    ];

    const months = MONTHS.map((m) => ({
      accessorKey: m,
      header: m,
      size: 90,
      Cell: monthCell,
    }));

    return [walletCol, ...meta, ...months];
  }, []);

interface FiltersProps {
  years: string[];
  yearsOpts: string[];
  media: RealisationType[];
  mediaOpts: RealisationType[];
  suppliers: string[];
  suppliersOpts: string[];
  segments: string[];
  segmentOpts: string[];
  setYears: (v: string[]) => void;
  setMedia: (v: RealisationType[]) => void;
  setSuppliers: (v: string[]) => void;
  setSegments: (v: string[]) => void;
}

const Filters: React.FC<FiltersProps> = ({
  years,
  yearsOpts,
  media,
  mediaOpts,
  suppliers,
  suppliersOpts,
  segments,
  segmentOpts,
  setYears,
  setMedia,
  setSuppliers,
  setSegments,
}) => {
  const { t } = useTranslation('common');
  return (
    <Group justify="flex-start" pb="md">
      <MultiSelect
        label="Rok"
        value={years}
        onChange={setYears}
        data={yearsOpts}
        searchable
        clearable
      />
      <MultiSelect
        label="Nośnik"
        value={media}
        onChange={(vals) => setMedia(vals as RealisationType[])}
        data={mediaOpts.map((m) => ({ value: m, label: t(m) }))}
        clearable
        w="300px"
      />
      <MultiSelect
        label="Sprzedawca"
        value={suppliers}
        onChange={setSuppliers}
        data={suppliersOpts}
        searchable
        clearable
      />
      <MultiSelect
        label="Segment"
        value={segments}
        onChange={setSegments}
        data={segmentOpts.map((s) => ({ value: s, label: segLabel(s) }))}
        searchable
        clearable
      />
    </Group>
  );
};

const ExportMenu = ({ all, visible }: { all: () => void; visible: () => void }) => (
  <Group justify="flex-end" pb="md">
    <ButtonMenu>
      <Menu.Item>
        <Text size="sm" onClick={all}>
          Eksportuj wszystkie
        </Text>
      </Menu.Item>
      <Menu.Item>
        <Text size="sm" onClick={visible}>
          Eksportuj widoczne
        </Text>
      </Menu.Item>
    </ButtonMenu>
  </Group>
);

const AnnualPurchaseOverviewList = () => {
  const [years, setYears] = useState<string[]>([]);
  const [media, setMedia] = useState<RealisationType[]>([RealisationType.ENERGY]);
  const [suppliers, setSuppliers] = useState<string[]>([]);
  const [segments, setSegments] = useState<string[]>([]);

  const [columnFilters, setColumnFilters] = useState([]);
  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: LIST_DEFAULT_PAGE_SIZE,
  });

  /* --- backend filters --- */
  const filters = useMemo(() => {
    const backend: { id: string; value: string }[] = [];

    if (years.length) backend.push({ id: 'year', value: years.join(',') });
    if (media.length) backend.push({ id: 'mediaType', value: media.join(',') });
    if (suppliers.length) backend.push({ id: 'supplierName', value: suppliers.join(',') });
    if (segments.length) backend.push({ id: 'customerSegment', value: segments.join(',') });

    columnFilters
      .filter((f) => !/^M\d{1,2}$/.test(f.id))
      .forEach((f) => backend.push({ id: f.id, value: f.value }));

    return backend;
  }, [years, media, suppliers, segments, columnFilters]);

  const { data, isLoading, refetch } = useAnnualPurchaseOverview({
    sorting: [],
    pagination,
    filters,
  });

  const [optYears, setOptYears] = useState<Set<string>>(new Set());
  const [optSuppliers, setOptSuppliers] = useState<Set<string>>(new Set());
  const [optSegments, setOptSegments] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!data?.content) return;
    setOptYears((p) => new Set([...p, ...data.content.map((d) => d.year)]));
    setOptSuppliers((p) => new Set([...p, ...data.content.map((d) => d.supplierName)]));
    setOptSegments((p) => new Set([...p, ...data.content.map((d) => d.customerSegment)]));
  }, [data]);

  const visibleRows = useMemo(() => {
    const mFilt = columnFilters.filter((f) => /^M\d{1,2}$/.test(f.id)); // tylko filtry NIE będące M1–M12 na BA
    if (!mFilt.length) return data?.content ?? [];
    return (data?.content ?? []).filter((row) =>
      mFilt.every((f) =>
        (row as any)[f.id]?.toString().toLowerCase().includes(f.value.toLowerCase())
      )
    );
  }, [data, columnFilters]);

  const headers = ['Klient', 'Rok', 'Nośnik', 'Sprzedawca', 'Opis', ...MONTHS];
  const { handleExportVisible, handleExportAll } = getAnnualPurchaseExportHandlers({
    headers,
    currentData: data,
    filters,
    sorting: [],
  });

  const table = useMantineReactTable({
    columns: useColumns(),
    data: visibleRows,
    manualPagination: true,
    manualFiltering: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    initialState: { density: 'xs', showColumnFilters: false },
    state: { isLoading, pagination, columnFilters },
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    renderTopToolbarCustomActions: () => (
      <Group>
        <Tooltip label="Odśwież">
          <ActionIcon onClick={refetch}>
            <IconRefresh />
          </ActionIcon>
        </Tooltip>
      </Group>
    ),
    rowCount: data?.totalElements ?? 0,
  });

  return (
    <>
      <Filters
        years={years}
        yearsOpts={[...optYears].sort()}
        media={media}
        mediaOpts={Object.values(RealisationType)}
        suppliers={suppliers}
        suppliersOpts={[...optSuppliers].sort()}
        segments={segments}
        segmentOpts={[...optSegments].sort()}
        setYears={setYears}
        setMedia={setMedia}
        setSuppliers={setSuppliers}
        setSegments={setSegments}
      />

      <ExportMenu all={handleExportAll} visible={handleExportVisible} />
      <MantineReactTable table={table} />
    </>
  );
};

export default AnnualPurchaseOverviewList;
