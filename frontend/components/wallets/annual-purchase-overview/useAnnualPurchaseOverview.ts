import { useQuery } from '@tanstack/react-query';
import { WalletService } from '@/services/WalletService';
import { ApiListResponse, ListRequestGlobalProps } from '@/types/Common';
import { AnnualOverview } from '@/types/AnnualOverview';

export const useAnnualPurchaseOverview = (
    params: ListRequestGlobalProps
) => {
  return useQuery<ApiListResponse<AnnualOverview>>(
      WalletService.getOverviewQueryConfig(
          'annual-purchase-overview',
          params,
          WalletService.fetchAnnualPurchasesOverview
      )
  );
};
