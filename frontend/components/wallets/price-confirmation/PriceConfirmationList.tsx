import React, { useState } from 'react';
import { MRT_PaginationState } from 'mantine-react-table';
// @ts-ignore – mantine-react-table nie ma jeszcze pełnych typów dla `pl`
import { usePriceConfirmation } from '@/components/wallets/price-confirmation/usePriceConfirmation';
import { usePriceConfirmationFilters } from '@/hooks/usePriceConfirmationFilters';
import { useAccumulatedOptions } from '@/hooks/useAccumulatedOptions';
import { EDIT_MODE, Media } from '@/types/price-confirmation';
import Filters from '@/components/wallets/price-confirmation/Filters';
// …pozostałe importy

const PriceConfirmationList: React.FC = () => {
  // --- stany UI ---
  const [columnFilters, setColumnFilters] = useState<any[]>([]);
  const [years, setYears] = useState<string[]>([]);
  const [media, setMedia] = useState<Media[]>([Media.ENERGY]);
  const [suppliers, setSuppliers] = useState<string[]>([]);
  const [segments, setSegments] = useState<string[]>([]);
  const [editMode, setEditMode] = useState<EDIT_MODE>(EDIT_MODE.NONE);
  const [pagination, setPagination] = useState<MRT_PaginationState>({ pageIndex: 0, pageSize: 20 });

  // --- hook z backendFilters ---
  const backendFilters = usePriceConfirmationFilters(
    years,
    media,
    suppliers,
    segments,
    columnFilters
  );

  // --- pobranie danych ---
  const { data, isLoading, refetch } = usePriceConfirmation({
    sorting: [],
    pagination,
    filters: backendFilters,
  });

  // --- akumulowane opcje ---
  const yearsOpts = useAccumulatedOptions(data?.content, 'year');
  const suppliersOpts = useAccumulatedOptions(data?.content, 'supplierName');
  const segmentsOpts = useAccumulatedOptions(data?.content, 'customerSegment');

  // --- tabela, export, itd. ---
  //   • MonthCell używa osobnego komponentu
  //   • ToolbarActions przeniesione do osobnego pliku
  //   • Filters jest czystą “molekułą”

  return (
    <>
      <Filters
        years={years}
        media={media}
        suppliers={suppliers}
        segments={segments}
        yearsOpts={yearsOpts}
        suppliersOpts={suppliersOpts}
        segmentsOpts={segmentsOpts}
        setYears={setYears}
        setMedia={setMedia}
        setSuppliers={setSuppliers}
        setSegments={setSegments}
      />
      {/* eksport, toolbar, tabela… */}
    </>
  );
};

export default PriceConfirmationList;
