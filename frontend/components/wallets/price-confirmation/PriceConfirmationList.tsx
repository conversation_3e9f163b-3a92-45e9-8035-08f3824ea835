import React, { useEffect, useMemo, useState } from 'react';
import { MantineReactTable, MRT_PaginationState, useMantineReactTable } from 'mantine-react-table';
import {
  ActionIcon,
  Button,
  Checkbox,
  Group,
  Menu,
  MultiSelect,
  Select,
  Text,
  Tooltip,
} from '@mantine/core';
import { IconRefresh, IconWallet } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { LIST_DEFAULT_PAGE_SIZE } from '@/utils/defaults';
import { ButtonMenu } from '@/components/common/layout/ButtonMenu/ButtonMenu';
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { usePriceConfirmation } from '@/components/wallets/price-confirmation/usePriceConfirmation';
import { WalletService } from '@/services/WalletService';
import { getPriceConfirmationExportHandlers } from '@/components/wallets/price-confirmation/useExcelPriceConfirmationExport';
import { formatValue } from '@/utils/formatters';
import WalletLink from '@/components/common/forms/WalletLink/WalletLink';
import Truncate from '@/components/common/forms/TruncatedTextWithTooltip/Truncate';
import { EDIT_MODE, Media, PriceOverview } from '@/types/PriceConfirmation';

const COLORS = { GREEN: 'green', BLACK: 'black' } as const;

const MONTHS = Array.from({ length: 12 }, (_, i) => `M${i + 1}`);

const segLabel = (s: string) =>
  (
    ({
      LOCAL_GOVERNMENT_UNIT: 'JST',
      INDUSTRY: 'Przemysł',
      REAL_ESTATE: 'Nieruchomości',
      SHOPPING_CHAINS: 'Sieciowi',
    }) as Record<string, string>
  )[s] ?? s;

const PRICE_KINDS = {
  TOTAL: 'TOTAL',
  TGE: 'TGE',
} as const;

const PriceConfirmationList: React.FC = () => {
  const { t } = useTranslation('common');
  const [priceKind, setPriceKind] = useState<'TOTAL' | 'TGE'>('TOTAL');
  const [columnFilters, setColumnFilters] = useState<any[]>([]);
  const [years, setYears] = useState<string[]>([]);
  const [media, setMedia] = useState<Media[]>([Media.ENERGY]);
  const [suppliers, setSuppliers] = useState<string[]>([]);
  const [segments, setSegments] = useState<string[]>([]);
  const [editMode, setEditMode] = useState<EDIT_MODE>(EDIT_MODE.NONE);
  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: LIST_DEFAULT_PAGE_SIZE,
  });

  useEffect(() => setEditMode(EDIT_MODE.NONE), [priceKind]);

  const filters = useMemo(() => {
    const backend: { id: string; value: string }[] = [];

    if (years.length) backend.push({ id: 'year', value: years.join(',') });
    if (media.length) backend.push({ id: 'mediaType', value: media.join(',') });
    if (suppliers.length) backend.push({ id: 'supplierName', value: suppliers.join(',') });
    if (segments.length) backend.push({ id: 'customerSegment', value: segments.join(',') });
    if (priceKind === PRICE_KINDS.TGE) backend.push({ id: 'pricesTge', value: 'true' });
    columnFilters
      // pomijaj kolumny M1–M12 przy przekazywaniu do backendu
      .filter((f) => !/^M\d{1,2}$/.test(f.id))
      .forEach((f) => backend.push({ id: f.id, value: f.value }));

    return backend;
  }, [years, media, suppliers, segments, columnFilters, priceKind]);

  const { data, isLoading, refetch } = usePriceConfirmation({
    sorting: [],
    pagination,
    filters,
  });


  const optYears = useFilterOptions<PriceOverview>(data?.content, (d) => d.year);
  const optSuppliers = useFilterOptions<PriceOverview>(data?.content, (d) => d.supplierName);
  const optSegments = useFilterOptions<PriceOverview>(data?.content, (d) => d.customerSegment);

  /* ------ lokalny stan tabeli (zaznaczenia) ------ */
  const [tableData, setTableData] = useState<PriceOverview[]>([]);
  useEffect(() => {
    if (data?.content) setTableData(data.content as PriceOverview[]);
  }, [data]);

  /* ------ lokalne filtrowanie M1…M12 ------ */
  const visibleRows = useMemo(() => {
    const mFilters = columnFilters.filter((f) => /^M\d{1,2}$/.test(f.id));
    if (!mFilters.length) return tableData;

    return tableData.filter((row) =>
      mFilters.every((filter) => {
        const month = (row as any)[filter.id];
        const value = month?.price ?? '';
        return value.toString().includes(filter.value);
      })
    );
  }, [tableData, columnFilters]);

  const monthCellRenderer = ({ cell }: { cell: any }) => {
    const priceEntry = cell.getValue();
    const color = priceEntry?.confirmed ? COLORS.GREEN : COLORS.BLACK;
    const rowIndex = cell.row.index;
    const monthKey = cell.column.id;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const checked = e.currentTarget.checked;
      setTableData((prev) => {
        const next = [...prev];
        const updatedRow = { ...next[rowIndex] } as any;
        updatedRow[monthKey] = { ...updatedRow[monthKey], checked };
        next[rowIndex] = updatedRow;
        return next;
      });
    };

    return (
      <Group style={{ color, fontWeight: priceEntry?.confirmed ? 800 : 'inherit' }}>
        <div style={{ width: 50, textAlign: 'left' }}>{formatValue(priceEntry?.price)}</div>
        {(editMode === EDIT_MODE.DEFAULT || editMode === EDIT_MODE.CONFIRMED) && (
          <Checkbox
            disabled={editMode === EDIT_MODE.DEFAULT ? priceEntry?.confirmed : false}
            checked={priceEntry?.checked}
            onChange={handleChange}
          />
        )}
      </Group>
    );
  };

  const columns = useMemo(() => {
    const walletColumn = {
      accessorKey: 'customerName',
      header: 'Klient',
      size: 155,
      Cell: ({ row }: { row: any }) => (
        <WalletLink
          walletId={row.original.walletId}
          label={
            <Group>
              <IconWallet size={16} color="#F7931A" />
              <Truncate text={row.original.customerName} />
            </Group>
          }
        />
      ),
    } as const;

    const metaColumns = [
      { accessorKey: 'year', header: 'Rok', enableColumnFilter: false, size: 90 },
      { accessorKey: 'mediaType', header: 'Nośnik', enableColumnFilter: false, size: 90 },
      {
        accessorKey: 'supplierName',
        header: 'Sprzedawca',
        enableColumnFilter: false,
        Cell: ({ cell }: { cell: any }) => <Truncate text={cell.getValue()} />,
        size: 90,
      },
      {
        accessorKey: 'description',
        header: 'Opis',
        Cell: ({ cell }: { cell: any }) => <Truncate text={cell.getValue()} />,
        size: 90,
      },
    ];

    const monthColumns = MONTHS.map((m) => ({
      accessorKey: m,
      header: m,
      Cell: monthCellRenderer,
      size: 90,
    }));

    return [walletColumn, ...metaColumns, ...monthColumns];
  }, [editMode]);

  const headers = ['Klient', 'Rok', 'Nośnik', 'Sprzedawca', 'Opis', ...MONTHS];
  const { handleExportVisible, handleExportAll } = getPriceConfirmationExportHandlers({
    headers,
    sorting: [],
    filters,
    currentData: data,
  });

  const saveChanges = async () => {
    setEditMode(EDIT_MODE.NONE);
    await WalletService.updatePriceConfirmation(tableData);
    await refetch();
  };

  const renderToolbarActions = () => (
    <Group style={{ width: '100%' }}>
      <Tooltip label="Odśwież">
        <ActionIcon onClick={refetch}>
          <IconRefresh />
        </ActionIcon>
      </Tooltip>

      {priceKind === PRICE_KINDS.TOTAL && editMode === EDIT_MODE.NONE && (
        <Tooltip label="Edytuj">
          <Button onClick={() => setEditMode(EDIT_MODE.DEFAULT)}>Edytuj</Button>
        </Tooltip>
      )}

      {priceKind === PRICE_KINDS.TOTAL && editMode === EDIT_MODE.DEFAULT && (
        <>
          <Tooltip label="Anuluj">
            <Button onClick={() => setEditMode(EDIT_MODE.NONE)}>Anuluj</Button>
          </Tooltip>
          <Tooltip label="Pozwól modyfikować potwierdzone ceny">
            <Button onClick={() => setEditMode(EDIT_MODE.CONFIRMED)}>Modyfikuj potwierdzone</Button>
          </Tooltip>
        </>
      )}

      {priceKind === PRICE_KINDS.TOTAL &&
        (editMode === EDIT_MODE.DEFAULT || editMode === EDIT_MODE.CONFIRMED) && (
          <Tooltip label="Zapisz">
            <Button onClick={saveChanges}>Zapisz</Button>
          </Tooltip>
        )}
    </Group>
  );

  const table = useMantineReactTable({
    columns,
    data: visibleRows,
    manualPagination: true,
    manualFiltering: true,
    enableGlobalFilter: false,
    localization: MRT_Localization_PL,
    initialState: { density: 'xs', showColumnFilters: false },
    state: { isLoading, pagination, columnFilters },
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    renderTopToolbarCustomActions: renderToolbarActions,
    rowCount: data?.totalElements ?? 0,
  });

  return (
    <>
      <Group justify="flex-start" pb="md">
        <Select
          label="Rodzaj cen"
          value={priceKind}
          onChange={(val) => setPriceKind(val as keyof typeof PRICE_KINDS)}
          data={[
            { value: PRICE_KINDS.TOTAL, label: 'Ceny końcowe' },
            { value: PRICE_KINDS.TGE, label: 'Ceny TGE' },
          ]}
          w={180}
          withinPortal
          allowDeselect={false}
        />
      </Group>
      <Group justify="flex-start" pb="md">
        <MultiSelect
          label="Rok"
          value={years}
          onChange={setYears}
          data={[...optYears].sort()}
          searchable
          clearable
        />
        <MultiSelect
          label="Nośnik"
          value={media}
          onChange={(vals) => setMedia(vals as Media[])}
          data={Object.values(Media).map((m) => ({ value: m, label: t(m) }))}
          w="300px"
        />
        <MultiSelect
          label="Sprzedawca"
          value={suppliers}
          onChange={setSuppliers}
          data={[...optSuppliers].sort()}
          searchable
          clearable
        />
        <MultiSelect
          label="Segment"
          value={segments}
          onChange={setSegments}
          data={[...optSegments].sort().map((s) => ({ value: s, label: segLabel(s) }))}
          searchable
          clearable
        />
      </Group>
      <Group justify="flex-end" pb="md">
        <ButtonMenu>
          <Menu.Item>
            <Text size="sm" onClick={handleExportAll}>
              Eksportuj wszystkie dane
            </Text>
          </Menu.Item>
          <Menu.Item>
            <Text size="sm" onClick={handleExportVisible}>
              Eksportuj obecne wiersze
            </Text>
          </Menu.Item>
        </ButtonMenu>
      </Group>
      <MantineReactTable table={table} />
    </>
  );
};

export default PriceConfirmationList;
