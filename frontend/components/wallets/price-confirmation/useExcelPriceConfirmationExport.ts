import { exportDataToXlsx } from '@/utils/exportDataToXlsx';
import { WalletService } from '@/services/WalletService';
import { formatValue } from '@/utils/formatters';
import type { ListRequestGlobalProps } from '@/types/Common';
import type { PriceOverview } from './PriceConfirmationList';

const MONTHS = Array.from({ length: 12 }, (_, i) => `M${i + 1}`);

interface Params {
  headers: string[];
  sorting: any;
  filters: any;
  currentData: { content: PriceOverview[] };
}

export function getPriceConfirmationExportHandlers({
  headers,
  sorting,
  filters,
  currentData,
}: Params) {
  const formatRow = (row: PriceOverview): any[] => [
    row.customer,
    ...MONTHS.map((month) => formatValue((row as any)[month]?.price)),
  ];

  const handleExportVisible = () => {
    const data = currentData?.content?.map(formatRow) ?? [];
    exportDataToXlsx(data, headers, 'PriceConfirmationList');
  };

  const handleExportAll = async () => {
    const allData = await fetchAllPriceConfirmationsForExport({ sorting, filters });
    const data = allData.map(formatRow);
    exportDataToXlsx(data, headers, 'PriceConfirmationList-All');
  };

  return { handleExportVisible, handleExportAll };
}

export const fetchAllPriceConfirmationsForExport = async (
  params: Omit<ListRequestGlobalProps, 'pagination'>
): Promise<PriceOverview[]> => {
  const response = await WalletService.fetchPriceConfirmation({
    ...params,
    pagination: {
      pageIndex: 0,
      pageSize: 999999,
    },
  });
  return response.content.map((item: any) => ({
    ...item,
    walletId: item.walletId ?? '',
  })) as PriceOverview[];
};
