import { useQuery } from '@tanstack/react-query';
import { WalletService } from '@/services/WalletService';
import { ApiListResponse, ListRequestGlobalProps } from '@/types/Common';
import { PriceOverview } from './PriceConfirmationList';

export const usePriceConfirmation = (params: ListRequestGlobalProps) => {
  return useQuery<ApiListResponse<PriceOverview>>({
    queryKey: ['price-confirmation', params],
    queryFn: async () =>
      (await WalletService.fetchPriceConfirmation(params)) as ApiListResponse<PriceOverview>,
    staleTime: 5 * 60 * 1000,
    gcTime: 0,
  });
};
