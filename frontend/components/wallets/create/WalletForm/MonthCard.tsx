import React from 'react';
import { Card, Center, Divider, Group, Progress, Stack, Text } from '@mantine/core';
import { TrancheListAsTooltip } from '@/components/wallets/create/WalletForm/TrancheListAsTooltip';
import MonthCardRow from '@/components/wallets/create/WalletForm/MonthCardRow';
import { ElementType, ProductType } from '@/types/Wallet';
import { getEnumKey } from '@/utils/get-enum-key';
import { formatValue } from '@/utils/formatters';

const MonthCard = ({ data, tranches, timeUnit, products }) => {
  const findByElementType = (data, type) => {
    const key = getEnumKey(ElementType, type);
    return data?.find((item) => item.type === key) || null;
  };

  const findByProductType = (data, type, media) => {
    const key = getEnumKey(ProductType, type);
    return data?.find((item) => item.type === key && item.media === media) || null;
  };

  let volumeProduct =
    findByProductType(products, ProductType.VOLUME, 'ENERGY')?.value ||
    findByProductType(products, ProductType.VOLUME, 'GAS')?.value ||
    0.0;

  let rightsVolumeProduct =
    findByProductType(products, ProductType.VOLUME, 'GREEN_PROPERTY_RIGHTS')?.value || 0.0;

  // if (!volumeProduct) {
  //   // jeśli w produktach nie ma wpisu, obliczamy sumę rozmiarów z transz
  //   const computedVolume = (tranches || []).reduce((acc, tranche) => acc + (tranche.size || 0), 0);
  //   volumeProduct = { value: computedVolume };
  //   console.log(`Fallback: computed volume for ${timeUnit}:`, computedVolume);
  // }
  //
  // // wyodrębniamy transze, których kontrakt ma typ "PropertyRight"
  // const propertyRightsTranches = (tranches || []).filter(
  //   (tranche) => tranche.contract && tranche.contract['@type'] === 'PropertyRight'
  // );
  // const propertyRightsValue = propertyRightsTranches.reduce(
  //   (acc, tranche) => acc + (tranche.size || 0),
  //   0
  // );
  // obliczamy bazowy wolumen, odejmując wartość transz z praw majątkowych (aby się nie dublowało)
  // const baseVolume = Math.max(volumeProduct.value - propertyRightsValue, 0);

  const baseVolume = volumeProduct;

  const detailElementTypes = [
    ElementType.NET,
    ElementType.COSTS,
    ElementType.PROFILE,
    ElementType.PROFILE_PERCENT,
    ElementType.GREEN_PROPERTY_RIGHTS,
    ElementType.WHITE_PROPERTY_RIGHTS,
    ElementType.BLUE_PROPERTY_RIGHTS,
    ElementType.TOTAL_PROPERTY_RIGHTS,
    ElementType.EXCISE,
    ElementType.CORRECTION_FACTOR,
  ];

  return (
    <Card withBorder shadow="sm" radius="md">
      <Card.Section inheritPadding py="xs">
        <Group justify="space-between">
          <Text fw={500}>Ceny {timeUnit}</Text>
          <TrancheListAsTooltip tranches={tranches} timeUnit={timeUnit} />
        </Group>
      </Card.Section>
      <Divider label="Szczegóły składników" labelPosition="left" />
      <Stack gap="xs" mt="xs">
        {detailElementTypes.map((type) => (
          <MonthCardRow key={type} price={findByElementType(data, type)} />
        ))}
        <MonthCardRow price={findByElementType(data, ElementType.TOTAL)} />
        <Center>
          <Text size="xs" c="dimmed">
            Zakupiono {formatValue(baseVolume)}% wolumenu.
          </Text>
        </Center>
        <Progress value={baseVolume} color="var(--mantine-colors-baseOrange-6)" />
        {rightsVolumeProduct > 0 && (
          <>
            <Center>
              <Text size="xs" c="dimmed">
                Zakupiono {formatValue(rightsVolumeProduct)}% praw majątkowych.
              </Text>
            </Center>
            <Progress value={rightsVolumeProduct} color="green" />
          </>
        )}
      </Stack>
    </Card>
  );
};

export default MonthCard;
