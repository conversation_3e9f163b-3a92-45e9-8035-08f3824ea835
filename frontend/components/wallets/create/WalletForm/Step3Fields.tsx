import React, { useEffect, useMemo, useState } from 'react';
import { MantineReactTable, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Button, Group, Stack } from '@mantine/core';
import { IconPencil, IconTrash } from '@tabler/icons-react';
import { FieldValues, FormProvider, useForm, useFormContext } from 'react-hook-form';
import NumberInput from '@/components/common/forms/NumberInput/NumberInput';
import DatePicker from '@/components/common/forms/DatePicker/DatePicker';
import ContractSingleSelect from '@/components/common/forms/ContractSingleSelect/ContractSingleSelect';
import { ContractService } from '@/services/ContractService';
import CustomModal from '@/components/common/forms/Modal/CustomModal';
import { useWalletFormStore } from '@/stores/wallet-form-store/wallet-form-provider';
import TimeUnitSelect from '@/components/common/forms/TimeUnitSelect/TimeUnitSelect';
import PurchaseMethodSingleSelect from '@/components/common/forms/PurchaseMethodSingleSelect/PurchaseMethodSingleSelect';
import { TrancheForm } from '@/components/wallets/create/WalletForm/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { TrancheFormSchema } from '@/components/wallets/create/WalletForm/Schema';
import { TimeUnit } from '@/types/Wallet';
import useTranslation from 'next-translate/useTranslation';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { formatValue } from '@/utils/formatters';

const Step3Fields = () => {
  const { t } = useTranslation('common');
  const { setValue, watch } = useFormContext();
  const form = useForm({
    mode: 'onTouched',
    resolver: zodResolver(TrancheFormSchema),
    defaultValues: {
      executionDate: new Date(),
      size: 0.0,
      price: 0.0,
      contract: '',
      timeUnit: TimeUnit.Y.toString(),
      priceReference: '',
    },
  });
  const agreement = watch('agreement');
  const { tranches, setTranches, contracts, setContracts } = useWalletFormStore((state) => state);
  const [isModalOpen, setModalOpen] = useState(false);
  const [isEditForm, setIsEditForm] = useState(null);
  useEffect(() => {
    setValue('tranches', tranches);
  }, [tranches, setValue]);
  //TODO: gdyby setowanie agreementu przenieść do store'a to ten krok mógłby się dziać pomiędzy stepami, a dane były by już dostępne.
  useEffect(() => {
    const fetchData = async () => {
      const data = await ContractService.getContractsByAgreement(agreement);
      setContracts(
        data.content.map((contract) => ({
          value: contract.id,
          label: `${contract.name}`,
        }))
      );
    };
    fetchData().catch((err) => {
      console.error('Error fetching contracts:', err);
    });
  }, [agreement]);

  const columns = useMemo(
    () => [
      { accessorKey: 'executionDate', header: 'Data wykonania' },
      { accessorKey: 'size', header: 'Wielkość transzy (%)' },
      {
        accessorFn: (data) => contracts.find((c) => c.value === data.contract)?.label,
        header: 'Kontrakt',
      },
      { accessorFn: (data: any) => formatValue(data?.price), header: 'Cena (zł/MWh)' },
      { accessorKey: 'timeUnit', header: 'Jednostka czasu' },
      { accessorFn: (data) => t(data.priceReference), header: 'Sposób zakupu' },
      {
        id: 'actions',
        header: 'Operacje',
        Cell: ({ row }) => (
          <Group>
            <ActionIcon size="md" onClick={() => handleEdit(row.original)}>
              <IconPencil size={24} stroke={1.5} />
            </ActionIcon>
            <ActionIcon size="md" color="red" onClick={() => handleDelete(row.original)}>
              <IconTrash size={24} stroke={1.5} />
            </ActionIcon>
          </Group>
        ),
      },
    ],
    [tranches]
  );
  const handleEdit = (tranche: TrancheForm) => {
    setIsEditForm(tranche);
    setModalOpen(true);
    form.reset({
      ...tranche,
      executionDate: new Date(tranche.executionDate), // Ensure date is converted to Date object
    });
  };

  const handleDelete = (tranche: TrancheForm) => {
    const updatedTranches = tranches.filter((t) => t !== tranche);
    setTranches(updatedTranches);
  };

  const handleAdd = () => {
    setIsEditForm(null);
    setModalOpen(true);
    form.reset({
      executionDate: new Date(),
      size: 0.0,
      price: 0.0,
      contract: '',
      timeUnit: TimeUnit.Y,
      priceReference: '',
    });
  };

  const handleModalSubmit = async (data: FieldValues) => {
    await form.trigger();
    if (form.formState.isValid) {
      const formattedData = {
        ...data,
        executionDate:
          data.executionDate instanceof Date
            ? data.executionDate.toISOString().split('T')[0]
            : data.executionDate,
      };

      let updatedTranches;
      if (isEditForm) {
        //todo: raczej bug
        updatedTranches = tranches.map((t): TrancheForm => (t === isEditForm ? formattedData : t));
      } else {
        updatedTranches = [...tranches, formattedData];
      }

      setTranches(updatedTranches);
      setValue('tranches', updatedTranches);
      setModalOpen(false);
    }
  };

  const contract = form.watch('contract');
  useEffect(() => {
    const fetchData = async () => {
      try {
        if (contract) {
          const contractName = contracts.find((c) => c.value === contract)?.label;
          const priceRefs = await ContractService.getAgreementPriceReference(
            agreement,
            contractName
          );
          form.setValue('priceReference', priceRefs[0] || '');
          const tm = await ContractService.getContractTimeUnit(contractName);
          form.setValue('timeUnit', tm as TimeUnit);
        } else {
          form.setValue('timeUnit', null);
          form.setValue('priceReference', '');
        }
      } catch (err) {
        console.error('Błąd podczas pobierania priceReference/timeUnit:', err);
      }
    };

    fetchData();
  }, [contract, agreement, contracts, form]);

  const table = useMantineReactTable({
    columns,
    data: tranches,
    defaultColumn: {
      size: 20,
    },
    mantineTableBodyCellProps: { style: { padding: '4px 2px', fontSize: '0.9em' } },
    mantineTableHeadCellProps: { style: { padding: '2px', fontSize: '0.9em' } },
    manualFiltering: false,
    manualPagination: false,
    manualSorting: false,
    localization: MRT_Localization_PL,
    renderTopToolbarCustomActions: () => (
      <Group mt="md">
        <Button onClick={handleAdd} color="orange">
          Dodaj transzę
        </Button>
      </Group>
    ),
  });

  return (
    <>
      <MantineReactTable table={table} />
      <CustomModal
        opened={isModalOpen}
        onClose={() => setModalOpen(false)}
        onConfirm={() => handleModalSubmit(form.getValues())}
        size="30%"
        radius="md"
        title={isEditForm ? 'Edycja transzy' : 'Tworzenie nowej transzy'}
      >
        <FormProvider {...form}>
          <form>
            <Stack>
              <DatePicker
                name="executionDate"
                label="Data wykonania"
                placeholder="Wybierz datę z kalendarza"
                withAsterisk
              />
              <NumberInput
                name="size"
                label="Wielkość transzy (%)"
                placeholder="Wpisz wielkość transzy"
                hideControls
                withAsterisk
              />
              <ContractSingleSelect
                name="contract"
                label="Kontrakt"
                placeholder="Wybierz z listy"
                contracts={contracts}
                withAsterisk
              />
              <NumberInput
                name="price"
                label="Cena (zł/MWh)"
                placeholder="Wpisz cenę"
                hideControls
                withAsterisk
              />
              <TimeUnitSelect
                name="timeUnit"
                label="Jednostka czasu"
                placeholder="Wybierz z listy"
                withAsterisk
              />
              <PurchaseMethodSingleSelect
                name="priceReference"
                label="Sposób zakupu"
                placeholder="Wybierz z listy"
                withAsterisk
                disabled={false}
              />
            </Stack>
          </form>
        </FormProvider>
      </CustomModal>
    </>
  );
};

export default Step3Fields;
