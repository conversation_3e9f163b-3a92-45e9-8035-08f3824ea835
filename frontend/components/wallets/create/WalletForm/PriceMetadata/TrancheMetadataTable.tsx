import React from 'react';
import { Stack, Table, Text } from '@mantine/core';
import { PriceMetadata } from './types';

interface TrancheMetadataTableProps {
  metadata: PriceMetadata;
}

const TrancheMetadataTable: React.FC<TrancheMetadataTableProps> = ({ metadata }) => {
  if (!metadata.trancheMetadata || Object.keys(metadata.trancheMetadata).length === 0) {
    return null;
  }

  return (
    <Stack align="stretch" justify="center" gap="md">
      <Text size="sm" fw={600}>
        Parametry transz i dane globalne:
      </Text>
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>Transza</Table.Th>
            <Table.Th>Wibor (%)</Table.Th>
            <Table.Th>Stawka WIBOR</Table.Th>
            <Table.Th>Liczba miesięcy</Table.Th>
            {metadata.globalMetadata?.DUTY && <Table.Th>Obowiązek</Table.Th>}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {Object.entries(metadata.trancheMetadata).map(([id, trancheData]) => (
            <Table.Tr key={id}>
              <Table.Td>{id}</Table.Td>
              <Table.Td>{trancheData.WIBOR_PERCENT ?? 'Brak'}%</Table.Td>
              <Table.Td>
                {trancheData.WIBOR_RATE
                  ? `${(Number(trancheData.WIBOR_RATE) * 100).toFixed(3)}%`
                  : 'Brak'}
              </Table.Td>
              <Table.Td>{trancheData.MONTHS_COUNT ?? 'Brak'}</Table.Td>
              {metadata.globalMetadata?.DUTY && (
                <Table.Td>{Number(metadata.globalMetadata.DUTY) * 100}%</Table.Td>
              )}
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </Stack>
  );
};

export default TrancheMetadataTable;
