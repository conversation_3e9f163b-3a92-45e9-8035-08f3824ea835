.editablePriceContainer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.editablePriceRow {
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.editablePriceInput {
    width: 80px;
    padding: 5px;
    border-radius: 6px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    transition: all 0.3s ease;
}

.editablePriceInput:focus {
    border-color: var(--mantine-color-orange-6);
    background-color: #fff;
    box-shadow: 0 0 5px var(--mantine-color-orange-6);
    outline: none;
}