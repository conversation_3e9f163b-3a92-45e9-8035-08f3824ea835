import {useQuery} from '@tanstack/react-query';
import {RecommendationService} from '@/services/RecommendationService';
import {ApiListResponse} from "@/types/Common";
import {Recommendation, RecommendationStatus, RecommendationStatusMap} from "@/types/Recommendation";
import {isOverdue} from "@/utils/dateUtils";


export const mapStatus = (status: string): RecommendationStatus => {
    const statusMap: { [key: string]: RecommendationStatus } = {
        'new': RecommendationStatus.NEW,
        'send': RecommendationStatus.SEND,
        'error': RecommendationStatus.ERROR,
        'accepted_order_pending': RecommendationStatus.ACCEPTED_ORDER_PENDING,
        'rejected': RecommendationStatus.REJECTED,
        'order_placed': RecommendationStatus.ORDER_PLACED,
        'completed': RecommendationStatus.COMPLETED,
        'not_completed': RecommendationStatus.NOT_COMPLETED,
        'added_to_wallet': RecommendationStatus.ADDED_TO_WALLET,
    };

    const mappedStatus = statusMap[status.toLowerCase()];
    if (!mappedStatus) {
        throw new Error(`Unknown status: ${status}`);
    }
    return mappedStatus;
};


const useGetRecommendations = (params) => {
    return useQuery<ApiListResponse<Recommendation>>({
        queryKey: ['recommendations', params],
        queryFn: async () => {
            const response = await RecommendationService.fetchRecommendations(params);
            const mappedData = response.content.map(item => ({
                ...item,
                isOverdue: isOverdue(item.deadline),
                status: RecommendationStatusMap[mapStatus(item.status)]?.label,
            }));
            return {
                ...response,
                content: mappedData,
            };
        },
        staleTime: 5 * 60 * 1000,
        gcTime: 0
    });
};

export default useGetRecommendations;