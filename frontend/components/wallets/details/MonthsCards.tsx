import { Container, Grid } from '@mantine/core';
import MonthCard from '@/components/wallets/create/WalletForm/MonthCard';
import { TimeUnit } from '@/types/Wallet';

type gutterProps = {
  base: number | string;
  xs: number | string;
  md: number | string;
  xl: number | string;
};
const MonthsCards = ({ groupedPrices, groupedTranches, products }) => {
  const marginTop: number = 20;
  const gutter: gutterProps = {
    base: 5,
    xs: 'md',
    md: 'xl',
    xl: 50,
  };
  const columnSpan: number = 4;
  const units: TimeUnit[] = [
    TimeUnit.M1,
    TimeUnit.M2,
    TimeUnit.M3,
    TimeUnit.M4,
    TimeUnit.M5,
    TimeUnit.M6,
    TimeUnit.M7,
    TimeUnit.M8,
    TimeUnit.M9,
    TimeUnit.M10,
    TimeUnit.M11,
    TimeUnit.M12,
  ];

  return (
    <Container fluid mt={marginTop}>
      <Grid gutter={{ base: gutter.base, xs: gutter.xs, md: gutter.md, xl: gutter.xl }}>
        {units.map((unit: TimeUnit, key: number) => (
          <Grid.Col span={columnSpan} key={key}>
            <MonthCard
              data={groupedPrices[unit]}
              timeUnit={unit}
              tranches={groupedTranches[unit]}
              products={products[unit]}
            />
          </Grid.Col>
        ))}
      </Grid>
    </Container>
  );
};

export default MonthsCards;
