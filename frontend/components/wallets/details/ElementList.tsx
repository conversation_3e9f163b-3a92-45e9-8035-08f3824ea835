import { MantineReactTable, useMantineReactTable } from 'mantine-react-table';
import { ActionIcon, Button, Container, Group } from '@mantine/core';
import React, { useMemo } from 'react';
import { IconPencil, IconTrash } from '@tabler/icons-react';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { TimeUnit } from '@/types/Wallet';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { PERMISSIONS } from '@/utils/permissions';
// @ts-ignore
import { MRT_Localization_PL } from 'mantine-react-table/locales/pl/index.esm';
import { formatValue } from '@/utils/formatters';

const ElementList = ({
  walletQuery,
  setElementModalOpen,
  setIsElementEditForm,
  elementForm,
  setSelectedElement,
  setDeleteModalOpen,
}) => {
  const router = useRouter();

  const { t } = useTranslation('common');

  const elementColumns = useMemo(
    () => [
      { accessorFn: (data: any) => t(data.type), header: 'Element' },
      { accessorFn: (data: any) => t(data.media), header: 'Typ kontraktu' },
      { accessorFn: (data: any) => formatValue(data.value), header: 'Wartość' },
      { accessorKey: 'timeUnit', header: 'Jednostka czasu' },
      {
        id: 'actions',
        header: 'Operacje',
        Cell: ({ row }: { row: { original: any } }) => (
          <Group>
            <ActionIcon
              size="md"
              onClick={() => {
                handleElementEdit(row.original);
              }}
            >
              <IconPencil size={24} stroke={1.5} />
            </ActionIcon>
            <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.DELETE_WALLET] } }}>
              <ActionIcon
                size="md"
                color="red"
                onClick={() => {
                  openDeleteModal(row.original.id);
                }}
              >
                <IconTrash size={24} stroke={1.5} />
              </ActionIcon>
            </RoleBasedComponent>
          </Group>
        ),
      },
    ],
    []
  );

  const elementTable = useMantineReactTable({
    columns: elementColumns,
    data: walletQuery?.data?.elements ?? [],
    manualSorting: true,
    manualPagination: true,
    localization: MRT_Localization_PL,
    state: { isLoading: walletQuery.isLoading },
    renderTopToolbarCustomActions: () => (
      <Group mt="md">
        <Button onClick={handleElementAdd} color="orange">
          Dodaj element
        </Button>
      </Group>
    ),
  });

  const handleElementAdd = () => {
    setElementModalOpen(true);
    setIsElementEditForm(null);
    elementForm.reset({
      walletId: router.query.id,
      type: '',
      media: '',
      value: 0,
      timeUnit: TimeUnit.Y,
    });
  };

  const handleElementEdit = (component: any) => {
    setIsElementEditForm(component);
    setElementModalOpen(true);
    elementForm.reset(component);
  };

  const openDeleteModal = (elementId: string) => {
    setSelectedElement(elementId);
    setDeleteModalOpen(true);
  };

  return (
    <Container fluid mt={20}>
      <MantineReactTable table={elementTable} />
    </Container>
  );
};

export default ElementList;
