import ConfirmModal from '@/components/common/forms/ConfirmModal/ConfirmModal';
import React from 'react';
import ElementService from '@/services/ElementService';

const ElementDeleteForm = ({
  setDeleteModalOpen,
  isDeleteModalOpen,
  setSelectedElement,
  selectedElementId,
  walletQuery,
}) => {
  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setSelectedElement(null);
  };

  const confirmDelete = async () => {
    if (selectedElementId) {
      await ElementService.deleteElement(selectedElementId);
      walletQuery.refetch();
      closeDeleteModal();
    }
  };

  return (
    <ConfirmModal
      opened={isDeleteModalOpen}
      onClose={closeDeleteModal}
      onConfirm={confirmDelete}
      title="Potwierdzenie usunięcia"
    >
      Czy na pewno chcesz usunąć ten element?
    </ConfirmModal>
  );
};
export default ElementDeleteForm;
