import { useWalletFormStore } from '@/stores/wallet-form-store/wallet-form-provider';
import { TimeUnit } from '@/types/Wallet';
import styles from './SimulationCards.module.css';
import SimulationMonthCard from '@/components/wallets/simulations/SimulationCards/SimulationMonthCard';

export const monthlyUnits = [
  TimeUnit.M1,
  TimeUnit.M2,
  TimeUnit.M3,
  TimeUnit.M4,
  TimeUnit.M5,
  TimeUnit.M6,
  TimeUnit.M7,
  TimeUnit.M8,
  TimeUnit.M9,
  TimeUnit.M10,
  TimeUnit.M11,
  TimeUnit.M12,
];

export const timeUnitMapping = {
  Y: monthlyUnits,
  Q1: ['M1', 'M2', 'M3'],
  Q2: ['M4', 'M5', 'M6'],
  Q3: ['M7', 'M8', 'M9'],
  Q4: ['M10', 'M11', 'M12'],
};

//TODO: Wydaje mi się, że dane powinny zostać zwrócone z backendu w takiej formie.
// Cardy powinny bezmyślnie wyświetlić 12 miesięcy. Jak zmieni się algorytm podziałki to będziemy musieli
// szukać kodu za to odpowiedzialnego na froncie :D
// Dodatkowo komponent "MonthCards.tsx" oraz "SimulationCards.tsx" docelowo powinny stać się jednym komponentem.

const groupByTimeUnit = (data, timeUnits) =>
  timeUnits.reduce((acc, unit) => {
    acc[unit] = data.filter((item) => {
      const applicableMonths = timeUnitMapping[item.timeUnit] || [item.timeUnit];
      if (applicableMonths.includes(unit)) {
        item.originalTimeUnit = item.timeUnit;
        return true;
      }
      return false;
    });
    return acc;
  }, {});

const SimulationCards = () => {
  const { prices, tranches } = useWalletFormStore((state) => ({
    prices: state.prices,
    tranches: state.tranches,
  }));

  const groupedPrices = groupByTimeUnit(prices, monthlyUnits);
  const groupedTranches = groupByTimeUnit(tranches, monthlyUnits);

  return (
    <div className={styles.container}>
      {monthlyUnits.map((timeUnit) => (
        <div key={timeUnit} className={styles.cardBox}>
          <SimulationMonthCard
            data={groupedPrices[timeUnit]}
            timeUnit={timeUnit}
            tranches={groupedTranches[timeUnit]}
          />
        </div>
      ))}
    </div>
  );
};

export default SimulationCards;
