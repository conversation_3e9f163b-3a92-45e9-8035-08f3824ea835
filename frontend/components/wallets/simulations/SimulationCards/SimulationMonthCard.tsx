import { Progress } from '@mantine/core';
import { ElementTypeSimulation } from '@/types/Wallet';
import { formatValue } from '@/utils/formatters';
import { getEnumKey } from '@/utils/get-enum-key';
import { calculateVolumeBreakdown, calculateVolumePercentages } from '@/utils/trancheUtils';
import styles from './SimulationMonthCard.module.css';
import SimulationMonthCardRow from '@/components/wallets/simulations/SimulationCards/SimulationMonthCardRow';
import SimulationTrancheListTooltip from '@/components/wallets/simulations/SimulationCards/SimulationTrancheListAsTooltip';

const SimulationMonthCard = ({ data, tranches, timeUnit }) => {
  const findByType = (data, type) =>
    data?.find((item) => item.type === getEnumKey(ElementTypeSimulation, type)) || null;

  // główne
  const baseTranches = (tranches || []).filter(
    (tranche) => !(tranche.contract && tranche.contract['@type'] === 'PropertyRight')
  );

  // prawa majątkowe
  const rightsTranches = (tranches || []).filter(
    (tranche) => tranche.contract && tranche.contract['@type'] === 'PropertyRight'
  );

  // progress główne
  const {
    totalVolume: baseTotal,
    virtualVolume: baseVirtual,
    physicalVolume: basePhysical,
  } = calculateVolumeBreakdown(baseTranches);
  const { virtualPercentage: baseVirtualPerc, physicalPercentage: basePhysicalPerc } =
    calculateVolumePercentages(100, baseVirtual, basePhysical);
  const baseRemaining = Math.max(0, 100 - (baseVirtualPerc + basePhysicalPerc));

  // progress prawa majątkowe
  const {
    totalVolume: rightsTotal,
    virtualVolume: rightsVirtual,
    physicalVolume: rightsPhysical,
  } = calculateVolumeBreakdown(rightsTranches);
  const { virtualPercentage: rightsVirtualPerc, physicalPercentage: rightsPhysicalPerc } =
    calculateVolumePercentages(100, rightsVirtual, rightsPhysical);
  const rightsRemaining = Math.max(0, 100 - (rightsVirtualPerc + rightsPhysicalPerc));

  const detailElementTypes = [
    ElementTypeSimulation.NET,
    ElementTypeSimulation.COSTS,
    ElementTypeSimulation.PROFILE,
    ElementTypeSimulation.PROFILE_PERCENT,
    ElementTypeSimulation.GREEN_PROPERTY_RIGHTS,
    ElementTypeSimulation.WHITE_PROPERTY_RIGHTS,
    ElementTypeSimulation.BLUE_PROPERTY_RIGHTS,
    ElementTypeSimulation.TOTAL_PROPERTY_RIGHTS,
    ElementTypeSimulation.EXCISE,
    // ElementTypeSimulation.CORRECTION_FACTOR,
    // ElementTypeSimulation.TGE_CORRECTION_FACTOR,
    // ElementTypeSimulation.MULTIPLIER,
    // ElementTypeSimulation.CJ,
    // ElementTypeSimulation.WF,
    // ElementTypeSimulation.MARGIN,
  ];

  return (
    <div className={styles.box}>
      <div>
        <div className={styles.header}>
          <div>Symulacja {timeUnit}</div>
          <SimulationTrancheListTooltip tranches={tranches} timeUnit={timeUnit} />
        </div>
        <div className={styles.divider}></div>
      </div>
      <div className={styles.content}>
        <div>
          {detailElementTypes.map((type) => (
            <SimulationMonthCardRow key={type} price={findByType(data, type)} />
          ))}
          <SimulationMonthCardRow
            price={findByType(data, ElementTypeSimulation.TOTAL)}
            withAddedPriceStyling={true}
          />
        </div>
      </div>
      <div className={styles.boxFooter}>
        {/* pasek dla głównych */}
        <div className={styles.smallText}>
          Zakupiono {formatValue(basePhysical)}%, Symulowane: {formatValue(baseVirtual)}%
        </div>
        <Progress.Root
          size="xl"
          radius="md"
          className={styles.progressRoot}
          style={{
            '--physicalPercentage': `${basePhysicalPerc}%`,
            '--virtualPercentage': `${baseVirtualPerc}%`,
            '--remainingPercentage': `${baseRemaining}%`,
          }}
        >
          {basePhysicalPerc > 0 && (
            <Progress.Section value={basePhysicalPerc} color="blue">
              <Progress.Label className={`${styles.progressLabel} ${styles.progressLabelPhysical}`}>
                {formatValue(basePhysicalPerc)}%
              </Progress.Label>
            </Progress.Section>
          )}
          {baseVirtualPerc > 0 && (
            <Progress.Section value={baseVirtualPerc} color="orange">
              <Progress.Label className={`${styles.progressLabel} ${styles.progressLabelVirtual}`}>
                {formatValue(baseVirtualPerc)}%
              </Progress.Label>
            </Progress.Section>
          )}
          {baseRemaining > 0 && (
            <Progress.Section value={baseRemaining} color="whiteSmoke">
              <Progress.Label
                className={`${styles.progressLabel} ${styles.progressLabelRemaining}`}
              >
                {formatValue(baseRemaining)}%
              </Progress.Label>
            </Progress.Section>
          )}
        </Progress.Root>

        {/* pasek dla praw majątkowych */}
        {rightsTotal > 0 && (
          <>
            <div className={styles.smallText}>
              Zakupiono {formatValue(rightsPhysical)}%, Symulowane: {formatValue(rightsVirtual)}%
            </div>
            <Progress.Root
              size="xl"
              radius="md"
              className={styles.progressRoot}
              style={{
                '--physicalPercentage': `${rightsPhysicalPerc}%`,
                '--virtualPercentage': `${rightsVirtualPerc}%`,
                '--remainingPercentage': `${rightsRemaining}%`,
              }}
            >
              {rightsPhysicalPerc > 0 && (
                <Progress.Section value={rightsPhysicalPerc} color="ForestGreen">
                  <Progress.Label
                    className={`${styles.progressLabel} ${styles.progressLabelPhysical}`}
                  >
                    {formatValue(rightsPhysicalPerc)}%
                  </Progress.Label>
                </Progress.Section>
              )}
              {rightsVirtualPerc > 0 && (
                <Progress.Section value={rightsVirtualPerc} color="green">
                  <Progress.Label
                    className={`${styles.progressLabel} ${styles.progressLabelVirtual}`}
                  >
                    {formatValue(rightsVirtualPerc)}%
                  </Progress.Label>
                </Progress.Section>
              )}
              {rightsRemaining > 0 && (
                <Progress.Section value={rightsRemaining} color="whiteSmoke">
                  <Progress.Label
                    className={`${styles.progressLabel} ${styles.progressLabelRemaining}`}
                  >
                    {formatValue(rightsRemaining)}%
                  </Progress.Label>
                </Progress.Section>
              )}
            </Progress.Root>
          </>
        )}
      </div>
    </div>
  );
};

export default SimulationMonthCard;
