import { useState } from 'react';
import { ContractService } from '@/services/ContractService';
import { SimulationService } from '@/services/SimulationService';
import { useWalletFormStore } from '@/stores/wallet-form-store/wallet-form-provider';
import { notify } from '@/utils/notify';
import { NotificationType } from '@/types/Common';
import { createWalletPayload } from '@/components/wallets/simulations/useSimulationComponent';

export const useFullPurchaseSimulation = (wallet) => {
  const { setPrices, setTranches } = useWalletFormStore((state) => state);
  const [error, setError] = useState(null);

  const processVirtualTranches = (tranches) =>
    tranches
      .filter((tranche) => tranche.virtual === true)
      .map((tranche) => ({
        ...tranche,
        contract: tranche.contract.id,
      }));

  const loadContractsForAgreement = async (agreementId) => {
    try {
      const response = await ContractService.getContractsByAgreement(agreementId);
      return response.content.map((contract) => ({
        value: contract.id,
        label: contract.name,
        timeUnit: contract.timeUnit || null,
      }));
    } catch (err) {
      const errorMessage =
        err?.response?.data?.message || 'Wystąpił błąd podczas ładowania kontraktów.';
      notify(NotificationType.ERROR, errorMessage);
      throw err;
    }
  };

  const handleFullPurchase = async () => {
    if (!wallet) return;

    try {
      const payload = createWalletPayload(wallet);
      const { prices, tranches, missingContracts } = await SimulationService.fullPurchase(payload);

      if (Array.isArray(missingContracts) && missingContracts.length > 0) {
        const missing = missingContracts.join(', ');
        notify(NotificationType.INFO, `Brakuje notowań dla: ${missing}`);
      }

      setPrices(prices);
      setTranches(tranches);

      notify(NotificationType.SUCCESS, 'Symulacja do 100% zakończona sukcesem!');
      return processVirtualTranches(tranches);
    } catch (err) {
      const validationMessages = err?.response?.data?.validation?.messages;
      const hasValidationMessage =
        Array.isArray(validationMessages) && validationMessages.length > 0;

      const validationReason = hasValidationMessage ? validationMessages[0]?.reason : null;

      const fallbackMessage = err?.response?.data?.message;

      const errorMessage = validationReason || fallbackMessage || 'Błąd podczas symulacji do 100%';

      setError(errorMessage);
      notify(NotificationType.ERROR, errorMessage);
    }
  };

  return { handleFullPurchase, loadContractsForAgreement, error };
};
