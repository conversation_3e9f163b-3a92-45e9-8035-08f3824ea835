import bundleAnalyzer from '@next/bundle-analyzer';
import nextTranslate from 'next-translate-plugin';

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

export default nextTranslate(
  withBundleAnalyzer({
    compress: false,
    swcMinify: true,
    productionBrowserSourceMaps: true,
    reactStrictMode: true,
    output: 'standalone',
    logging: {
      fetches: {
        fullUrl: true,
      },
    },
    eslint: {
      ignoreDuringBuilds: true,
    },
    typescript: {
      ignoreBuildErrors: true,
    },

    // CDN
    // assetPrefix:
    //   process.env.NODE_ENV === 'production'
    //     ? 'https://portfel-efbeeqa2brgfhye6.z03.azurefd.net'
    //     : '',
    //
    // images: {
    //   domains: ['portfel-efbeeqa2brgfhye6.z03.azurefd.net'],
    //   unoptimized: false,
    // },

    matcher: [
      /*
       * Match all request paths except for the ones starting with:
       * - .swa (Azure Static Web Apps)
       */
      '/((?!.swa).*)',
    ],
    // CORS
    headers: async function () {
      return [
        {
          // matching all API routes
          source: '/api/:path*',
          headers: [
            {
              key: 'Access-Control-Allow-Credentials',
              value: 'true',
            },
            { key: 'Access-Control-Allow-Origin', value: '*' },
            {
              key: 'Access-Control-Allow-Methods',
              value: 'GET,DELETE,PATCH,POST,PUT',
            },
            {
              key: 'Access-Control-Allow-Headers',
              value:
                'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version',
            },
          ],
        },
        // CDN HEADERS
        // {
        //   source: '/_next/static/(.*)',
        //   headers: [
        //     {
        //       key: 'Cache-Control',
        //       value: 'public, max-age=31536000, immutable',
        //     },
        //   ],
        // },
      ];
    },

    // BACKEND REWRITES
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL}/api/:path*`
        }
      ];
    },
  })
);
