{"compilerOptions": {"target": "es2020", "module": "esnext", "jsx": "preserve", "strict": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@api/*": ["src/generated/*"], "shared-types": ["../../packages/shared-types/src"]}, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}