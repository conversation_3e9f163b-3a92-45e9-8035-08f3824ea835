export interface PriceDto {
  date: string;
  contract: string;
  value: number;
  volume: number;
  percentageChange?: number;
}

export interface AveragePriceDto {
  contract: string;
  periodStart: string;
  periodEnd: string;
  averagePrice: number;
  dataPoints: number;
}

export interface AverageSpotPriceResponse {
  overall: AveragePriceDto[];
  monthly: AveragePriceDto[];
}

export interface MarketData {
  contractName: string;
  previousPrice: number;
  currentPrice: number;
  dailyChangePercentage: number;
  monthlyChangePercentage: number;
}