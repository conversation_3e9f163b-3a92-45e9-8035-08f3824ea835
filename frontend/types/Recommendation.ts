import {TimeUnit} from "@/types/Wallet";

export type Recommendation = {
    id: string;
    status: string;
    customer: string;
    customerName: string;
    contractId: string;
    contract: string;
    carrier: string;
    purchaseMethod: string;
    volume: string;
    deadline: string;
    price: string;
    supplierName: string;
    emailTemplateComment: string;
    executor: string;
    requiresCustomerAcceptance: boolean;
    sendRecommendation: boolean;
    authorizedBuyers: string[];
    orderType: string;
    availableActions: RecommendationAction[];
    agreementGroup?: string;
    createdAt: string
    humanReadableAgreementId: string;
    timeUnit: TimeUnit;
};

export enum RecommendationStatus {
    NEW = "new",
    SEND = "send",
    ERROR = "error",
    ACCEPTED_ORDER_PENDING = "acceptedOrderPending",
    REJECTED = "rejected",
    ORDER_PLACED = "orderPlaced",
    COMPLETED = "completed",
    NOT_COMPLETED = "notCompleted",
    ADDED_TO_WALLET = "addedToWallet",
}

export const RecommendationStatusMap = {
    [RecommendationStatus.NEW]: { label: "Nowa", className: "statusNew" },
    [RecommendationStatus.SEND]: { label: "Wysłana", className: "statusSend" },
    [RecommendationStatus.ERROR]: { label: "Błąd wysyłki", className: "statusError" },
    [RecommendationStatus.ACCEPTED_ORDER_PENDING]: { label: "Zaakceptowana", className: "statusAcceptedOrderPending" },
    [RecommendationStatus.REJECTED]: { label: "Odrzucona", className: "statusRejected" },
    [RecommendationStatus.ORDER_PLACED]: { label: "Zlecono zakup", className: "statusOrderPlaced" },
    [RecommendationStatus.COMPLETED]: { label: "Zrealizowany", className: "statusCompleted" },
    [RecommendationStatus.NOT_COMPLETED]: { label: "Niezrealizowany", className: "statusNotCompleted" },
    [RecommendationStatus.ADDED_TO_WALLET]: { label: "Dodany do portfela", className: "statusAddedToWallet" },
};

export enum RecommendationAction {
    VIEW = "VIEW",
    DELETE = "DELETE",
    EDIT = "EDIT",
    SEND = "SEND",
    RETRY = "RETRY",
    MARK_AS_ACCEPTED = "MARK_AS_ACCEPTED",
    MARK_AS_REJECTED = "MARK_AS_REJECTED",
    MARK_AS_ORDERED = "MARK_AS_ORDERED",
    MARK_AS_COMPLETED = "MARK_AS_COMPLETED",
    MARK_AS_NOT_COMPLETED = "MARK_AS_NOT_COMPLETED",
    ADD_TO_WALLET = "ADD_TO_WALLET",
    COPY = "COPY"
}

export const mapLabelToStatus = (label: string): RecommendationStatus => {
    const statusMap = {
        "Nowa": RecommendationStatus.NEW,
        "Wysłana": RecommendationStatus.SEND,
        "Błąd wysyłki": RecommendationStatus.ERROR,
        "Zaakceptowana": RecommendationStatus.ACCEPTED_ORDER_PENDING,
        "Odrzucona": RecommendationStatus.REJECTED,
        "Zlecono zakup": RecommendationStatus.ORDER_PLACED,
        "Zrealizowany": RecommendationStatus.COMPLETED,
        "Niezrealizowany": RecommendationStatus.NOT_COMPLETED,
        "Dodany do portfela": RecommendationStatus.ADDED_TO_WALLET,
    };

    const status = statusMap[label];
    if (!status) {
        throw new Error(`Unknown status label: ${label}`);
    }

    return status;
};

export type CreateRecommendationResponse = {
    id: string;
};

export type UpdateRecommendationResponse = {
    id: string;
};

export interface ValidationError {
    row: number;
    message: string;
}

export interface ValidationResult {
    errors: ValidationError[];
    errorFileId?: string;
    successfulRecords: number;
}

export interface ValidationStore {
    validationResult: ValidationResult | null;
    hasValidationResult: boolean;
    setValidationResult: (result: ValidationResult) => void;
    clearValidationResult: () => void;
}