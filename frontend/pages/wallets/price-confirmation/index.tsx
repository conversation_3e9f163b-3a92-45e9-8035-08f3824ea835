import { Container } from '@mantine/core';
import { HeaderWithTitle } from '@/components/common/shared/HeaderWithTitle/HeaderWithTitle';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { PERMISSIONS } from '@/utils/permissions';
import PriceConfirmation from '@/components/wallets/price-confirmation/PriceConfirmation';

const PriceConfirmationPage = () => (
  <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_WALLET] } }}>
    <Container fluid>
      <HeaderWithTitle title="Przegląd zakupów" />
      <Container fluid mt="md">
        <PriceConfirmation />
      </Container>
    </Container>
  </RoleBasedComponent>
);

export default PriceConfirmationPage;
