import { Container } from '@mantine/core';
import { HeaderWithTitle } from '@/components/common/shared/HeaderWithTitle/HeaderWithTitle';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { PERMISSIONS } from '@/utils/permissions';
import AnnualPurchaseOverviewList from '@/components/wallets/annual-purchase-overview/AnnualPurchaseOverviewList';

/**
 *    <Container fluid>
 *       <HeaderWithTitle title="Przegląd wszystkich zakupów" />
 *         <ShoppingOverviewList/>
 *     </Container>
 * @constructor
 */

const WalletListPage = () => (
  <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_WALLET] } }}>
    <Container fluid>
      <HeaderWithTitle title="Przegląd zakupów" />
      <Container fluid mt="md">
        <AnnualPurchaseOverviewList />
      </Container>
    </Container>
  </RoleBasedComponent>
);

export default WalletListPage;
