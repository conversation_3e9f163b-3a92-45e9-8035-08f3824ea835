import React from 'react';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import ContractList from '@/components/agreements/details/ContractList';
import { PERMISSIONS } from '@/utils/permissions';
import { HeaderWithTitle } from '@/components/common/shared/HeaderWithTitle/HeaderWithTitle';
import { Container, Loader } from '@mantine/core';
import useContractList from '@/components/agreements/details/useContract';
import { useRouter } from 'next/router';

export default function AgreementDetailsContractsPage() {
  const router = useRouter();
  const labelFromQuery = router.query.label as string | undefined;

  const contractQuery = useContractList();

  const headerTitle = labelFromQuery
    ? `Lista kontraktów umowy: ${labelFromQuery}`
    : 'Lista kontraktów umowy';

  if (contractQuery.isLoading) {
    return <Loader color="orange" size="md" />;
  }

  return (
    <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_AGREEMENT] } }}>
      <Container fluid>
        <HeaderWithTitle title={headerTitle} />
        <ContractList />
      </Container>
    </RoleBasedComponent>
  );
}
