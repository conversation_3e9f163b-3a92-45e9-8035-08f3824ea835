import React from 'react';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import ContractList from '@/components/agreements/details/ContractList';
import { PERMISSIONS } from '@/utils/permissions';
import { HeaderWithTitle } from '@/components/common/shared/HeaderWithTitle/HeaderWithTitle';
import { Container } from '@mantine/core';
import useContractList from '@/components/agreements/details/useContractList';
import { useRouter } from 'next/router';

export default function AgreementDetailsContractsPage() {
  const router = useRouter();
  const labelFromQuery = router.query.label as string | undefined;

  const { data, isSuccess, isError } = useContractList();

  const headerTitle = labelFromQuery
    ? `Lista kontraktów umowy: ${labelFromQuery}`
    : 'Lista kontraktów umowy';

  if (isError) return <p style={{ padding: 16 }}>Ni<PERSON> udało się wczytać danych.</p>;

  return (
    <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_AGREEMENT] } }}>
      <Container fluid>
        <HeaderWithTitle title={headerTitle} />
        {isSuccess && <ContractList />}
      </Container>
    </RoleBasedComponent>
  );
}
