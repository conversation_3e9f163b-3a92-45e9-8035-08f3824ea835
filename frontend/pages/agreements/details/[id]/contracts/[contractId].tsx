import ContractDetails from '@/components/agreements/details/ContractDetails';
import RoleBasedComponent from '@/components/common/role/RoleBasedComponent';
import { PERMISSIONS } from '@/utils/permissions';
import {HeaderWithTitle} from "@/components/common/shared/HeaderWithTitle/HeaderWithTitle";
import React from "react";
import {Container} from "@mantine/core";
import { useRouter } from 'next/router';

export default function AgreementDetailsContractDetailsPage() {

  const router = useRouter();
  const agreementLabel = router.query.label as string | undefined;
  const headerTitle = agreementLabel ? `Szczegóły kontraktu: ${agreementLabel}` : 'Szczegóły kontraktu';

  return (
    <RoleBasedComponent roles={{ resource: { wallet: [PERMISSIONS.VIEW_AGREEMENT] } }}>
      <Container fluid>
        <HeaderWithTitle title={headerTitle} />
        <ContractDetails />
      </Container>
    </RoleBasedComponent>
  );
}