/* (C)2025 */
package com.codemonkeys.wallet.application.auth.keycloak;

import com.codemonkeys.wallet.application.auth.common.ChangePasswordRequest;
import com.codemonkeys.wallet.application.auth.common.TenantRequest;
import com.codemonkeys.wallet.application.auth.common.UserCreationRequest;
import com.codemonkeys.wallet.application.auth.config.KeycloakProperties;
import com.codemonkeys.wallet.application.auth.domain.enums.UserAction;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

  private final KeycloakProperties keycloakProperties;
  private final TokenService tokenService;
  private final Keycloak keycloak;
  private final EmailService emailService;
  private final ObjectMapper objectMapper = new ObjectMapper();

  /**
   * Creates a new user in Keycloak.
   *
   * @param token the authorization token
   * @param userRequest the user creation request data
   * @return a ResponseEntity with the result of the operation
   */
  public ResponseEntity<String> createUser(String token, UserCreationRequest userRequest) {
    Keycloak tokenKeycloak = tokenService.getKeycloakWithToken(token);

    UserRepresentation user = createUserRepresentation(userRequest);

    Response response = tokenKeycloak.realm(keycloakProperties.getRealm()).users().create(user);
    String responseEntity = response.readEntity(String.class);

    if (response.getStatus() != 201) {
      log.info("Response status: {}", response.getStatus());
      log.debug("Response entity: {}", responseEntity);
      return ResponseEntity.status(response.getStatus()).body(responseEntity);
    }

    String userId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");

    setUserPassword(tokenKeycloak, userId, userRequest);
    addUserRole(tokenKeycloak, userId, userRequest.getRole());
    /* remove default roles (e.g., default-roles-wallet) which are automatically assigned and may inherit parent client permissions from 'wallet'. This is necessary to avoid over-privileging users like external clients or ES admins.*/
    if ("realm_external_client".equals(userRequest.getRole())
        || "realm_es_administration".equals(userRequest.getRole())) {
      removeDefaultRoles(tokenKeycloak, userId);
    }
    if (userRequest.getRequiredActions() != null) {
      EnumSet<UserAction> requiredActions = userRequest.getRequiredActions();
      if (requiredActions.contains(UserAction.VERIFY_EMAIL)
          && requiredActions.contains(UserAction.UPDATE_PASSWORD)) {
        emailService.sendVerifyAndPasswordResetEmail(token, userId);
      } else if (requiredActions.contains(UserAction.VERIFY_EMAIL)) {
        emailService.sendVerifyEmail(token, userId);
      } else if (requiredActions.contains(UserAction.UPDATE_PASSWORD)) {
        emailService.sendPasswordResetEmail(token, userId);
      }
    }

    return ResponseEntity.ok("User created with ID: " + userId);
  }

  /**
   * Removes all realm-level roles from the user that start with "default-roles".
   *
   * <p>This is typically used to prevent automatic assignment of default roles like
   * "default-roles-wallet" when creating users that should not have them.
   *
   * @param keycloak the Keycloak client instance
   * @param userId the ID of the user to remove the default roles from
   */
  private void removeDefaultRoles(Keycloak keycloak, String userId) {
    try {
      List<RoleRepresentation> rolesToRemove =
          keycloak.realm(keycloakProperties.getRealm()).roles().list().stream()
              .filter(role -> role.getName().startsWith("default-roles"))
              .collect(Collectors.toList());

      if (!rolesToRemove.isEmpty()) {
        keycloak
            .realm(keycloakProperties.getRealm())
            .users()
            .get(userId)
            .roles()
            .realmLevel()
            .remove(rolesToRemove);
        log.info("Removed default roles from user {}", userId);
      }
    } catch (Exception e) {
      log.warn("Could not remove default roles from user {}: {}", userId, e.getMessage());
    }
  }

  /**
   * Creates a UserRepresentation object based on the provided user creation request data.
   *
   * @param userRequest the user creation request data
   * @return the created UserRepresentation object
   */
  private UserRepresentation createUserRepresentation(UserCreationRequest userRequest) {
    UserRepresentation user = new UserRepresentation();
    user.setUsername(userRequest.getUsername());
    user.setEmail(userRequest.getEmail());
    user.setFirstName(userRequest.getFirstName());
    user.setLastName(userRequest.getLastName());
    user.setEnabled(true);

    setRequiredUserActions(user, userRequest);

    Map<String, List<String>> attributes = new HashMap<>();

    if (userRequest.getWhiteListTenant() != null && !userRequest.getWhiteListTenant().isEmpty()) {
      List<String> tenantValues =
          userRequest.getWhiteListTenant().stream()
              .map(
                  t -> {
                    try {
                      return objectMapper.writeValueAsString(t); // mini-JSON
                    } catch (JsonProcessingException e) {
                      throw new RuntimeException("Failed to convert tenant to JSON", e);
                    }
                  })
              .collect(Collectors.toList());
      attributes.put("whiteList_tenant", tenantValues);

      String tenantId = userRequest.getTenantId();
      if (tenantId == null || tenantId.isEmpty()) {
        tenantId = userRequest.getWhiteListTenant().get(0).getUuid();
      }
      attributes.put("tenant-id", List.of(tenantId));
    }

    user.setAttributes(attributes);
    return user;
  }

  /**
   * Sets the required user actions for the provided user representation based on the user creation
   * request data.
   *
   * @param user the UserRepresentation object
   * @param userRequest the user creation request data
   */
  private void setRequiredUserActions(UserRepresentation user, UserCreationRequest userRequest) {
    if (userRequest.getRequiredActions() == null) return;
    List<String> actions =
        userRequest.getRequiredActions().stream().map(Enum::name).collect(Collectors.toList());
    user.setRequiredActions(actions);
  }

  /**
   * Sets the password for the specified user in Keycloak.
   *
   * @param tokenKeycloak the Keycloak client instance
   * @param userId the ID of the user
   * @param userRequest the user creation request data containing the password
   */
  private void setUserPassword(
      Keycloak tokenKeycloak, String userId, UserCreationRequest userRequest) {
    if (userRequest.getPassword() == null || userRequest.getPassword().isEmpty()) {
      return;
    }
    CredentialRepresentation passwordCred = new CredentialRepresentation();
    boolean isTemp =
        userRequest.getRequiredActions() != null
            && userRequest.getRequiredActions().contains(UserAction.UPDATE_PASSWORD);
    passwordCred.setTemporary(isTemp);
    passwordCred.setType(CredentialRepresentation.PASSWORD);
    passwordCred.setValue(userRequest.getPassword());
    tokenKeycloak
        .realm(keycloakProperties.getRealm())
        .users()
        .get(userId)
        .resetPassword(passwordCred);
  }

  /**
   * Adds the specified role to the user in Keycloak.
   *
   * @param tokenKeycloak the Keycloak client instance
   * @param userId the ID of the user
   * @param role the role to be added to the user
   */
  private void addUserRole(Keycloak tokenKeycloak, String userId, String role) {
    if (role == null || role.isEmpty()) return;
    tokenKeycloak
        .realm(keycloakProperties.getRealm())
        .users()
        .get(userId)
        .roles()
        .realmLevel()
        .add(
            Collections.singletonList(
                tokenKeycloak
                    .realm(keycloakProperties.getRealm())
                    .roles()
                    .get(role)
                    .toRepresentation()));
  }

  /**
   * Retrieves a list of all users.
   *
   * @param token The authorization token required for authenticating the request.
   * @return A ResponseEntity containing a list of UserRepresentation objects, along with HTTP
   *     status 200 (OK).
   */
  public ResponseEntity<List<UserRepresentation>> getAllUsers(String token) {
    Keycloak tokenKeycloak = tokenService.getKeycloakWithToken(token);

    List<UserRepresentation> users =
        tokenKeycloak.realm(keycloakProperties.getRealm()).users().list();

    for (UserRepresentation user : users) {
      UserResource userResource =
          tokenKeycloak.realm(keycloakProperties.getRealm()).users().get(user.getId());
      List<RoleRepresentation> userRoles = userResource.roles().getAll().getRealmMappings();
      user.setRealmRoles(userRoles.stream().map(RoleRepresentation::getName).toList());

      unifyWhiteListTenantAttribute(user);
    }

    return ResponseEntity.ok(users);
  }

  /**
   * Retrieves a user identified by the given userId.
   *
   * @param token The authorization token required for authenticating the request.
   * @param userId The ID of the user to be retrieved.
   * @return A ResponseEntity containing the UserRepresentation object, along with HTTP status 200
   *     (OK).
   */
  public ResponseEntity<UserRepresentation> getUserById(String token, String userId) {
    Keycloak tokenKeycloak = tokenService.getKeycloakWithToken(token);

    UserRepresentation user =
        tokenKeycloak.realm(keycloakProperties.getRealm()).users().get(userId).toRepresentation();
    UserResource userResource =
        tokenKeycloak.realm(keycloakProperties.getRealm()).users().get(user.getId());
    List<RoleRepresentation> userRoles = userResource.roles().getAll().getRealmMappings();
    user.setRealmRoles(userRoles.stream().map(RoleRepresentation::getName).toList());

    unifyWhiteListTenantAttribute(user);

    return ResponseEntity.ok(user);
  }

  private void unifyWhiteListTenantAttribute(UserRepresentation user) {
    if (user.getAttributes() == null) return;
    List<String> multiValues = user.getAttributes().get("whiteList_tenant");
    if (multiValues == null || multiValues.isEmpty()) {
      return;
    }

    // każda wartość to np. {"uuid":"...","name":"..."}
    // parsujemy do TenantRequest
    List<TenantRequest> tenantList =
        multiValues.stream()
            .map(
                str -> {
                  try {
                    return objectMapper.readValue(str, TenantRequest.class);
                  } catch (IOException e) {
                    log.error("Error parsing whiteList_tenant mini-JSON: {}", str, e);
                    return null;
                  }
                })
            .filter(t -> t != null)
            .collect(Collectors.toList());

    // wstawiamy jako jedną wartość JSON
    try {
      String json = objectMapper.writeValueAsString(tenantList);
      user.getAttributes().put("whiteList_tenant", List.of(json));
    } catch (JsonProcessingException e) {
      log.error("Error converting tenantList to JSON array: ", e);
    }
  }

  public ResponseEntity<String> updateUser(
      String token, String userId, UserCreationRequest userRequest) {
    try {
      Keycloak tokenKeycloak = tokenService.getKeycloakWithToken(token);

      UserResource userResource =
          tokenKeycloak.realm(keycloakProperties.getRealm()).users().get(userId);
      UserRepresentation user = userResource.toRepresentation();

      user.setUsername(userRequest.getUsername());
      user.setEmail(userRequest.getEmail());
      user.setFirstName(userRequest.getFirstName());
      user.setLastName(userRequest.getLastName());
      user.setEnabled(userRequest.getEnabled() != null ? userRequest.getEnabled() : true);

      // Update tenants and tenantId
      try {
        Map<String, List<String>> updatedAttributes =
            updateTenantAttributes(
                user.getAttributes(), userRequest.getWhiteListTenant(), userRequest.getTenantId());
        user.setAttributes(updatedAttributes);
      } catch (IllegalArgumentException e) {
        return ResponseEntity.badRequest().body(e.getMessage());
      }

      userResource.update(user);

      // Update password if provided
      if (userRequest.getPassword() != null && !userRequest.getPassword().isEmpty()) {
        CredentialRepresentation passwordCred = new CredentialRepresentation();
        boolean isTemp =
            userRequest.getRequiredActions() != null
                && userRequest.getRequiredActions().contains(UserAction.UPDATE_PASSWORD);
        passwordCred.setTemporary(isTemp);
        passwordCred.setType(CredentialRepresentation.PASSWORD);
        passwordCred.setValue(userRequest.getPassword());
        userResource.resetPassword(passwordCred);
      }

      // role
      List<RoleRepresentation> roles = userResource.roles().realmLevel().listAll();
      RoleRepresentation currentRole = roles.isEmpty() ? null : roles.get(0);

      // Update role if provided
      if (userRequest.getRole() != null
          && !userRequest.getRole().isEmpty()
          && currentRole != null
          && !currentRole.getName().equals(userRequest.getRole())) {

        userResource.roles().realmLevel().remove(List.of(currentRole));
        userResource
            .roles()
            .realmLevel()
            .add(
                Collections.singletonList(
                    tokenKeycloak
                        .realm(keycloakProperties.getRealm())
                        .roles()
                        .get(userRequest.getRole())
                        .toRepresentation()));
      }

      return ResponseEntity.ok("User updated successfully");
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error updating user");
    }
  }

  /**
   * Updates or initializes tenant-related user attributes.
   *
   * @param attributes Existing user attributes, or {@code null} to create a new map.
   * @param whiteListTenant List of allowed tenant IDs. Must not be empty if provided.
   * @param tenantId Specific tenant ID to set. Defaults to the first entry in {@code
   *     whiteListTenant} if null.
   * @return Updated attributes map with "whiteList_tenant" and "tenant-id".
   * @throws IllegalArgumentException If {@code whiteListTenant} is empty or {@code tenantId} is not
   *     in {@code whiteListTenant}.
   */
  private Map<String, List<String>> updateTenantAttributes(
      Map<String, List<String>> attributes, List<TenantRequest> whiteListTenant, String tenantId) {

    if (attributes == null) {
      attributes = new HashMap<>();
    }

    if (whiteListTenant == null || whiteListTenant.isEmpty()) {
      attributes.put("whiteList_tenant", Collections.emptyList());
      attributes.put("tenant-id", Collections.emptyList());
      return attributes;
    }

    // tenantRequest -> String JSON
    List<String> tenantValues =
        whiteListTenant.stream()
            .map(
                t -> {
                  try {
                    return objectMapper.writeValueAsString(t);
                  } catch (JsonProcessingException e) {
                    throw new RuntimeException("Failed to convert tenant to JSON", e);
                  }
                })
            .collect(Collectors.toList());
    attributes.put("whiteList_tenant", tenantValues);

    if (tenantId != null) {
      boolean isValidTenant =
          whiteListTenant.stream().anyMatch(tenant -> tenant.getUuid().equals(tenantId));
      if (!isValidTenant) {
        throw new IllegalArgumentException("Provided tenant_id must exist in whiteList_tenant");
      }
      attributes.put("tenant-id", List.of(tenantId));
    } else {
      attributes.put("tenant-id", List.of(whiteListTenant.get(0).getUuid()));
    }

    return attributes;
  }

  /**
   * Changes the password of the authenticated user.
   *
   * @param token the authorization token
   * @param passwordRequest the password change request containing current and new passwords
   * @return a ResponseEntity with the result of the operation
   */
  public ResponseEntity<String> changePassword(
      String token, ChangePasswordRequest passwordRequest) {
    String strippedToken = tokenService.stripBearerToken(token);
    Keycloak tokenKeycloak =
        KeycloakBuilder.builder()
            .serverUrl(keycloakProperties.getAuthServerUrl())
            .realm(keycloakProperties.getRealm())
            .authorization(strippedToken)
            .build();

    // Fetch the user ID from the token
    String userId = tokenService.getUserIdFromToken(strippedToken);

    // Fetch the user
    UserResource userResource =
        tokenKeycloak.realm(keycloakProperties.getRealm()).users().get(userId);
    UserRepresentation user = userResource.toRepresentation();

    // Validate current password
    if (!validateCurrentPassword(user.getUsername(), passwordRequest.getCurrentPassword())) {
      return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Current password is incorrect");
    }

    // Check if new passwords match
    if (!passwordRequest.getNewPassword().equals(passwordRequest.getConfirmNewPassword())) {
      return ResponseEntity.badRequest().body("New passwords do not match");
    }

    // Update password
    CredentialRepresentation passwordCred = new CredentialRepresentation();
    passwordCred.setTemporary(false);
    passwordCred.setType(CredentialRepresentation.PASSWORD);
    passwordCred.setValue(passwordRequest.getNewPassword());

    userResource.resetPassword(passwordCred);

    return ResponseEntity.ok("Password changed successfully");
  }

  /**
   * Deletes a user identified by the given userId.
   *
   * @param token The authorization token required for authenticating the request.
   * @param userId The ID of the user to be deleted.
   * @return A ResponseEntity with HTTP status 204 (No Content) if the user is successfully deleted,
   *     or HTTP status 403 (Forbidden) if an error occurs during the deletion process.
   */
  public ResponseEntity<Void> deleteUser(String token, String userId) {
    try {
      Keycloak tokenKeycloak = tokenService.getKeycloakWithToken(token);

      tokenKeycloak.realm(keycloakProperties.getRealm()).users().get(userId).remove();
      return ResponseEntity.noContent().build();
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }
  }

  /**
   * Validates the current password of the user.
   *
   * @param username the username of the user
   * @param currentPassword the current password of the user
   * @return true if the current password is valid, false otherwise
   */
  private boolean validateCurrentPassword(String username, String currentPassword) {
    try {
      Keycloak keycloak =
          KeycloakBuilder.builder()
              .serverUrl(keycloakProperties.getAuthServerUrl())
              .realm(keycloakProperties.getRealm())
              .clientId(keycloakProperties.getResource())
              .clientSecret(keycloakProperties.getCredentials().getSecret())
              .username(username)
              .password(currentPassword)
              .grantType(OAuth2Constants.PASSWORD)
              .build();

      return keycloak.tokenManager().grantToken() != null;
    } catch (Exception e) {
      log.error("Error validating current password for user: {}", username, e);
      return false;
    }
  }

  /**
   * Sends a password reset email to the user with the given email address.
   *
   * @param email The email address of the user requesting a password reset.
   * @return true if the email was successfully sent, false if the user was not found.
   */
  public boolean sendPasswordResetEmailForUser(String email) {
    try {
      List<UserRepresentation> users =
          keycloak
              .realm(keycloakProperties.getRealm())
              .users()
              .search(null, null, null, email, 0, 10);

      if (users.isEmpty()) {
        log.warn("No user found with email: {}", email);
        return false;
      }

      String userId = users.get(0).getId();
      keycloak
          .realm(keycloakProperties.getRealm())
          .users()
          .get(userId)
          .executeActionsEmail(List.of("UPDATE_PASSWORD"));
      log.info("Password reset email sent to user with email: {}", email);
      return true;
    } catch (Exception e) {
      log.error("Error sending password reset email", e);
      return false;
    }
  }
}
