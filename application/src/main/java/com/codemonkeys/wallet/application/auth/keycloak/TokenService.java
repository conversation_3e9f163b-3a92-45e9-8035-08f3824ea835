/* (C)2024-2025 */
package com.codemonkeys.wallet.application.auth.keycloak;

import com.codemonkeys.wallet.application.auth.config.KeycloakProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import java.util.Base64;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class TokenService {

  private final KeycloakProperties keycloakProperties;
  private final RestTemplate restTemplate = new RestTemplate();
  private final ObjectMapper objectMapper = new ObjectMapper();
  private Keycloak keycloak;
  private static final HttpHeaders DEFAULT_HEADERS;

  static {
    DEFAULT_HEADERS = new HttpHeaders();
    DEFAULT_HEADERS.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
  }

  @PostConstruct
  public void init() {
    this.keycloak =
        KeycloakBuilder.builder()
            .serverUrl(keycloakProperties.getAuthServerUrl())
            .realm(keycloakProperties.getRealm())
            .clientId(keycloakProperties.getResource())
            .clientSecret(keycloakProperties.getCredentials().getSecret())
            .grantType(OAuth2Constants.CLIENT_CREDENTIALS)
            .build();
  }

  /**
   * Returns a Keycloak client instance with the provided authorization token.
   *
   * @param token the authorization token
   * @return the Keycloak client instance
   */
  public Keycloak getKeycloakWithToken(String token) {
    String strippedToken = stripBearerToken(token);
    return KeycloakBuilder.builder()
        .serverUrl(keycloakProperties.getAuthServerUrl())
        .realm(keycloakProperties.getRealm())
        .authorization(strippedToken)
        .build();
  }

  /**
   * Strips the "Bearer " prefix from the token if it exists.
   *
   * @param token the token string
   * @return the token string without the "Bearer " prefix
   */
  public String stripBearerToken(String token) {
    if (StringUtils.hasText(token) && token.startsWith("Bearer ")) {
      return token.substring(7);
    }
    return token;
  }

  /**
   * Retrieves an access token for the specified username and password.
   *
   * @param username the username
   * @param password the password
   * @return a ResponseEntity with the token data
   */
  public ResponseEntity<Map<String, Object>> getToken(String username, String password) {
    return getTokenResponse(username, password, "password");
  }

  /**
   * Refreshes the access token using the provided refresh token.
   *
   * @param refreshToken the refresh token
   * @return a ResponseEntity with the new token data
   */
  public ResponseEntity<Map<String, Object>> refreshToken(String refreshToken) {
    return getTokenResponse(refreshToken, null, "refresh_token");
  }

  /**
   * Retrieves a token response for the specified parameters.
   *
   * @param primaryParam the primary parameter (username or refresh token)
   * @param secondaryParam the secondary parameter (password), if applicable
   * @param grantType the grant type
   * @return a ResponseEntity with the token data
   */
  private ResponseEntity<Map<String, Object>> getTokenResponse(
      String primaryParam, String secondaryParam, String grantType) {
    MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
    body.add("client_id", keycloakProperties.getResource());
    body.add("client_secret", keycloakProperties.getCredentials().getSecret());
    body.add("grant_type", grantType);
    body.add(grantType.equals("password") ? "username" : "refresh_token", primaryParam);
    if (secondaryParam != null) {
      body.add("password", secondaryParam);
    }

    HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(body, DEFAULT_HEADERS);

    try {
      ResponseEntity<Map> response =
          restTemplate.exchange(getTokenUrl(), HttpMethod.POST, entity, Map.class);
      return handleResponse(response);
    } catch (HttpClientErrorException e) {
      return handleHttpClientErrorException(e);
    }
  }

  /**
   * Constructs the token URL.
   *
   * @return the token URL
   */
  private String getTokenUrl() {
    return String.format(
        "%s/realms/%s/protocol/openid-connect/token",
        keycloakProperties.getAuthServerUrl(), keycloakProperties.getRealm());
  }

  /**
   * Handles the response from the Keycloak server.
   *
   * @param response the response
   * @return a ResponseEntity with the response data
   */
  private ResponseEntity<Map<String, Object>> handleResponse(ResponseEntity<Map> response) {
    if (response.getStatusCode() == HttpStatus.OK) {
      return ResponseEntity.ok(response.getBody());
    } else {
      return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
    }
  }

  /**
   * Handles HttpClientErrorException by parsing the error response.
   *
   * @param e the HttpClientErrorException
   * @return a ResponseEntity with the error data
   */
  private ResponseEntity<Map<String, Object>> handleHttpClientErrorException(
      HttpClientErrorException e) {
    try {
      String responseBody = e.getResponseBodyAsString();
      if (responseBody != null && !responseBody.isEmpty()) {
        Map<String, Object> errorResponse = objectMapper.readValue(responseBody, Map.class);
        return ResponseEntity.status(e.getStatusCode()).body(errorResponse);
      } else {
        return ResponseEntity.status(e.getStatusCode()).body(Map.of("error", e.getStatusText()));
      }
    } catch (Exception ex) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(Map.of("error", "An error occurred while processing the error response"));
    }
  }

  /**
   * Extracts the user ID from the given token.
   *
   * @param token the token from which to extract the user ID
   * @return the extracted user ID
   */
  public String getUserIdFromToken(String token) {
    String[] tokenParts = token.split("\\.");
    if (tokenParts.length < 2) {
      throw new IllegalArgumentException("Invalid token");
    }
    String payload = new String(Base64.getUrlDecoder().decode(tokenParts[1]));
    JSONObject jsonObject = new JSONObject(payload);
    return jsonObject.getString("sub");
  }


  public ResponseEntity<Map<String, Object>> exchangeAuthorizationCode(String code) {

    MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
    form.add("grant_type", "authorization_code");
    form.add("client_id", keycloakProperties.getResource());
    form.add("redirect_uri", keycloakProperties.getRedirectUri());
    form.add("code", code);
    form.add("client_secret", keycloakProperties.getCredentials().getSecret());

    HttpEntity<MultiValueMap<String, String>> entity =
        new HttpEntity<>(form, DEFAULT_HEADERS);

    try {
      ResponseEntity<Map> resp = restTemplate
          .exchange(getTokenUrl(), HttpMethod.POST, entity, Map.class);
      return handleResponse(resp);
    } catch (HttpClientErrorException e) {
      return handleHttpClientErrorException(e);
    }
  }
}
