/* (C)2024-2025 */
package com.codemonkeys.wallet.application.auth;

import com.codemonkeys.wallet.application.auth.common.AuthConstants;
import com.codemonkeys.wallet.application.auth.common.ChangePasswordRequest;
import com.codemonkeys.wallet.application.auth.common.UserCreationRequest;
import com.codemonkeys.wallet.application.auth.keycloak.AuthService;
import com.codemonkeys.wallet.application.auth.keycloak.TokenService;
import com.codemonkeys.wallet.application.auth.keycloak.UserService;
import com.codemonkeys.wallet.application.tenant.TenantService;
import com.codemonkeys.wallet.common.framework.security.Permission;
import com.codemonkeys.wallet.common.framework.security.Permissions;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(AuthConstants.PATH)
@RequiredArgsConstructor
public class AuthController {

  private final AuthService authService;
  private final TokenService tokenService;
  private final UserService userService;
  private final TenantService tenantService;

  /**
   * Retrieves an access token for the specified username and password.
   *
   * @param username the username
   * @param password the password
   * @return a ResponseEntity with the token data
   */
  @PostMapping("/token")
  public ResponseEntity<Map<String, Object>> getToken(
      @RequestParam String username, @RequestParam String password) {
    ResponseEntity<Map<String, Object>> response = tokenService.getToken(username, password);
    if (response.getStatusCode().is2xxSuccessful()) {
      return response;
    } else if (response.getStatusCode().is4xxClientError()) {
      return ResponseEntity.status(403).body(response.getBody());
    }
    return ResponseEntity.status(500).body(Map.of("message", "Internal Server Error"));
  }

  /**
   * Refreshes the access token using the provided refresh token.
   *
   * @param body the request body containing the refresh token
   * @return a ResponseEntity with the new token data
   */
  @PostMapping("/refresh-token")
  public ResponseEntity<Map<String, Object>> refreshToken(@RequestBody Map<String, String> body) {
    String refreshToken = body.get("refresh_token");
    if (!StringUtils.hasText(refreshToken)) {
      return ResponseEntity.badRequest()
          .body(Map.of("error", "Required parameter 'refresh_token' is not present."));
    }
    return tokenService.refreshToken(refreshToken);
  }

  /**
   * Logs out the user by invalidating the refresh token.
   *
   * @param body the request body containing the refresh token
   * @return a ResponseEntity with the result of the operation
   */
  @PostMapping("/logout")
  public ResponseEntity<?> logout(@RequestBody Map<String, String> body) {
    String refreshToken = body.get("refresh_token");
    if (!StringUtils.hasText(refreshToken)) {
      return ResponseEntity.badRequest().body("Required parameter 'refresh_token' is not present.");
    }
    return authService.logout(refreshToken);
  }

  /**
   * Creates a new user in Keycloak.
   *
   * @param token the authorization token
   * @param userRequest the user creation request data
   * @return a ResponseEntity with the result of the operation
   */
  @PostMapping("/users")
  @Permission(Permissions.CREATE_EMPLOYEE_ACCOUNT)
  public ResponseEntity<String> createUser(
      @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
      @Valid @RequestBody UserCreationRequest userRequest) {
    return userService.createUser(token, userRequest);
  }

  /**
   * Handles GET requests for retrieving all users.
   *
   * @param token The authorization token passed in the request header.
   * @return A ResponseEntity containing a list of UserRepresentation objects.
   */
  @GetMapping("/users")
  @Permission(Permissions.VIEW_EMPLOYEE_ACCOUNT)
  public ResponseEntity<List<UserRepresentation>> getAllUsers(
      @RequestHeader(HttpHeaders.AUTHORIZATION) String token) {
    return userService.getAllUsers(token);
  }

  /**
   * Handles GET requests for retrieving a user by ID.
   *
   * @param token The authorization token passed in the request header.
   * @param userId The ID of the user to be retrieved.
   * @return A ResponseEntity containing the UserRepresentation object with the user details, or an
   *     appropriate error status if the user is not found or if there is an authorization error.
   */
  @GetMapping("/users/{userId}")
  @Permission(Permissions.VIEW_EMPLOYEE_ACCOUNT)
  public ResponseEntity<UserRepresentation> getUserById(
      @RequestHeader(HttpHeaders.AUTHORIZATION) String token, @PathVariable String userId) {
    return userService.getUserById(token, userId);
  }

  /**
   * Handles PUT requests for updating a user by ID.
   *
   * @param token The authorization token passed in the request header.
   * @param userId The ID of the user to be updated.
   * @param userRequest The user data to update.
   * @return A ResponseEntity with the result of the update operation. Returns status 200 (OK) if
   *     the user is successfully updated, or an appropriate error status if the update fails.
   */
  @PutMapping("/users/{userId}")
  @Permission(Permissions.UPDATE_EMPLOYEE_ACCOUNT)
  public ResponseEntity<String> updateUser(
      @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
      @PathVariable String userId,
      @Valid @RequestBody UserCreationRequest userRequest) {
    return userService.updateUser(token, userId, userRequest);
  }

  /**
   * Endpoint for changing the user's password.
   *
   * @param token Authorization header containing the user's JWT token.
   * @param passwordRequest Request body containing the current password, new password, and
   *     confirmation of the new password.
   * @return HTTP response indicating the result of the password change operation. Returns status
   *     200 (OK) with a success message if the password was changed successfully. Returns an
   *     appropriate error status and message if the operation failed.
   */
  @PostMapping("/change-password")
  public ResponseEntity<String> changePassword(
      @RequestHeader(HttpHeaders.AUTHORIZATION) String token,
      @Valid @RequestBody ChangePasswordRequest passwordRequest) {
    return userService.changePassword(token, passwordRequest);
  }

  /**
   * Endpoint for deleting a user by ID.
   *
   * @param token Authorization header containing the user's JWT token.
   * @param userId The ID of the user to delete.
   * @return HTTP response indicating the result of the delete operation. Returns status 204 (No
   *     Content) if the user was deleted successfully. Returns an appropriate error status if the
   *     operation failed.
   */
  @DeleteMapping("/users/{userId}")
  @Permission(Permissions.DELETE_EMPLOYEE_ACCOUNT)
  public ResponseEntity<Void> deleteUser(
      @RequestHeader(HttpHeaders.AUTHORIZATION) String token, @PathVariable String userId) {
    return userService.deleteUser(token, userId);
  }

  /**
   * Endpoint for requesting a password reset for a given email. This endpoint is public and does
   * not require authentication.
   *
   * @param email The email of the user requesting a password reset.
   * @return HTTP response indicating the result of the password reset request.
   */
  @PostMapping("/reset-password")
  public ResponseEntity<String> resetPassword(@RequestParam String email) {
    boolean result = userService.sendPasswordResetEmailForUser(email);
    if (result) {
      return ResponseEntity.ok("Password reset email sent successfully");
    } else {
      return ResponseEntity.status(HttpStatus.NOT_FOUND)
          .body("User with the given email not found");
    }
  }

  /**
   * Switches the tenant for the current session and refreshes the token.
   *
   * <p>This endpoint receives the new tenant ID and refresh token in the request body, delegates
   * the tenant switch and token refresh logic to the TenantService, and returns the refreshed token
   * data.
   *
   * @param accessToken the current access token provided in the Authorization header
   * @param body a map containing "newTenantId" and "refreshToken"
   * @return a ResponseEntity containing the refreshed token data or an error message
   */
  @PutMapping("/tenant")
  public ResponseEntity<Map<String, Object>> switchTenantId(
      @RequestHeader(HttpHeaders.AUTHORIZATION) String accessToken,
      @RequestBody Map<String, String> body) {

    return tenantService.switchTenantAndRefresh(accessToken, body);
  }
}
