/* (C)2024-2025 */
package com.codemonkeys.wallet.application.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "keycloak")
@Data
public class KeycloakProperties {
  private String authServerUrl;
  private String realm;
  private String resource;
  private Credentials credentials;

  @Data
  public static class Credentials {
    private String secret;
  }
}
