server:
  servlet:
    context-path: /api
spring:
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  jackson:
    deserialization:
      ACCEPT_EMPTY_STRING_AS_NULL_OBJECT: true
  application:
    name: wallet
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ***************************************
    username: postgres
    password: postgres
  jpa:
    generate-ddl: false
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        tenant_identifier_resolver: com.codemonkeys.wallet.application.tenant.TenantIdentifierResolver
  liquibase:
    enabled: true
    change-log: classpath:liquibase/db-changelog-master.xml
  mail:
    host: smtp.eu.mailgun.org
    port: 587
    username: <EMAIL>
    password: **************************************************
    smtp-auth: true
    starttls-enable: true
    mime-charset: UTF-8
    debug: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://keycloak.dev.es-t.pl/realms/wallet
          jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs

email:
  technical-addresses:
    enabled: true
    recipients:
      - <EMAIL>
  templates:
    application-url: https://walletv2.dev.es-t.pl
    sender:
      from-address: <EMAIL>
      from-name: Portfolio Management
      reply-to: <EMAIL>


keycloak:
  auth-server-url: https://keycloak.dev.es-t.pl
  realm: wallet
  resource: wallet
  # TODO do ustawienia ponizej redirect-uri względem adresu srodowiska
  redirect-uri: https://walletv2.dev.es-t.pl/auth/callback
  token-uri: https://keycloak.dev.es-t.pl/realms/wallet/protocol/openid-connect/token
  credentials:
    secret: WEtq16WeqEKHEGEozObV5Wl9Kq0tr36Q

jwt:
  auth:
    converter:
      resource-id: wallet
      principle-attribute: preferred_username
    mvc:
      versioned-context-path: /api/v1


storage:
  access-key: reiRbrV4TBM3cQIBtbcV
  secret-key: bRTcFC9hAml4S6Dyxg8Outs9tgpDU5g1kAbo6RYF
  endpoint: http://127.0.0.1:9000
  prefix: uploads
  bucket: wallet

archive-agreements:
  cron: "@daily"
attachment:
  cron: "@daily"
price-email:
  cron: 0 0 19 * * ?
price:
  cron: 0 0 17 * * *
  retry:
    max-attempts: 3
    delay: 2000
    multiplier: 1.5
  prices:
    energy:
      url: https://tge.pl/energia-elektryczna-otf
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazOtfPriceService
      expectedRecords:
        days:
          MONDAY: 7
          TUESDAY: 7
          WEDNESDAY: 7
          THURSDAY: 7
          FRIDAY: 7
          SATURDAY: 0
          SUNDAY: 0
        holiday: 0
      expectedContracts:
        days:
          ANY:
            - regex: "BASE_Y-\\d{2}"
              min: 2
            - regex: "BASE_Q-[1-4]-\\d{2}"
              min: 4
            - regex: "BASE_M-\\d{2}-\\d{2}"
              min: 1
    gas-otf:
      url: https://tge.pl/gaz-otf
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazOtfPriceService
      expectedRecords:
        days:
          MONDAY: 7
          TUESDAY: 7
          WEDNESDAY: 7
          THURSDAY: 7
          FRIDAY: 7
          SATURDAY: 0
          SUNDAY: 0
        holiday: 0
      expectedContracts:
        days:
          ANY:
            - regex: "GAS_BASE_Y-\\d{2}"
              min: 2
            - regex: "GAS_BASE_Q-[1-4]-\\d{2}"
              min: 4
            - regex: "GAS_BASE_M-\\d{2}-\\d{2}"
              min: 1
    energy-rdn-fixing-1:
      url: https://tge.pl/energia-elektryczna-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.FixingOneRdnPriceService
      expectedRecords:
        days:
          MONDAY: 24
          TUESDAY: 24
          WEDNESDAY: 24
          THURSDAY: 24
          FRIDAY: 24
          SATURDAY: 24
          SUNDAY: 24
        holiday: 24
      expectedContracts:
        days:
          ANY:
            - regex: "TGe24-H(?:[1-9]|1[0-9]|2[0-4])"
              min: 24
    property-rights:
      url: https://tge.pl/prawa-majatkowe-rpm
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.PropertyRightsPriceService
      expectedRecords:
        days:
          MONDAY: 0
          TUESDAY: 3
          WEDNESDAY: 0
          THURSDAY: 3
          FRIDAY: 0
          SATURDAY: 0
          SUNDAY: 0
        holiday: 0
      expectedContracts:
        days:
          TUESDAY:
            - exact: "PMOZE_A"
            - exact: "PMOZE-BIO"
            - exact: "PMEF_F"
          THURSDAY:
            - exact: "PMOZE_A"
            - exact: "PMOZE-BIO"
            - exact: "PMEF_F"
    energy-rdn:
      url: https://tge.pl/energia-elektryczna-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazRdnPriceService
      expectedRecords:
        days:
          MONDAY: 1
          TUESDAY: 1
          WEDNESDAY: 1
          THURSDAY: 1
          FRIDAY: 1
          SATURDAY: 1
          SUNDAY: 1
        holiday: 1
      expectedContracts:
        days:
          ANY:
            - exact: "TGe24"
    gas-rdn:
      url: https://tge.pl/gaz-rdn
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.EnergyGazRdnPriceService
      expectedRecords:
        days:
          MONDAY: 1
          TUESDAY: 1
          WEDNESDAY: 1
          THURSDAY: 1
          FRIDAY: 1
          SATURDAY: 1
          SUNDAY: 1
        holiday: 1
      expectedContracts:
        days:
          ANY:
            - exact: "TGEgasDA"
    gas-rdb:
      url: https://tge.pl/gaz-rdb
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.GazRdbPriceService
      expectedRecords:
        days:
          MONDAY: 1
          TUESDAY: 1
          WEDNESDAY: 1
          THURSDAY: 1
          FRIDAY: 1
          SATURDAY: 1
          SUNDAY: 1
        holiday: 1
      expectedContracts:
        days:
          ANY:
            - exact: "TGEgasID"
    wibor1r:
      url: https://stooq.pl/q/d/l/?s=plopln1y&i=d
      enabled: true
      type: com.codemonkeys.wallet.price.service.scraper.WiborCsvPriceService
      expectedRecords:
        days:
          MONDAY: 1
          TUESDAY: 1
          WEDNESDAY: 1
          THURSDAY: 1
          FRIDAY: 1
          SATURDAY: 0
          SUNDAY: 0
        holiday: 0


logging:
  level:
    root: info
    org.springframework: INFO
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: INFO
    org.springframework.retry: INFO
#    org.hibernate.SQL: DEBUG
#    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
#    org.hibernate.orm.jdbc.bind: TRACE
    org.hibernate.engine.jdbc.spi.SqlExceptionHelper: INFO
    org.springframework.security.web: INFO
    com.codemonkeys.wallet.analytical.handlers.dashed.VWAPCalculator: INFO