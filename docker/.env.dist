BACKEND_IMAGE_TAG=a380247
ADMIN_IMAGE_TAG=72fa845

DATABASE_URL=*********************************************
ADMIN_DATABASE_USERNAME=postgres
ADMIN_DATABASE_PASSOWRD=postgres
DATABASE_USERNAME=wallet
DATABASE_PASSWORD=wallet
MINIO_ROOT_USER=minio
MINIO_ROOT_PASSWORD=minio-root
NEXT_PUBLIC_API_URL=http://localhost:8080
DATABASE_NAME=wallet
POSTGRES_MULTIPLE_DATABASES=wallet,keycloak



KC_DB=postgres
KC_PORT=8081:8080
KC_DB_URL_HOST=wallet-database
KC_DB_URL_PORT=5432
KC_DB_SCHEMA=public
KC_DB_URL_DATABASE=keycloak
KC_DB_USERNAME=keycloak
KC_DB_PASSWORD=keycloak
KEYCLOAK_ADMIN_PASSWORD=test123
#KC_LOG_LEVEL=DEBUG
#,org.keycloak.admin.ui:DEBUG,io.quarkus.http.access-log:TRACE
QUARKUS_HTTP_ACCESS_LOG_ENABLED=true
QUARKUS_HTTP_ACCESS_LOG_PATTERN=%r\n%{ALL_REQUEST_HEADERS}
