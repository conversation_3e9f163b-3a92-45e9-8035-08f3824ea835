<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.codemonkeys</groupId>
    <artifactId>parent</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <artifactId>domain</artifactId>
  <name>Domain</name>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>cz.habarta.typescript-generator</groupId>
        <artifactId>typescript-generator-maven-plugin</artifactId>
        <version>${typescript-generator.version}</version>

        <executions>
          <execution>
            <id>generate-dts</id>
            <phase>process-classes</phase>
            <goals><goal>generate</goal></goals>

            <configuration>
              <outputFile>${project.parent.basedir}/../shared-types/src/index.d.ts</outputFile>
              <outputKind>module</outputKind>
              <outputFileType>declarationFile</outputFileType>
              <classPatterns>
                <pattern>com.codemonkeys.tds.**</pattern>
              </classPatterns>

              <jsonLibrary>jackson2</jsonLibrary>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
