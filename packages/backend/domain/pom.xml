<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>


  <parent>
    <groupId>com.yourcompany</groupId>
    <artifactId>backend-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <artifactId>domain</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>cz.habarta.typescript-generator</groupId>
        <artifactId>typescript-generator-maven-plugin</artifactId>
        <version>${typescript-generator.version}</version>
        <executions>
          <execution>
            <id>generate-dts</id>
            <phase>generate-sources</phase>
            <goals><goal>generate</goal></goals>
            <configuration>
              <outputFile>${project.basedir}/../../frontend/src/generated/index.d.ts</outputFile>
              <outputKind>module</outputKind>
              <outputFileType>declarationFile</outputFileType>
              <classPatterns>
                <classPattern>com.yourcompany.domain.dto.**</classPattern>
              </classPatterns>
              <mapPackages>
                <mapPackage>
                  <from>java.time</from>
                  <to>string</to>
                </mapPackage>
              </mapPackages>
              <jsonLibrary>jackson2</jsonLibrary>
              <nonConstEnums>true</nonConstEnums>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
