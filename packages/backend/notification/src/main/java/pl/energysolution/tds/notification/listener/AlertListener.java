package pl.example.tds.notification.listener;


import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.stereotype.Component;


@Component
public class AlertListener {

    @ServiceActivator(inputChannel = "notifications-inbound")
    public void onMessage(String payload) {
        // TODO parse JSON → decide what to notify
        System.out.println("Received alert event: " + payload);
    }
}
