package pl.example.tds.alerts.listener;

import com.azure.spring.messaging.servicebus.annotation.ServiceBusQueueListener;
import org.springframework.stereotype.Component;

@Component
public class AlertsListener {

    @ServiceBusQueueListener(queueName = "alerts-inbound")
    public void onMessage(String payload) {
        System.out.println("Alerts received: " + payload);
        // TODO implement business logic
    }
}
