package pl.example.tds.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO przechowujący surowy JSON od urządzenia.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RawJson {

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("payload")
    private String payload; // or JsonNode if you prefer

    @JsonProperty("receivedAt")
    private String receivedAt; // ISO8601
}
