export interface TrancheLite {
  id?: string;
  timeUnit?: string;
  contract?: { id?: string; name?: string };
  executionDate?: string;
  size?: number;
  price?: number;
  virtual?: boolean;
}

const PROPERTY_RIGHTS_ORDER = ['PMOZE_A', 'PMOZE_BIO', 'PMEF_F'];

/** wyciąga Y / Q1-4 / M1-12 z nazwy (BASE_M-06-25 → M6) */
const parseUnitFromName = (name?: string): string | undefined => {
  if (!name) return;

  if (/_Y-\d{2}$/i.test(name)) return 'Y';

  const q = name.match(/_Q-(\d)-/i);
  if (q) return `Q${q[1]}`;

  const m = name.match(/_M-(\d{2})-/i);
  if (m) return `M${Number(m[1])}`;
};

/** Y=0, Q1=1 … Q4=4, M1=5 … M12=16 */
const unitWeight = (unit?: string): number | undefined => {
  if (!unit) return;

  if (unit === 'Y') return 0;

  const q = unit.match(/^Q([1-4])$/);
  if (q) return 1 + Number(q[1]) - 1;

  const m = unit.match(/^M([1-9]|1[0-2])$/);
  if (m) return 5 + (Number(m[1]) - 1);
};

/* ---------- ranking ---------- */

export const getTrancheRank = (t: TrancheLite): number => {
  /** 1) base contracts – name */
  const unit = parseUnitFromName(t.contract?.name) || t.timeUnit;
  const w = unitWeight(unit);
  if (w !== undefined) return w;

  /** 2) property rights */
  const prIdx = PROPERTY_RIGHTS_ORDER.indexOf(t.contract?.name ?? '');
  if (prIdx !== -1) return 100 + prIdx;

  /** 3) others */
  return Number.MAX_SAFE_INTEGER;
};

/* ---------- sort + split ---------- */

export const sortTranches = <T extends TrancheLite>(arr: T[]): T[] =>
  [...arr].sort((a, b) => getTrancheRank(a) - getTrancheRank(b));

export const splitByPropertyRights = <T extends TrancheLite>(arr: T[]) => ({
  base: arr.filter((t) => !PROPERTY_RIGHTS_ORDER.includes(t.contract?.name ?? '')),
  rights: arr.filter((t) => PROPERTY_RIGHTS_ORDER.includes(t.contract?.name ?? '')),
});
