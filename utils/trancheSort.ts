import { TimeUnit } from '@/types/Wallet';

export interface TrancheLite {
  id?: string;
  timeUnit?: TimeUnit | string;
  contract?: { id?: string; name?: string };
  executionDate?: string;
  size?: number;
  price?: number;
  virtual?: boolean;
}

/* prawa maj<PERSON>tkowe */
const PROPERTY_RIGHTS_ORDER = ['TGe24', 'PMOZE_A', 'PMOZE_BIO', 'PMEF_F'];

/* ---------- helpers ---------- */

const parseTimeUnitFromName = (name?: string): string | undefined => {
  if (!name) return;

  if (/_Y-\d{2}$/.test(name)) return 'Y';

  const q = name.match(/_Q-(\d)-/);
  if (q) return `Q${q[1]}`;

  const m = name.match(/_M-(\d{2})-/);
  if (m) return `M${parseInt(m[1], 10)}`; // „08” → 8
};

const timeUnitWeight = (tu?: string): number | undefined => {
  if (!tu) return;

  if (tu === 'Y') return 0;

  const q = tu.match(/^Q([1-4])$/);
  if (q) return 1 + Number(q[1]) - 1; // 1-4

  const m = tu.match(/^M(\d{1,2})$/);
  if (m) return 5 + (Number(m[1]) - 1); // 5-16 (M1→5 … M12→16)
};

/* ---------- ranking ---------- */

export const getTrancheRank = (t: TrancheLite): number => {
  const tu = t.timeUnit ?? parseTimeUnitFromName(t.contract?.name);
  const w = timeUnitWeight(tu);
  if (w !== undefined) return w;

  const prIdx = PROPERTY_RIGHTS_ORDER.indexOf(t.contract?.name ?? '');
  if (prIdx !== -1) return 100 + prIdx; // prawa majątkowe > 99

  return Number.MAX_SAFE_INTEGER;
};

/* ---------- sort + split ---------- */

export const sortTranches = <T extends TrancheLite>(list: T[]): T[] =>
  [...list].sort((a, b) => getTrancheRank(a) - getTrancheRank(b));

export const splitByPropertyRights = <T extends TrancheLite>(list: T[]) => ({
  base: list.filter((t) => !PROPERTY_RIGHTS_ORDER.includes(t.contract?.name ?? '')),
  rights: list.filter((t) => PROPERTY_RIGHTS_ORDER.includes(t.contract?.name ?? '')),
});