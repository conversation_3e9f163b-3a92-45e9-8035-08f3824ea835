import { showNotification } from '@mantine/notifications';
import { NotificationType } from '@/types/Common';

export const notify = (type: NotificationType, message: string) => {
  showNotification({
    title:
      type === NotificationType.SUCCESS
        ? 'Sukces'
        : type === NotificationType.INFO
          ? 'Informacja'
          : 'Błąd',
    message,
    color:
      type === NotificationType.SUCCESS
        ? 'green'
        : type === NotificationType.INFO
          ? 'yellow'
          : 'red',
    autoClose: 120000,
  });
};
